<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.wormhole</groupId>
        <artifactId>wormhole-hotelds</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>wormhole-hotelds-service</artifactId>
    <version>1.0.0</version>

    <name>wormhole-hotelds-service</name>
    <packaging>jar</packaging>


    <dependencies>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-hotelds-api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Wormhole -->

        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-agent-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-wechat-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-object-storage-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-mq-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-channel-client</artifactId>
        </dependency>

        <!-- spring-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <!-- Job -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk</artifactId>
            <classifier>shade</classifier>
        </dependency>
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk-biz</artifactId>
            <!--      <classifier>shade</classifier>-->
        </dependency>

    </dependencies>

    <build>
        <finalName>wormhole-hotelds</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/test/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>--add-opens java.desktop/java.beans=ALL-UNNAMED</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
