//package com.wormhole.platform.agent.controller;
//
//import com.wormhole.common.result.Result;
//import com.wormhole.common.util.JacksonUtils;
//import com.wormhole.platform.Application;
//import com.wormhole.platform.web.model.req.SpaceSaveReq;
//import com.wormhole.platform.agent.vo.ImageInfo;
//import com.wormhole.platform.web.model.vo.SpaceVO;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@ActiveProfiles("local")
//@SpringBootTest(classes = {Application.class})
//class SpaceControllerTest {
//    @Resource
//    private SpaceController spaceController;
//
//    @Test
//    void create() {
//        SpaceSaveReq spaceSaveReq = new SpaceSaveReq();
//        spaceSaveReq.setName("0218");
//        spaceSaveReq.setDescription("完整测试");
//        ImageInfo imageInfo = new ImageInfo();
//        imageInfo.setName("百度");
//        imageInfo.setUrl("http://www.baidu.com");
//        spaceSaveReq.setImageInfo(imageInfo);
//        Result<SpaceVO> block = spaceController.create(spaceSaveReq).block();
//        System.out.println(JacksonUtils.writeValueAsString(block.getData()));
//    }
//
//    @Test
//    void delete() {
//    }
//
//    @Test
//    void findById() {
//    }
//
//    @Test
//    void list() {
//    }
//}