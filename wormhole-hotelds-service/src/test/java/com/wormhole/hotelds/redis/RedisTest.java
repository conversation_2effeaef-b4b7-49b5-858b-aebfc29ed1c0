package com.wormhole.hotelds.redis;

import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.util.CodeGeneratorUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonReactiveClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.ReactiveRedisTemplate;

import java.time.Duration;

@SpringBootTest(classes = Application.class)
class RedisTest {

    @Resource
    ReactiveRedisTemplate reactiveRedisTemplate;

    @Resource
    RedissonReactiveClient redissonReactiveClient;

    @Resource
    private CodeGeneratorUtils codeGeneratorUtils;

    @Test
    void save() {
        reactiveRedisTemplate.opsForValue().set("demo-test", "first", Duration.ofMinutes(1)).block();
    }

    @Test
    void get() {
        reactiveRedisTemplate.opsForValue().get("demo-test").doOnNext(System.out::println).block();
    }

    @Test
    void delete() {
        reactiveRedisTemplate.opsForValue().delete("demo-test").doOnNext(System.out::println).block();
    }

    @Test
    void getValue() {
        redissonReactiveClient.getMap("redisson").get("demo-test").doOnNext(System.out::println).block();
    }

    @Test
    void setValue() {
        redissonReactiveClient.getMap("redisson")
                .put("demo-test", "first")
                .doOnNext(System.out::println)
                .block();
    }

    @Test
    void generateHotelId(){
        codeGeneratorUtils.generateCode(BussinessTypeEnum.BRAND.getBusinessType()).block();
    }

}