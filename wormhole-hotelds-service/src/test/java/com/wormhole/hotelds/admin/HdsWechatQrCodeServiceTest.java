package com.wormhole.hotelds.admin;

import com.google.common.collect.Lists;
import com.wormhole.common.result.PageResult;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.admin.model.req.DevicePositionSearchReq;
import com.wormhole.hotelds.admin.model.req.WechatQrCodeDownlandReq;
import com.wormhole.hotelds.admin.model.req.WechatQrCodeSearchReq;
import com.wormhole.hotelds.admin.model.vo.HdsWechatQrCodeVO;
import com.wormhole.hotelds.admin.service.HdsWechatQrCodeService;
import com.wormhole.hotelds.constant.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/4/22 10:01
 */
@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
public class HdsWechatQrCodeServiceTest {

    @Autowired
    private HdsWechatQrCodeService hdsWechatQrCodeService;

    @Test
    void downlandByPositionCode() {
        String hotelCode = "HWI8H5G";
        String positionCode = "XR8F3I0";

//        String block = hdsWechatQrCodeService.downlandByPositionCode(hotelCode, positionCode, QrCodeTypeEnum.WECHAT.getCode()).block();
//        System.out.println(block);
        // 普通二维码
        Mono<Boolean> booleanMono = hdsWechatQrCodeService.getOrCreateEntity(hotelCode, positionCode)
                .flatMap(entity -> hdsWechatQrCodeService.generateNormalQrCodeAndPoster(hotelCode, positionCode, "2002", entity));
        Boolean block = booleanMono.block();
        System.err.println(block);

//        Boolean block1 =
//                hdsWechatQrCodeService.generateNormalQrCodeAndPoster(hotelCode, positionCode, "", null).block();
//        System.err.println(block1);

        // 微信二维码
//        System.err.println("开始生成微信二维码, 酒店编码: " + hotelCode + ", 位置编码: " + positionCode);
//        String block1 = hdsWechatQrCodeService.handleQrOrPoster(hotelCode, positionCode, true, "3201").block();
//        System.err.println(block1);
//        System.err.println("微信二维码生成完成");

    }

    @Test
    void downlandByIds() {
        WechatQrCodeDownlandReq req = new WechatQrCodeDownlandReq();
        req.setIdList(Lists.newArrayList("5", "6"));
        String block = hdsWechatQrCodeService.downlandByIds(req).block();
        System.out.println(block);
    }

    @Test
    void test() {
        DevicePositionSearchReq wechatQrCodeReq = new DevicePositionSearchReq();
        wechatQrCodeReq.setCurrent(1);
        wechatQrCodeReq.setPageSize(10);
        wechatQrCodeReq.setHotelCode("H001");

        PageResult<HdsWechatQrCodeVO> block = Mono.zip(hdsWechatQrCodeService.searchCount(wechatQrCodeReq), hdsWechatQrCodeService.search(wechatQrCodeReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2())).block();
        System.out.println(block);
    }

}
