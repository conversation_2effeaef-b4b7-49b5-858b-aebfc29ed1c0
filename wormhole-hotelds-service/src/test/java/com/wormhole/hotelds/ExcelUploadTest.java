package com.wormhole.hotelds;

import com.wormhole.common.excel.ReactiveExcelUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.storage.FileService;
import com.wormhole.hotelds.storage.file.FileAdapter;
import com.wormhole.hotelds.storage.file.FileInput;
import com.wormhole.hotelds.storage.model.FileUploadResultDTO;
import com.wormhole.hotelds.storage.model.ObjectFileTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/3/28 15:00
 * @Description：ExcelUploadTest
 */
@SpringBootTest(classes = Application.class)
public class ExcelUploadTest {

    @Resource
    private FileService fileService;

    @Test
    public void testUpload() {
//        File file = new File("/Users/<USER>/Downloads/设备位置导入模板.xlsx");
        File file = new File("/Users/<USER>/Downloads/用户导入模板.xlsx");

        String filePath = "/Users/<USER>/Downloads/用户导入模板.xlsx";
        System.out.println("filePath: " + filePath);
        FileInput fileInput = new FileAdapter(file);
        FileUploadResultDTO fileUploadResult = fileService.upload("hotelds/file", fileInput).block();
        System.out.println(JacksonUtils.writeValueAsString(fileUploadResult));
//        List<Map<Integer, String>> dataList = ReactiveExcelUtils.readExcelToFlux(file).collectList().block();
//        System.out.println(JacksonUtils.writeValuePretty(dataList));
    }
}
