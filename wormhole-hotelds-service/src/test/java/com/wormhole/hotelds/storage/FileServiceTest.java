package com.wormhole.hotelds.storage;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.internal.SkipMd5CheckStrategy;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.BaseTest;
import com.wormhole.hotelds.storage.config.BucketProperties;
import com.wormhole.hotelds.storage.file.FileAdapter;
import com.wormhole.hotelds.storage.file.FileInput;
import com.wormhole.hotelds.storage.model.*;
import com.wormhole.storage.model.ObjectMetaData;
import com.wormhole.storage.model.StorageParams;
import com.wormhole.storage.service.CosObjectStorageService;
import com.wormhole.storage.service.OssObjectStorageService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.nio.charset.StandardCharsets;

class FileServiceTest extends BaseTest {

    private static final String HOTEL_CODE_LIST_PATH = "bdw/bdw-hotel-manage/hotel_info/%s/hotel_codes_%s.json";

    @Resource
    private FileService fileService;
    @Resource
    private OssObjectStorageService ossObjectStorageService;

    @Resource
    private CosObjectStorageService cosObjectStorageService;
    @Resource
    private BucketProperties bucketProperties;

    @Test
    void testGetContent() {
        String date = "2025-01-18";
        String objectKey = String.format(HOTEL_CODE_LIST_PATH, date, date);
        String content = fileService.getObjectContent(bucketProperties.getCommonBucketName(), objectKey, StandardCharsets.UTF_8.name()).block();
        System.out.println(content);
    }

    @Test
    void testUpload() {

//        String filePath = System.getProperty("user.home") + "/Documents/ai-paper/2409.14924v1.pdf";
//        String filePath = "/Users/<USER>/file/图标/腾讯云.png";
//        System.out.println("filePath: " + filePath);
//        File file = new File(filePath);
//        FileInput fileInput = new FileAdapter(file);
//        FileUploadResultDTO fileUploadResult = fileService.upload(ObjectFileTypeEnum.img.name(), fileInput).block();
//        System.out.println(JacksonUtils.writeValueAsString(fileUploadResult));

        // 1 初始化用户身份信息（secretId, secretKey）。
//        String secretId = "AKIDPz74vb9Wjsz5EHCBC4mZMA1nhU6c2LWY";
//        String secretKey = "340ACLVjsskQzNet15zgAXounaDdZM1X";
//        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
//// 2 设置 bucket 的地域, COS 地域的简称请参照 https://cloud.tencent.com/document/product/436/6224
//// clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
//        Region region = new Region("ap-guangzhou");
//        ClientConfig clientConfig = new ClientConfig(region);
//// 这里建议设置使用 https 协议
//        clientConfig.setHttpProtocol(HttpProtocol.https);
//// 3 生成 cos 客户端。
//        COSClient cosClient = new COSClient(cred, clientConfig);
//// Bucket 的命名格式为 BucketName-APPID ，此处填写的存储桶名称必须为此格式
//        String bucketName = "bdw-test-1326469441";
//
//        System.setProperty(SkipMd5CheckStrategy.DISABLE_PUT_OBJECT_MD5_VALIDATION_PROPERTY, "true");
//
////        System.setProperty(SkipMd5CheckStrategy.DISABLE_PUT_OBJECT_MD5_VALIDATION_PROPERTY, "true");
//
//
//        String content = "Hello COS";
//        String key = "test.java";
//        PutObjectResult putObjectResult = cosClient.putObject(bucketName, key, content);
//        String requestId = putObjectResult.getRequestId();
//        System.out.println(requestId);
        StorageParams storageParams  = StorageParams.builder()
                .bucketName("test-bwagent-1326469441")
                .objectKey("wormhole/hotelds/file/2025/07/18/20250718134817268b32dd0412.xlsx")
                        .build();
        String objectUrl = ossObjectStorageService.getObjectUrl(storageParams);
        System.out.println(objectUrl);

    }

    @Test
    void testDownload() {
        StorageParams storageParams = StorageParams.builder()
                .bucketName(bucketProperties.getCommonBucketName())
                .objectKey("wormhole/docs/20250120154926151a52ef536f.pdf")
                .build();
        String filePath = System.getProperty("user.home") + "/Documents/ai-paper/2409.14924-test.pdf";
        File file = new File(filePath);
        ObjectMetaData objectMetaData = ossObjectStorageService.getObject(storageParams, file).block();
        System.out.println(JacksonUtils.writeValueAsString(objectMetaData));
    }

    @Test
    void testDelete() {
        StorageParams storageParams = StorageParams.builder()
                .bucketName(bucketProperties.getCommonBucketName())
                .objectKey("wormhole/docs/20250120154926151a52ef536f.pdf")
                .build();
        ossObjectStorageService.deleteObject(storageParams).block();
    }
}