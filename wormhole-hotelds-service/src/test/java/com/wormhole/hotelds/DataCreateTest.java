package com.wormhole.hotelds;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.wormhole.hotelds.admin.model.req.CtripHotelPlaceInfoReq;
import com.wormhole.hotelds.admin.repository.*;
import com.wormhole.hotelds.admin.service.CtripLinkParseService;
import com.wormhole.hotelds.config.CommonProperties;
import com.wormhole.hotelds.core.enums.DeviceStatusEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceInfoFieldEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceModelEntity;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.SimpleDateUtils;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.cashier.CashierClient;
import com.yeepay.yop.sdk.service.cashier.CashierClientBuilder;
import com.yeepay.yop.sdk.service.cashier.request.UnifiedOrderRequest;
import com.yeepay.yop.sdk.service.cashier.response.UnifiedOrderResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * @Author：flx
 * @Date：2025/3/28 14:07
 * @Description：DataCreateTest
 */
@Slf4j
@SpringBootTest(classes = Application.class)
public class DataCreateTest {

    @Resource
    private HotelRepository hotelRepository;

    @Resource
    private BrandRepository brandRepository;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private DeviceModelRepository deviceModelRepository;

    @Resource
    private DeviceRepository deviceRepository;

    @Resource
    private DevicePositionRepository devicePositionRepository;

    @Resource
    private DeviceInfoRepository deviceInfoRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private CtripLinkParseService ctripLinkParseService;

    private final CashierClient api = CashierClientBuilder.builder().build();

    @Test
    public void createData() {
//        System.out.println(SimpleDateUtils.isValidSlashDateFormat("2024-01-01"));
//        System.out.println(SimpleDateUtils.slashStringToStartDateTime("2024/01/01"));
//        System.out.println(SimpleDateUtils.slashStringToStartDateTime("2025/2/20"));

//        codePoolManager.clearPool("hotel").block();
//        codePoolManager.clearPool("brand").block();
//        codePoolManager.clearPool("merchant").block();
//        List<HdsRoomEntity> roomEntityList = new ArrayList<>();
//        for (int i = 0; i < 12; i++) {
//            HdsRoomEntity roomEntity = new HdsRoomEntity();
//            roomEntity.setHotelId(i);
//            roomEntity.setRoomNum(String.valueOf(100 + i));
//            roomEntity.setFloor(7);
//            roomEntity.setRoomTypeName("大床房");
//            roomEntity.setWifiSsid("wifi" + i);
//            roomEntity.setWifiPassword("123456");
//            roomEntityList.add(roomEntity);
//        }
//        roomRepository.saveAll(roomEntityList)
//                .doOnNext(roomEntity -> System.out.println("Saved room: " + roomEntity))
//                .doOnError(error -> System.err.println("Error saving room: " + error.getMessage()))
//                .blockLast();;
//        List<HdsHotelInfoEntity> hotelEntityList = new ArrayList<>();
//        for (int i = 0; i < 12; i++) {
//            HdsHotelInfoEntity hotelEntity = new HdsHotelInfoEntity();
//            hotelEntity.setHotelId("H" + String.format("%03d", i))
//                    .setHotelCode("HC" + String.format("%03d", i))
//                    .setHotelName("测试酒店" + i)
//                    .setHotelNamePinYin("CeshiJiudian" + i)
//                    .setHotelType(i % 2 + 1)  // 1单体 2连锁
//                    .setTotalRoom(100 + i * 10)
//                    .setDeviceStatus(1)
//                    .setBookFlag(i % 4)  // 0关店 1营业 2筹备中 3暂停营业
//
//                    // 品牌和商家信息
//                    .setBrandCode("BC" + String.format("%03d", i))
//                    .setBrandName("测试品牌" + i)
//                    .setMerchantId("M" + String.format("%03d", i))
//                    .setSubjectName("测试企业" + i)
//
//                    // 联系人信息
//                    .setMainPerson("联系人" + i)
//                    .setPhone("1380000" + String.format("%04d", i))
//                    .setEmail("test" + i + "@example.com")
//                    .setOpenDate("2024-" + String.format("%02d", (i % 12) + 1) + "-01")
//
//                    // 地址信息
//                    .setCountryCode("CN")
//                    .setCountryName("中国")
//                    .setProvinceCode("BJ")
//                    .setProvinceName("北京市")
//                    .setCityCode("BJ01")
//                    .setCityName("北京市")
//                    .setDistrictCode("BJ0101")
//                    .setDistrictName("朝阳区")
//                    .setAddress("北京市朝阳区测试路" + i + "号")
//
//                    // 其他信息
//                    .setDescription("这是一家位于" + "北京市朝阳区的" + (i % 2 == 0 ? "连锁" : "单体") + "酒店")
//                    .setHotelLogo("https://example.com/hotel/logo/" + i + ".png")
//                    .setCtripEbkUrl("https://ebk.ctrip.com/hotel/" + i);
//
//            // 设置基础字段（继承自BaseEntity）
//            hotelEntity.setRowStatus(1); // 1-有效
//            hotelEntity.setCreatedBy("admin");
//            hotelEntity.setCreatedByName("系统管理员");
//            hotelEntity.setCreatedAt(LocalDateTime.now());
//            hotelEntity.setUpdatedBy("admin");
//            hotelEntity.setUpdatedByName("系统管理员");
//            hotelEntity.setUpdatedAt(LocalDateTime.now());
//            hotelEntityList.add(hotelEntity);
//        }
//        hotelRepository.saveAll(hotelEntityList)
//                .doOnNext(hotelEntity -> System.out.println("Saved hotel: " + hotelEntity))
//                .doOnError(error -> System.err.println("Error saving hotel: " + error.getMessage()))
//                .blockLast();
//        List<HdsBrandEntity> brandEntityList = new ArrayList<>();
//        for (int i = 0; i < 12; i++) {
//            HdsBrandEntity brandEntity = new HdsBrandEntity();
//            brandEntity.setBrandCode("BC" + String.format("%03d", i))
//                    .setBrandName("测试品牌" + i)
//                    .setBrandLogo("https://example.com/brand/logo/" + i + ".png")
//                    .setBrandDesc("这是一家位于" + "北京市朝阳区的" + (i % 2 == 0 ? "连锁" : "单体") + "酒店")
//                    .setMerchantId("M" + String.format("%03d", i))
//                    .setMerchantName("测试企业" + i)
//                    .setStarType(i % 5) // 星级类型
//                    .setStatus(1); // 1-有效
//            brandEntityList.add(brandEntity);
//        }
//        brandRepository.saveAll(brandEntityList)
//                .doOnNext(brandEntity -> System.out.println("Saved brand: " + brandEntity))
//                .doOnError(error -> System.err.println("Error saving brand: " + error.getMessage()))
//                .blockLast();
    }

    @Test
    public void addTestDeviceModels() {
        List<HdsDeviceModelEntity> models = new ArrayList<>();

        for (int i = 1; i <= 12; i++) {
            HdsDeviceModelEntity model = new HdsDeviceModelEntity();
            model.setModelCode("MDL-" + i);
            model.setModelName("测试设备型号" + i);
            model.setCategory(String.valueOf(i % 10)); // 0-9循环
            model.setCompanyName("测试公司" + i);
            model.setManufacturer("测试厂商" + i);
            model.setManufacturerContactPerson("联系人" + i);
            model.setManufacturerContact("138" + String.format("%08d", i));
            model.setImageUrl("https://example.com/img" + i + ".jpg");
            model.setWarrantyMonths(12);
            model.setSpecDesc("这是第" + i + "个测试设备的规格");
            model.setStatus(1);
            model.setRemark("备注" + i);
            model.setTotalInventory(100);
            model.setInventory(80);

            // 设置审计字段
            model.setCreatedAt(LocalDateTime.now());
            model.setCreatedBy("admin");
            model.setCreatedByName("admin");

            models.add(model);
        }

        deviceModelRepository.saveAll(models).blockLast(); // 阻塞等待完成
        System.out.println("添加了12条测试数据");
    }

    @Test
    public void createTestDevicePositions() {
        List<HdsDevicePositionEntity> positions = new ArrayList<>();
        String hotelCode = "H1022";

        // A栋和B栋
        String[] blocks = {"A", "B"};
        for (String block : blocks) {
            // 每栋1-3层
            for (int floor = 1; floor <= 3; floor++) {
                // 每层01-03房间
                for (int room = 1; room <= 3; room++) {
                    String roomNumber = String.format("%d%02d", floor, room);  // 101-103, 201-203, 301-303
                    positions.add(new HdsDevicePositionEntity()
                            .setHotelCode(hotelCode)
                            .setDeviceAppType(room == 1 ? "front" : "room")  // 01房间是前台，其他是客房
                            .setBlock(block)
                            .setArea(String.valueOf(floor))
                            .setPositionCode(String.format("H1022-%s-%d-%s", block, floor, roomNumber))
                            .setPositionName(roomNumber)
                            .setAppTypeSortOrder(room == 1 ? 100 : 90)  // 前台优先级100，客房90
                            .setBlockAreaSortOrder(block.equals("A") ? 90 - floor : 80 - floor)  // A栋优先级高于B栋
                            .setPositionSortOrder(100 - (floor * 10 + room)));  // 按房间号排序
                }
            }
        }

        devicePositionRepository.saveAll(positions)
                .doOnNext(position -> System.out.println("Saved position: " + position))
                .doOnError(error -> System.err.println("Error saving position: " + error.getMessage()))
                .blockLast(); // 阻塞等待完成
        // 生成SQL语句
        System.out.println(positions);
    }


    @Test
    public void createTestDevices() {
        List<HdsDeviceEntity> deviceEntities = new ArrayList<>();
        String hotelCode = "H1022";

        // A栋和B栋
        String[] blocks = {"A", "B"};
        for (String block : blocks) {
            // 每栋1-3层
            for (int floor = 1; floor <= 3; floor++) {
                // 每层01-03房间
                for (int room = 1; room <= 3; room++) {
                    String roomNumber = String.format("%d%02d", floor, room);  // 101-103, 201-203, 301-303
                    deviceEntities.add(new HdsDeviceEntity()
                            .setDeviceSn(UUID.randomUUID().toString())
                            .setHotelCode(hotelCode)
                            .setPositionCode(String.format("H1022-%s-%d-%s", block, floor, roomNumber))
                                    .setDeviceStatus(DeviceStatusEnum.ONLINE.getCode())
                            .setDeviceAppType(room == 1 ? "front" : "room"));
                }
            }
        }

        deviceRepository.saveAll(deviceEntities)
                .doOnNext(position -> System.out.println("Saved position: " + position))
                .doOnError(error -> System.err.println("Error saving position: " + error.getMessage()))
                .blockLast(); // 阻塞等待完成
        System.out.println(deviceEntities);
    }

    @Test
    public void unifiedOrderTest() throws YopClientException {
        UnifiedOrderRequest request = new UnifiedOrderRequest();
        request.setParentMerchantNo("10012426765");
        request.setMerchantNo("10012426765");
        request.setOrderId("ORDER202401010515217305372872");
        request.setOrderAmount(new BigDecimal("0"));
        request.setGoodsName("旺仔牛奶");
        request.setFundProcessType("REAL_TIME");
        request.setNotifyUrl("https://notify.merchant.com/xxx");
        request.setMemo("memo_example");
        request.setExpiredTime("2025-07-09 18:31:03");
        request.setReturnUrl("https://notify.merchant.com/xxx");
        request.setCsNotifyUrl("csNotifyUrl_example");
        request.setBusinessInfo("businessInfo_example");
        request.setPayerInfo("{\"bankCardNo\":\"6225750005882152077\",\"cardName\":\"爱丽丝\",\"idCardNo\":\"130225199006093323\",\"mobilePhoneNo\":\"***********\",\"userID\":\"200578\"}");
        request.setLimitPayType("YJZF,EBANK_B2B,BANK_TRANSFER_PAY");
        request.setCardType("DEBIT");
        request.setAggParam("{\"appId\":\"wx9e13bd68a8f1921e\",\"openId\":\"zml_wechat\",\"scene\":{\"WECHAT\":\"XIANXIA\",\"ALIPAY\":\"XIANXIA\"}}");
        request.setNoCardParam("{\"userNo\":\"111\",\"userType\":\"USER_ID\"}");
        request.setProductInfo("[{\"id\":\"random_reduction_pro\"}]");
        request.setDivideDetail("[{\"amount\":\"金额\",\"ledgerNo\":\"分账商编\",\"divideDetailDesc\":\"分账说明\"}]");
        request.setDivideNotifyUrl("divideNotifyUrl_example");
        request.setOtherTypeParam("{\"checkType\":\"ACCOUNTNO_BANK\",\"customerId\":\"请填客户号\",\"bankAccountNo\":\"请填银行账户号\"}");
        UnifiedOrderResponse response = api.unifiedOrder(request);
        log.info("result:{}", response.getResult());
        // TODO: test validations
    }
}
