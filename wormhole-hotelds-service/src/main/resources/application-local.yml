server:
  port: 8082

management:
  endpoints:
    web:
      exposure:
        include: health
      base-path: /actuator
  endpoint:
    health:
      probes:
        enabled: true
  metrics:
    tags:
      application: ${spring.application.name}
  tracing:
    enabled: true

spring:
  main:
    allow-circular-references: true
  application:
    name: wormhole-hotelds
  reactor:
    context-propagation: auto
  freemarker:
    check-template-location: false
  data:
    redis:
      host: ************
      database: 8
      password: 6iaEg*fXjl6Rzxe*

  r2dbc:
    url: r2dbc:mysql://***********:3306/wormhole?characterEncoding=UTF8&autoReconnect=true&allowMultiQueries=true&rewriteBatchedStatements=true&serverTimezone=GMT%2B8&zeroDateTimeBehavior=convertToNull&useSSL=false
    username: dev_rw
    password: z8E3SuIy.1MlCril8

wormhole:
  agent:
    url: http://localhost:8080
  auth:
    api-key:
      excluded-paths:
        - "/actuator/**"
        - "/api/**"
      valid-api-keys:
        "sk-delonix-office"
  buckets:
    common-bucket-name: test-bwagent-**********
  object-storage:
    configs:
      OSS:
        enabled: false
        buckets:
          - bucket-name: bdw-test
            accessKeyId: LTAI5tLjDNipNDcoyrHzVTkq
            accessKeySecret: ******************************
            public-endpoint: https://oss-cn-shenzhen.aliyuncs.com
            intranet-endpoint: https://oss-cn-shenzhen.aliyuncs.com
            cdn-domain: https://test.img.betterwood.com
            maxSize: 50
            policyExpire: 300
            region: cn-shenzhen
            sts-config:
              bucket-name: bdw-test
              access-key-id: LTAI5tLjDNipNDcoyrHzVTkq
              access-key-secret: ******************************
              region: cn-shenzhen
              roleArn: acs:ram::1908865586809974:role/test-oss-kms
              durationSeconds: 900
      COS:
        enabled: true
        buckets:
          - bucket-name: test-bwagent-**********
            access-key-id: AKIDPz74vb9Wjsz5EHCBC4mZMA1nhU6c2LWY
            access-key-secret: 340ACLVjsskQzNet15zgAXounaDdZM1X
            region: ap-guangzhou
            cdn-domain: https://test-img.bwagent.net
            public-endpoint: https://test-bwagent-**********.cos.ap-guangzhou.myqcloud.com
            intranet-endpoint: https://test-bwagent-**********.cos.ap-guangzhou.myqcloud.com
            enabled: true
            skipMd5Check: true

  chat:
    local:
      read-local-workflow: true

  xxl:
    job:
      addresses: https://dev-xxl-job-admin.delonix.work/xxl-job-admin
      port: 9999
      log-retention-days: 30
      log-path: /var/delonix/logs/local/jobs/

logging:
  level:
    io.asyncer.r2dbc.mysql.client: ERROR

# 海报配置
poster:
  templatePath: poster_template.png
  qrcodeX: 302
  qrcodeY: 690
  qrRedirectUrl: https://dev-chat.bwagent.net/redirect?entryType=1&type=1&accessType=1&withbdwapp=true&hotelCode=%s&positionCode=%s
  wxTemplatePath: wx_poster_template.png
  wxQrcodeY: 575
