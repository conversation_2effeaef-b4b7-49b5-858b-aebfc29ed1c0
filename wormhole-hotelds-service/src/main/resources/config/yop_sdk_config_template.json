{
  "app_key": "<Your appKey>;覆盖优先级：YopRequest >> YopConfig >> 配置文件",
  "server_root": "普通API请求地址。默认https://openapi.yeepay.com/yop-center",
  "yos_server_root": "文件类API请求地址。默认https://yos.yeepay.com/yop-center",
  "sandbox_server_root": "沙箱环境请求地址，默认https://sandbox.yeepay.com/yop-center",
  "preferred_server_roots": [//普通API请求地址列表，默认：https://openapi-a.yeepay.com/yop-center、https://openapi-h.yeepay.com/yop-center
    "https://openapi-a.yeepay.com/yop-center",
    "https://openapi-h.yeepay.com/yop-center"
  ],
  "isv_private_key": [
    {
      "app_key": "<Your appKey>",
      "store_type": "string",
      "cert_type": "RSA2048",
      "value": "<Your RSA2048 private key>"
    },
    {
      "app_key": "<Your appKey>",
      "store_type": "file_p12",
      "cert_type": "RSA2048",
      "password": "123456",
      "value": "/10015004197.p12"
    },
    {
      "app_key": "<Your appKey>",
      "store_type": "string",
      "cert_type": "SM2",
      "value": "<Your SM2 private key>"
    }
  ],
  "isv_encrypt_key": [
    {
      "app_key": "<Your appKey>",
      "store_type": "string",
      "cert_type": "SM4",
      "value": "<Your SM4 encrypt key>"
    }
  ],
  "yop_cert_store": {
    "enable": true,//是否开启本地存储
    "path": "<Your cert store path, 默认/tmp/yop/certs>",
    "valid_after_expire_period": 86400000,//证书过期后可用时间(毫秒)，默认24小时
    "refresh_before_expire_period": 259200000//证书过期前开始刷新时间(毫秒)，默认72小时
  },
  "http_client": {
    "connect_timeout": 10000,//全局连接超时，默认10000
    "connect_request_timeout": 10000,//从连接池获取到连接的超时时间，默认10000
    "read_timeout": 30000,//全局读取超时时间，默认30000
    "max_conn_total": 200,//最大连接数，默认200
    "max_conn_per_route": 100,//每个域下的最大连接数，默认100
    "retry_exceptions": [//当笔可重试异常列表，格式:"{异常类名}:{异常消息}"
      "java.net.UnknownHostException",
      "java.net.ConnectException:No route to host (connect failed)",
      "java.net.ConnectException:Connection refused (Connection refused)",
      "java.net.ConnectException:Connection refused: connect",
      "java.net.SocketTimeoutException:connect timed out"
    ],
    "circuit_breaker": {//域名熔断配置
      "enable": true,//是否开启熔断，默认true
      "yop_exclude_exceptions": [//非熔断异常列表，即出现该异常不计入失败笔数，格式:"{异常类名}:{异常消息}"
        "java.net.SocketTimeoutException:Read timed out"
      ],
      "rules": [//熔断规则列表
        {
          "grade": 2,//熔断策略，默认2，按错误笔数熔断，其他可选项：1(按错误率熔断)
          "count": 2,//熔断阈值，默认2，即错误3笔后进入熔断
          "time_window": 5,//熔断间歇时长(秒，该窗口期后，会进入半开),默认5(单位s)
          "stat_interval_ms": 30000//统计窗口时长(毫秒，失败数会在该窗口内汇总计算)，默认30000(单位ms)
        }
      ]
    }
  },
  "proxy": {
    "host": "",//代理服务器IP
    "port": "",//代理服务器端口，默认值：-1
    "username": "",//代理账号
    "password": "",//代理密码
    "domain": "",//代理域(可选)
    "workstation": ""//代理工作站(可选)
  }
}