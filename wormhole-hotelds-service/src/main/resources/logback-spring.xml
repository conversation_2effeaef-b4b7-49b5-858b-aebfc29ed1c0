<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <property resource="bootstrap.yml"/>

    <!-- 引入 Spring Boot 默认的 logback 配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 引入 Spring Boot 默认的控制台输出配置 -->
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <!--读取NaCos公共配置 application-${ENV}.yml -->
    <!-- 日志存放路径 -->
    <springProperty scope="context" name="LOG_PATH" source="log.path" defaultValue="/var/delonix/logs/wormhole"/>

    <!--读取bootstrap。yml配置-->
    <!-- 环境配置 -->
    <springProperty scope="context" name="ENV" source="spring.profiles.active" defaultValue="env"/>
    <springProperty scope="context" name="PROJECT_NAME" source="spring.application.name" defaultValue="wormhole-hotelds"/>

    <!-- 日志输出格式 FILE_LOG_PATTERN -->
    <property name="file_info_pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS}] [${PROJECT_NAME}] [%thread] [${ENV}] [%level] [%X{traceId}] [%X{spanId}] [%X{url}] [%X{method}] [%X{protocol}] [%X{httpCode}] [%X{ip}] [%X{userId}] [%file:%line] [%logger{50}] %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level> <!-- 只输出 INFO 及以上级别的日志 -->
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 系统日志输出 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${PROJECT_NAME}.info.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${LOG_PATH}/${PROJECT_NAME}.%d{yyyy-MM-dd}.%i.info.log.gz</fileNamePattern>
            <!-- 日志最大的历史 -->
            <maxHistory>3</maxHistory>
            <!--日志文件最大的大小-->
            <MaxFileSize>500MB</MaxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>${file_info_pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!--接受INFO及以上日志（即INFO, WARN, ERROR-->
            <level>INFO</level>
        </filter>
    </appender>
    <!--配置异步打印日志-->
    <appender name="ASYNC_FILE_INFO" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="file_info"/>
        <includeCallerData>true</includeCallerData>
    </appender>


    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${PROJECT_NAME}.error.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${LOG_PATH}/${PROJECT_NAME}.%d{yyyy-MM-dd}.%i.error.log.gz</fileNamePattern>
            <!-- 日志最大的历史 -->
            <maxHistory>3</maxHistory>
            <!--日志文件最大的大小-->
            <MaxFileSize>500MB</MaxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>${file_info_pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!--配置异步打印日志-->
    <appender name="ASYNC_FILE_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="file_error"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="warn"/>
    <logger name="springfox.documentation" level="error"/>
    <logger name="com.alibaba" level="warn"/>
    <logger name="com.github.binarywang" level="warn"/>
    <logger name="org.mybatis" level="error"/>
    <logger name="com.netflix" level="error"/>
    <!--@ResultLog指定输出-->

    <!--系统操作日志-->
    <springProfile name="dev,test,local,prod,intl-prod,task">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_FILE_INFO"/>
            <appender-ref ref="ASYNC_FILE_ERROR"/>
        </root>
    </springProfile>
</configuration>