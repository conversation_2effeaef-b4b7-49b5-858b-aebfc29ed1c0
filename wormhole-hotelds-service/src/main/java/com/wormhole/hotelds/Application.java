package com.wormhole.hotelds;

import com.yeepay.yop.sdk.service.cashier.CashierClient;
import com.yeepay.yop.sdk.service.cashier.CashierClientBuilder;
import com.yeepay.yop.sdk.service.trade.TradeClient;
import com.yeepay.yop.sdk.service.trade.TradeClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.reactive.config.EnableWebFlux;

@Slf4j
@EnableWebFlux
@EnableConfigurationProperties()
@ConfigurationPropertiesScan("com.wormhole")
@ComponentScan(basePackages = {"com.wormhole"})
@SpringBootApplication
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        log.info("==================Application startup success!==================");
    }

    @Bean
    public CashierClient cashierClient() {
        return CashierClientBuilder.builder().build();
    }

    @Bean
    public TradeClient tradeClient(){
        return TradeClientBuilder.builder().build();
    }
}