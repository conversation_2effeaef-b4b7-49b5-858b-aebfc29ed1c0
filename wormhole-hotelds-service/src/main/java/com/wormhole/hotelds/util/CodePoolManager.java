package com.wormhole.hotelds.util;

import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/3 10:46
 * @Description：编码池管理器，用于维护和提供业务编码
 */
@Slf4j
@Component
public class CodePoolManager {

    /**
     * 池子键前缀
     */
    private static final String POOL_KEY_PREFIX = "code:pool:";

    /**
     * 池子大小配置
     */
    private static final int POOL_MIN_SIZE = 100;
    private static final int POOL_MAX_SIZE = 1000;
    private static final int BATCH_SIZE = 50;
    private static final int MAX_RETRY = 3;

    @Resource
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;

    @Resource
    private CodeGeneratorUtils codeGenerator;

    @Resource
    private HotelRepository hotelRepository;

    @Resource
    private BrandRepository brandRepository;

    @Resource
    private MerchantRepository merchantRepository;

    @Resource
    private DeviceRepository deviceRepository;

    @Resource
    private DevicePositionRepository devicePositionRepository;

    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;

    @Resource
    private InvitationCodeRepository invitationCodeRepository;

    @Resource
    private UserLeadRepository userLeadRepository;

    @Resource
    private HotelLeadRepository hotelLeadRepository;

    @Resource
    private HdsPackageRepository hdsPackageRepository;

    @Resource
    private HdsHotelPackageRepository hdsHotelPackageRepository;

    @Resource
    private CodeGeneratorUtils codeGeneratorUtils;

    /**
     * 从池子获取编码
     *
     * @param businessType 业务类型
     * @return 唯一编码
     */
    public Mono<String> getCodeFromPool(String businessType) {
        String poolKey = getPoolKey(businessType);
        return reactiveRedisTemplate.opsForList().size(poolKey)
                .flatMap(size -> {
                    if (size == 0) {
                        // 池子为空，生成新编码
                        return generateSingleValidCode(businessType)
                                .doOnNext(code -> fillPoolAsync(businessType));
                    }

                    // 池子不为空，获取编码
                    return reactiveRedisTemplate.opsForList().rightPop(poolKey)
                            .flatMap(code -> isCodeValid(businessType, code)
                                    .flatMap(isValid -> {
                                        if (isValid) {
                                            log.debug("获取到有效编码: {}", code);
                                            return Mono.just(code);
                                        } else {
                                            log.warn("编码[{}]已被使用，重新获取", code);
                                            return getCodeFromPool(businessType);
                                        }
                                    }))
                            .switchIfEmpty(generateSingleValidCode(businessType));
                })
                .doOnError(e -> log.error("获取编码失败: {}", e.getMessage(), e));
    }

    /**
     * 生成单个有效编码
     *
     * @param businessType 业务类型
     * @return 有效编码
     */
    private Mono<String> generateSingleValidCode(String businessType) {
        return codeGenerator.generateCode(businessType)
                .flatMap(code -> isCodeValid(businessType, code)
                        .flatMap(isValid -> {
                            if (isValid) {
                                return Mono.just(code);
                            } else {
                                return generateSingleValidCode(businessType);
                            }
                        }))
                .retryWhen(Retry.backoff(MAX_RETRY, Duration.ofMillis(100))
                        .maxBackoff(Duration.ofSeconds(1)));
    }

    /**
     * 异步填充池子
     *
     * @param businessType 业务类型
     */
    private void fillPoolAsync(String businessType) {
        String poolKey = getPoolKey(businessType);
        reactiveRedisTemplate.opsForList().size(poolKey)
                .flatMap(size -> {
                    if (size < POOL_MIN_SIZE) {
                        return fillPool(businessType, POOL_MIN_SIZE - size.intValue());
                    }
                    return Mono.empty();
                })
                .doOnError(error -> log.error("业务类型[{}]池子填充失败: {}", businessType, error.getMessage(), error))
                .doOnSuccess(v -> log.info("业务类型[{}]池子填充完成", businessType))
                .subscribe();
    }

    /**
     * 填充池子
     *
     * @param businessType 业务类型
     * @param count        需要生成的数量
     * @return 操作结果
     */
    private Mono<Void> fillPool(String businessType, int count) {
        if (count <= 0) {
            return Mono.empty();
        }
        String poolKey = POOL_KEY_PREFIX + businessType;
        return reactiveRedisTemplate.opsForList().size(poolKey)
                .flatMap(size -> {
                    // 如果池子大小已经超过最大值，不再填充
                    if (size >= POOL_MAX_SIZE) {
                        return Mono.empty();
                    }
                    // 调整填充数量，确保不超过最大池大小
                    int adjustedCount = Math.min(count, POOL_MAX_SIZE - size.intValue());
                    int batchCount = Math.min(adjustedCount, BATCH_SIZE);

                    if (batchCount <= 0) {
                        return Mono.empty();
                    }
                    return generateValidCodes(businessType, batchCount)
                            .flatMap(codes -> {
                                if (codes.isEmpty()) {
                                    return Mono.empty();
                                }
                                // 批量添加到池子
                                List<String> reversedCodes = new ArrayList<>(codes);
                                Collections.reverse(reversedCodes);

                                return reactiveRedisTemplate.opsForList()
                                        .leftPushAll(poolKey, reversedCodes.toArray(new String[0]))
                                        .doOnSuccess(pushCount -> log.info("成功添加{}个编码到池子[{}]",
                                                pushCount, businessType))
                                        .then();
                            });
                });
    }

    /**
     * 生成一批有效编码
     *
     * @param businessType 业务类型
     * @param count        数量
     * @return 有效编码列表
     */
    private Mono<List<String>> generateValidCodes(String businessType, int count) {
        // 生成2倍数量，以应对无效编码的情况
        return Flux.range(0, count * 2)
                .flatMap(i -> codeGenerator.generateCode(businessType))
                .flatMap(code -> isCodeValid(businessType, code)
                        .flatMap(isValid -> isValid ? Mono.just(code) : Mono.empty()))
                // 只取需要的数量
                .take(count)
                .collectList()
                .doOnNext(codes -> {
                    if (codes.size() < count) {
                        log.warn("请求生成{}个编码，但只生成了{}个有效编码",
                                count, codes.size());
                    } else {
                        log.debug("成功生成{}个有效编码", codes.size());
                    }
                })
                .retryWhen(Retry.backoff(MAX_RETRY, Duration.ofMillis(100))
                        .maxBackoff(Duration.ofSeconds(1)));
    }

    /**
     * 检查编码是否有效（未被使用）
     *
     * @param businessType 业务类型
     * @param code         编码
     * @return 是否有效
     */
    private Mono<Boolean> isCodeValid(String businessType, String code) {
        BussinessTypeEnum bussinessTypeEnum = BussinessTypeEnum.getByBusinessType(businessType);
        if (bussinessTypeEnum == null) {
            log.warn("未知业务类型: {}", businessType);
            return Mono.just(false);
        }
        return switch (bussinessTypeEnum) {
            case HOTEL -> hotelRepository.existsByHotelCode(code).map(exists -> !exists);
            case BRAND -> brandRepository.existsByBrandCode(code).map(exists -> !exists);
            case MERCHANT -> merchantRepository.existsByMerchantId(code).map(exists -> !exists);
            case DEVICE -> deviceRepository.existsByDeviceId(code).map(exists -> !exists);
            case DEVICE_POSITION -> devicePositionRepository.existsByPositionCode(code).map(exists -> !exists);
            case EMPLOYEE -> hdsEmployeeRepository.existsByUsername(code).map(exists -> !exists);
            case INVITATION_CODE -> invitationCodeRepository.existsByInvitationCode(code).map(exists -> !exists);
            case USER_LEAD -> userLeadRepository.existsByLeadCode(code).map(exists -> !exists);
            case HOTEL_LEAD -> hotelLeadRepository.existsByLeadCode(code).map(exists -> !exists);
            case DEVICE_AREA_CODE -> devicePositionRepository.existsByAreaCode(code).map(exists -> !exists);
            case PAYMENT_PACKAGE -> hdsPackageRepository.existsByPackageCode(code).map(exists -> !exists);
            case HOTEL_PAYMENT_PACKAGE -> hdsHotelPackageRepository.existsByPackageCode(code).map(exists -> !exists);
            default -> Mono.just(true);
        };
    }

    /**
     * 定期维护池子大小
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000)
    public void maintainPool() {
        log.info("开始定期维护编码池");

        for (BussinessTypeEnum type : BussinessTypeEnum.values()) {
            // 只处理需要编码的业务类型
            if (!BussinessTypeEnum.HOTEL.equals(type)
                    && !BussinessTypeEnum.BRAND.equals(type)
                    && !BussinessTypeEnum.MERCHANT.equals(type)
                    && !BussinessTypeEnum.DEVICE.equals(type)
                    && !BussinessTypeEnum.DEVICE_POSITION.equals(type)
                    && !BussinessTypeEnum.EMPLOYEE.equals(type)
                    && !BussinessTypeEnum.INVITATION_CODE.equals(type)
                    && !BussinessTypeEnum.USER_LEAD.equals(type)
                    && !BussinessTypeEnum.HOTEL_LEAD.equals(type)
                    && !BussinessTypeEnum.DEVICE_AREA_CODE.equals(type)
                    && !BussinessTypeEnum.PAYMENT_PACKAGE.equals(type)
                    && !BussinessTypeEnum.HOTEL_PAYMENT_PACKAGE.equals(type)
            ) {
                continue;
            }

            String businessType = type.getBusinessType();
            final String finalBusinessType = businessType;

            reactiveRedisTemplate.opsForList().size(getPoolKey(businessType))
                    .doOnNext(size -> log.info("业务类型[{}]的池子大小: {}", finalBusinessType, size))
                    .flatMap(size -> {
                        if (size < POOL_MIN_SIZE) {
                            return fillPool(finalBusinessType, POOL_MIN_SIZE - size.intValue());
                        }
                        return Mono.empty();
                    })
                    .doOnError(error -> log.error("业务类型[{}]池子维护失败: {}", finalBusinessType, error.getMessage(), error))
                    .doOnSuccess(v -> log.debug("业务类型[{}]池子维护完成", finalBusinessType))
                    .subscribe();
        }
    }

    /**
     * 获取池子的key
     *
     * @param businessType 业务类型
     * @return 池子key
     */
    private String getPoolKey(String businessType) {
        return POOL_KEY_PREFIX + businessType;
    }

    /**
     * 清理池子（用于测试或重置）
     *
     * @param businessType 业务类型
     * @return 操作结果
     */
    public Mono<Long> clearPool(String businessType) {
        String poolKey = POOL_KEY_PREFIX + businessType;
        return reactiveRedisTemplate.delete(poolKey)
                .doOnSuccess(result -> log.info("已清理业务类型[{}]的池子", businessType));
    }

    /**
     * 批量从池子获取编码
     *
     * @param businessType 业务类型
     * @param size         需要的编码数量
     * @return 编码列表
     */
    public Mono<List<String>> getBatchCodesFromPool(String businessType, int size) {
        if (size <= 0) {
            return Mono.just(Collections.emptyList());
        }

        String poolKey = getPoolKey(businessType);

        return reactiveRedisTemplate.opsForList().size(poolKey)
                .flatMap(poolSize -> {
                    int availableSize = poolSize.intValue();
                    log.debug("业务类型[{}]当前池子大小: {}, 请求数量: {}",
                            businessType, availableSize, size);

                    if (availableSize == 0) {
                        // 池子为空，直接生成新编码
                        return generateValidCodes(businessType, size)
                                .doOnNext(codes -> fillPoolAsync(businessType));
                    }

                    // 从池子中获取可用的编码
                    return getBatchCodesFromPoolWithValidation(businessType, size);
                })
                .doOnError(e -> log.error("批量获取编码失败: businessType={}, size={}, error={}",
                        businessType, size, e.getMessage(), e))
                .retryWhen(Retry.backoff(MAX_RETRY, Duration.ofMillis(100))
                        .maxBackoff(Duration.ofSeconds(1)));
    }

    /**
     * 从池子中获取编码并验证
     */
    private Mono<List<String>> getBatchCodesFromPoolWithValidation(String businessType, int size) {
        String poolKey = getPoolKey(businessType);
        List<String> validCodes = new ArrayList<>();

        return Flux.range(0, size)
                // 一个一个获取编码并验证
                .flatMap(i -> reactiveRedisTemplate.opsForList().rightPop(poolKey)
                        .flatMap(code -> isCodeValid(businessType, code)
                                .flatMap(isValid -> {
                                    if (isValid) {
                                        return Mono.just(code);
                                    } else {
                                        log.warn("编码[{}]已被使用，跳过", code);
                                        return Mono.empty();
                                    }
                                }))
                )
                .collectList()
                .flatMap(codes -> {
                    validCodes.addAll(codes);
                    int remaining = size - validCodes.size();

                    if (remaining > 0) {
                        // 如果获取的有效编码数量不足，生成补充编码
                        log.debug("从池子获取到{}个有效编码，还需要生成{}个",
                                validCodes.size(), remaining);

                        return generateValidCodes(businessType, remaining)
                                .map(supplementCodes -> {
                                    validCodes.addAll(supplementCodes);
                                    return validCodes;
                                });
                    }

                    return Mono.just(validCodes);
                })
                .doOnNext(codes -> {
                    // 如果池子大小低于最小值，异步填充
                    if (codes.size() < POOL_MIN_SIZE) {
                        fillPoolAsync(businessType);
                    }
                });
    }

    /**
     * 生成订单号
     * @return
     */
    public Mono<String> generateOrderNo() {
        return codeGeneratorUtils.generateOrderNo();
    }
}