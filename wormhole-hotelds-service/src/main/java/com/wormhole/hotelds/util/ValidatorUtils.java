package com.wormhole.hotelds.util;

import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.hotelds.admin.model.req.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ValidatorUtils {

    // 公共校验方法
    public static void requireNonNull(Object object, String errorMessage) {
        if (object == null) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

    public static void assertNotBlank(String value, String errorMessage) {
        if (StringUtils.isBlank(value)) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

    public static void checkLength(String value, int maxLength, String errorMessage) {
        if (StringUtils.isBlank(value)) {
            return;
        }
        if (value.length() > maxLength) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

    public static void checkContact(String value, String errorMessage) {
        if (ContactValidatorUtils.isValidMobile(value)) {
            return;
        }
        throw new IllegalArgumentException(errorMessage);
    }

    /**
     * 校验商户保存请求参数
     */
    public static void validateMerchantSaveReq(MerchantSaveReq req) {
        requireNonNull(req, "商户保存请求不能为空");
        assertNotBlank(req.getMerchantName(), "商户名称不能为空");
        assertNotBlank(req.getSubjectName(), "主体名称不能为空");
        requireNonNull(req.getType(), "商户类型不能为空");
        assertNotBlank(req.getCountryName(), "国家名称不能为空");
        assertNotBlank(req.getProvinceName(), "省份名称不能为空");
        assertNotBlank(req.getCityName(), "城市名称不能为空");
        assertNotBlank(req.getDistrictName(), "区域名称不能为空");
        assertNotBlank(req.getAddress(), "详细地址不能为空");
        assertNotBlank(req.getMerchantPhone(), "商户电话不能为空");
        assertNotBlank(req.getMerchantEmail(), "商户邮箱不能为空");
        assertNotBlank(req.getMerchantContacts(), "商户联系人不能为空");
        assertNotBlank(req.getMerchantLicenseUrl(), "营业执照URL不能为空");
        assertNotBlank(req.getAccountName(), "开户名称不能为空");
        assertNotBlank(req.getAccountBank(), "开户银行不能为空");
        assertNotBlank(req.getAccountSubBank(), "开户支行不能为空");
        assertNotBlank(req.getAccountCardNumber(), "银行卡号不能为空");
        assertNotBlank(req.getAccountReserveMobile(), "预留手机号不能为空");
        assertNotBlank(req.getAccountCashMobile(), "提现手机号不能为空");
    }

    public static void validateBrandSaveReq(BrandSaveReq req) {
        requireNonNull(req, "品牌保存请求不能为空");
        assertNotBlank(req.getBrandName(), "品牌名称不能为空");
        assertNotBlank(req.getBrandLogo(), "品牌Logo不能为空");
        requireNonNull(req.getStarType(), "星级类型不能为空");
    }

    public static void validateRoomSaveReq(RoomSaveReq roomSaveReq) {
        requireNonNull(roomSaveReq, "房间保存请求不能为空");
        requireNonNull(roomSaveReq.getHotelId(), "酒店ID不能为空");
        requireNonNull(roomSaveReq.getFloor(), "楼层不能为空");
        assertNotBlank(roomSaveReq.getRoomTypeName(), "房型名称不能为空");
    }

    public static void validateSpaceSaveReq(SpaceSaveReq spaceSaveReq) {
        requireNonNull(spaceSaveReq, "空间保存请求不能为空");
        requireNonNull(spaceSaveReq.getHotelId(), "酒店ID不能为空");
        requireNonNull(spaceSaveReq.getFloor(), "楼层不能为空");
        assertNotBlank(spaceSaveReq.getSpaceCode(), "空间编码不能为空");
        assertNotBlank(spaceSaveReq.getSpaceType(), "空间类型不能为空");
        assertNotBlank(spaceSaveReq.getSpaceName(), "空间名称不能为空");

        if (spaceSaveReq.getHotelId() <= 0) {
            throw new IllegalArgumentException("酒店ID必须大于0");
        }
        if (spaceSaveReq.getFloor() < 0) {
            throw new IllegalArgumentException("楼层必须大于等于0");
        }

        if (!SpaceTypeEnum.isValid(spaceSaveReq.getSpaceType())) {
            throw new IllegalArgumentException("无效的空间类型：" + spaceSaveReq.getSpaceType());
        }

        if (StringUtils.isNotBlank(spaceSaveReq.getWifiSsid())) {
            if (StringUtils.isBlank(spaceSaveReq.getWifiPassword())) {
                throw new IllegalArgumentException("已提供WiFi名称时，WiFi密码不能为空");
            }
        }
    }

    public static void validateHotelSaveReq(HotelSaveReq hotelSaveReq) {
        requireNonNull(hotelSaveReq, "酒店保存请求不能为空");

        if (Objects.equals(hotelSaveReq.getSource(), 0)) {
            assertNotBlank(hotelSaveReq.getHotelName(), "酒店名称不能为空");
            assertNotBlank(hotelSaveReq.getCountryName(), "国家名称不能为空");
            assertNotBlank(hotelSaveReq.getProvinceName(), "省份名称不能为空");
            assertNotBlank(hotelSaveReq.getCityName(), "城市名称不能为空");
            assertNotBlank(hotelSaveReq.getDistrictName(), "区域名称不能为空");
            assertNotBlank(hotelSaveReq.getAddress(), "详细地址不能为空");

            if (!Objects.isNull(hotelSaveReq.getHotelType()) && !HotelTypeEnum.isValid(hotelSaveReq.getHotelType())) {
                throw new IllegalArgumentException("无效的酒店类型：" + hotelSaveReq.getHotelType());
            }

            // 校验 AI 产品类型
            if (CollectionUtils.isNotEmpty(hotelSaveReq.getAiProductTypes())) {
                List<String> aiProductTypes = hotelSaveReq.getAiProductTypes();
                for (String code : aiProductTypes) {
                    String trimmedCode = code.trim();
                    if (Objects.isNull(AiProductTypeEnum.fromCode(trimmedCode))) {
                        throw new IllegalArgumentException("无效的 AI 产品类型：" + trimmedCode);
                    }
                }
            }
        }
    }

    public static void validateHdsAgreementSaveReq(HdsAgreementSaveReq req) {
        requireNonNull(req, "协议保存请求不能为空");
        assertNotBlank(req.getHotelCode(), "酒店编码不能为空");
    }

    public static void validateDeviceModelSaveReq(DeviceModelSaveReq deviceModelSaveReq, boolean update) {
        if (update) {
            requireNonNull(deviceModelSaveReq.getId(), "设备型号ID不能为空");
        }
        requireNonNull(deviceModelSaveReq, "设备型号保存请求不能为空");
        assertNotBlank(deviceModelSaveReq.getModelName(), "设备类型名称不能为空");
        assertNotBlank(deviceModelSaveReq.getModelCode(), "设备型号不能为空");
        assertNotBlank(deviceModelSaveReq.getBrandName(), "设备品牌不能为空");
        assertNotBlank(deviceModelSaveReq.getManufacturer(), "厂商名称不能为空");
        assertNotBlank(deviceModelSaveReq.getCompanyName(), "企业名称不能为空");
        assertNotBlank(deviceModelSaveReq.getManufacturerContactPerson(), "联系人不能为空");
        assertNotBlank(deviceModelSaveReq.getManufacturerContact(), "联系方式不能为空");
        if (deviceModelSaveReq.getWarrantyMonths() == null) {
            throw new IllegalArgumentException("保修期不能为空");
        }
        if (deviceModelSaveReq.getWarrantyMonths() < 0) {
            throw new IllegalArgumentException("保修期必须大于等于0");
        }
        if (StringUtils.isNotBlank(deviceModelSaveReq.getWarrantyInfo())) {
            checkLength(deviceModelSaveReq.getWarrantyInfo(), 50, "保修情况长度不能超过50个字符");
        }
        checkLength(deviceModelSaveReq.getModelCode(), 50, "设备型号长度不能超过50个字符");
        checkLength(deviceModelSaveReq.getModelName(), 100, "设备类型名称长度不能超过100个字符");
        checkLength(deviceModelSaveReq.getManufacturer(), 100, "厂商名称长度不能超过100个字符");
        checkLength(deviceModelSaveReq.getCompanyName(), 200, "企业名称长度不能超过200个字符");
        checkLength(deviceModelSaveReq.getManufacturerContactPerson(), 32, "联系人长度不能超过32个字符");
        checkLength(deviceModelSaveReq.getManufacturerContact(), 100, "联系方式长度不能超过100个字符");
        checkLength(deviceModelSaveReq.getManufacturerServiceProvider(), 100, "厂商服务热线长度不能超过100个字符");
        checkLength(deviceModelSaveReq.getWarrantyInfo(), 50, "保修情况长度不能超过50个字符");
        checkLength(deviceModelSaveReq.getRemark(), 255, "备注长度不能超过255个字符");
    }

    public static void validateDeviceSaveReq(DeviceSaveReq deviceSaveReq, boolean update) {
        if (update) {
            requireNonNull(deviceSaveReq.getId(), "设备ID不能为空");
        }
        requireNonNull(deviceSaveReq, "设备保存请求不能为空");
        requireNonNull(deviceSaveReq.getModelId(), "设备型号ID不能为空");
        requireNonNull(deviceSaveReq.getWarrantyStart(), "保修开始时间不能为空");
        requireNonNull(deviceSaveReq.getWarrantyEnd(), "保修结束时间不能为空");

        if (!SimpleDateUtils.isValidSlashDateFormat(deviceSaveReq.getWarrantyStart())) {
            throw new IllegalArgumentException("保修开始时间格式必须为yyyy/MM/dd");
        }
        if (!SimpleDateUtils.isValidSlashDateFormat(deviceSaveReq.getWarrantyEnd())) {
            throw new IllegalArgumentException("保修结束时间格式必须为yyyy/MM/dd");
        }
        if (StringUtils.isBlank(deviceSaveReq.getDeviceSn()) && StringUtils.isBlank(deviceSaveReq.getImei())) {
            throw new IllegalArgumentException("设备序列号和IMEI不能同时为空");
        }
        checkLength(deviceSaveReq.getRemark(), 255, "备注长度不能超过255个字符");
    }

    public static void validateDeviceDeleteReq(DeviceDeleteReq deviceDeleteReq) {
        requireNonNull(deviceDeleteReq, "设备删除请求不能为空");
        requireNonNull(deviceDeleteReq.getId(), "设备ID不能为空");
    }

    public static void validateUpdateDeviceStatusReq(UpdateDeviceStatusReq updateDeviceStatusReq) {
        requireNonNull(updateDeviceStatusReq, "设备状态更新请求不能为空");
        requireNonNull(updateDeviceStatusReq.getIdList(), "设备ID列表不能为空");
        if (CollectionUtils.isEmpty(updateDeviceStatusReq.getIdList())) {
            throw new IllegalArgumentException("设备ID列表不能为空");
        }
        if (updateDeviceStatusReq.getIdList().size() > 100) {
            throw new IllegalArgumentException("设备ID列表不能超过100个");
        }
        if (!UpdateDeviceStatusEnum.isValid(updateDeviceStatusReq.getUpdateDeviceStatus())) {
            throw new IllegalArgumentException("无效的设备状态");
        }
    }

    public static void validateDevicePositionSaveReq(DevicePositionSaveReq devicePositionSaveReq) {
        requireNonNull(devicePositionSaveReq, "设备位置保存请求不能为空");
        requireNonNull(devicePositionSaveReq.getHotelCode(), "酒店编码不能为空");
        requireNonNull(devicePositionSaveReq.getDeviceAppType(), "设备应用类型不能为空");
        requireNonNull(devicePositionSaveReq.getBlock(), "楼栋不能为空");
        requireNonNull(devicePositionSaveReq.getArea(), "区域不能为空");
        requireNonNull(devicePositionSaveReq.getPositionName(), "位置名称不能为空");
    }

    public static void validateAppVersionsSaveReq(AppVersionsSaveReq req, boolean isUpdate) {
        // 基础校验
        requireNonNull(req, "版本保存请求不能为空");

        // 更新操作时必须有ID
        if (isUpdate) {
            requireNonNull(req.getId(), "版本ID不能为空");
        }
        // 校验平台
        requireNonNull(req.getPlatform(), "平台不能为空");
        if (!PlatformEnum.isValid(req.getPlatform())) {
            throw new IllegalArgumentException("无效的平台类型，必须是 android 或 ios");
        }

        // 校验版本号
        requireNonNull(req.getAppVersion(), "版本号不能为空");
        if (!isValidVersionFormat(req.getAppVersion())) {
            throw new IllegalArgumentException("无效的版本号格式，正确格式如：1.0.0");
        }

        requireNonNull(req.getOnlineTime(), "上线时间不能为空");
        if (!SimpleDateUtils.isValidSlashDateFormat(req.getOnlineTime())) {
            throw new IllegalArgumentException("上线时间格式必须为yyyy-MM-dd");
        }

        // 校验更新内容
        requireNonNull(req.getUpdateContent(), "版本更新内容不能为空");
        if (req.getUpdateContent().length() > 1000) {
            throw new IllegalArgumentException("版本更新内容不能超过1000字符");
        }

        // 校验下载地址
        requireNonNull(req.getDownloadUrl(), "下载地址不能为空");
        if (req.getDownloadUrl().length() > 255) {
            throw new IllegalArgumentException("下载地址长度不能超过255字符");
        }
//        if (!isValidUrl(req.getDownloadUrl())) {
//            throw new IllegalArgumentException("无效的下载地址格式");
//        }

        // 校验强制更新标志
        requireNonNull(req.getIsForce(), "强制更新标志不能为空");
        if (!Arrays.asList(0, 1).contains(req.getIsForce())) {
            throw new IllegalArgumentException("强制更新标志必须是0或1");
        }

        // 校验状态（如果提供）
        if (req.getStatus() != null && !Arrays.asList(0, 1).contains(req.getStatus())) {
            throw new IllegalArgumentException("状态必须是0或1");
        }
    }

    /**
     * 校验套餐保存请求参数
     */
    public static void validatePackageSaveReq(PackageSaveReq req) {
        requireNonNull(req, "套餐保存请求不能为空");
        assertNotBlank(req.getPackageName(), "套餐名称不能为空");
        checkLength(req.getPackageName(), 20, "套餐名称长度不能超过20个字符");

        // 校验付费方式
        requireNonNull(req.getPaymentMode(), "付费方式不能为空");
        if (!Arrays.asList(0, 1).contains(req.getPaymentMode())) {
            throw new IllegalArgumentException("付费方式必须是0（按房间数量收费）或1（按门店收费）");
        }

        // 校验优惠方式
        requireNonNull(req.getDiscountMode(), "优惠方式不能为空");
        if (!Arrays.asList(0, 1).contains(req.getDiscountMode())) {
            throw new IllegalArgumentException("优惠方式必须是0（折扣）或1（一口价）");
        }

        // 校验产品列表
        if (CollectionUtils.isEmpty(req.getProducts())) {
            throw new IllegalArgumentException("产品列表不能为空");
        }
        if (req.getProducts().size() > 5) {
            throw new IllegalArgumentException("产品列表不能超过5个");
        }

        // 校验每个产品
        for (int i = 0; i < req.getProducts().size(); i++) {
            PackageSaveReq.ProductSaveReq product = req.getProducts().get(i);
            validateProductSaveReq(product, i + 1, req.getDiscountMode());
        }
    }

    /**
     * 校验产品保存请求参数
     */
    private static void validateProductSaveReq(PackageSaveReq.ProductSaveReq product, int index, Integer discountMode) {
        checkLength(product.getProductName(), 50, "第" + index + "个产品名称长度不能超过20个字符");

        // 校验产品描述
        if (product.getProductDescription().size() > 10) {
            throw new IllegalArgumentException("第" + index + "个产品描述不能超过10条");
        }

        for (int j = 0; j < product.getProductDescription().size(); j++) {
            String desc = product.getProductDescription().get(j);
            if (StringUtils.isBlank(desc)) {
                continue;
            }
            checkLength(desc, 50, "第" + index + "个产品第" + (j + 1) + "条描述长度不能超过50个字符");
        }

        // 校验定价列表
        if (CollectionUtils.isEmpty(product.getPricing())) {
            throw new IllegalArgumentException("第" + index + "个产品定价不能为空");
        }

        // 检查是否有月度定价（必须）
        boolean hasMonthlyPricing = product.getPricing().stream()
                .anyMatch(pricing -> pricing.getPeriodType() != null && pricing.getPeriodType() == 1);
        if (!hasMonthlyPricing) {
            throw new IllegalArgumentException("第" + index + "个产品必须包含月度定价");
        }

        // 校验每个定价
        for (int k = 0; k < product.getPricing().size(); k++) {
            PackageSaveReq.PricingSaveReq pricing = product.getPricing().get(k);
            validatePricingSaveReq(pricing, index, k + 1, discountMode);
        }
    }

    /**
     * 校验定价保存请求参数
     */
    private static void validatePricingSaveReq(PackageSaveReq.PricingSaveReq pricing, int productIndex, int pricingIndex, Integer discountMode) {
        requireNonNull(pricing, "第" + productIndex + "个产品第" + pricingIndex + "个定价不能为空");

        // 根据优惠方式校验
        if (discountMode == 1) {
            // 一口价模式
            if (pricing.getPeriodType() == 1) {
                // 月度定价，优惠价必填
                assertNotBlank(pricing.getDiscountPrice(), "一口价模式下第" + productIndex + "个产品月度优惠价不能为空");
                validatePriceFormat(pricing.getDiscountPrice(), "第" + productIndex + "个产品第" + pricingIndex + "个定价优惠价");
            }
            // 季度和年度可选，如果填写则校验格式
            if (StringUtils.isNotBlank(pricing.getDiscountPrice())) {
                validatePriceFormat(pricing.getDiscountPrice(), "第" + productIndex + "个产品第" + pricingIndex + "个定价优惠价");
            }
        } else {
            // 折扣模式
            if (pricing.getPeriodType() == 1) {
                // 月度定价，优惠价必填
                assertNotBlank(pricing.getDiscountPrice(), "折扣模式下第" + productIndex + "个产品月度优惠价不能为空");
                validatePriceFormat(pricing.getDiscountPrice(), "第" + productIndex + "个产品第" + pricingIndex + "个定价优惠价");
            }
            // 季度和年度的折扣比例可选，如果填写则校验格式
            if (StringUtils.isNotBlank(pricing.getDiscountRate())) {
                validateDiscountRateFormat(pricing.getDiscountRate(), "第" + productIndex + "个产品第" + pricingIndex + "个定价折扣比例");
            }
        }
    }

    /**
     * 校验价格格式
     */
    private static void validatePriceFormat(String price, String fieldName) {
        if (StringUtils.isBlank(price)) {
            return;
        }

        try {
            BigDecimal priceDecimal = new BigDecimal(price);
            if (priceDecimal.compareTo(java.math.BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException(fieldName + "不能为负数");
            }
            if (priceDecimal.scale() > 2) {
                throw new IllegalArgumentException(fieldName + "最多保留2位小数");
            }
            if (priceDecimal.compareTo(new java.math.BigDecimal("999999.99")) > 0) {
                throw new IllegalArgumentException(fieldName + "不能超过999999.99");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(fieldName + "格式不正确，必须是有效的数字");
        }
    }

    /**
     * 校验折扣比例格式
     */
    private static void validateDiscountRateFormat(String discountRate, String fieldName) {
        if (StringUtils.isBlank(discountRate)) {
            return;
        }

        try {
            BigDecimal rate = new BigDecimal(discountRate);
            if (rate.compareTo(java.math.BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException(fieldName + "不能为负数");
            }
            if (rate.compareTo(new java.math.BigDecimal("100")) > 0) {
                throw new IllegalArgumentException(fieldName + "不能超过100");
            }
            if (rate.scale() > 2) {
                throw new IllegalArgumentException(fieldName + "最多保留2位小数");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(fieldName + "格式不正确，必须是有效的数字");
        }
    }

    /**
     * 校验版本号格式
     */
    private static boolean isValidVersionFormat(String version) {
        return version.matches("^\\d+(\\.\\d+)*$");
    }

    public static void validateOrderPayReq(OrderPayReq req) {
        requireNonNull(req, "订单保存请求不能为空");
        assertNotBlank(req.getPackageCode(), "套餐编码不能为空");
//        assertNotBlank(req.getHotelPackageCode(), "门店套餐编码不能为空");
        requireNonNull(req.getProductId(), "商品id不能为空");

        // 校验付费方式
        requireNonNull(req.getPeriodType(), "周期类型不能为空");
        if (!Arrays.asList(1, 2, 3).contains(req.getPeriodType())) {
            throw new IllegalArgumentException("周期类型必须是1-月度，2-季度，3-年度");
        }

        requireNonNull(req.getPayMethod(), "支付方式不能为空");
        if (!Arrays.asList(1, 2).contains(req.getPayMethod())) {
            throw new IllegalArgumentException("支付方式必须是1-微信，2-支付宝");
        }
    }

    public static void validateHotelPackageSaveReq(HotelPackageSaveReq req) {
        requireNonNull(req.getHotelCode(), "门店编码不能为空");
        requireNonNull(req.getPackageCode(), "套餐编码不能为空");
        checkLength(req.getAdjustPriceReason(), 100, "调价原因长度不能超过100个字符");
    }

    /**
     * 校验发票信息保存请求
     * @param req 发票信息保存请求
     */
    public static void validateInvoiceInfoSaveReq(HdsInvoiceInfoSaveReq req) {
        requireNonNull(req, "发票信息保存请求不能为空");
        assertNotBlank(req.getHotelCode(), "酒店编码不能为空");
        assertNotBlank(req.getInvoiceHeader(), "发票抬头不能为空");
        checkLength(req.getInvoiceHeader(), 50, "发票抬头不能超过50个字符");

        assertNotBlank(req.getTaxNumber(), "纳税人识别号不能为空");
        checkLength(req.getTaxNumber(), 30, "纳税人识别号不能超过30个字符");
        if (!req.getTaxNumber().matches("^[0-9A-Z]+$")) {
            throw new IllegalArgumentException("纳税人识别号只能包含数字和大写字母");
        }

        assertNotBlank(req.getReceiveEmail(), "接收邮箱不能为空");
        if (!isValidEmail(req.getReceiveEmail())) {
            throw new IllegalArgumentException("邮箱格式不正确");
        }

        if (StringUtils.isNotBlank(req.getMoreInfo())) {
            checkLength(req.getMoreInfo(), 150, "更多信息不能超过150个字符");
        }

        if (req.getIsDefault() != null && !Arrays.asList(0, 1).contains(req.getIsDefault())) {
            throw new IllegalArgumentException("是否默认必须是0(否)或1(是)");
        }
    }

    /**
     * 校验邮箱格式
     * @param email 邮箱
     * @return 是否有效
     */
    public static boolean isValidEmail(String email) {
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        return email.matches(emailRegex);
    }

    /**
     * 校验发票申请创建请求
     * @param req 发票申请创建请求
     */
    public static void validateInvoiceApplicationCreateReq(HdsInvoiceApplicationCreateReq req) {
        requireNonNull(req, "发票申请创建请求不能为空");
        
        if (CollectionUtils.isEmpty(req.getBillIds())) {
            throw new IllegalArgumentException("账单ID不能为空");
        }

        if (req.getIsDefault() != null && !Arrays.asList(0, 1).contains(req.getIsDefault())) {
            throw new IllegalArgumentException("是否默认必须是0(否)或1(是)");
        }
    }
}
