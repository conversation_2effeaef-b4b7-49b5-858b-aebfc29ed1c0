package com.wormhole.hotelds.util;


import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author：flx
 * @Date：2025/4/1 15:17
 * @Description：联系方式校验工具类:用于校验手机号和邮箱地址格式
 */
public class ContactValidatorUtils {

    // 中国大陆手机号正则表达式
    private static final Pattern CHINA_MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    // 国际手机号正则表达式（宽松模式，以+开头，6-15位数字）
    private static final Pattern INTERNATIONAL_MOBILE_PATTERN = Pattern.compile("^\\+[0-9]{6,15}$");

    // 邮箱地址正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$");

    /**
     * 校验中国大陆手机号
     *
     * @param mobile 手机号码
     * @return 是否为有效的中国大陆手机号
     */
    public static boolean isValidChineseMobile(String mobile) {
        if (!StringUtils.isNotBlank(mobile)) {
            return false;
        }
        return CHINA_MOBILE_PATTERN.matcher(mobile).matches();
    }

    /**
     * 校验国际手机号
     *
     * @param mobile 手机号码（包括国际区号，如+8613800138000）
     * @return 是否为有效的国际手机号
     */
    public static boolean isValidInternationalMobile(String mobile) {
        if (!StringUtils.isNotBlank(mobile)) {
            return false;
        }
        return INTERNATIONAL_MOBILE_PATTERN.matcher(mobile).matches();
    }

    /**
     * 校验手机号（支持中国大陆和国际格式）
     *
     * @param mobile 手机号码
     * @return 是否为有效的手机号
     */
    public static boolean isValidMobile(String mobile) {
        if (!StringUtils.isNotBlank(mobile)) {
            return false;
        }

        // 尝试去除+86前缀
        String normalizedMobile = mobile;
        if (mobile.startsWith("+86")) {
            normalizedMobile = mobile.substring(3);
        }

        return isValidChineseMobile(normalizedMobile) || isValidInternationalMobile(mobile);
    }

    /**
     * 校验邮箱地址
     *
     * @param email 邮箱地址
     * @return 是否为有效的邮箱地址
     */
    public static boolean isValidEmail(String email) {
        if (!StringUtils.isNotBlank(email)) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 提取字符串中的第一个手机号
     *
     * @param text 包含手机号的文本
     * @return 提取的手机号，如果没有找到则返回null
     */
    public static String extractMobile(String text) {
        if (!StringUtils.isNotBlank(text)) {
            return null;
        }

        Matcher matcher = Pattern.compile("(1[3-9]\\d{9})").matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 提取字符串中的第一个邮箱地址
     *
     * @param text 包含邮箱的文本
     * @return 提取的邮箱地址，如果没有找到则返回null
     */
    public static String extractEmail(String text) {
        if (!StringUtils.isNotBlank(text)) {
            return null;
        }

        Matcher matcher = EMAIL_PATTERN.matcher(text);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    /**
     * 隐藏手机号中间四位
     *
     * @param mobile 手机号码
     * @return 隐藏中间四位的手机号，如137****8000
     */
    public static String maskMobile(String mobile) {
        if (!StringUtils.isNotBlank(mobile) || mobile.length() < 7) {
            return mobile;
        }

        int length = mobile.length();
        int prefixLength = length / 3;
        int suffixLength = length / 3;

        StringBuilder masked = new StringBuilder();
        masked.append(mobile, 0, prefixLength);
        for (int i = 0; i < length - prefixLength - suffixLength; i++) {
            masked.append('*');
        }
        masked.append(mobile.substring(length - suffixLength));

        return masked.toString();
    }

    /**
     * 隐藏邮箱地址的用户名部分
     *
     * @param email 邮箱地址
     * @return 隐藏用户名的邮箱地址，如a****@example.com
     */
    public static String maskEmail(String email) {
        if (!StringUtils.isNotBlank(email) || !email.contains("@")) {
            return email;
        }

        int atIndex = email.indexOf('@');
        String username = email.substring(0, atIndex);
        String domain = email.substring(atIndex);

        if (username.length() <= 2) {
            return username + "***" + domain;
        }

        return username.charAt(0) +
                "****" +
                username.charAt(username.length() - 1) +
                domain;
    }
}
