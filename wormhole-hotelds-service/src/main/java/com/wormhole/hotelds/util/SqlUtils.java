package com.wormhole.hotelds.util;

import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.wormhole.hotelds.constant.SystemConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class SqlUtils {

    private static final String PERCENT = "%";

    public static String like(String value) {
        return Joiner.on("").join(PERCENT, value, PERCENT);
    }

    /**
     * sql中检索字段名
     *
     * @param filedList
     * @param prefix
     * @return
     */
    public static String getSelectFields(String prefix, List<String> filedList) {
        Preconditions.checkArgument(StringUtils.isNotBlank(prefix), "prefix must not be blank");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(filedList), "filedList must not be empty");
        return SystemConstant.COMMA_JOINER.join(filedList.stream().map(fieldName -> String.format("%s.%s", prefix, fieldName)).collect(Collectors.toList()));
    }

    /**
     * 获取排序方向，防止sql注入
     *
     * @param direction
     * @return
     */
    public static String getSortDirection(Sort.Direction direction) {
        return Optional.ofNullable(direction).orElse(Sort.Direction.DESC).isDescending() ? Sort.Direction.DESC.name() : Sort.Direction.ASC.name();
    }

}
