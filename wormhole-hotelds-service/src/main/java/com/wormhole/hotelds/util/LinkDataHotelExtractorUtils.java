package com.wormhole.hotelds.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.hotelds.api.hotel.req.PluginHotelReq;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import javax.net.ssl.*;
import java.lang.reflect.Field;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 链接数据酒店提取器
 * 参考CtripHotelExtractor的解析逻辑，完成链接数据转换为PluginHotelDTO
 */
@Slf4j
public class LinkDataHotelExtractorUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 静态初始化块 - 禁用SSL验证
    static {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        public X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }

                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        }

                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        }
                    }
            };

            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // 创建不验证主机名的HostnameVerifier
            HostnameVerifier allHostsValid = (hostname, session) -> true;
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            System.out.println("✅ SSL证书验证已禁用");
        } catch (Exception e) {
            System.err.println("❌ SSL配置失败: " + e.getMessage());
        }
    }

    /**
     * 主要提取方法 - 从链接提取数据并转换为PluginHotelDTO
     *
     * @param url 酒店页面URL
     * @return PluginHotelDTO对象
     */
    public static PluginHotelReq extractHotelData(String url, String hotelCode) {
        // 从URL提取酒店编码
//        String hotelCode = extractHotelCodeFromUrl(url);

        // 尝试多种方式获取数据
        for (int attempt = 1; attempt <= 3; attempt++) {
            try {
                // 获取页面HTML
                Document doc = Jsoup.connect(url)
                        .userAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                        .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
                        .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                        .timeout(30000)
                        .followRedirects(true)
                        .ignoreHttpErrors(true)
                        .get();

                // 提取数据
                JsonNode ibuHotelData = extractIbuHotelJson(doc);
                JsonNode newIbuHotelData = extractNewIbuHotelJson(doc);

                if (ibuHotelData != null || newIbuHotelData != null) {
                    return convertToPluginHotelDTO(hotelCode, ibuHotelData, newIbuHotelData);
                }

            } catch (Exception e) {
                if (attempt < 3) {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException ie) {
                        break;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 从URL提取酒店编码
     */
    private static String extractHotelCodeFromUrl(String url) {
        Pattern pattern = Pattern.compile("hotels/(\\d+)\\.html");
        Matcher matcher = pattern.matcher(url);
        return matcher.find() ? matcher.group(1) : "";
    }

    /**
     * 提取window.IBU_HOTEL数据
     */
    private static JsonNode extractIbuHotelJson(Document doc) {
        try {
            String html = doc.outerHtml();

            // 多种正则表达式模式
            String[] patterns = {
                    "window\\.IBU_HOTEL\\s*=\\s*([\\s\\S]*?});(?=\\s*(?:window\\.|</script>|$))",
                    "window\\.IBU_HOTEL\\s*=\\s*([\\s\\S]*?})\\s*;",
                    "IBU_HOTEL\\s*=\\s*([\\s\\S]*?});"
            };

            for (String patternStr : patterns) {
                Pattern pattern = Pattern.compile(patternStr, Pattern.MULTILINE | Pattern.DOTALL);
                Matcher matcher = pattern.matcher(html);

                if (matcher.find()) {
                    String jsonStr = cleanJsonString(matcher.group(1));
                    try {
                        return objectMapper.readTree(jsonStr);
                    } catch (Exception e) {
                        System.err.println("JSON解析失败，尝试下一个模式: " + e.getMessage());
                        continue;
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("提取IBU_HOTEL数据失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 提取新版本的IBU_HOTEL数据
     */
    private static JsonNode extractNewIbuHotelJson(Document doc) {
        // 参考CtripHotelExtractor的实现
        return null;
    }

    /**
     * 清理JSON字符串
     */
    private static String cleanJsonString(String jsonStr) {
        // 移除JavaScript注释
        jsonStr = jsonStr.replaceAll("//.*?\\n", "\n");
        jsonStr = jsonStr.replaceAll("/\\*[\\s\\S]*?\\*/", "");

        // 移除尾部的分号
        jsonStr = jsonStr.replaceAll(";\\s*$", "");

        // 处理函数调用包装
        if (jsonStr.startsWith("(") && jsonStr.endsWith(")")) {
            jsonStr = jsonStr.substring(1, jsonStr.length() - 1);
        }

        return jsonStr.trim();
    }

    /**
     * 转换为PluginHotelDTO对象
     */
    private static PluginHotelReq convertToPluginHotelDTO(String hotelCode, JsonNode ibuHotelData, JsonNode newIbuHotelData) {
        try {
            // 优先使用ibuHotelData，如果没有则使用newIbuHotelData
            JsonNode primaryData = ibuHotelData != null ? ibuHotelData : newIbuHotelData;
            JsonNode secondaryData = ibuHotelData != null ? newIbuHotelData : null;

            // 提取酒店基本信息
            String hotelName = extractHotelName(primaryData, secondaryData);

            // 构建PluginHotelDTO
            PluginHotelReq dto = new PluginHotelReq();
            setField(dto, "hotelCode", hotelCode);
            setField(dto, "hotelName", hotelName);
            setField(dto, "dataSource", "ctrip");
            setField(dto, "operatorInfo", createOperatorInfo(hotelCode, hotelName));
            setField(dto, "pluginRoom", createPluginRoom(primaryData, secondaryData));
            setField(dto, "pluginHotel", createPluginHotel(primaryData, secondaryData));
            setField(dto, "pluginPlace", createPluginPlace(primaryData, secondaryData));

            return dto;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 提取酒店名称
     */
    private static String extractHotelName(JsonNode primaryData, JsonNode secondaryData) {
        // 从多个可能的路径提取酒店名称
        String[] paths = {
                "initData.base.hotelName",
                "initData.staticHotelInfo.hotelName",
                "initData.staticHotelInfo.name",
                "initData.staticHotelInfo.hotelBasicInfo.hotelName",
                "initData.hotelDetailInfo.data.hotelDescriptionInfo.basic.hotelName",
                "initData.hotelDetailInfo.data.hotelDescriptionInfo.hotelName",
                "initData.hotelDetailInfo.hotelName",
                "hotelInfo.hotelName",
                "hotelDetailResponse.hotelDescriptionInfo.basic.hotelName"
        };

        for (String path : paths) {
            String name = getValueByPath(primaryData, path);
            if (name != null && !name.isEmpty() && !"null".equals(name)) {
                return name;
            }
            if (secondaryData != null) {
                name = getValueByPath(secondaryData, path);
                if (name != null && !name.isEmpty() && !"null".equals(name)) {
                    return name;
                }
            }
        }

        return "未知酒店";
    }

    /**
     * 使用反射设置字段值
     */
    private static void setField(Object obj, String fieldName, Object value) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (Exception e) {
            System.err.println("设置字段失败: " + fieldName + " = " + value);
        }
    }

    /**
     * 创建OperatorInfo
     */
    private static PluginHotelReq.OperatorInfo createOperatorInfo(String hotelCode, String hotelName) {
        PluginHotelReq.OperatorInfo operatorInfo = new PluginHotelReq.OperatorInfo();
        setField(operatorInfo, "operatorId", "system");
        setField(operatorInfo, "operatorName", "系统自动提取");
        setField(operatorInfo, "hotelCode", hotelCode);
        setField(operatorInfo, "hotelName", hotelName);
        return operatorInfo;
    }

    /**
     * 根据路径获取值
     */
    private static String getValueByPath(JsonNode node, String path) {
        if (node == null) return null;

        String[] parts = path.split("\\.");
        JsonNode current = node;

        for (String part : parts) {
            if (current == null || !current.has(part)) {
                return null;
            }
            current = current.get(part);
        }

        return current != null ? current.asText() : null;
    }

    /**
     * 根据路径获取JsonNode
     */
    private static JsonNode getNodeByPath(JsonNode node, String path) {
        if (node == null) return null;

        String[] parts = path.split("\\.");
        JsonNode current = node;

        for (String part : parts) {
            if (current == null || !current.has(part)) {
                return null;
            }
            current = current.get(part);
        }

        return current;
    }

    /**
     * 创建PluginRoom
     */
    private static PluginHotelReq.PluginRoom createPluginRoom(JsonNode primaryData, JsonNode secondaryData) {
        try {
            // 提取客房数量
            Integer roomCount = extractRoomCount(primaryData, secondaryData);

            // 创建房间映射数据
            JsonNode roomMap = objectMapper.createObjectNode();

            PluginHotelReq.PluginRoom pluginRoom = new PluginHotelReq.PluginRoom();
            setField(pluginRoom, "room_count", roomCount);
            setField(pluginRoom, "physic_room_map", roomMap);
            return pluginRoom;

        } catch (Exception e) {
            System.err.println("创建PluginRoom失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 提取客房数量
     */
    private static Integer extractRoomCount(JsonNode primaryData, JsonNode secondaryData) {
        // 从多个路径提取客房数量
        String[] paths = {
                "initData.base.roomCount",
                "initData.staticHotelInfo.hotelInfo.roomCount",
                "initData.hotelDetailInfo.data.hotelDescriptionInfo.basic.roomCount"
        };

        // 先尝试直接的数字字段
        for (String path : paths) {
            JsonNode roomCountNode = getNodeByPath(primaryData, path);
            if (roomCountNode != null && roomCountNode.isNumber()) {
                return roomCountNode.asInt();
            }
            if (secondaryData != null) {
                roomCountNode = getNodeByPath(secondaryData, path);
                if (roomCountNode != null && roomCountNode.isNumber()) {
                    return roomCountNode.asInt();
                }
            }
        }

        // 从label中提取客房数量
        String[] labelPaths = {
                "initData.hotelDetailInfo.data.hotelDescriptionInfo.basic.label",
                "initData.staticHotelInfo.hotelInfo.basic.label"
        };

        for (String path : labelPaths) {
            JsonNode labels = getNodeByPath(primaryData, path);
            if (labels == null && secondaryData != null) {
                labels = getNodeByPath(secondaryData, path);
            }

            if (labels != null && labels.isArray()) {
                for (JsonNode label : labels) {
                    String labelText = label.asText();
                    if (labelText.contains("客房") || labelText.contains("房间")) {
                        Pattern pattern = Pattern.compile("(\\d+)");
                        Matcher matcher = pattern.matcher(labelText);
                        if (matcher.find()) {
                            return Integer.parseInt(matcher.group(1));
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 创建PluginHotel
     */
    private static PluginHotelReq.PluginHotel createPluginHotel(JsonNode primaryData, JsonNode secondaryData) {
        try {
            // 提取各种酒店信息
            JsonNode hotelPolicy = extractHotelPolicy(primaryData, secondaryData);
            JsonNode hotelFacility = extractHotelFacility(primaryData, secondaryData);
            JsonNode reservationTips = extractReservationTips(primaryData, secondaryData);
            Map<String, Object> hotelInfo = extractHotelInfoMap(primaryData, secondaryData);
            JsonNode positionInfo = extractPositionInfo(primaryData, secondaryData);
            JsonNode hotelComment = extractHotelComment(primaryData, secondaryData);

            PluginHotelReq.PluginHotel pluginHotel = new PluginHotelReq.PluginHotel();
            setField(pluginHotel, "hotelPolicy", hotelPolicy);
            setField(pluginHotel, "hotelFacility", hotelFacility);
            setField(pluginHotel, "reservationNoticeTipsInfo", reservationTips);
            setField(pluginHotel, "hotelInfo", hotelInfo);
            setField(pluginHotel, "positionInfo", positionInfo);
            setField(pluginHotel, "hotelComment", hotelComment);
            return pluginHotel;

        } catch (Exception e) {
            System.err.println("创建PluginHotel失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 创建PluginPlace
     */
    private static PluginHotelReq.PluginPlace createPluginPlace(JsonNode primaryData, JsonNode secondaryData) {
        try {
            JsonNode placeInfoList = extractPlaceInfoList(primaryData, secondaryData);

            PluginHotelReq.PluginPlace pluginPlace = new PluginHotelReq.PluginPlace();
            setField(pluginPlace, "place_info_list", placeInfoList);
            return pluginPlace;

        } catch (Exception e) {
            System.err.println("创建PluginPlace失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 提取酒店政策信息
     */
    private static JsonNode extractHotelPolicy(JsonNode primaryData, JsonNode secondaryData) {
        try {
            // 使用LinkedHashMap保持字段顺序，对应其它门店的hotel_policy结构
            Map<String, Object> completePolicy = new LinkedHashMap<>();

            // 按照您的代码逻辑提取政策信息
            JsonNode policyData = extractByPaths(primaryData, secondaryData, new String[]{
                    "initData.hotelDetailInfo.data.hotelPolicyInfo",
                    "hotelDetailResponse.hotelPolicyInfo",
                    "initData.staticHotelInfo.hotelPolicy",
                    "initData.newHotelPolicyInfo"
            });

            // 如果找到了基本政策数据，还需要合并credit信息
            if (policyData != null) {
                // 尝试获取credit信息并合并
                JsonNode creditData = getNodeByPath(primaryData, "initData.staticHotelInfo.hotelPolicy.credit");
                if (creditData != null && policyData.isObject()) {
                    // 创建新的政策对象，包含credit信息
                    Map<String, Object> policyMap = objectMapper.convertValue(policyData, Map.class);
                    policyMap.put("credit", creditData);
                    return objectMapper.valueToTree(policyMap);
                }
                return policyData;
            }

            // 如果没有找到政策数据，返回空对象
            return objectMapper.valueToTree(new LinkedHashMap<>());

        } catch (Exception e) {
            System.err.println("提取政策信息失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从多个路径中提取数据
     */
    private static JsonNode extractByPaths(JsonNode primaryData, JsonNode secondaryData, String[] paths) {
        for (String path : paths) {
            JsonNode result = getNodeByPath(primaryData, path);
            if (result != null) {
                return result;
            }
            if (secondaryData != null) {
                result = getNodeByPath(secondaryData, path);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 提取酒店设施信息
     */
    private static JsonNode extractHotelFacility(JsonNode primaryData, JsonNode secondaryData) {
        try {
            // 使用LinkedHashMap保持字段顺序
            Map<String, Object> completeFacility = new LinkedHashMap<>();

            // 1. hotelPopularFacility (第一个) - 参考CtripHotelExtractor的实现
            Map<String, Object> hotelPopularFacility = new LinkedHashMap<>();

            // 首先尝试从hotelFacilityPage.hotelPopularFacility提取
            JsonNode facilityPageData = getNodeByPath(primaryData, "initData.staticHotelInfo.hotelFacilityPage.hotelPopularFacility");
            if (facilityPageData != null) {
                // 从实际数据中提取
                hotelPopularFacility.put("title", facilityPageData.has("title") ? facilityPageData.get("title").asText() : "设施服务");
                hotelPopularFacility.put("categoryId", facilityPageData.has("categoryId") ? facilityPageData.get("categoryId").asInt() : 1);
                hotelPopularFacility.put("showStyle", facilityPageData.has("showStyle") ? facilityPageData.get("showStyle").asInt() : 0);
                hotelPopularFacility.put("icon", facilityPageData.has("icon") ? facilityPageData.get("icon").asText() : "mostPop");

                // 提取list
                List<Map<String, Object>> facilityList = new ArrayList<>();
                if (facilityPageData.has("list") && facilityPageData.get("list").isArray()) {
                    for (JsonNode facility : facilityPageData.get("list")) {
                        Map<String, Object> facilityItem = new LinkedHashMap<>();
                        facilityItem.put("facilityDesc", facility.has("facilityDesc") ? facility.get("facilityDesc").asText() : "");
                        facilityItem.put("showStyle", facility.has("showStyle") ? facility.get("showStyle").asInt() : 2);
                        facilityItem.put("showTitle", facility.has("showTitle") ? facility.get("showTitle").asText() : "免费");
                        facilityItem.put("code", facility.has("code") ? facility.get("code").asInt() : 0);
                        facilityItem.put("icon", facility.has("icon") ? facility.get("icon").asText() : "");

                        // 提取facilityInfo
                        if (facility.has("facilityInfo") && facility.get("facilityInfo").isArray()) {
                            facilityItem.put("facilityInfo", facility.get("facilityInfo"));
                        } else {
                            facilityItem.put("facilityInfo", new ArrayList<>());
                        }
                        facilityList.add(facilityItem);
                    }
                }
                hotelPopularFacility.put("list", facilityList);
            } else {
                // 如果没有找到hotelPopularFacility，尝试从hotFacility构建
                hotelPopularFacility.put("title", "设施服务");
                hotelPopularFacility.put("categoryId", 1);
                hotelPopularFacility.put("showStyle", 0);
                hotelPopularFacility.put("icon", "mostPop");

                List<Map<String, Object>> facilityList = new ArrayList<>();
                JsonNode hotFacility = getNodeByPath(primaryData, "initData.hotFacility.list");
                if (hotFacility != null && hotFacility.isArray()) {
                    for (JsonNode facility : hotFacility) {
                        Map<String, Object> facilityItem = new LinkedHashMap<>();
                        facilityItem.put("facilityDesc", facility.has("facilityName") ? facility.get("facilityName").asText() : "");
                        facilityItem.put("showStyle", 2);
                        facilityItem.put("showTitle", "免费");
                        facilityItem.put("code", facility.has("facilityId") ? facility.get("facilityId").asInt() : 0);
                        facilityItem.put("icon", "");
                        facilityItem.put("facilityInfo", new ArrayList<>());
                        facilityList.add(facilityItem);
                    }
                }
                hotelPopularFacility.put("list", facilityList);
            }

            completeFacility.put("hotelPopularFacility", hotelPopularFacility);

            // 2. pictureGroups (第二个) - 从实际数据中提取
            JsonNode pictureGroupsData = getNodeByPath(primaryData, "initData.staticHotelInfo.hotelFacilityPage.pictureGroups");
            if (pictureGroupsData != null) {
                completeFacility.put("pictureGroups", pictureGroupsData);
            }

            // 3. hotelFacility (第三个) - 从实际数据中提取
            JsonNode hotelFacilityData = getNodeByPath(primaryData, "initData.staticHotelInfo.hotelFacility");
            if (hotelFacilityData != null) {
                completeFacility.put("hotelFacility", hotelFacilityData);
            }

            // 4. ubtData (第四个) - 从实际数据中提取
            JsonNode ubtDataNode = getNodeByPath(primaryData, "initData.staticHotelInfo.hotelFacilityPage.ubtData");
            if (ubtDataNode != null) {
                completeFacility.put("ubtData", ubtDataNode);
            }

            // 5. hotelNormalFacilityList (第五个) - 从实际数据中提取
            JsonNode normalFacilityListData = getNodeByPath(primaryData, "initData.staticHotelInfo.hotelFacilityPage.hotelNormalFacilityList");
            if (normalFacilityListData != null) {
                completeFacility.put("hotelNormalFacilityList", normalFacilityListData);
            }

            // 只有当有实际数据时才返回completeFacility
            if (completeFacility.size() > 1) { // 除了hotelPopularFacility之外还有其他数据
                return objectMapper.valueToTree(completeFacility);
            }

        } catch (Exception e) {
            System.err.println("提取设施信息失败: " + e.getMessage());
        }

        // 如果上面的方法没有找到足够的数据，按照您的代码逻辑尝试其他路径
        return extractByPaths(primaryData, secondaryData, new String[]{
                "initData.hotelDetailInfo.data.hotelFacilityPopV2",
                "hotelDetailResponse.hotelFacilityPopV2",
                "initData.staticHotelInfo.hotelFacility",
                "initData.newFacilityPop",
                "initData.hotFacility"
        });
    }

    /**
     * 提取预订通知信息
     */
    private static JsonNode extractReservationTips(JsonNode primaryData, JsonNode secondaryData) {
        try {
            // 按照您提供的代码逻辑提取 - 优先从Response，备选从data
            JsonNode reservationData = extractByPaths(primaryData, secondaryData, new String[]{
                "Response.reservationNoticeTipsInfo",
                "data.hotelReservationTips",
                "initData.staticHotelInfo.reservationNoticeTipsInfo",
                "initData.reservationNoticeTipsInfo",
                "initData.hotelDetailInfo.data.reservationNoticeTipsInfo"
            });

            if (reservationData != null && reservationData.isArray() && reservationData.size() > 0) {
                return reservationData;
            }

            // 如果没有找到实际数据，返回空数组
            return objectMapper.valueToTree(new ArrayList<>());

        } catch (Exception e) {
            System.err.println("提取预订通知信息失败: " + e.getMessage());
            // 出错时返回空数组而不是null
            try {
                return objectMapper.valueToTree(new ArrayList<>());
            } catch (Exception ex) {
                return null;
            }
        }
    }

    /**
     * 提取酒店信息Map
     */
    private static Map<String, Object> extractHotelInfoMap(JsonNode primaryData, JsonNode secondaryData) {
        // 使用LinkedHashMap保持字段顺序
        Map<String, Object> hotelInfo = new LinkedHashMap<>();

        try {
            // 按照您的代码逻辑首先提取hotelDescriptionInfo
            JsonNode hotelDescInfo = extractByPaths(primaryData, secondaryData, new String[]{
                "initData.hotelDetailInfo.data.hotelDescriptionInfo",
                "hotelDetailResponse.hotelDescriptionInfo"
            });

            // 如果找到了hotelDescriptionInfo，直接返回它
            if (hotelDescInfo != null) {
                return objectMapper.convertValue(hotelDescInfo, Map.class);
            }

            // 如果没有找到，按照其它门店的字段顺序提取数据
            // 1. hotelDescTitle (第一个)
            hotelInfo.put("hotelDescTitle", "酒店简介");

            // 2. name (第二个) - 提取酒店名称
            String hotelName = extractHotelName(primaryData, secondaryData);
            if (hotelName != null && !"未知酒店".equals(hotelName)) {
                hotelInfo.put("name", hotelName);
            }

            // 3. image (第三个) - 提取主图片
            JsonNode image = getNodeByPath(primaryData, "initData.album.bigPic.src");
            if (image == null) {
                image = getNodeByPath(primaryData, "initData.staticHotelInfo.image");
            }
            if (image != null) {
                hotelInfo.put("image", image.asText());
            }

            // 4. detailDescPopTags (第四个) - 从staticHotelInfo.hotelInfo.basic.label提取
            List<Map<String, Object>> detailDescPopTags = new ArrayList<>();

            // 从staticHotelInfo.hotelInfo.basic.label中提取真实数据
            JsonNode staticHotelInfo = getNodeByPath(primaryData, "initData.staticHotelInfo.hotelInfo.basic");
            if (staticHotelInfo != null && staticHotelInfo.has("label")) {
                JsonNode labelArray = staticHotelInfo.get("label");
                if (labelArray.isArray()) {
                    for (JsonNode label : labelArray) {
                        String labelText = label.asText().trim();
                        if (!labelText.isEmpty()) {
                            if (labelText.startsWith("开业：")) {
                                Map<String, Object> openTimeTag = new HashMap<>();
                                openTimeTag.put("type", "openTime");
                                openTimeTag.put("value", labelText);
                                detailDescPopTags.add(openTimeTag);
                            } else if (labelText.startsWith("装修：")) {
                                Map<String, Object> fitmentTag = new HashMap<>();
                                fitmentTag.put("type", "fitmentTime");
                                fitmentTag.put("value", labelText);
                                detailDescPopTags.add(fitmentTag);
                            } else if (labelText.startsWith("客房数：")) {
                                Map<String, Object> roomTag = new HashMap<>();
                                roomTag.put("type", "roomNum");
                                roomTag.put("value", labelText);
                                detailDescPopTags.add(roomTag);
                            }
                        }
                    }
                }

                // 提取电话信息
                if (staticHotelInfo.has("telephone")) {
                    JsonNode telephoneArray = staticHotelInfo.get("telephone");
                    if (telephoneArray.isArray() && telephoneArray.size() > 0) {
                        Map<String, Object> phoneTag = new HashMap<>();
                        phoneTag.put("type", "tel");
                        phoneTag.put("value", "");
                        List<String> phoneValues = new ArrayList<>();
                        for (JsonNode phone : telephoneArray) {
                            phoneValues.add(phone.asText());
                        }
                        phoneTag.put("values", phoneValues);
                        detailDescPopTags.add(phoneTag);
                    }
                }
            }

            hotelInfo.put("detailDescPopTags", detailDescPopTags);

            // 5. lables (第五个) - 从staticHotelInfo.hotelInfo.basic.label提取
            List<String> labels = new ArrayList<>();

            // 从staticHotelInfo.hotelInfo.basic.label中提取真实标签
            if (staticHotelInfo != null && staticHotelInfo.has("label")) {
                JsonNode labelArray = staticHotelInfo.get("label");
                if (labelArray.isArray()) {
                    for (JsonNode label : labelArray) {
                        String labelText = label.asText().trim();
                        if (!labelText.isEmpty()) {
                            labels.add(labelText);
                        }
                    }
                }
            }

            hotelInfo.put("lables", labels);

            // 6. description (第六个) - 提取描述
            JsonNode desc = getNodeByPath(primaryData, "initData.staticHotelInfo.description");
            if (desc == null) {
                desc = getNodeByPath(primaryData, "initData.hotelDetailInfo.data.hotelDescriptionInfo.description");
            }
            if (desc != null) {
                hotelInfo.put("description", desc.asText());
            }

            // 7. sectionList (第七个) - 提取章节列表，按示例字段顺序
            if (desc != null) {
                List<Map<String, Object>> sectionList = new ArrayList<>();
                Map<String, Object> section = new LinkedHashMap<>();
                // 按示例顺序: [title, desc, pictureList, dataType, securityKey]
                section.put("title", "");
                section.put("desc", desc.asText());
                section.put("pictureList", new ArrayList<>());
                section.put("dataType", "");
                section.put("securityKey", "");
                sectionList.add(section);
                hotelInfo.put("sectionList", sectionList);
            }

            // 8. needTranslate (第八个)
            hotelInfo.put("needTranslate", false);

            // 9. licenses (第九个) - 从staticHotelInfo.hotelInfo.basic.certiInfo.picUrls提取
            List<String> licenseList = new ArrayList<>();

            // 从staticHotelInfo.hotelInfo.basic.certiInfo.picUrls中提取真实许可证图片
            if (staticHotelInfo != null && staticHotelInfo.has("certiInfo")) {
                JsonNode certiInfo = staticHotelInfo.get("certiInfo");
                if (certiInfo.has("picUrls")) {
                    JsonNode picUrls = certiInfo.get("picUrls");
                    if (picUrls.isArray()) {
                        for (JsonNode picUrl : picUrls) {
                            licenseList.add(picUrl.asText());
                        }
                    }
                }
            }

            hotelInfo.put("licenses", licenseList);

            // 10. licenseTitle (第十个)
            hotelInfo.put("licenseTitle", "预订服务提供方");

            // 11. licenseName (第十一个)
            hotelInfo.put("licenseName", "");

            // 12. licenseNo (第十二个)
            hotelInfo.put("licenseNo", "");

        } catch (Exception e) {
            System.err.println("提取酒店信息失败: " + e.getMessage());
        }

        return hotelInfo;
    }

    /**
     * 提取位置信息
     */
    private static JsonNode extractPositionInfo(JsonNode primaryData, JsonNode secondaryData) {
        try {
            // 提取位置信息
            JsonNode positionInfo = extractByPaths(primaryData, secondaryData, new String[]{
                    "initData.position",
                    "hotelDetailResponse.hotelPositionInfo"
            });

            if (positionInfo != null) {
                // 创建完整的位置信息对象，包含周边信息
                Map<String, Object> completePositionInfo = new HashMap<>();

                // 基本位置信息
                if (positionInfo.has("lng")) completePositionInfo.put("lng", positionInfo.get("lng").asText());
                if (positionInfo.has("lat")) completePositionInfo.put("lat", positionInfo.get("lat").asText());
                if (positionInfo.has("address"))
                    completePositionInfo.put("address", positionInfo.get("address").asText());
                if (positionInfo.has("mapType"))
                    completePositionInfo.put("mapType", positionInfo.get("mapType").asText());

                // 提取周边信息 - placeInfo
                Map<String, Object> placeInfoMap = new HashMap<>();

                // 尝试多个路径提取地点信息
                String[] placeInfoPaths = {
                        "initData.inspiration",
                        "initData.placeInfo",
                        "initData.position.placeInfo",
                        "initData.staticHotelInfo.placeInfo",
                        "Response.placeInfoList"
                };

                for (String path : placeInfoPaths) {
                    JsonNode placeInfo = getNodeByPath(primaryData, path);
                    if (placeInfo != null) {
                        // 提取地点列表 - placeList
                        JsonNode placeList = placeInfo.get("placeList");
                        if (placeList != null && placeList.isArray() && placeList.size() > 0) {
                            placeInfoMap.put("placeList", placeList);
                        }

                        // 提取完整POI信息列表 - wholePoiInfoList
                        JsonNode wholePoiInfoList = placeInfo.get("wholePoiInfoList");
                        if (wholePoiInfoList != null && wholePoiInfoList.isArray() && wholePoiInfoList.size() > 0) {
                            placeInfoMap.put("wholePoiInfoList", wholePoiInfoList);
                        }

                        // 提取POI描述
                        JsonNode poiDesc = placeInfo.get("poiDesc");
                        if (poiDesc != null) {
                            placeInfoMap.put("poiDesc", poiDesc.asText());
                        }

                        // 如果找到了有用的数据就停止
                        if (!placeInfoMap.isEmpty()) {
                            break;
                        }
                    }
                }

                // 如果没有找到数据，不创建默认值

                completePositionInfo.put("placeInfo", placeInfoMap);

                // 提取地图URL
                JsonNode url = positionInfo.get("url");
                if (url != null) {
                    completePositionInfo.put("url", url.asText());
                }

                return objectMapper.valueToTree(completePositionInfo);
            }

        } catch (Exception e) {
            System.err.println("提取位置信息失败: " + e.getMessage());
        }

        return extractByPaths(primaryData, secondaryData, new String[]{
                "initData.position",
                "hotelDetailResponse.hotelPositionInfo"
        });
    }

    /**
     * 提取酒店评论信息
     */
    private static JsonNode extractHotelComment(JsonNode primaryData, JsonNode secondaryData) {
        try {
            // 按照您的代码逻辑提取评论信息
            JsonNode commentData = extractByPaths(primaryData, secondaryData, new String[]{
                    "initData.hotelDetailInfo.data.hotelComment",
                    "hotelDetailResponse.hotelComment",
                    "initData.comment",
                    "initData.initialReview"
            });

            // 创建完整的评论结构，对应其它门店的comment结构
            Map<String, Object> completeComment = new LinkedHashMap<>();
            Map<String, Object> comment = new LinkedHashMap<>();

            if (commentData != null) {
                return commentData;
            }

            // 如果没有找到评论数据，返回空对象
            return objectMapper.valueToTree(new LinkedHashMap<>());

        } catch (Exception e) {
            System.err.println("提取评论信息失败: " + e.getMessage());
        }

        return extractByPaths(primaryData, secondaryData, new String[]{
                "initData.comment",
                "initData.initialReview",
                "initData.hotelDetailInfo.data.hotelComment",
                "hotelDetailResponse.hotelComment"
        });
    }

    /**
     * 提取地点信息列表
     */
    private static JsonNode extractPlaceInfoList(JsonNode primaryData, JsonNode secondaryData) {
        // 按照您的代码逻辑提取地点信息
        return extractByPaths(primaryData, secondaryData, new String[]{
                "Response.placeInfoList",
                "data.hotelPoiInfo",
                "initData.position.poiList",
                "initData.position.placeList",
                "initData.placeInfo"
        });
    }

    /**
     * 将PluginHotelDTO转换为JSON字符串，保持与示例一模一样的顺序
     *
     * @param dto PluginHotelDTO对象
     * @return JSON字符串
     */
    public static String convertToJson(PluginHotelReq dto) {
        try {
            StringBuilder json = new StringBuilder();
            json.append("{\n");

            // 1. hotel_code (第一个)
            Object hotelCode = getField(dto, "hotelCode");
            if (hotelCode != null) {
                json.append("  \"hotel_code\": \"").append(hotelCode).append("\",\n");
            }

            // 2. hotel_name (第二个)
            Object hotelName = getField(dto, "hotelName");
            if (hotelName != null) {
                json.append("  \"hotel_name\": \"").append(hotelName).append("\",\n");
            }

            // 3. operator_info (第三个)
            Object operatorInfo = getField(dto, "operatorInfo");
            if (operatorInfo != null) {
                json.append("  \"operator_info\": ").append(objectMapper.writeValueAsString(operatorInfo)).append(",\n");
            }

            // 4. data_source (第四个)
            Object dataSource = getField(dto, "dataSource");
            if (dataSource != null) {
                json.append("  \"data_source\": \"").append(dataSource).append("\",\n");
            }

            // 5. plugin_hotel (第五个，注意：按照其它门店的顺序)
            Object pluginHotel = getField(dto, "pluginHotel");
            if (pluginHotel != null) {
                json.append("  \"plugin_hotel\": {\n");

                // 5.1 hotel_info (第一个)
                Object hotelInfo = getField(pluginHotel, "hotelInfo");
                if (hotelInfo != null) {
                    json.append("    \"hotel_info\": ").append(objectMapper.writeValueAsString(hotelInfo)).append(",\n");
                }

                // 5.2 reservation_notice_tips_info (第二个)
                Object reservationTips = getField(pluginHotel, "reservationNoticeTipsInfo");
                if (reservationTips != null) {
                    json.append("    \"reservation_notice_tips_info\": ").append(objectMapper.writeValueAsString(reservationTips)).append(",\n");
                }

                // 5.3 hotel_facility (第三个)
                Object hotelFacility = getField(pluginHotel, "hotelFacility");
                if (hotelFacility != null) {
                    json.append("    \"hotel_facility\": ").append(objectMapper.writeValueAsString(hotelFacility)).append(",\n");
                }

                // 5.4 hotel_policy (第四个)
                Object hotelPolicy = getField(pluginHotel, "hotelPolicy");
                if (hotelPolicy != null) {
                    json.append("    \"hotel_policy\": ").append(objectMapper.writeValueAsString(hotelPolicy)).append(",\n");
                }

                // 5.5 position_info (第五个)
                Object positionInfo = getField(pluginHotel, "positionInfo");
                if (positionInfo != null) {
                    json.append("    \"position_info\": ").append(objectMapper.writeValueAsString(positionInfo)).append(",\n");
                }

                // 5.6 hotel_comment (第六个)
                Object hotelComment = getField(pluginHotel, "hotelComment");
                if (hotelComment != null) {
                    json.append("    \"hotel_comment\": ").append(objectMapper.writeValueAsString(hotelComment)).append("\n");
                }

                json.append("  },\n");
            }

            // 6. plugin_room (第六个)
            Object pluginRoom = getField(dto, "pluginRoom");
            if (pluginRoom != null) {
                json.append("  \"plugin_room\": ").append(objectMapper.writeValueAsString(pluginRoom)).append(",\n");
            }

            // 7. plugin_place (第七个)
            Object pluginPlace = getField(dto, "pluginPlace");
            if (pluginPlace != null) {
                json.append("  \"plugin_place\": ").append(objectMapper.writeValueAsString(pluginPlace)).append("\n");
            }

            // 移除最后的逗号
            String jsonStr = json.toString();
            if (jsonStr.endsWith(",\n")) {
                jsonStr = jsonStr.substring(0, jsonStr.length() - 2) + "\n";
            }

            jsonStr += "}";
            return jsonStr;

        } catch (Exception e) {
            System.err.println("转换为JSON失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用反射获取字段值
     */
    private static Object getField(Object obj, String fieldName) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            return null;
        }
    }

//    /**
//     * 主方法 - 测试用
//     */
//    public static void main(String[] args) {
//        String url = "https://hotels.ctrip.com/hotels/113544116.html";
//
//        System.out.println("开始提取酒店数据...");
//        PluginHotelReq dto = extractHotelData(url);
//
//        if (dto != null) {
//            System.out.println("✅ 数据提取成功");
//
//            // 转换为JSON
//            String json = convertToJson(dto);
//            if (json != null) {
//                System.out.println("✅ JSON转换成功");
//                System.out.println(json);
//            } else {
//                System.err.println("❌ JSON转换失败");
//            }
//        } else {
//            System.err.println("❌ 数据提取失败");
//        }
//    }
}
