package com.wormhole.hotelds.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.*;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.wormhole.hotelds.admin.constant.*;
import lombok.extern.slf4j.*;
import org.springframework.stereotype.Component;

import javax.imageio.*;
import java.awt.image.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class QrCodeUtil {
    
    private static final String CHARSET = "UTF-8";
    
    public static byte[] generateQrCode(String content) throws WriterException, IOException {
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, CHARSET);
        hints.put(EncodeHintType.MARGIN, 1);

        BitMatrix bitMatrix = new MultiFormatWriter().encode(
                content,
                BarcodeFormat.QR_CODE,
                QrCodeConstant.QR_CODE_SIZE,
                QrCodeConstant.QR_CODE_SIZE,
                hints
        );
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 把指定内容生成base64的二维码
     */
    public static String generateQrCodeImage(String text, int width, int height) {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix;
        try {
            bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height);
            BufferedImage image = MatrixToImageWriter.toBufferedImage(bitMatrix);
            return imgToBase64(image, "PNG");
        } catch (WriterException e) {
            log.error("二维码生成失败! text: {}", text, e);
        }
        return null;
    }

    /**
     * 将图片转换成base64格式进行存储
     */
    public static String imgToBase64(BufferedImage image, String type) {
        String imageString = null;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, type, bos);
            byte[] imageBytes = bos.toByteArray();
            imageString = java.util.Base64.getEncoder().encodeToString(imageBytes);
            bos.close();
        } catch (IOException e) {
            log.info("图片转换成base64格式失败", e);
        }
        return imageString;
    }
} 