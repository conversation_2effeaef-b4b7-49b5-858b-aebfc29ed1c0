package com.wormhole.hotelds.util;

import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/5/12 14:51
 * @Description：FieldMappingConfigUtil
 */
public class FieldMappingConfigUtil {

    private static final Map<String, Map<String, String>> FIELD_MAPPING = new HashMap<>();

    // 静态初始化块，预加载所有业务类型的字段映射
    static {
        // 初始化设备型号的字段映射
        initDeviceModelMapping();

        // 初始化门店的字段映射
        initHotelMapping();

        // 初始化员工的字段映射
        initEmployeeMapping();

        // 初始化设备的字段映射
        initDeviceMapping();

        // 初始化设备位置的字段映射
        initDevicePositionMapping();

    }

    /**
     * 获取指定业务类型的字段映射
     * @param businessType 业务类型
     * @return 字段映射Map
     */
    public static Map<String, String> getFieldMapping(String businessType) {
        return FIELD_MAPPING.getOrDefault(businessType, new HashMap<>());
    }

    /**
     * 初始化设备型号的字段映射
     */
    private static void initDeviceModelMapping() {
        Map<String, String> mapping = new HashMap<>();
        // 基本信息
        mapping.put("modelName", "设备类型名称");
        mapping.put("deviceAppType", "App类型");
        mapping.put("modelCode", "设备型号");

        // 品牌信息
        mapping.put("brandName", "设备品牌");

        // 厂商信息
        mapping.put("manufacturer", "厂商名称");
        mapping.put("companyName", "厂商企业名称");
        mapping.put("manufacturerContactPerson", "厂商联系人");
        mapping.put("manufacturerContact", "厂商联系方式");
        mapping.put("manufacturerServiceProvider", "厂商服务热线\n");

        // 保修信息
        mapping.put("warrantyMonths", "保修期");
        mapping.put("warrantyInfo", "保修情况");

        // 其他信息
        mapping.put("remark", "备注");

        FIELD_MAPPING.put(BussinessTypeEnum.DEVICE_MODEL.getBusinessType(), mapping);
    }

    /**
     * 初始化门店的字段映射
     */
    private static void initHotelMapping() {
        Map<String, String> mapping = new HashMap<>();
        mapping.put("hotelLogo", "门店Logo");
        mapping.put("hotelName", "门店名称");
        mapping.put("countryCode", "国家编码");
        mapping.put("countryName", "国家名称");
        mapping.put("provinceCode", "省份编码");
        mapping.put("provinceName", "省份名称");
        mapping.put("cityCode", "城市编码");
        mapping.put("cityName", "城市名称");
        mapping.put("districtCode", "区县编码");
        mapping.put("districtName", "区县名称");
        mapping.put("address", "地址");
        mapping.put("longitude", "经度");
        mapping.put("latitude", "纬度");
        mapping.put("merchantName", "商户名称");
        mapping.put("merchantShortName", "商户简称");
        mapping.put("brandName", "品牌名称");
        mapping.put("subjectName", "企业名称");
        mapping.put("mainPerson", "门店联系人");
        mapping.put("phone", "联系电话");
        mapping.put("email", "联系邮箱");
        mapping.put("totalRoom", "房间数量");
        mapping.put("ctripEbkUrl", "携程EBK链接");

        FIELD_MAPPING.put(BussinessTypeEnum.HOTEL.getBusinessType(), mapping);
    }

    /**
     * 初始化设备型号的字段映射
     */
    private static void initEmployeeMapping() {
        Map<String, String> mapping = new HashMap<>();
        // 基本信息
        mapping.put("name", "姓名");
        mapping.put("username", "自定义账号");
        mapping.put("mobile", "手机号码");
        mapping.put("email", "邮箱");

        FIELD_MAPPING.put(BussinessTypeEnum.EMPLOYEE.getBusinessType(), mapping);
    }

    /**
     * 初始化设备型号的字段映射
     */
    private static void initDeviceMapping() {
        Map<String, String> mapping = new HashMap<>();
        // 基本信息
        mapping.put("modelId", "设备类型");
        mapping.put("deviceSn", "设备SN码");
        mapping.put("imei", "IMEI");
        mapping.put("purchaseTime", "采购日期");
        mapping.put("warrantyStart", "保修开始时间");
        mapping.put("warrantyEnd", "保修结束时间");

        FIELD_MAPPING.put(BussinessTypeEnum.DEVICE.getBusinessType(), mapping);
    }

    /**
     * 初始化设备型号的字段映射
     */
    private static void initDevicePositionMapping() {
        Map<String, String> mapping = new HashMap<>();
        // 基本信息
        mapping.put("block", "楼栋");
        mapping.put("area", "区域");
        mapping.put("positionName", "位置名称");

        FIELD_MAPPING.put(BussinessTypeEnum.DEVICE_POSITION.getBusinessType(), mapping);
    }
}
