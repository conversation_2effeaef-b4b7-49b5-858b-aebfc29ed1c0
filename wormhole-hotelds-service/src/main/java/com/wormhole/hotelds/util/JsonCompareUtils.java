package com.wormhole.hotelds.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.core.enums.DeviceStatusEnum;

import java.util.*;

/**
 * @Author：flx
 * @Date：2025/5/12 14:53
 * @Description：JsonCompareUtils
 */
public class JsonCompareUtils {

    /**
     * 通用排除字段，不参与比较
     */
    private static final Set<String> EXCLUDE_FIELDS = new HashSet<>(Arrays.asList(
            "createdAt", "updatedAt", "createdBy", "updatedBy",
            "createdByName", "updatedByName","password"));

    /**
     * 比较两个JSON对象，返回变更描述列表
     */
    public static List<String> compareJsonObjects(String beforeJson,
                                                  String afterJson,
                                                  String objectType,
                                                  String oprMethod) {
        List<String> changes = new ArrayList<>();

        try {
            // 处理特殊操作类型
            if ("updateHotelList".equals(oprMethod)) {
                return compareHotelList(beforeJson, afterJson);
            }
            if ("resetPassword".equals(oprMethod)) {
                return Collections.singletonList("重置密码");
            }

            // 处理普通对象比较
            return compareNormalObject(beforeJson, afterJson, objectType);
        } catch (Exception e) {
            changes.add("变更内容解析失败: " + e.getMessage());
            return changes;
        }
    }

    /**
     * 比较门店列表
     */
    private static List<String> compareHotelList(String beforeJson, String afterJson) {
        List<String> changes = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode beforeNode = mapper.readTree(beforeJson);
            JsonNode afterNode = mapper.readTree(afterJson);

            if (beforeNode.isArray() && afterNode.isArray()) {
                String beforeArrStr = arrayToSortedString(beforeNode);
                String afterArrStr = arrayToSortedString(afterNode);
                if (!beforeArrStr.equals(afterArrStr)) {
                    changes.add("关联门店从\"" + beforeArrStr + "\"修改为\"" + afterArrStr + "\"");
                }
            }
        } catch (Exception e) {
            changes.add("变更内容解析失败: " + e.getMessage());
        }
        return changes;
    }

    /**
     * 比较普通对象
     */
    private static List<String> compareNormalObject(String beforeJson, String afterJson, String objectType) {
        List<String> changes = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode beforeNode = mapper.readTree(beforeJson);
            JsonNode afterNode = mapper.readTree(afterJson);

            Map<String, String> fieldNameMap = FieldMappingConfigUtil.getFieldMapping(objectType);
            Iterator<Map.Entry<String, JsonNode>> fields = afterNode.fields();

            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                String fieldName = field.getKey();

                if (EXCLUDE_FIELDS.contains(fieldName)) {
                    continue;
                }

                JsonNode beforeValue = beforeNode.get(fieldName);
                JsonNode afterValue = field.getValue();

                if (beforeValue != null && !beforeValue.equals(afterValue)) {
                    String displayName = fieldNameMap.getOrDefault(fieldName, fieldName);
                    String beforeValueStr = formatValue(fieldName, beforeValue, objectType);
                    String afterValueStr = formatValue(fieldName, afterValue, objectType);

                    changes.add(displayName + "从\"" + beforeValueStr + "\"修改为\"" + afterValueStr + "\"");
                }
            }
        } catch (Exception e) {
            changes.add("变更内容解析失败: " + e.getMessage());
        }
        return changes;
    }

    /**
     * 获取格式化后的字段值
     *
     * @param fieldName  字段名
     * @param valueNode  字段值节点
     * @param objectType 业务类型
     * @return 格式化后的值
     */
    public static String formatValue(String fieldName, JsonNode valueNode, String objectType) {
        if (valueNode == null || valueNode.isNull()) {
            return "无";
        }
        BussinessTypeEnum businessType = BussinessTypeEnum.getByBusinessType(objectType);
        return switch (businessType) {
            case DEVICE_MODEL -> formatDeviceModelValue(fieldName, valueNode);
            case HOTEL -> formatHotelValue(fieldName, valueNode);
            case DEVICE -> formatDeviceValue(fieldName,valueNode);
            default -> valueNode.asText();
        };
    }

    /**
     * 格式化设备型号字段值
     */
    private static String formatDeviceModelValue(String fieldName, JsonNode valueNode) {
        return switch (fieldName) {
            case "status" -> {
                int status = valueNode.asInt();
                yield status == 1 ? "启用" : "禁用";
            }
            case "deviceAppType" -> {
                String type = valueNode.asText();
                yield switch (type) {
                    case "front" -> "ai智能座机";
                    case "room" -> "ai云电话pad版";
                    default -> type;
                };
            }
            default -> valueNode.asText();
        };
    }

    /**
     * 格式化门店字段值
     */
    private static String formatHotelValue(String fieldName, JsonNode valueNode) {
        return valueNode.asText();
    }

    private static String formatDeviceValue(String fieldName, JsonNode valueNode) {
        return switch (fieldName) {
            case "deviceStatus" -> {
                int status = valueNode.asInt();
                yield switch (status) {
                    case 0 -> "待激活";
                    case 1 -> "在线";
                    case 2 -> "离线";
                    case 3 -> "停用";
                    default -> String.valueOf(status);
                };
            }
            case "deviceAppType" -> {
                String type = valueNode.asText();
                yield switch (type) {
                    case "front" -> "ai智能座机";
                    case "room" -> "ai云电话pad版";
                    default -> type;
                };
            }
            default -> valueNode.asText();
        };
    }

    private static String arrayToSortedString(JsonNode arrayNode) {
        if (arrayNode == null || !arrayNode.isArray()) {
            return "";
        }
        List<String> list = new ArrayList<>();
        for (JsonNode node : arrayNode) {
            list.add(node.asText());
        }
        Collections.sort(list);
        return String.join(",", list);
    }
}
