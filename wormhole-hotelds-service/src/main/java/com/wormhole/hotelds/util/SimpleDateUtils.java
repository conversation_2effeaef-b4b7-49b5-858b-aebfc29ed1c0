package com.wormhole.hotelds.util;

import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;
import java.util.regex.Pattern;

@Slf4j
public class SimpleDateUtils implements DateParsePatterns {

    private static final String GMT = "GMT";

    private static final Locale LOCALE_US = Locale.US;

    private static final DateTimeFormatter SLASH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter DOT_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM.dd");


    // 日期格式正则表达式
    private static final Pattern SLASH_DATE_PATTERN = Pattern.compile("\\d{4}-\\d{2}-\\d{2}");


    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT_THREAD_LOCAL = ThreadLocal.withInitial(() -> {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(EEE_DD_MMM_YYYY_HH_MM_SS_Z, LOCALE_US);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone(GMT));
        return simpleDateFormat;
    });

    public static String getGmtDateString(Date date) {
        return DATE_FORMAT_THREAD_LOCAL.get().format(date);
    }

    /**
     * 解析日期字符串
     *
     * @param str
     * @param parsePatterns
     * @return
     */
    public static Date parseDate(String str, String... parsePatterns) {
        Date date = null;
        if (StringUtils.isBlank(str) || ArrayUtils.isEmpty(parsePatterns)) {
            return date;
        }
        try {
            date = DateUtils.parseDate(str, parsePatterns);
        } catch (ParseException e) {
            throw new IllegalArgumentException("parsePatterns error:" + e.getMessage());
        }
        return date;
    }

    public static Calendar toCalendar(Date date) {
        Preconditions.checkArgument(Objects.nonNull(date), "date must not be null.");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c;
    }

    /**
     * 设置一天的开始时间（00:00:00.0）
     *
     * @param calendar
     * @return
     */
    public static Calendar setStartTime(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    /**
     * 设置一天的结束时间（23:59:59.999）
     *
     * @param calendar
     * @return
     */
    public static Calendar setEndTime(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar;
    }

    /**
     * 返回指定时间的开始时间
     *
     * @param date
     * @return
     */
    public static Date getStartTimeOfDay(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = toCalendar(date);
        return setStartTime(calendar).getTime();
    }

    /**
     * 返回指定时间的开始时间
     *
     * @param date
     * @return
     */
    public static Date getStartTimeOfDayForDate(String date) {
        if (date == null) {
            return null;
        }
        return getStartTimeOfDay(parseDate(date, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_DASH));
    }

    /**
     * 返回指定时间的开始时间
     *
     * @param date
     * @return
     */
    public static String getStartTimeOfDayForStr(String date) {
        Date startTimeOfDayForDate = getStartTimeOfDayForDate(date);
        return DateFormatUtils.format(startTimeOfDayForDate, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_DASH);
    }

    /**
     * 返回指定时间的开始时间
     *
     * @param date
     * @return
     */
    public static String getStartTimeForStr(String date) {
        Date startTimeOfDayForDate = getStartTimeOfDayForDate(date);
        return DateFormatUtils.format(startTimeOfDayForDate, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
    }

    public static Date getTomorrowStartTimeOfDay(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = toCalendar(DateUtils.addDays(date, 1));
        return setStartTime(calendar).getTime();
    }

    public static Date getNextStartTimeOfDayForDate(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        return getTomorrowStartTimeOfDay(parseDate(date, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_DASH));
    }

    public static String getNextStartTimeOfDayForStr(String date) {
        Date tomorrowStartTimeOfDay = getNextStartTimeOfDayForDate(date);
        return DateFormatUtils.format(tomorrowStartTimeOfDay, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_DASH);
    }

    public static String getNextStartTimeForStr(String date) {
        Date tomorrowStartTimeOfDay = getNextStartTimeOfDayForDate(date);
        return DateFormatUtils.format(tomorrowStartTimeOfDay, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 返回指定日期的结束时间
     *
     * @param date
     * @return
     */
    public static Date getEndTimeOfDay(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = toCalendar(date);
        return setEndTime(calendar).getTime();
    }

    /**
     * 返回指定日期所在周的第一天的开始时间
     *
     * @param date           日期
     * @param firstDayOfWeek 取值范围：Calendar.MONDAY ~ Calendar.SUNDAY，设置周几作为一周的开始
     * @return
     */
    public static Date getFirstDayStartTimeOfWeek(Date date, int firstDayOfWeek) {
        Calendar calendar = toCalendar(date);
        int curDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        if (curDayOfWeek < firstDayOfWeek) {
            // 当前日期所在周的区间外，先减去6天切换到上周，然后设置上周的开始时间
            calendar.add(Calendar.DATE, -6);
        }
        calendar.set(Calendar.DAY_OF_WEEK, firstDayOfWeek);
        return setStartTime(calendar).getTime();
    }

    /**
     * 返回指定日期所在周的最后一天的结束时间
     *
     * @param date           日期
     * @param firstDayOfWeek 取值范围：Calendar.MONDAY ~ Calendar.SUNDAY，设置周几作为一周的开始
     * @return
     */
    public static Date getLastDayEndTimeOfWeek(Date date, int firstDayOfWeek) {
        Calendar calendar = toCalendar(date);
        int curDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        if (curDayOfWeek < firstDayOfWeek) {
            // 当前日期在所在周的区间内
            calendar.set(Calendar.DAY_OF_WEEK, firstDayOfWeek - 1);
        } else {
            // 当前日期所在周的区间外
            calendar.set(Calendar.DAY_OF_WEEK, firstDayOfWeek);
            calendar.add(Calendar.DATE, 6);
        }
        return setEndTime(calendar).getTime();
    }

    /**
     * 返回指定日期所属周数，可以指定周一到周日作为一周的开始
     *
     * @param date           日期
     * @param firstDayOfWeek 取值范围：Calendar.MONDAY ~ Calendar.SUNDAY，设置周几作为一周的开始
     * @return
     */
    public static int getWeekOfYear(Date date, int firstDayOfWeek) {
        Calendar calendar = toCalendar(date);
        calendar.setFirstDayOfWeek(firstDayOfWeek);
        return calendar.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 返回日期所在月的第一天的开始时间
     *
     * @param date
     * @return
     */
    public static Date getFirstDayStartTimeOfMonth(Date date) {
        Calendar calendar = toCalendar(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        return setStartTime(calendar).getTime();
    }

    /**
     * 返回日期所在月的最后一天的结束时间
     *
     * @param date
     * @return
     */
    public static Date getLastDayEndTimeOfMonth(Date date) {
        Calendar calendar = toCalendar(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return setEndTime(calendar).getTime();
    }

    /**
     * 获取季度的开始时间
     *
     * @param date
     * @return
     */
    public static Date getFirstDayStartTimeOfQuarter(Date date) {
        Calendar calendar = toCalendar(date);
        Quarter quarter = Quarter.valueOf(calendar.get(Calendar.MONTH));
        calendar.set(Calendar.MONTH, quarter.startMonth());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return setStartTime(calendar).getTime();
    }

    /**
     * 获取季度的结束时间
     *
     * @param date
     * @return
     */
    public static Date getLastDayEndTimeOfQuarter(Date date) {
        Calendar calendar = toCalendar(date);
        Quarter quarter = Quarter.valueOf(calendar.get(Calendar.MONTH));
        calendar.set(Calendar.DAY_OF_MONTH, 1);// 这里统一设置为1号，避免day为31号的时候，set
        // Calendar.MONTH后的日期因为没有31号，导致日期变为下月1号的问题
        calendar.set(Calendar.MONTH, quarter.endMonth());
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return setEndTime(calendar).getTime();
    }

    /**
     * 返回日期所在年的第一天的开始时间
     *
     * @param date
     * @return
     */
    public static Date getFirstDayStartTimeOfYear(Date date) {
        Calendar calendar = toCalendar(date);
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        return setStartTime(calendar).getTime();
    }

    /**
     * 返回日期所在年的最后一天的结束时间
     *
     * @param date
     * @return
     */
    public static Date getLastDayEndTimeOfYear(Date date) {
        Calendar calendar = toCalendar(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.getActualMaximum(Calendar.DAY_OF_YEAR));
        return setEndTime(calendar).getTime();
    }

    /**
     * 计算耗时分钟
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static double betweenAsMinute(Date startTime, Date endTime) {
        Preconditions.checkArgument(Objects.nonNull(startTime), "startTime must not be null");
        Preconditions.checkArgument(Objects.nonNull(endTime), "endTime must not be null");
        long elapsed = endTime.getTime() - startTime.getTime();
        return MathUtils.division(elapsed, 60 * 1000, 2);
    }

    /**
     * 返回可读的时间间隔
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static String betweenAsHumanReadable(Date startTime, Date endTime) {
        Preconditions.checkArgument(Objects.nonNull(startTime), "startTime must not be null");
        Preconditions.checkArgument(Objects.nonNull(endTime), "endTime must not be null");
        long elapsedInSecond = (endTime.getTime() - startTime.getTime()) / 1000;
        if (elapsedInSecond < 60) {
            return elapsedInSecond + "秒";
        } else if (elapsedInSecond < 60 * 60) {
            return MathUtils.division(elapsedInSecond, 60, 2) + "分钟";
        } else if (elapsedInSecond < 24 * 60 * 60) {
            return MathUtils.division(elapsedInSecond, 60 * 60, 2) + "小时";
        } else {
            return MathUtils.division(elapsedInSecond, 60 * 60 * 24, 2) + "天";
        }
    }

    public static String formatDatetime(Date datetime) {
        return Objects.nonNull(datetime) ? DateFormatUtils.format(datetime, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SLASH) : "--";
    }

    /**
     * 判断日期区间是否跨年
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static boolean isCrossYear(Date startDate, Date endDate) {
        if (startDate != null || endDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            int startYear = calendar.get(Calendar.YEAR);
            calendar.setTime(endDate);
            int endYear = calendar.get(Calendar.YEAR);
            return startYear != endYear;
        }
        return false;
    }

    /**
     * 将startDate和endDate去掉日后面的时间后计算
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int truncatedDaysBetween(Date startDate, Date endDate) {
        Date truncatedStartDate = DateUtils.truncate(startDate, Calendar.DATE);
        Date truncatedEndDate = DateUtils.truncate(endDate, Calendar.DATE);
        return (int) ((truncatedEndDate.getTime() - truncatedStartDate.getTime()) / 86400000);
    }

    /**
     * 获取date之后minute分钟的时间
     *
     * @param date
     * @param minute
     * @return
     */
    public static Date evenMinuteDate(Date date, int minute) {
        if (date == null) {
            date = new Date();
        }
        Calendar c = toCalendar(date);
        c.set(Calendar.MINUTE, c.get(Calendar.MINUTE) + minute);
        return c.getTime();
    }

    /**
     * 获取date之后second秒的时间
     *
     * @param date
     * @param second
     * @return
     */
    public static Date evenSecondDate(Date date, int second) {
        if (date == null) {
            date = new Date();
        }
        Calendar c = toCalendar(date);
        c.set(Calendar.SECOND, c.get(Calendar.SECOND) + second);
        return c.getTime();
    }

    /**
     * 将斜杠分隔的日期字符串(如"2025/02/20")转换为LocalDateTime
     * 默认时间设置为当天的开始(00:00:00)
     *
     * @param dateStr 日期字符串，如"2025/02/20"
     * @return 转换后的LocalDateTime，转换失败返回null
     */
    public static LocalDateTime slashStringToStartDateTime(String dateStr) {
        return stringToDateTime(dateStr, SLASH_FORMATTER, LocalTime.MIN);
    }

    /**
     * 将斜杠分隔的日期字符串(如"2025/02/20")转换为LocalDateTime
     * 默认时间设置为当天的结束(23:59:59)
     *
     * @param dateStr 日期字符串，如"2025/02/20"
     * @return 转换后的LocalDateTime，转换失败返回null
     */
    public static LocalDateTime slashStringToEndDateTime(String dateStr) {
        return stringToDateTime(dateStr, SLASH_FORMATTER, LocalTime.MAX);
    }

    /**
     * 将LocalDateTime转换为yyyy-MM-dd格式的字符串
     *
     * @param dateTime LocalDateTime对象
     * @return 格式化后的日期字符串，如"2025-02-20"
     */
    public static String formatLocalDateTimeToDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(SLASH_FORMATTER);
    }

    /**
     * 将LocalDateTime转换为yyyy-MM-dd格式的字符串
     *
     * @param dateTime LocalDateTime对象
     * @return 格式化后的日期字符串，如"2025-02-20 00:00:00"
     */
    public static String formatLocalDateTimeToDateHour(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATE_FORMATTER);
    }

    /**
     * 将点号分隔的日期字符串(如"2025.02.20")转换为LocalDateTime
     * 默认时间设置为当天的开始(00:00:00)
     *
     * @param dateStr 日期字符串，如"2025.02.20"
     * @return 转换后的LocalDateTime，转换失败返回null
     */
    public static LocalDateTime dotStringToStartDateTime(String dateStr) {
        return stringToDateTime(dateStr, DOT_FORMATTER, LocalTime.MIN);
    }

    /**
     * 将点号分隔的日期字符串(如"2025.02.20")转换为LocalDateTime
     * 默认时间设置为当天的结束(23:59:59.999999999)
     *
     * @param dateStr 日期字符串，如"2025.02.20"
     * @return 转换后的LocalDateTime，转换失败返回null
     */
    public static LocalDateTime dotStringToEndDateTime(String dateStr) {
        return stringToDateTime(dateStr, DOT_FORMATTER, LocalTime.MAX);
    }

    /**
     * 验证字符串是否为有效的斜杠分隔日期格式(yyyy/MM/dd)
     *
     * @param dateStr 日期字符串
     * @return 如果是有效的日期格式返回true，否则返回false
     */
    public static boolean isValidSlashDateFormat(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return false;
        }

        if (!SLASH_DATE_PATTERN.matcher(dateStr).matches()) {
            return false;
        }

        try {
            LocalDate.parse(dateStr, SLASH_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 将日期字符串转换为LocalDateTime
     *
     * @param dateStr 日期字符串
     * @param formatter 日期格式化器
     * @param defaultTime 默认时间
     * @return 转换后的LocalDateTime，转换失败返回null
     */
    private static LocalDateTime stringToDateTime(String dateStr, DateTimeFormatter formatter, LocalTime defaultTime) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        try {
            LocalDate date = LocalDate.parse(dateStr.trim(), formatter);
            return LocalDateTime.of(date, defaultTime);
        } catch (DateTimeParseException e) {
            //log.warn("日期解析失败: {}", dateStr, e);
            return null;
        }
    }

    /**
     * 返回yyyy-MM-dd HH:mm:ss格式的时间
     * 1、将1970-01-01转为1970-01-01 00:00:00
     * 2、若1970-01-01 00:00:00则直接返回
     * 不对入参的强校验.
     *
     * @param datetimeString
     * @return
     */
    public static String getDatetimeFullString(String datetimeString) {
        if (StringUtils.isBlank(datetimeString)) {
            return null;
        }
        String[] s = datetimeString.split(" ");
        if (s.length == 1) {
            return datetimeString + " 00:00:00";
        }
        return datetimeString;
    }

    public static String getCurrentTimestamp(String format) {
        return DateFormatUtils.format(new Date(), format);
    }

    /**
     * 获取当前时间戳，时分秒字符串
     *
     * @return
     */
    public static String getCurrentTimestamp() {
        return DateFormatUtils.format(new Date(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 获取当前时间戳，时分秒字符串
     *
     * @return
     */
    public static String getDatetimeFullString() {
        return DateFormatUtils.format(new Date(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SLASH);
    }

    /**
     * 季度工具类
     *
     * <AUTHOR>
     * @version 2016年11月15日下午6:24:42
     */
    public enum Quarter {
        FIRST(Calendar.JANUARY, Calendar.MARCH),
        SECOND(Calendar.APRIL, Calendar.JUNE),
        THIRD(Calendar.JULY,
                Calendar.SEPTEMBER),
        FOURTH(Calendar.OCTOBER, Calendar.DECEMBER);

        private final int startMonth, endMonth;

        /**
         * Creates a quarter with the given start and end month.
         *
         * @param startMonth the first month of the quarter
         * @param endMonth   the last month of the quarter
         */
        private Quarter(int startMonth, int endMonth) {
            this.startMonth = startMonth;
            this.endMonth = endMonth;
        }

        /**
         * Used to convert the month value to its corresponding Quarter object. The values are 0-based.
         *
         * @param month the number of the month (0-based)
         * @return the Quarter containing the given month
         */
        public static Quarter valueOf(final int month) {
            Quarter qtr = null;
            if (month >= 0 && month < 3) {
                qtr = Quarter.FIRST;
            } else if (month > 2 && month < 6) {
                qtr = Quarter.SECOND;
            } else if (month > 5 && month < 9) {
                qtr = Quarter.THIRD;
            } else if (month > 8 && month < 12) {
                qtr = Quarter.FOURTH;
            } else {
                throw new IllegalArgumentException("month must be 0-11, but current is " + month);
            }
            return qtr;
        }

        /**
         * Used to retrieve the first month of the quarter.
         *
         * @return the first month of the quarter
         */
        public int startMonth() {
            return startMonth;
        }

        /**
         * Used to retrieve the last month of the quarter.
         *
         * @return the last month of the quarter
         */
        public int endMonth() {
            return endMonth;
        }

        /**
         * 上个季度.
         *
         * @return 上个季度
         */
        public Quarter previous() {
            Quarter[] qtrs = values();
            int idx = ordinal() - 1;
            if (idx < 0) {
                idx = qtrs.length - 1;
            }
            return qtrs[idx];
        }

        /**
         * 下个季度.
         *
         * @return 下个季度
         */
        public Quarter next() {
            Quarter[] qtrs = values();
            int idx = ordinal() + 1;
            if (idx >= qtrs.length) {
                idx = 0;
            }
            return qtrs[idx];
        }
    }

    /**
     * 生成日期数组
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<Date> getDateRange(Date startDate, Date endDate) {
        List<Date> dateList = new ArrayList<>();
        long currentTime = startDate.getTime();
        long endTime = endDate.getTime();

        while (currentTime <= endTime) {
            dateList.add(new Date(currentTime));
            currentTime += 24 * 60 * 60 * 1000;
        }

        return dateList;
    }


    /**
     * 生成日期数组
     *
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    public static List<String> getDateRange(String startDateStr, String endDateStr) {

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        List<String> dateList = new ArrayList<>();
        try {
            Date startDate = dateFormat.parse(startDateStr);
            Date endDate = dateFormat.parse(endDateStr);

            long currentTime = startDate.getTime();
            long endTime = endDate.getTime();

            while (currentTime <= endTime) {
                Date current = new Date(currentTime);
                dateList.add(dateFormat.format(current));
                currentTime += 24 * 60 * 60 * 1000;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return dateList;
    }

    /**
     * 将日期字符串转换为LocalDate
     *
     * @param dateStr 日期字符串，格式为yyyy-MM-dd
     * @return LocalDate对象
     */
    public static LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        try {
            return LocalDate.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("解析日期字符串失败: {}", dateStr, e);
            return null;
        }
    }

    /**
     * 将日期时间字符串转换为LocalDateTime
     *
     * @param dateTimeStr 日期时间字符串，格式为yyyy-MM-dd HH:mm:ss
     * @return LocalDateTime对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            return LocalDateTime.parse(dateTimeStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("解析日期时间字符串失败: {}", dateTimeStr, e);
            return null;
        }
    }
}
