package com.wormhole.hotelds.util;

public interface DateParsePatterns {
    /**
     * yyyy-MM-dd HH:mm:ss.SSS
     */
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    /**
     * yyyy-MM-dd HH:mm:ss
     */
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_DASH = "yyyy-MM-dd HH:mm:ss";
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_DASH_UTC = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SSSSSSSSS_UTC = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'";
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SSSSSS_UTC = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'";
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SSS_UTC = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    /**
     * yyyy/MM/dd HH:mm:ss
     */
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SLASH = "yyyy/MM/dd HH:mm:ss";
    /**
     * yyyyMMddHHmmss
     */
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyyMMddHHmmss";
    /**
     * yyyyMMddHHmm
     */
    String DATE_FORMAT_YYYY_MM_DD_HH_MM = "yyyyMMddHHmm";
    /**
     * yyyyMMddHH
     */
    String DATE_FORMAT_YYYY_MM_DD_HH = "yyyyMMddHH";
    /**
     * yyyyMMdd
     */
    String DATE_FORMAT_YYYY_MM_DD = "yyyyMMdd";
    /**
     * yyyy-MM-dd
     */
    String DATE_FORMAT_YYYY_MM_DD_DASH = "yyyy-MM-dd";
    /**
     * yyyy/MM/dd
     */
    String DATE_FORMAT_YYYY_MM_DD_SLASH = "yyyy/MM/dd";
    /**
     * yyyy.MM.dd
     */
    String DATE_FORMAT_YYYY_MM_DD_DOT = "yyyy.MM.dd";
    /**
     * yyyy年MM月dd日
     */
    String DATE_FORMAT_YYYY_MM_DD_CN = "yyyy年MM月dd日";
    /**
     * yyyy-MM
     */
    String DATE_FORMAT_YYYY_MM = "yyyy-MM";
    /**
     * yyyy-M
     */
    String DATE_FORMAT_YYYY_M = "yyyy-M";
    /**
     * MM-dd
     */
    String DATE_FORMAT_MM_DD = "MM-dd";
    /**
     * HH:mm
     */
    String DATE_FORMAT_HH_MM = "HH:mm";
    /**
     * HH:mm
     */
    String DATE_FORMAT_HH_MM_SS = "HH:mm:ss";
    String EEE_DD_MMM_YYYY_HH_MM_SS_Z = "EEE, dd MMM yyyy hh:mm:ss z";
}
