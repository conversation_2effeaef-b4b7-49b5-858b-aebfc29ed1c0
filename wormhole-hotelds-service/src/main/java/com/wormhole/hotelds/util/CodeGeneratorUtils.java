package com.wormhole.hotelds.util;

import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

/**
 * @Author：flx
 * @Date：2025/3/27 09:09
 * @Description：编码生成工具类：生成业务ID和编码，格式为前缀+6位字母数字混合
 */
@Component
public class CodeGeneratorUtils {

    /**
     * Redis键前缀
     */
    private static final String ID_KEY_PREFIX = "code:id:";
    private static final String CODE_KEY_PREFIX = "code:code:";
    private static final String ORDER_NO_KEY_PREFIX = "order:no:";

    /**
     * 编码相关常量
     */
    private static final String CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int RADIX = CHARS.length();
    private static final int CODE_LENGTH = 6;

    /**
     * 日期格式化器，用于生成按年月隔离的Redis键
     */
    private static final DateTimeFormatter YEAR_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    /**
     * 订单号时间戳格式化器
     */
    private static final DateTimeFormatter ORDER_TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Resource
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;

    /**
     * 生成业务ID - 格式：前缀 + 6位字母数字混合
     *
     * @param businessType 业务类型
     * @return 前缀+6位编码，例如: H2XY9P
     */
    public Mono<String> generateId(String businessType) {
        return generateUniqueCode(businessType, ID_KEY_PREFIX);
    }

    /**
     * 生成业务编码 - 格式：前缀 + 6位字母数字混合
     *
     * @param businessType 业务类型
     * @return 前缀+6位编码，例如: M7BNFC
     */
    public Mono<String> generateCode(String businessType) {
//        return generateUniqueCode(businessType, CODE_KEY_PREFIX);
        if (BussinessTypeEnum.EMPLOYEE.getBusinessType().equals(businessType)) {
            return generateEmployeeCode();
        } else {
            return generateUniqueCode(businessType, CODE_KEY_PREFIX);
        }
    }

    /**
     * 为员工生成特殊格式的编码 - 格式: E + 6位字母数字混合 + 数字后缀(总长度6-10位)
     *
     * @return 员工编码，例如: E7BNFC123
     */
    private Mono<String> generateEmployeeCode() {
        // 使用原有机制生成基础编码
        return generateUniqueCode(BussinessTypeEnum.EMPLOYEE.getBusinessType(), CODE_KEY_PREFIX)
                .map(baseCode -> {
                    // 获取当前时间戳的后几位作为数字后缀
                    long timestamp = System.currentTimeMillis() % 10000; // 获取毫秒时间戳的后4位
                    String numericSuffix = String.valueOf(timestamp);

                    // 确保编码长度不超过10位
                    int maxSuffixLength = 10 - baseCode.length();
                    if (maxSuffixLength <= 0) {
                        return baseCode; // 如果基础编码已经达到10位，不添加后缀
                    }

                    // 如果后缀太长，截取合适长度
                    if (numericSuffix.length() > maxSuffixLength) {
                        numericSuffix = numericSuffix.substring(0, maxSuffixLength);
                    }

                    // 组合基础编码和数字后缀
                    return baseCode + numericSuffix;
                });
    }

    /**
     * 核心编码生成方法 - 保证唯一性
     *
     * @param businessType 业务类型
     * @param keyPrefix    Redis键前缀
     * @return 唯一编码
     */
    private Mono<String> generateUniqueCode(String businessType, String keyPrefix) {
        // 验证业务类型
        BussinessTypeEnum businessTypeEnum = BussinessTypeEnum.getByBusinessType(businessType);
        if (businessTypeEnum == null) {
            return Mono.error(new IllegalArgumentException("无效的业务类型: " + businessType));
        }

        // 获取业务前缀
        String prefix = getBusinessPrefix(businessTypeEnum);

        // 构建Redis键: 前缀 + 业务类型 + 年月
        String yearMonth = LocalDateTime.now().format(YEAR_MONTH_FORMATTER);
        String redisKey = keyPrefix + businessType + ":" + yearMonth;

        // 自增序列号并生成编码
        return reactiveRedisTemplate.opsForValue()
                .increment(redisKey)
                .map(sequence -> {
                    // 使用增强的编码算法生成编码
                    String code = generateEnhancedCode(sequence);
                    return prefix + code;
                });
    }

    /**
     * 生成增强编码 - 结合序列号和随机元素
     *
     * @param sequence 序列号
     * @return 6位编码
     */
    private String generateEnhancedCode(long sequence) {
        // 初始化安全随机数生成器
        SecureRandom random = new SecureRandom();
        random.setSeed(sequence);

        // 构建6位编码
        StringBuilder code = new StringBuilder(CODE_LENGTH);

        // 编码策略：混合使用确定性映射和伪随机元素

        // 将序列号分解为多个部分
        int part1 = (int) (sequence % RADIX);
        int part2 = (int) ((sequence / RADIX) % RADIX);
        int part3 = (int) ((sequence / (RADIX * RADIX)) % RADIX);

        // 部分1：使用序列号的低位直接映射（确定性）
        code.append(CHARS.charAt(part1));

        // 部分2：使用序列号的中位部分（确定性）
        if (sequence >= RADIX) {
            code.append(CHARS.charAt(part2));
        } else {
            // 序列号较小时，使用基于序列号的伪随机字符
            code.append(CHARS.charAt(random.nextInt(RADIX)));
        }

        // 部分3：使用序列号的高位部分或伪随机字符
        if (sequence >= (long) RADIX * RADIX) {
            code.append(CHARS.charAt(part3));
        } else {
            // 序列号较小时，使用基于序列号的伪随机字符
            code.append(CHARS.charAt((random.nextInt(RADIX) + part1) % RADIX));
        }

        // 部分4-6：使用伪随机字符，但变种依赖于序列号
        for (int i = 3; i < CODE_LENGTH; i++) {
            int randomIndex = (random.nextInt(RADIX) + i * part1) % RADIX;
            code.append(CHARS.charAt(randomIndex));
        }

        return code.toString();
    }

    /**
     * 获取业务类型前缀
     *
     * @param businessTypeEnum 业务类型枚举
     * @return 业务前缀字符
     */
    private String getBusinessPrefix(BussinessTypeEnum businessTypeEnum) {
        return switch (businessTypeEnum) {
            case HOTEL -> "H";
            case BRAND -> "B";
            case MERCHANT -> "M";
            case ROOM -> "R";
            case DEVICE_MODEL -> "DM";
            case DEVICE -> "D";
            case DEVICE_POSITION -> "DP";
            case EMPLOYEE -> "E";
            case INVITATION_CODE -> "";
            case USER_LEAD -> "UL";
            case HOTEL_LEAD -> "HL";
            case DEVICE_AREA_CODE -> "DAC";
            case PAYMENT_PACKAGE -> "PP";
            case HOTEL_PAYMENT_PACKAGE -> "HP";
            default -> "X";
        };
    }

    /**
     * 生成业务ID和编码
     *
     * @param businessType 业务类型
     * @return 包含ID和编码的对象
     */
    public Mono<CodeInfo> generateCodes(String businessType) {
        return Mono.zip(
                generateId(businessType),
                generateCode(businessType)
        ).map(tuple -> new CodeInfo(tuple.getT1(), tuple.getT2()));
    }


    /**
     * 生成订单号 - 格式：时间戳(14位) + 随机数(6位)
     * 示例：20250110143056789123
     *
     * @return 订单号
     */
    public Mono<String> generateOrderNo() {
        return Mono.defer(() -> {
            LocalDateTime now = LocalDateTime.now();
            String timestamp = now.format(ORDER_TIMESTAMP_FORMATTER);
            String randomSuffix = generateRandomSuffix();
            String orderNo = timestamp + randomSuffix;

            String redisKey = ORDER_NO_KEY_PREFIX + orderNo;
            return reactiveRedisTemplate.opsForValue()
                    .setIfAbsent(redisKey, "1", Duration.ofDays(1))
                    .flatMap(success -> {
                        if (Boolean.TRUE.equals(success)) {
                            return Mono.just(orderNo);
                        } else {
                            return generateOrderNo();
                        }
                    });
        });
    }

    private String generateRandomSuffix() {
        int random = ThreadLocalRandom.current().nextInt(1000000);
        return String.format("%06d", random);
    }

    /**
     * 生成发票申请编号 - 格式：INV + 毫秒级时间戳
     * 示例：INV1735228800123
     *
     * @return 发票申请编号
     */
    public Mono<String> generateInvoiceApplicationNo() {
        return Mono.fromCallable(() -> {
            long timestamp = System.currentTimeMillis();
            return "INV" + timestamp;
        });
    }
}