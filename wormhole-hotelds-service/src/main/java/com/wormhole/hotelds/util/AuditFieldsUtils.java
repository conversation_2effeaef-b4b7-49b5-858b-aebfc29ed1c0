package com.wormhole.hotelds.util;

import com.wormhole.common.model.entity.BaseEntity;
import com.wormhole.common.util.HeaderUtils;

import java.time.LocalDateTime;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-25 18:30:00
 * @Description: 审计字段设置工具类
 */
public class AuditFieldsUtils {

    /**
     * 设置实体的审计字段
     */
    public static void setAuditFields(BaseEntity entity, HeaderUtils.HeaderInfo headerInfo, boolean isCreate) {
        entity.setUpdatedBy(UserUtils.getUserId(headerInfo));
        entity.setUpdatedByName(UserUtils.getUserName(headerInfo));
        entity.setUpdatedAt(LocalDateTime.now());
        
        if (isCreate) {
            entity.setCreatedBy(UserUtils.getUserId(headerInfo));
            entity.setCreatedByName(UserUtils.getUserName(headerInfo));
            entity.setCreatedAt(LocalDateTime.now());
            entity.setRowStatus(1);
        }
    }

} 