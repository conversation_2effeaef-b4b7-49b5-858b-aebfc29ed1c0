package com.wormhole.hotelds.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @Author：flx
 * @Date：2025/5/8 15:02
 * @Description：文件下载工具类
 */
public class FileDownloadUtil {
    private static final Logger log = LoggerFactory.getLogger(FileDownloadUtil.class);

    /**
     * 生成符合各种浏览器要求的 Content-Disposition 头
     * @param fileName 文件名
     * @param userAgent
     * @return 适合当前浏览器的 Content-Disposition 值
     */
    public static String getContentDispositionHeader(String fileName, String userAgent) {
        try {
            log.debug("处理文件名: {}, User-Agent: {}", fileName, userAgent);

            // 检测特殊浏览器
            boolean isIE = userAgent != null && (userAgent.contains("MSIE") || userAgent.contains("Trident"));
            boolean isMacChrome = userAgent != null && userAgent.contains("Macintosh") && userAgent.contains("Chrome");
            boolean isFirefox = userAgent != null && userAgent.contains("Firefox");

            // 对于Mac Chrome的特殊处理
            if (isMacChrome) {
                log.debug("检测到Mac Chrome，使用特殊处理方法");
                return getMacChromeContentDisposition(fileName);
            }
            // IE浏览器处理
            else if (isIE) {
                log.debug("检测到IE浏览器，使用IE兼容模式");

                // IE浏览器仅使用URL编码的filename参数
                String urlEncoded = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                        .replaceAll("\\+", "%20");
                return "attachment; filename=\"" + urlEncoded + "\"";
            }
            // Firefox浏览器
            else if (isFirefox) {
                log.debug("检测到Firefox浏览器");

                // Firefox需要同时提供filename和filename*
                String urlEncoded = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                        .replaceAll("\\+", "%20");
                StringBuilder contentDisposition = new StringBuilder("attachment;");
                contentDisposition.append(" filename=\"").append(urlEncoded).append("\"");
                contentDisposition.append("; filename*=UTF-8''").append(urlEncoded);
                return contentDisposition.toString();
            }
            // 其他浏览器的通用处理
            else {
                log.debug("使用通用浏览器处理");

                // 常规处理方式
                String urlEncoded = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                        .replaceAll("\\+", "%20");

                StringBuilder contentDisposition = new StringBuilder("attachment;");
                // 添加基本filename参数
                contentDisposition.append(" filename=\"").append(urlEncoded).append("\"");

                // 添加RFC 5987编码
                contentDisposition.append("; filename*=UTF-8''").append(urlEncoded);

                return contentDisposition.toString();
            }
        } catch (Exception e) {
            log.error("生成Content-Disposition时发生错误", e);
            return "attachment; filename=\"export.xlsx\"";
        }
    }

    /**
     * 为Mac系统的Chrome浏览器特殊处理文件名
     * 对于Mac Chrome，特殊处理Content-Disposition头
     * @param fileName 原始文件名
     * @return 适合Mac Chrome的Content-Disposition值
     */
    public static String getMacChromeContentDisposition(String fileName) {
        try {
            // 尝试多种编码方法，找到Mac Chrome能正确处理的格式

            // 方法1：使用ISO-8859-1编码
            String isoEncoded = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);

            // 方法2：标准URL编码
            String urlEncoded = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                    .replaceAll("\\+", "%20");

            // 构建特殊格式的Content-Disposition
            StringBuilder disposition = new StringBuilder("attachment;");

            // Mac Chrome可能需要未处理的原始文件名
            disposition.append(" filename=\"").append(isoEncoded).append("\"");

            // 同时提供RFC 5987编码格式
            disposition.append("; filename*=UTF-8''").append(urlEncoded);

            log.debug("Mac Chrome特殊Content-Disposition: {}", disposition);
            return disposition.toString();

        } catch (Exception e) {
            log.error("Mac Chrome文件名处理失败", e);
            return "attachment; filename=\"export.xlsx\"";
        }
    }
}