package com.wormhole.hotelds.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.NetUtils;
import com.wormhole.hotelds.admin.aop.LoggingContext;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.admin.repository.OperationLogRepository;
import com.wormhole.hotelds.core.model.entity.HdsOperationLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * @Author：flx
 * @Date：2025/4/7 15:14
 * @Description：OperationLogUtil
 */
@Slf4j
@Component
public class OperationLogUtil {

    @Resource
    private OperationLogRepository operationLogRepository;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 记录成功日志
     *
     * @param context
     * @return
     */
    public Mono<HdsOperationLogEntity> recordSuccess(LoggingContext context) {
        try {
            // 创建日志实体
            HdsOperationLogEntity logEntity = createLogEntity(context);

            // 保存日志
            return saveLogEntity(logEntity);
        } catch (Exception e) {
            log.error("创建成功日志失败: {}", e.getMessage(), e);
            return Mono.empty();
        }
    }

    /**
     * 记录失败日志
     *
     * @param context
     * @return
     */
    public Mono<HdsOperationLogEntity> recordFail(LoggingContext context) {

        try {
            // 创建日志实体
            HdsOperationLogEntity logEntity = createLogEntity(context);

            // 保存日志
            return saveLogEntity(logEntity);
        } catch (Exception e) {
            log.error("创建失败日志失败: {}", e.getMessage(), e);
            return Mono.empty();
        }
    }

    /**
     * 创建日志实体
     */
    private HdsOperationLogEntity createLogEntity(LoggingContext context) {
        // 获取上下文中的关键信息
        HeaderUtils.HeaderInfo headerInfo = context.getHeaderInfo();
        BussinessTypeEnum businessType = context.getBusinessType();
        OperationTypeEnum operationType = context.getOperationType();
        Object beforeObj = context.getBeforeObj();
        Object afterObj = context.getAfterObj();
        LocalDateTime oprTime = context.getOprTime();

        // 从主要对象中获取ID
        Object mainObj = afterObj != null ? afterObj : beforeObj;
        Long objectId = getObjectId(mainObj);

        // 创建并初始化日志实体
        HdsOperationLogEntity logEntity = new HdsOperationLogEntity();

        logEntity.setHotelCode(headerInfo.getHotelCode());

        // 设置基本信息
        logEntity.setEmployeeId(Optional.ofNullable(headerInfo.getUserId())
                .map(Long::valueOf).orElse(null));
        logEntity.setEmployeeName(headerInfo.getUsername());
        logEntity.setOprTime(oprTime);
        logEntity.setCreateTime(LocalDateTime.now());
        logEntity.setOprIp(NetUtils.localIP());
        logEntity.setExecutionTime(context.getExecutionTime());

        // 设置模块和对象信息
        logEntity.setModuleCode(String.valueOf(businessType.getCode()));
        logEntity.setModuleName(businessType.getBusinessTypeDesc());
        logEntity.setObjectId(objectId);
        logEntity.setObjectType(businessType.getBusinessType());
        logEntity.setObjectName(operationType.getDesc() + businessType.getBusinessType());

        // 设置操作信息
        logEntity.setOprType(operationType.getCode());
        logEntity.setOprMethod(context.getMethodName());
        logEntity.setOprContent(context.getOprContent());
        logEntity.setOprResult(context.getOprResult());
        logEntity.setErrorMsg(context.getErrorMsg());

        // 序列化对象
        serializeObjectsToEntity(logEntity, context.getOprParams(), beforeObj, afterObj);

        return logEntity;
    }

    /**
     * 序列化对象到实体中
     */
    private void serializeObjectsToEntity(HdsOperationLogEntity entity, Object params, Object before, Object after) {
        try {
            // 序列化请求参数
            Optional.ofNullable(params)
                    .ifPresent(p -> {
                        try {
                            entity.setOprParams(objectMapper.writeValueAsString(p));
                        } catch (Exception e) {
                            log.warn("序列化请求参数失败: {}", e.getMessage());
                        }
                    });

            // 序列化修改前对象
            Optional.ofNullable(before)
                    .ifPresent(b -> {
                        try {
                            entity.setOprBefore(objectMapper.writeValueAsString(b));
                        } catch (Exception e) {
                            log.warn("序列化修改前对象失败: {}", e.getMessage());
                        }
                    });

            // 序列化修改后对象
            Optional.ofNullable(after)
                    .ifPresent(a -> {
                        try {
                            entity.setOprAfter(objectMapper.writeValueAsString(a));
                        } catch (Exception e) {
                            log.warn("序列化修改后对象失败: {}", e.getMessage());
                        }
                    });
        } catch (Exception e) {
            log.warn("序列化对象失败: {}", e.getMessage());
        }
    }

    /**
     * 保存日志实体
     */
    private Mono<HdsOperationLogEntity> saveLogEntity(HdsOperationLogEntity logEntity) {
        return operationLogRepository.save(logEntity)
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSuccess(saved -> log.debug("操作日志保存成功: id={}", saved.getId()))
                .doOnError(error -> log.error("操作日志保存失败: {}", error.getMessage()));
    }

    /**
     * 获取对象ID，支持多种数值类型
     *
     * @param entity 实体对象
     * @return ID的Long表示，如果无法获取则返回null
     */
    private Long getObjectId(Object entity) {
        if (entity == null) {
            return null;
        }

        try {
            Method method = entity.getClass().getMethod("getId");
            Object result = method.invoke(entity);
            return convertToLong(result);
        } catch (Exception e) {
            log.debug("通过getId方法获取ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将各种类型的ID值转换为Long类型
     *
     * @param value ID值，可能是各种数值类型
     * @return Long类型的ID，如果无法转换则返回null
     */
    private Long convertToLong(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else if (value instanceof Number) {
            // 处理其他Number类型
            return ((Number) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }

        // 不支持的类型
        return null;
    }
}