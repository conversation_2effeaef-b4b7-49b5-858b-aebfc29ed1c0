package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author：AI Assistant
 * @Date：2025/09/02
 * @Description：工单管理分页查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketAdminPageReq extends QueryCondition {

    /**
     * 门店编码
     */
    private String hotelCode;

    /**
     * 工单号
     */
    private String ticketNo;

    /**
     * 工单类型
     */
    private String type;

    /**
     * 工单状态
     */
    private String status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;
}
