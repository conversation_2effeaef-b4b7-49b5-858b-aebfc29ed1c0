package com.wormhole.hotelds.api.hotel.client;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.req.TicketInfoReq;
import com.wormhole.hotelds.api.hotel.resp.TicketAdminListResp;
import com.wormhole.hotelds.api.hotel.resp.TicketDetailResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;

/**
 * @Author：AI Assistant
 * @Date：2025/09/02
 * @Description：酒店DS API客户端实现类
 * 注意：这是一个临时实现，实际项目中应该通过HTTP客户端调用真实的API
 */
@Slf4j
@Component
public class HotelDsApiClientImpl implements HotelDsApiClient {

    @Override
    public Mono<Result<PageResult<TicketAdminListResp>>> getTicketPageFeign(TicketAdminPageReq req) {
        log.warn("HotelDsApiClientImpl.getTicketPageFeign - 这是一个临时实现，请替换为真实的API调用");
        
        // 临时返回空结果，实际实现中应该调用真实的API
        PageResult<TicketAdminListResp> pageResult = new PageResult<>();
        pageResult.setRecords(Collections.emptyList());
        pageResult.setTotal(0L);
        pageResult.setCurrent(req.getCurrent() != null ? req.getCurrent() : 1);
        pageResult.setSize(req.getPageSize() != null ? req.getPageSize() : 10);
        
        return Result.success(pageResult);
    }

    @Override
    public Mono<Result<TicketDetailResp>> getTicketDetailFeign(TicketInfoReq req) {
        log.warn("HotelDsApiClientImpl.getTicketDetailFeign - 这是一个临时实现，请替换为真实的API调用");
        
        // 临时返回空结果，实际实现中应该调用真实的API
        TicketDetailResp resp = new TicketDetailResp();
        return Result.success(resp);
    }
}
