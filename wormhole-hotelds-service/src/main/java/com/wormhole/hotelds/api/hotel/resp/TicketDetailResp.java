package com.wormhole.hotelds.api.hotel.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：AI Assistant
 * @Date：2025/09/02
 * @Description：工单详情响应
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketDetailResp {

    /**
     * 工单ID
     */
    private Long id;

    /**
     * 工单号
     */
    private String ticketNo;

    /**
     * 门店编码
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 类型
     */
    private String type;

    /**
     * 位置名称
     */
    private String positionName;

    /**
     * 客人请求
     */
    private String guestRequest;

    /**
     * 对话类型
     */
    private String conversationType;

    /**
     * 处理方式
     */
    private String handleMethod;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 处理时长
     */
    private String handleDuration;

    /**
     * 聊天记录详情
     */
    private String chatRecordDetail;
}
