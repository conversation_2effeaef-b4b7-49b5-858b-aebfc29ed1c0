package com.wormhole.hotelds.api.hotel.client;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.req.TicketInfoReq;
import com.wormhole.hotelds.api.hotel.resp.TicketAdminListResp;
import com.wormhole.hotelds.api.hotel.resp.TicketDetailResp;
import reactor.core.publisher.Mono;

/**
 * @Author：AI Assistant
 * @Date：2025/09/02
 * @Description：酒店DS API客户端接口
 */
public interface HotelDsApiClient {

    /**
     * 获取工单分页列表
     *
     * @param req 分页查询请求
     * @return 工单列表分页结果
     */
    Mono<Result<PageResult<TicketAdminListResp>>> getTicketPageFeign(TicketAdminPageReq req);

    /**
     * 获取工单详情
     *
     * @param req 工单信息请求
     * @return 工单详情
     */
    Mono<Result<TicketDetailResp>> getTicketDetailFeign(TicketInfoReq req);
}
