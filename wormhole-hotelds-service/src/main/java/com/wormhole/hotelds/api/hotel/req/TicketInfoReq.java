package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：AI Assistant
 * @Date：2025/09/02
 * @Description：工单信息请求
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketInfoReq {

    /**
     * 工单ID
     */
    private Long ticketId;

    /**
     * 工单号
     */
    private String ticketNo;
}
