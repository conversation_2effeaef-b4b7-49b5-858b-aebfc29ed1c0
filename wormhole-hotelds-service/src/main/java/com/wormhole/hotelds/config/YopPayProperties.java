package com.wormhole.hotelds.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author：flx
 * @Date：2025/6/19 16:47
 * @Description：YeePayProperties
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wormhole.yop.pay")
public class YopPayProperties {

    private String parentMerchantNo;

    private String merchantNo;

    /**
     * 支付回调地址
     */
    private String notifyUrl;

    /**
     * 支付成功后跳转地址
     */
    private String returnUrl;

    /**
     * 测试用收银台地址
     */
    private String mockCashierUrl;
}
