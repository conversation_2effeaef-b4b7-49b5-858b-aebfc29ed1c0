package com.wormhole.hotelds.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 普通二维码海报配置
 * @Author: wj
 * @Date: 2025/6/13 14:23
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "poster")
public class NormalPosterProperties {
    /**
     * 海报模板路径
     */
    private String templatePath = "poster_template.png";

    /**
     * 二维码X坐标
     */
    private int qrcodeX;

    /**
     * 二维码Y坐标
     */
    private int qrcodeY;

    /**
     * 房间号Y坐标
     */
    private int roomNumY;

    /**
     * 酒店名称Y坐标
     */
    private int hotelNameY;

    /**
     * 域名
     */
    private String qrRedirectUrl;

    /**
     * 微信海报模板路径
     */
    private String wxTemplatePath = "wx_poster_template.png";

    /**
     * 小程序码Y坐标
     */
    private int wxQrcodeY;
}