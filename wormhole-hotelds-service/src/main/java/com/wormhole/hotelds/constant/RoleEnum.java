package com.wormhole.hotelds.constant;

import com.wormhole.hotelds.admin.model.enums.*;
import lombok.*;

import java.util.*;
import java.util.stream.*;

@Getter
@AllArgsConstructor
public enum RoleEnum {

    ADMIN("ADMIN", "管理员"),
    SERVICE_PROVIDER("SERVICE_PROVIDER", "服务商"),
    HOTEL_ADMIN("HOTEL_ADMIN", "Ai客服管理员"),
    HOTEL_STAFF("HOTEL_STAFF", "Ai客服员工"),

    OTA_AGENT_ADMIN("OTA_AGENT_ADMIN", "OTA智能体管理员"),
    OTA_AGENT_STAFF("OTA_AGENT_STAFF", "OTA智能体员工");

    private final String code;
    private final String name;

    private static final Map<String, RoleEnum> CODE_MAP = Stream.of(values())
            .collect(Collectors.toMap(RoleEnum::getCode, e -> e));

    private static final Map<String, RoleEnum> NAME_MAP = Stream.of(values())
            .collect(Collectors.toMap(RoleEnum::getName, e -> e));

    public static RoleEnum fromCode(String code) {
        return CODE_MAP.getOrDefault(code, null);
    }

    public static String getCodeByName(String name) {
        RoleEnum role = NAME_MAP.get(name);
        return role != null ? role.getCode() : null;
    }

    public static final Map<String, List<String>> PRODUCT_ROLE_REMOVE_MAP = Map.of(
            AiProductTypeEnum.OTA_AGENT.getCode(), List.of(RoleEnum.OTA_AGENT_ADMIN.getCode(), RoleEnum.OTA_AGENT_STAFF.getCode()),
            AiProductTypeEnum.AI_CUSTOMER.getCode(), List.of(RoleEnum.HOTEL_ADMIN.getCode(), RoleEnum.HOTEL_STAFF.getCode())
    );
}
