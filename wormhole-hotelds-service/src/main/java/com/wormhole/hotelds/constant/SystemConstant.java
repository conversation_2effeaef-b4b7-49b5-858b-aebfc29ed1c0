package com.wormhole.hotelds.constant;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;

import java.util.regex.Pattern;

public class SystemConstant {


    public static final int DEFAULT_ONLINE_VERSION_TAG = 1;
    public static final int DEFAULT_OFFLINE_VERSION_TAG = 0;

    public static final int DEFAULT_PAGE_NUMBER = 1;

    public static final int DEFAULT_PAGE_SIZE = 20;

    public static final int PAGE_SIZE_30 = 30;

    public static final int PAGE_SIZE_50 = 50;

    public static final int PAGE_SIZE_200 = 200;
    public static final int PAGE_SIZE_500 = 500;
    public static final int PAGE_SIZE_1000 = 1000;

    public static final String TAR_GZ_EXTENSION = "tar.gz";

    public static final String MIDLINE = "-";
    public static final String UNDERLINE = "_";
    public static final String DEFAULT_ACCOUNT_NAME = "delonix";
    public static final String DEFAULT_ACCOUNT = "99999";
    /**
     * 逗号切分
     */
    public static final Splitter COMMA_SPLITTER = Splitter.on(",").trimResults().omitEmptyStrings();
    /**
     * 冒号切分
     */
    public static final Splitter COLON_SPLITTER = Splitter.on(":").trimResults().omitEmptyStrings();
    /**
     * 分号切分
     */
    public static final Splitter SEMICOLON_SPLITTER = Splitter.on(";").trimResults().omitEmptyStrings();
    /**
     * 竖线
     */
    public static final Splitter VERTICAL_BAR_SPLITTER = Splitter.on("|").trimResults().omitEmptyStrings();
    /**
     * $切分
     */
    public static final Splitter DOLLAR_SPLITTER = Splitter.on("$").trimResults().omitEmptyStrings();
    /**
     * #切分
     */
    public static final Splitter SHARP_SPLITTER = Splitter.on("#").trimResults().omitEmptyStrings();
    /**
     * 换行切分
     */
    public static final Splitter NEW_LINE_SPLITTER = Splitter.on(Pattern.compile("\r|\n")).trimResults().omitEmptyStrings();
    /**
     * 逗号连接
     */
    public static final Joiner COMMA_JOINER = Joiner.on(",");
    /**
     * 空格连接
     */
    public static final Joiner SPACE_JOINER = Joiner.on(" ");
    /**
     * 空连接
     */
    public static final Joiner BLANK_JOINER = Joiner.on("");
    /**
     * 下划线连接
     */
    public static final Joiner UNDERLINE_JOINER = Joiner.on("_");
    /**
     * 中线连接
     */
    public static final Joiner MIDLINE_JOINER = Joiner.on(MIDLINE);

    public static final String META_DATA_DIR = "META-INF/com/android/metadata";

    public static final String POST_BUILD = "post-build";

    public static final String META_DATA_FILE = "metadata";
    public static final String ANDROID_ROM_EXTENSION = "zip";

    public static final String PRODUCT_NAME = "product_name";

    public static final String BUILD_NUMBER = "2700VER";
    public static final String BUILD_LANGUAGE = "language";
    public static final String SYNC_DEV_SIM_STATUS_FROM_PRO_LOCK = "SYNC:DEV:SIM:STATUS:FROM:PRO:LOCK";

    public static final String INDEX_TIMESTAMP = "@timestamp";

    public static final String TAB_KEY = "\t";


    public static final Pattern COMMON_NAME_PATTERN = Pattern.compile("^[a-z][\\w-]*[a-z0-9]+$");

    /**
     * 订单超时事件主题
     */
    public static final String ORDER_OVERDUE_EVENT_TOPIC = "hotelds_order_overdue_event_topic";

    /**
     * 订单超时消费者组
     */
    public static final String ORDER_OVERDUE_CONSUMER_GROUP = "hotelds_order_overdue_consumer_group";

    /**
     * 支付状态检查事件主题
     */
    public static final String ORDER_PAYMENT_CHECK_TOPIC = "hotelds_order_payment_check_topic";

    /**
     * 支付状态检查消费者组
     */
    public static final String ORDER_PAYMENT_CHECK_CONSUMER_GROUP = "hotelds_order_payment_check_consumer_group";
}
