package com.wormhole.hotelds.storage;

import com.qcloud.cos.utils.Jackson;
import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Policy;
import com.tencent.cloud.Response;
import com.tencent.cloud.Statement;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.IdUtils;
import com.wormhole.hotelds.admin.model.req.CosTemporaryCredentialReq;
import com.wormhole.hotelds.admin.model.resp.CosTemporaryCredentialResp;
import com.wormhole.hotelds.storage.config.BucketProperties;
import com.wormhole.hotelds.storage.file.FileInput;
import com.wormhole.hotelds.storage.model.FileUploadResultDTO;
import com.wormhole.hotelds.storage.model.UploadContext;
import com.wormhole.hotelds.storage.util.StorageUtils;
import com.wormhole.storage.autoconfigure.ObjectStorageProperties;
import com.wormhole.storage.model.EndpointTypeEnum;
import com.wormhole.storage.model.StorageParams;
import com.wormhole.storage.model.StorageType;
import com.wormhole.storage.model.TemporaryCredentials;
import com.wormhole.storage.service.CosObjectStorageService;
import com.wormhole.storage.service.OssObjectStorageService;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.FileSystemUtils;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;

/**
 * FileService
 * <p>
 * path_format : wormhole/{project}/{category}/{yyyy}/{MM}/{dd}/{filename}
 *
 * <AUTHOR>
 * @version 2024/11/4
 */
@Service
@Slf4j
public class FileService {

    private static final int DAYS = 365 * 50;

    @Resource
    private OssObjectStorageService ossObjectStorageService;

    @Resource
    private CosObjectStorageService cosObjectStorageService;

    @Resource
    private BucketProperties bucketProperties;

    @Resource
    private ObjectStorageProperties objectStorageProperties;

    public Mono<FileUploadResultDTO> upload(String basePath, FileInput fileInput) {
        UploadContext uploadContext = UploadContext.builder().basePath(generateFilePath(basePath)).build();
        return setUploadContext(uploadContext, fileInput)
                .flatMap(context -> fileInput.transferToPath(context.getTempFilePath()).thenReturn(context))
                .flatMap(this::uploadToObjectStorage)
                .map(this::buildUploadResult)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.FILE_ERROR)))
                .doFinally(signal -> cleanupTempFile(uploadContext.getTempFilePath()));
    }

    public Mono<UploadContext> setUploadContext(UploadContext uploadContext, FileInput fileInput) {
        return Mono.fromCallable(() -> {
                    if (Objects.isNull(fileInput) || Objects.isNull(uploadContext)) {
                        throw new BusinessException(ResultCode.FILE_ERROR);
                    }

                    String encodeFileName;
                    try {
                        encodeFileName = URLEncoder.encode(fileInput.getFileName(), StandardCharsets.UTF_8);
                    } catch (Exception e) {
                        throw new BusinessException(e);
                    }

                    uploadContext.setFileName(fileInput.getFileName());
                    uploadContext.setEncodeFileName(encodeFileName);

                    String basePath = uploadContext.getBasePath();
                    String objectKey = StorageUtils.getObjectKey(basePath, uploadContext.getFileName());
                    String timestampUuid = IdUtils.generateDateTimePrefixId();

                    Path directory = Paths.get(basePath);
                    if (!Files.exists(directory)) {
                        Files.createDirectories(directory);
                    }

                    String prefix = basePath.substring(basePath.lastIndexOf("/") + 1);
                    Path tempFilePath = Files.createTempFile(directory, prefix, timestampUuid);

                    uploadContext.setObjectKey(objectKey);
                    uploadContext.setTempFilePath(tempFilePath);
                    uploadContext.setTimestampUuid(timestampUuid);
                    return uploadContext;

                }).subscribeOn(Schedulers.boundedElastic())
                .onErrorResume(e -> {
                    log.error("Error creating upload context", e);
                    return Mono.error(new BusinessException(ResultCode.FILE_ERROR, e.getMessage()));
                });
    }

    // 上传到对象存储
    private Mono<UploadContext> uploadToObjectStorage(UploadContext context) {
        log.info("Start uploading to object storage, objectKey: {}", context.getObjectKey());
        StorageParams storageParams = StorageParams.builder().bucketName(bucketProperties.getCommonBucketName()).objectKey(context.getObjectKey()).build();
        return cosObjectStorageService.putObject(storageParams, context.getTempFilePath().toFile(), context.getEncodeFileName())
                .doOnSuccess(result -> {
                    log.info("Upload successful, MD5: {}", result.getMd5());
                })
                .doOnError(t -> log.error("Error during upload to object storage: {}", t.getMessage()))
                .map(result -> {
                    context.setFileSize(result.getContentLength());
                    context.setContentMd5(result.getMd5());
                    return context;
                })
                .onErrorResume(t -> Mono.error(new BusinessException(ResultCode.FILE_ERROR)));
    }

    // 构建上传结果
    private FileUploadResultDTO buildUploadResult(UploadContext context) {
        Duration duration = Duration.ofDays(DAYS);
        String url = cosObjectStorageService.generatePresignedUrl(
                StorageParams.builder().endpointType(EndpointTypeEnum.PUBLIC).bucketName(bucketProperties.getCommonBucketName()).objectKey(context.getObjectKey()).build(),
                duration
        );
        String intranetUrl = cosObjectStorageService.generatePresignedUrl(
                StorageParams.builder().endpointType(EndpointTypeEnum.INTRANET).bucketName(bucketProperties.getCommonBucketName()).objectKey(context.getObjectKey()).build(),
                duration
        );
        return FileUploadResultDTO.builder()
                .uid(context.getTimestampUuid())
                .md5(context.getContentMd5())
                .name(context.getFileName())
                .objectKey(context.getObjectKey())
                .url(url)
                .intranetUrl(intranetUrl)
                .size(context.getFileSize())
                .type(getFileExtension(context.getFileName()))
                .build();
    }


    public static String getFileExtension(String fileName) {
        if (StringUtil.isBlank(fileName)) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        } else {
            return "";
        }
    }

    // 清理临时文件
    private void cleanupTempFile(Path tempFilePath) {
        Mono.fromRunnable(() -> {
                    try {
                        if (Objects.nonNull(tempFilePath)) {
                            FileSystemUtils.deleteRecursively(tempFilePath);
                            log.info("Successfully deleted temp file: {}", tempFilePath);
                        }
                    } catch (Exception e) {
                        log.error("Failed to delete temp file: {}", tempFilePath, e);
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
    }

    public Mono<String> getObjectContent(String bucketName, String objectKey, String charset) {
        return cosObjectStorageService.getObjectAsString(StorageParams.builder().bucketName(bucketName).objectKey(objectKey).build(), charset);
    }

    public Mono<String> getObjectUrl(String bucketName, String objectKey, String charset) {
        return Mono.just(cosObjectStorageService.getObjectUrl(StorageParams.builder().bucketName(bucketName).objectKey(objectKey).build()));
    }

    private String generateFilePath(String basePath) {
        LocalDate today = LocalDate.now();
        String datePath = today.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return String.format("%s/%s", basePath, datePath);
    }

    /**
     * 获取腾讯云COS上传临时密钥
     *
     * @param req 请求参数
     * @return 临时密钥信息
     */
    public Mono<CosTemporaryCredentialResp> getCosTemporaryCredential(CosTemporaryCredentialReq req) {
        return Mono.fromSupplier(() -> Optional.ofNullable(objectStorageProperties.getConfigs())
                        .map(configs -> configs.get(StorageType.COS))
                        // 增加对 isEnabled 的判断
                        .filter(ObjectStorageProperties.StorageConfigs::isEnabled)
                        .map(storageConfigs -> storageConfigs.getBuckets().stream()
                                .findFirst()
                                .orElseThrow(() -> new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "未找到COS存储桶配置")))
                        .orElseThrow(() -> new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "未找到COS存储桶配置")))
                .flatMap(bucketProperties -> cosObjectStorageService.getTemporaryCredentials(bucketProperties.getBucketName()))
                .map(temporaryCredentials -> buildCredentialResponse(temporaryCredentials, req))
                .onErrorResume(e -> {
                    log.error("获取腾讯云COS临时密钥失败", e);
                    return Mono.error(new RuntimeException("获取COS临时密钥失败: " + e.getMessage()));
                });
    }

    /**
     * 构建临时凭证响应对象
     *
     * @param credentials 临时凭证
     * @param req         请求参数
     * @return 响应对象
     */
    private CosTemporaryCredentialResp buildCredentialResponse(TemporaryCredentials credentials, CosTemporaryCredentialReq req) {
        CosTemporaryCredentialResp result = new CosTemporaryCredentialResp();
        result.setTmpSecretId(credentials.getAccessKeyId());
        result.setTmpSecretKey(credentials.getSecretAccessKey());
        result.setSessionToken(credentials.getSessionToken());
        result.setBucket(credentials.getBucketName());
        result.setRegion(credentials.getRegion());
        result.setPath(StorageUtils.getObjectKey(req.getBasePath(), req.getFileName()));
        return result;
    }
}
