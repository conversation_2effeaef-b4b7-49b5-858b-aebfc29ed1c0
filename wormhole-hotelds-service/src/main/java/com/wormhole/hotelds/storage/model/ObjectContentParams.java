package com.wormhole.hotelds.storage.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ObjectContentParams
 *
 * <AUTHOR>
 * @version 2025/1/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ObjectContentParams {

    private String bucketName;

    private String objectKey;

    private String encoding;

}
