package com.wormhole.hotelds.storage.file;

import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

/**
 * FileAdapter
 *
 * <AUTHOR>
 * @version 2025/1/20
 */
public class FileAdapter implements FileInput {
    private final File file;

    public FileAdapter(File file) {
        this.file = file;
    }

    @Override
    public String getFileName() {
        return file.getName();
    }

    @Override
    public Mono<Path> transferToPath(Path destinationPath) {
        return Mono.fromCallable(() -> {
            Files.copy(file.toPath(), destinationPath, StandardCopyOption.REPLACE_EXISTING);
            return destinationPath;
        }).subscribeOn(Schedulers.boundedElastic());
    }
}