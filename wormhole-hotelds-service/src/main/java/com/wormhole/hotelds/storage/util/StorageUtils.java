package com.wormhole.hotelds.storage.util;

import com.google.common.base.Preconditions;
import com.wormhole.common.util.IdUtils;
import com.wormhole.hotelds.constant.SystemConstant;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * StorageUtils
 *
 * <AUTHOR>
 * @version 2024/11/4
 */
public class StorageUtils {

    /**
     * 获取ObjectStorage的key
     *
     * @param basePath
     * @param fileName
     * @return
     */
    public static String getObjectKey(String basePath, String fileName) {
        Preconditions.checkArgument(StringUtils.isNotBlank(basePath), "objectFileType must not be null.");
        Preconditions.checkArgument(StringUtils.length(basePath) <= 32, "objectFileType length must less than 32.");
        String objectName = IdUtils.generateDateTimePrefixId();
        String extension = getExtension(fileName);
        if (StringUtils.isNotBlank(extension)) {
            objectName = objectName + "." + extension;
        }
        return String.format("wormhole/%s/%s", basePath, objectName);
    }

    public static String getExtension(String originalFileName) {
        Preconditions.checkArgument(StringUtils.isNotBlank(originalFileName), "originalFileName must not be blank");
        return StringUtils.endsWith(originalFileName, SystemConstant.TAR_GZ_EXTENSION) ? SystemConstant.TAR_GZ_EXTENSION : FilenameUtils.getExtension(originalFileName);
    }
}
