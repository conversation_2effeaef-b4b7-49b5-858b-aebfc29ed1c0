package com.wormhole.hotelds.storage.model;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;

/**
 * FileUploadResultDTO
 *
 * <AUTHOR>
 * @version 2024/11/4
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileUploadResultDTO {

    /**
     * 文件uid，对应fileId
     * 前端框架要求的字段，字段名不可修改
     */
    private String uid;
    /**
     * 文件md5
     * 后端扩展字段，没要求
     */
    private String md5;
    /**
     * 原始文件名
     * 前端框架要求的字段，字段名不可修改
     */
    private String name;
    /**
     * 上传状态
     * 前端框架要求的字段，字段名不可修改
     */
    private String status = "done";
    /**
     * 公网域名Url
     * 前端框架要求的字段，字段名不可修改
     */
    private String url;
    /**
     * 上传文件转存的oss地址
     * 后端扩展字段，没要求
     */
    private String objectKey;
    /**
     * 私网域名Url
     * 后端扩展字段，没要求
     */
    private String intranetUrl;
    /**
     * 文件大小
     * 后端扩展字段，没要求
     */
    private long size;
    /**
     * 文件类型
     *
     */
    private String type;

}
