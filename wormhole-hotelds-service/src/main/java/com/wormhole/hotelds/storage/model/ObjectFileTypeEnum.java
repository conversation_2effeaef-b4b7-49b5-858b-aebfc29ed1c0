package com.wormhole.hotelds.storage.model;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * OssFileTypeEnum
 *
 * <AUTHOR>
 * @version 2024/11/4
 */
public enum ObjectFileTypeEnum {
    /**
     * img
     */
    img,
    /**
     * avatar
     */
    avatar,
    /**
     * doc
     */
    doc,
    ;

    public static ObjectFileTypeEnum getEnum(String name) {
        return Arrays.stream(ObjectFileTypeEnum.values()).filter(item -> StringUtils.equalsIgnoreCase(item.name(), name)).findFirst().orElse(null);
    }


}
