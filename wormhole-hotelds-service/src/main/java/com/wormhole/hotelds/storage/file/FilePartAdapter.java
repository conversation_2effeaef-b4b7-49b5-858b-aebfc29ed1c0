package com.wormhole.hotelds.storage.file;

import org.springframework.http.codec.multipart.FilePart;
import reactor.core.publisher.Mono;

import java.nio.file.Path;

/**
 * FilePartAdapter
 *
 * <AUTHOR>
 * @version 2025/1/20
 */
public class FilePartAdapter implements FileInput {
    private final FilePart filePart;

    public FilePartAdapter(FilePart filePart) {
        this.filePart = filePart;
    }

    @Override
    public String getFileName() {
        return filePart.filename();
    }

    @Override
    public Mono<Path> transferToPath(Path destinationPath) {
        return filePart.transferTo(destinationPath).thenReturn(destinationPath);
    }
}