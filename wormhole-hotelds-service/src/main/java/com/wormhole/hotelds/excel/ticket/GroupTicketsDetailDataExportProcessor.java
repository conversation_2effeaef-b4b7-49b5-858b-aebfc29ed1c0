package com.wormhole.hotelds.excel.ticket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.resp.TicketAdminListResp;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExcelFormattingProvider;
import com.wormhole.hotelds.excel.ExportContext;
import com.wormhole.hotelds.util.SimpleDateUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

import static com.wormhole.hotelds.admin.constant.ExcelConstant.*;

/**
 * @Author：mzy
 * @Date：2025/09/02
 * @Description：集团工单明细导出处理器
 */
@Slf4j
@Component
public class GroupTicketsDetailDataExportProcessor implements DataExportProcessor<GroupTicketsDetailExportDTO, GroupTicketsDetailExportReq>, ExcelFormattingProvider {

    @Resource
    private HotelDsApiClient hotelDsApiClient;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.GROUP_TICKETS_DETAIL.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return new ArrayList<>(Arrays.asList(
                FIELD_HOTEL_CODE_2,
                FIELD_HOTEL_NAME,
                FIELD_TICKET_NO,
                FIELD_TICKET_TYPE,
                FIELD_TICKET_POSITION_NAME,
                FIELD_TICKET_GUEST_REQUEST,
                FIELD_TICKET_CONVERSATION_TYPE,
                FIELD_TICKET_HANDLE_METHOD,
                FIELD_TICKET_STATUS,
                FIELD_TICKET_CREATOR,
                FIELD_TICKET_HANDLER,
                FIELD_TICKET_CREATE_TIME,
                FIELD_TICKET_HANDLE_TIME,
                FIELD_TICKET_HANDLE_DURATION,
                FIELD_TICKET_CHAT_RECORD
        ));
    }

    @Override
    public Flux<GroupTicketsDetailExportDTO> queryData(GroupTicketsDetailExportReq request, ExportContext context) {
        try {
            // 解析requestJson为TicketAdminPageReq
            TicketAdminPageReq ticketReq = parseRequestJson(request.getRequestJson());
            
            // 设置分页参数为导出模式
            ticketReq.setCurrent(1);
            ticketReq.setPageSize(2000);

            // 调用HotelDsApiClient获取工单数据
            return hotelDsApiClient.getTicketPageFeign(ticketReq)
                    .flatMapMany(result -> {
                        if (!result.getDataList().isEmpty()) {
                            PageResult<TicketAdminListResp> pageResult = result.getData();
                            if (pageResult != null && pageResult.getRecords() != null) {
                                return Flux.fromIterable(pageResult.getRecords())
                                        .map(this::convertToExportDTO);
                            }
                        }
                        return Flux.empty();
                    })
                    .onErrorResume(e -> {
                        log.error("查询集团工单数据失败", e);
                        return Flux.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "查询工单数据失败: " + e.getMessage()));
                    });
        } catch (Exception e) {
            log.error("解析请求参数失败", e);
            return Flux.error(new BusinessException(ResultCode.INVALID_PARAMETER, "请求参数解析失败: " + e.getMessage()));
        }
    }

    private TicketAdminPageReq parseRequestJson(String requestJson) throws Exception {
        if (StringUtils.isBlank(requestJson)) {
            return new TicketAdminPageReq();
        }
        return objectMapper.readValue(requestJson, TicketAdminPageReq.class);
    }

    private GroupTicketsDetailExportDTO convertToExportDTO(TicketAdminListResp resp) {
        GroupTicketsDetailExportDTO dto = new GroupTicketsDetailExportDTO();

        // 映射字段
        dto.setHotelCode(resp.getHotelCode())
           .setHotelName(resp.getHotelName())
           .setTicketNo(resp.getTicketNo())
           .setType(resp.getType())
           .setPositionName(resp.getPositionName())
           .setGuestRequest(resp.getGuestRequest())
           .setConversationType(resp.getConversationType())
           .setHandleMethod(resp.getHandleMethod())
           .setStatus(resp.getStatus())
           .setCreator(resp.getCreator())
           .setHandler(resp.getHandler())
           .setCreateTime(resp.getCreateTime())
           .setHandleTime(resp.getHandleTime())
           .setHandleDuration(resp.getHandleDuration())
           .setChatRecord(""); // 聊天记录暂时空置

        return dto;
    }

    @Override
    public List<String> convertToRow(GroupTicketsDetailExportDTO data) {
        Map<String, Function<GroupTicketsDetailExportDTO, String>> extractors = new HashMap<>();
        
        extractors.put(FIELD_HOTEL_CODE, GroupTicketsDetailExportDTO::getHotelCode);
        extractors.put(FIELD_HOTEL_NAME, GroupTicketsDetailExportDTO::getHotelName);
        extractors.put(FIELD_TICKET_NO, GroupTicketsDetailExportDTO::getTicketNo);
        extractors.put(FIELD_TICKET_TYPE, GroupTicketsDetailExportDTO::getType);
        extractors.put(FIELD_TICKET_POSITION_NAME, GroupTicketsDetailExportDTO::getPositionName);
        extractors.put(FIELD_TICKET_GUEST_REQUEST, GroupTicketsDetailExportDTO::getGuestRequest);
        extractors.put(FIELD_TICKET_CONVERSATION_TYPE, GroupTicketsDetailExportDTO::getConversationType);
        extractors.put(FIELD_TICKET_HANDLE_METHOD, GroupTicketsDetailExportDTO::getHandleMethod);
        extractors.put(FIELD_TICKET_STATUS, GroupTicketsDetailExportDTO::getStatus);
        extractors.put(FIELD_TICKET_CREATOR, GroupTicketsDetailExportDTO::getCreator);
        extractors.put(FIELD_TICKET_HANDLER, GroupTicketsDetailExportDTO::getHandler);
        extractors.put(FIELD_TICKET_CREATE_TIME, dto -> SimpleDateUtils.formatDateTime(dto.getCreateTime()));
        extractors.put(FIELD_TICKET_HANDLE_TIME, dto -> SimpleDateUtils.formatDateTime(dto.getHandleTime()));
        extractors.put(FIELD_TICKET_HANDLE_DURATION, GroupTicketsDetailExportDTO::getHandleDuration);
        extractors.put(FIELD_TICKET_CHAT_RECORD, GroupTicketsDetailExportDTO::getChatRecord);

        // 按照表头顺序提取数据
        List<String> row = new ArrayList<>();
        for (String header : getExcelHeaders()) {
            Function<GroupTicketsDetailExportDTO, String> extractor = extractors.get(header);
            if (extractor != null) {
                String value = extractor.apply(data);
                row.add(value != null ? value : "");
            } else {
                row.add("");
            }
        }

        return row;
    }

    @Override
    public String getExportFileName(GroupTicketsDetailExportReq request) {
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now());
        return "集团工单明细_" + dateStr + ".xlsx";
    }

    @Override
    public Class<GroupTicketsDetailExportReq> getRequestClass() {
        return GroupTicketsDetailExportReq.class;
    }

    @Override
    public Map<String, Integer> getColumnWidthMap() {
        Map<String, Integer> widthMap = new HashMap<>();
        widthMap.put(FIELD_HOTEL_CODE, 15);
        widthMap.put(FIELD_HOTEL_NAME, 20);
        widthMap.put(FIELD_TICKET_NO, 15);
        widthMap.put(FIELD_TICKET_TYPE, 12);
        widthMap.put(FIELD_TICKET_POSITION_NAME, 15);
        widthMap.put(FIELD_TICKET_GUEST_REQUEST, 30);
        widthMap.put(FIELD_TICKET_CONVERSATION_TYPE, 12);
        widthMap.put(FIELD_TICKET_HANDLE_METHOD, 12);
        widthMap.put(FIELD_TICKET_STATUS, 10);
        widthMap.put(FIELD_TICKET_CREATOR, 12);
        widthMap.put(FIELD_TICKET_HANDLER, 12);
        widthMap.put(FIELD_TICKET_CREATE_TIME, 18);
        widthMap.put(FIELD_TICKET_HANDLE_TIME, 18);
        widthMap.put(FIELD_TICKET_HANDLE_DURATION, 12);
        widthMap.put(FIELD_TICKET_CHAT_RECORD, 25);
        return widthMap;
    }

    @Override
    public Map<String, String> getColumnFormatMap() {
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put(FIELD_TICKET_CREATE_TIME, "date");
        formatMap.put(FIELD_TICKET_HANDLE_TIME, "date");
        return formatMap;
    }
}
