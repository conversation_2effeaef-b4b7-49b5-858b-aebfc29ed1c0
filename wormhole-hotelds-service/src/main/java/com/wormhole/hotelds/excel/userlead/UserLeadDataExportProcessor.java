package com.wormhole.hotelds.excel.userlead;

import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.ExportTypeEnum;
import com.wormhole.hotelds.admin.repository.HdsEmployeeRepository;
import com.wormhole.hotelds.admin.repository.HotelLeadRepository;
import com.wormhole.hotelds.admin.repository.UserHotelLeadMappingRepository;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExcelFormattingProvider;
import com.wormhole.hotelds.excel.ExportContext;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wormhole.hotelds.admin.constant.ExcelConstant.*;

/**
 * @Author：flx
 * @Date：2025/5/28 09:41
 * @Description：UserLeadDataExportProcessor
 */
@Slf4j
@Component
public class UserLeadDataExportProcessor implements DataExportProcessor<UserLeadExportDTO, UserLeadExportReq>, ExcelFormattingProvider {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;

    @Resource
    private UserHotelLeadMappingRepository userHotelLeadMappingRepository;

    @Resource
    private HotelLeadRepository hotelLeadRepository;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.USER_LEAD.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return new ArrayList<>(Arrays.asList(
                FIELD_USER_LEAD_ID,
                FIELD_USER_LEAD_CODE,
                FIELD_USER_LEAD_MOBILE,
                FIELD_USER_LEAD_STATUS,
                FIELD_USER_LEAD_EBK_COUNT,
                FIELD_USER_LEAD_HOTEL_COUNT,
                FIELD_USER_LEAD_INVITED_BY,
                FIELD_USER_LEAD_INVITE_CODE,
                FIELD_USER_LEAD_REGISTERED_AT
        ));
    }

    @Override
    public Flux<UserLeadExportDTO> queryData(UserLeadExportReq request, ExportContext context) {
        // 构建查询条件
        Criteria criteria = buildQueryCriteria(request);

        // 执行查询
        return r2dbcEntityTemplate.select(Query.query(criteria)
                        .sort(Sort.by(Sort.Direction.DESC, HdsUserLeadFieldEnum.created_at.name())), HdsUserLeadEntity.class)
                .collectList()
                .flatMapMany(userLeadList -> {
                    if (userLeadList.isEmpty()) {
                        return Flux.empty();
                    }

                    // 提取相关ID
                    Set<Long> userLeadIds = userLeadList.stream()
                            .map(HdsUserLeadEntity::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());

                    Set<Integer> employeeIds = new HashSet<>();
                    // 添加邀请人ID
                    userLeadList.stream()
                            .map(HdsUserLeadEntity::getInviterId)
                            .filter(Objects::nonNull)
                            .forEach(employeeIds::add);

                    // 添加被邀请人ID
                    userLeadList.stream()
                            .map(HdsUserLeadEntity::getInviteeId)
                            .filter(Objects::nonNull)
                            .forEach(employeeIds::add);

                    // 批量查询员工信息
                    Mono<Map<Integer, HdsEmployeeEntity>> employeeMapMono = employeeIds.isEmpty()
                            ? Mono.just(Collections.emptyMap())
                            : hdsEmployeeRepository.findByIdIn(employeeIds)
                            .collectList()
                            .map(employees -> employees.stream()
                                    .filter(employee -> employee.getId() != null) // 过滤null键
                                    .collect(Collectors.toMap(
                                            HdsEmployeeEntity::getId,
                                            employee -> employee,
                                            (e1, e2) -> e1
                                    )))
                            .defaultIfEmpty(Collections.emptyMap());

                    // 批量查询用户-门店线索映射关系
                    Mono<Map<Long, Set<Long>>> userHotelLeadMapMono = userLeadIds.isEmpty()
                            ? Mono.just(Collections.emptyMap())
                            : userHotelLeadMappingRepository.findByUserLeadIdIn(userLeadIds)
                            .collectList()
                            .flatMap(mappings -> {
                                // 获取所有门店线索ID
                                Set<Long> hotelLeadIds = mappings.stream()
                                        .map(HdsUserHotelLeadMappingEntity::getHotelLeadId)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toSet());

                                if (hotelLeadIds.isEmpty()) {
                                    return Mono.just(Collections.<Long, Set<Long>>emptyMap());
                                }

                                // 查询存在的门店线索
                                return hotelLeadRepository.findAllByIdIn(hotelLeadIds)
                                        .map(HdsHotelLeadEntity::getId)
                                        .collectList()
                                        .map(existingHotelLeadIds -> {
                                            Set<Long> validHotelLeadIds = new HashSet<>(existingHotelLeadIds);
                                            return createUserHotelLeadMap(mappings, validHotelLeadIds);
                                        });
                            })
                            .defaultIfEmpty(Collections.emptyMap())
                            .onErrorResume(e -> {
                                log.error("获取用户-门店线索映射关系失败", e);
                                return Mono.just(Collections.emptyMap());
                            });

                    // 合并获取门店线索信息
                    return userHotelLeadMapMono.flatMap(userHotelLeadMap -> {
                                // 提取所有门店线索ID
                                Set<Long> hotelLeadIds = userHotelLeadMap.values().stream()
                                        .flatMap(Set::stream)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toSet());

                                Mono<Map<Long, HdsHotelLeadEntity>> hotelLeadMapMono = hotelLeadIds.isEmpty()
                                        ? Mono.just(Collections.emptyMap())
                                        : hotelLeadRepository.findByIdIn(hotelLeadIds)
                                        .collectList()
                                        .map(hotelLeads -> hotelLeads.stream()
                                                .filter(hotelLead -> hotelLead.getId() != null) // 过滤null键
                                                .collect(Collectors.toMap(
                                                        HdsHotelLeadEntity::getId,
                                                        hotelLead -> hotelLead,
                                                        (e1, e2) -> e1
                                                )))
                                        .defaultIfEmpty(Collections.emptyMap());

                                return Mono.zip(employeeMapMono, hotelLeadMapMono)
                                        .map(tuple -> {
                                            Map<Integer, HdsEmployeeEntity> employeeMap = tuple.getT1();
                                            Map<Long, HdsHotelLeadEntity> hotelLeadMap = tuple.getT2();

                                            // 构建导出DTO
                                            List<UserLeadExportDTO> resultList = new ArrayList<>();
                                            for (HdsUserLeadEntity entity : userLeadList) {
                                                UserLeadExportDTO dto = buildUserLeadExportDTO(
                                                        entity,
                                                        employeeMap,
                                                        userHotelLeadMap,
                                                        hotelLeadMap
                                                );
                                                resultList.add(dto);
                                            }
                                            return resultList;
                                        });
                            })
                            .flatMapMany(Flux::fromIterable);
                });
    }

    /**
     * 创建用户-门店线索映射
     */
    private Map<Long, Set<Long>> createUserHotelLeadMap(List<HdsUserHotelLeadMappingEntity> mappings, Set<Long> validHotelLeadIds) {
        Map<Long, Set<Long>> userHotelLeadMap = new HashMap<>();

        for (HdsUserHotelLeadMappingEntity mapping : mappings) {
            if (mapping.getUserLeadId() != null && mapping.getHotelLeadId() != null
                    && validHotelLeadIds.contains(mapping.getHotelLeadId())) {
                userHotelLeadMap
                        .computeIfAbsent(mapping.getUserLeadId(), k -> new HashSet<>())
                        .add(mapping.getHotelLeadId());
            }
        }

        return userHotelLeadMap;
    }

    /**
     * 构建用户线索导出DTO
     */
    private UserLeadExportDTO buildUserLeadExportDTO(
            HdsUserLeadEntity entity,
            Map<Integer, HdsEmployeeEntity> employeeMap,
            Map<Long, Set<Long>> userHotelLeadMap,
            Map<Long, HdsHotelLeadEntity> hotelLeadMap) {

        UserLeadExportDTO dto = new UserLeadExportDTO();
        dto.setId(entity.getId());
        dto.setLeadCode(entity.getLeadCode());
        dto.setInviteCode(entity.getInviteCode());
        dto.setRegisteredAt(SimpleDateUtils.formatLocalDateTimeToDateHour(entity.getCreatedAt()));

        // 设置被邀请人手机号
        if (entity.getInviteeId() != null) {
            HdsEmployeeEntity invitee = employeeMap.get(entity.getInviteeId());
            if (invitee != null) {
                dto.setMobile(invitee.getMobile());
            }
        }

        // 设置邀请人信息
        if (entity.getInviterId() != null) {
            HdsEmployeeEntity inviter = employeeMap.get(entity.getInviterId());
            if (inviter != null) {
                dto.setInvitedBy(getInvitedBy(inviter));
            }
        }

        // 设置EBK数和门店数
        Set<Long> relatedHotelLeadIds = userHotelLeadMap.getOrDefault(entity.getId(), Collections.emptySet());

        // 统计获取EBK数 - 关联的门店线索数量
        int ebkCount = relatedHotelLeadIds.size();
        dto.setEbkCount(ebkCount);

        // 统计创建门店数 - 关联的已完成门店线索数量
        int completedHotelCount = (int) relatedHotelLeadIds.stream()
                .filter(hotelLeadId -> {
                    HdsHotelLeadEntity hotelLead = hotelLeadMap.get(hotelLeadId);
                    return hotelLead != null && hotelLead.getCompleteTime() != null;
                })
                .count();
        dto.setHotelCount(completedHotelCount);

        // 设置状态
        dto.setStatus(ebkCount > 0 ? "有效" : "无效");

        return dto;
    }

    /**
     * 根据员工信息生成邀请人字符串
     */
    private String getInvitedBy(HdsEmployeeEntity employee) {
        if (employee == null) {
            return "";
        }

        // 获取姓名，确保不为空
        String employeeName = employee.getName();

        // 按优先级获取账号
        String account = null;

        if (StringUtils.isNotBlank(employee.getMobile())) {
            // 优先使用手机号
            account = employee.getMobile();
        } else if (StringUtils.isNotBlank(employee.getUsername())) {
            // 其次使用自定义账号
            account = employee.getUsername();
        } else if (StringUtils.isNotBlank(employee.getEmail())) {
            // 最后使用邮箱
            account = employee.getEmail();
        }
        // 组合姓名和账号
        return employeeName + "/" + account;
    }

    /**
     * 根据导出类型和请求参数构建查询条件
     */
    private Criteria buildQueryCriteria(UserLeadExportReq request) {
        // 根据导出类型选择不同的查询策略
        ExportTypeEnum exportType = ExportTypeEnum.getByCode(request.getExportType());

        return switch (exportType) {
            case ALL ->
                // 全部导出 - 使用空条件
                    Criteria.empty();
            case CURRENT -> {
                // 当前页导出 - 根据IDs列表
                if (CollectionUtils.isEmpty(request.getCurrentIds())) {
                    // 如果ID列表为空，返回一个永远为false的条件，确保不会有数据
                    yield Criteria.where(HdsUserLeadFieldEnum.id.name()).is(-1);
                }
                yield Criteria.where(HdsUserLeadFieldEnum.id.name()).in(request.getCurrentIds());
            }
            default ->
                // 筛选导出 - 使用过滤条件
                    buildFilterCriteria(request);
        };
    }

    /**
     * 构建筛选查询条件
     */
    private Criteria buildFilterCriteria(UserLeadExportReq request) {
        Criteria criteria = Criteria.empty();

        // 状态筛选
        if (Objects.nonNull(request.getStatus())) {
            criteria = criteria.and(HdsUserLeadFieldEnum.status.name()).is(request.getStatus());
        }

        // 手机号筛选（通过员工表关联）
//        if (StringUtils.isNotBlank(request.getMobile())) {
//            // 注意：这里假设手机号搜索已在外部预处理，将转换为对应的employee IDs
//            // 在实际场景中，可能需要先查询员工表获取ID列表
//            criteria = criteria.and(HdsUserLeadFieldEnum.invitee_id.name()).in(request.getEmployeeIds());
//        }


        return criteria;
    }

    /**
     * 将数据转成excel行
     */
    @Override
    public List<String> convertToRow(UserLeadExportDTO data) {
        // 定义表头到数据提取器的映射
        Map<String, Function<UserLeadExportDTO, String>> extractors = new LinkedHashMap<>();
        extractors.put(FIELD_USER_LEAD_ID, dto -> dto.getId() != null ? String.valueOf(dto.getId()) : "");
        extractors.put(FIELD_USER_LEAD_CODE, UserLeadExportDTO::getLeadCode);
        extractors.put(FIELD_USER_LEAD_MOBILE, UserLeadExportDTO::getMobile);
        extractors.put(FIELD_USER_LEAD_STATUS, UserLeadExportDTO::getStatus);
        extractors.put(FIELD_USER_LEAD_EBK_COUNT, dto -> dto.getEbkCount() != null ? String.valueOf(dto.getEbkCount()) : "0");
        extractors.put(FIELD_USER_LEAD_HOTEL_COUNT, dto -> dto.getHotelCount() != null ? String.valueOf(dto.getHotelCount()) : "0");
        extractors.put(FIELD_USER_LEAD_INVITED_BY, UserLeadExportDTO::getInvitedBy);
        extractors.put(FIELD_USER_LEAD_INVITE_CODE, UserLeadExportDTO::getInviteCode);
        extractors.put(FIELD_USER_LEAD_REGISTERED_AT, UserLeadExportDTO::getRegisteredAt);

        // 按照表头顺序提取数据
        List<String> row = new ArrayList<>();
        for (String header : getExcelHeaders()) {
            Function<UserLeadExportDTO, String> extractor = extractors.get(header);
            if (extractor != null) {
                String value = extractor.apply(data);
                row.add(value != null ? value : "");
            } else {
                // 未知表头，添加空值
                row.add("");
            }
        }

        return row;
    }

    /**
     * 获取导出文件名
     */
    @Override
    public String getExportFileName(UserLeadExportReq request) {
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now());
        return "用户线索列表_" + dateStr + ".xlsx";
    }

    /**
     * 获取请求类
     */
    @Override
    public Class<UserLeadExportReq> getRequestClass() {
        return UserLeadExportReq.class;
    }

    @Override
    public Map<String, Integer> getColumnWidthMap() {
        Map<String, Integer> widthMap = new HashMap<>();
        widthMap.put(FIELD_USER_LEAD_CODE, 15);
        widthMap.put(FIELD_USER_LEAD_MOBILE, 15);
        widthMap.put(FIELD_USER_LEAD_STATUS, 15);
        widthMap.put(FIELD_USER_LEAD_EBK_COUNT, 15);
        widthMap.put(FIELD_USER_LEAD_HOTEL_COUNT, 15);
        widthMap.put(FIELD_USER_LEAD_INVITED_BY, 20);
        widthMap.put(FIELD_USER_LEAD_INVITE_CODE, 15);
        widthMap.put(FIELD_USER_LEAD_REGISTERED_AT, 20);
        return widthMap;
    }

    @Override
    public Map<String, String> getColumnFormatMap() {
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put(FIELD_USER_LEAD_REGISTERED_AT, "datetime");
        return formatMap;
    }

    @Override
    public Set<String> getHiddenColumns() {
        Set<String> hiddenColumns = new HashSet<>();
        hiddenColumns.add(FIELD_USER_LEAD_ID); // 隐藏ID列
        return hiddenColumns;
    }
}
