package com.wormhole.hotelds.excel;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/8 10:39
 * @Description：BaseExportReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BaseExportReq {

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 导出类型: all-全部导出, filtered-筛选导出, current-当前页导出
     */
    private String exportType = "filtered";

    /**
     * 当前页数据的IDs (当exportType=current时使用)
     */
    private List<Long> currentIds;

    /**
     * 文件名(可选，不提供则使用默认名称)
     */
    private String fileName;

    /**
     * 请求参数
     */
    private String requestJson;
}
