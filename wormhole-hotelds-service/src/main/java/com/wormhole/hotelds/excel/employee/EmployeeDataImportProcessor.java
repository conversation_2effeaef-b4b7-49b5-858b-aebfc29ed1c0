package com.wormhole.hotelds.excel.employee;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.google.common.collect.Lists;
import com.wormhole.common.model.entity.BaseEntity;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.req.HdsEmployeeSaveReq;
import com.wormhole.hotelds.admin.service.HdsEmployeeHotelService;
import com.wormhole.hotelds.admin.service.HdsEmployeeService;
import com.wormhole.hotelds.admin.service.HotelService;
import com.wormhole.hotelds.constant.RoleEnum;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.excel.DataImportProcessor;
import com.wormhole.hotelds.excel.EntityCollection;
import com.wormhole.hotelds.excel.ImportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Date：2025/5/15
 * @Description：员工数据处理器
 */
@Slf4j
@Component
public class EmployeeDataImportProcessor implements DataImportProcessor<EmployeeImportDTO, EntityCollection> {

    @Resource
    private HdsEmployeeService hdsEmployeeService;

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private HotelService hotelService;

    @Resource
    private TransactionalOperator transactionalOperator;

    // 自定义账号校验规则
    private static final String ACCOUNT_PATTERN = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{6,10}$";
    private static final String MOBILE_PATTERN = "^1[3-9]\\d{9}$";
    private static final String EMAIL_PATTERN = "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$";

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.EMPLOYEE.getBusinessType();
    }

    @Override
    public Set<String> getRequiredHeaders(ImportContext context) {
        HashSet<String> headers = new HashSet<>(Arrays.asList(
                EmployeeValidationRule.EMPLOYEE_NAME.getFieldName(),
                EmployeeValidationRule.USERNAME.getFieldName(),
                EmployeeValidationRule.MOBILE.getFieldName(),
                EmployeeValidationRule.EMAIL.getFieldName(),
                EmployeeValidationRule.ROLE.getFieldName(),
                EmployeeValidationRule.ACCOUNT_TYPE.getFieldName(),
                EmployeeValidationRule.TICKET_ASSIGNMENT_FLAG.getFieldName()
        ));
        HeaderUtils.HeaderInfo headerInfo = context.getHeaderInfo();
        if (StringUtils.isBlank(headerInfo.getHotelCode())) {
            headers.add(EmployeeValidationRule.HOTEL_CODE.getFieldName());
        }
        return headers;
    }

    @Override
    public Flux<EmployeeImportDTO> validateData(Flux<EmployeeImportDTO> data, ImportContext context) {
        return data.collectList()
                .flatMapMany(dtoList -> {
                    // 1. 设置总数
                    context.setTotalCount(dtoList.size());

                    // 2. 检查当前批次内的重复
                    Map<String, List<EmployeeImportDTO>> duplicatesInBatch = getDuplicatesInBatch(dtoList);

                    // 3. 提取所有用户名和手机号以及邮箱
                    Set<String> allUsernames = dtoList.stream()
                            .map(EmployeeImportDTO::getUsername)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    Set<String> allMobiles = dtoList.stream()
                            .map(EmployeeImportDTO::getMobile)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    Set<String> allEmails = dtoList.stream()
                            .map(EmployeeImportDTO::getEmail)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    // 6. 收集所有门店编码
                    Set<String> allHotelCodes = dtoList.stream()
                            .map(EmployeeImportDTO::getHotelCode)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    // 7. 准备数据容器
                    Map<String, HdsEmployeeEntity> mobileToEmployeeMap = new HashMap<>();
                    Map<String, HdsEmployeeEntity> usernameToEmployeeMap = new HashMap<>();
                    Map<String, HdsEmployeeEntity> emailToEmployeeMap = new HashMap<>();
                    Map<String, HdsHotelInfoEntity> hotelMap = new HashMap<>();

                    // 8. 构建查询流程
                    Mono<List<HdsEmployeeEntity>> employeeQuery = batchQueryEmployees(allMobiles, allUsernames, allEmails);
                    Mono<List<HdsHotelInfoEntity>> hotelQuery = batchQueryHotels(allHotelCodes);

                    return Mono.zip(employeeQuery, hotelQuery)
                            .flatMapMany(tuple -> {
                                // 处理员工查询结果
                                List<HdsEmployeeEntity> employees = tuple.getT1();
                                for (HdsEmployeeEntity employee : employees) {
                                    if (StringUtils.isNotBlank(employee.getMobile())) {
                                        mobileToEmployeeMap.put(employee.getMobile(), employee);
                                    }
                                    if (StringUtils.isNotBlank(employee.getUsername())) {
                                        usernameToEmployeeMap.put(employee.getUsername(), employee);
                                    }
                                    if (StringUtils.isNotBlank(employee.getEmail())) {
                                        emailToEmployeeMap.put(employee.getEmail(), employee);
                                    }
                                }

                                // 处理门店查询结果
                                List<HdsHotelInfoEntity> hotels = tuple.getT2();
                                for (HdsHotelInfoEntity hotel : hotels) {
                                    if (StringUtils.isNotBlank(hotel.getHotelCode())) {
                                        hotelMap.put(hotel.getHotelCode(), hotel);
                                    }
                                }

                                // 处理每条数据
                                return Flux.fromIterable(dtoList)
                                        .filterWhen(dto -> validateEmployee(
                                                dto,
                                                mobileToEmployeeMap,
                                                usernameToEmployeeMap,
                                                emailToEmployeeMap,
                                                hotelMap,
                                                duplicatesInBatch,
                                                context
                                        ));
                            });
                })
                .doOnError(error -> {
                    log.error("数据验证过程发生错误: {}", error.getMessage(), error);
                    context.addRowError(0, "数据验证过程发生错误：" + error.getMessage());
                });
    }

    /**
     * 批量查询员工信息
     *
     * @param mobiles   手机号列表
     * @param usernames 用户名列表
     * @param emails    邮箱列表
     * @return 员工列表
     */
    public Mono<List<HdsEmployeeEntity>> batchQueryEmployees(Set<String> mobiles, Set<String> usernames, Set<String> emails) {
        // 构建查询条件
        return hdsEmployeeService.batchQueryEmployees(mobiles, usernames, emails)
                .defaultIfEmpty(Collections.emptyList());
    }

    /**
     * 批量查询门店信息
     *
     * @param hotelCodes 门店编码列表
     * @return 门店列表
     */
    public Mono<List<HdsHotelInfoEntity>> batchQueryHotels(Set<String> hotelCodes) {
        return hotelService.batchQueryHotels(hotelCodes)
                .defaultIfEmpty(Collections.emptyList());
    }

    /**
     * 获取员工唯一标识Key
     * 优先使用手机号，如果手机号为空则依次尝试用户名和邮箱
     */
    private String getEmployeeKey(EmployeeImportDTO dto) {
        // 如果手机号不为空，直接使用手机号
        if (StringUtils.isNotBlank(dto.getMobile())) {
            return String.format("%s_%s", dto.getHotelCode(), dto.getMobile());
        }

        // 否则，尝试用户名和邮箱
        String identifier = Stream.of(dto.getUsername(), dto.getEmail())
                .filter(StringUtils::isNotBlank)
                .findFirst()
                .orElse("unknown");

        return String.format("%s_%s", dto.getHotelCode(), identifier);
    }

    /**
     * 检查批次内重复
     */
    private Map<String, List<EmployeeImportDTO>> getDuplicatesInBatch(List<EmployeeImportDTO> dtoList) {
        return dtoList.stream()
                .collect(Collectors.groupingBy(
                        this::getEmployeeKey,
                        Collectors.toList()
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 验证单个员工数据
     */
    private Mono<Boolean> validateEmployee(EmployeeImportDTO dto,
                                           Map<String, HdsEmployeeEntity> mobileToEmployeeMap,
                                           Map<String, HdsEmployeeEntity> usernameToEmployeeMap,
                                           Map<String, HdsEmployeeEntity> emailToEmployeeMap,
                                           Map<String, HdsHotelInfoEntity> hotelMap,
                                           Map<String, List<EmployeeImportDTO>> duplicatesInBatch,
                                           ImportContext context) {
        if (!validateHotel(dto, context)) {
            return Mono.just(false);
        }
        // 基础格式验证
        if (!validateBasicFields(dto, context)) {
            return Mono.just(false);
        }

        // 检查手机号是否已存在
        HdsEmployeeEntity existingEmployee = mobileToEmployeeMap.get(dto.getMobile());
        if (existingEmployee == null) {
            existingEmployee = usernameToEmployeeMap.get(dto.getUsername());
        }
        if (existingEmployee == null) {
            existingEmployee = emailToEmployeeMap.get(dto.getEmail());
        }
        if (existingEmployee != null) {
            // 手机号存在，视为新增该手机号对应账号与门店的关联
            dto.setExists(true);
            // 使用已存在员工的信息
            dto.setUsername(existingEmployee.getUsername());
            if (StringUtils.isBlank(dto.getName())) {
                dto.setName(existingEmployee.getName());
            }
            if (StringUtils.isBlank(dto.getEmail())) {
                dto.setEmail(existingEmployee.getEmail());
            }
            // 设置员工ID用于后续关联
            dto.setEmployeeId(existingEmployee.getId());
        }

        // 解析角色
        if (!validateRole(dto, context)) {
            return Mono.just(false);
        }

        // 解析账号类型
        if (!validateAccountType(dto, context)) {
            return Mono.just(false);
        }
        if (!validateTicketAssignFlag(dto, context)){
            return Mono.just(false);
        }

        // 解析工单类型
//        if (!validateTicketTypes(dto, context)) {
//            return Mono.just(false);
//        }
        if (!validateDuplicates(dto, duplicatesInBatch, context)) {
            return Mono.just(false);
        }
        // 校验门店是否存在
        if (!validateHotelExists(dto, hotelMap, context)) {
            return Mono.just(false);
        }
        return validateEmployeeHotelRelation(dto, context);
    }

    private boolean validateDuplicates(
            EmployeeImportDTO dto,
            Map<String, List<EmployeeImportDTO>> duplicatesInBatch,
            ImportContext context) {

        String positionKey = getEmployeeKey(dto);
        List<EmployeeImportDTO> duplicates = duplicatesInBatch.get(positionKey);
        if (duplicates != null && duplicates.get(0) != dto) {
            context.addRowError(dto.getRowNum(),
                    String.format("位置信息在本次导入中重复，已采用第%d行的数据",
                            duplicates.get(0).getRowNum()));
            return false;
        }
        return true;
    }

    /**
     * 基础字段验证
     */
    private boolean validateBasicFields(EmployeeImportDTO dto, ImportContext context) {
        List<String> errors = Arrays.stream(EmployeeValidationRule.values())
                .filter(EmployeeValidationRule::isRequired)
                .filter(rule -> StringUtils.isBlank(rule.getValueGetter().apply(dto)))
                .map(rule -> String.format("%s不能为空", rule.getFieldName()))
                .toList();

        errors.forEach(error -> context.addRowError(dto.getRowNum(), error));
        if (CollectionUtils.isNotEmpty(errors)) {
            return false;
        }

        // 检查用户名、手机号、邮箱的填写情况
        boolean hasUsername = StringUtils.isNotBlank(dto.getUsername());
        boolean hasMobile = StringUtils.isNotBlank(dto.getMobile());
        boolean hasEmail = StringUtils.isNotBlank(dto.getEmail());

        // 计算已填写的字段数量
        int filledCount = 0;
        if (hasUsername) {
            filledCount++;
        }
        if (hasMobile) {
            filledCount++;
        }
        if (hasEmail) {
            filledCount++;
        }

        // 必须有一个字段填写，但不能全部填写
        if (filledCount == 0) {
            context.addRowError(dto.getRowNum(), "手机号、自定义账号、邮箱三个至少填写一个");
            return false;
        } else if (filledCount > 1) {
            context.addRowError(dto.getRowNum(), "手机号、自定义账号、邮箱只能填写一个");
            return false;
        }

        // 自定义账号校验
        if (StringUtils.isNotBlank(dto.getUsername())) {
            Matcher matcher = Pattern.compile(ACCOUNT_PATTERN).matcher(dto.getUsername());
            if (!matcher.matches()) {
                context.addRowError(dto.getRowNum(), "自定义账号不符合规则");
                return false;
            }
        }

        if (StringUtils.isNotBlank(dto.getMobile())) {
            Matcher matcher = Pattern.compile(MOBILE_PATTERN).matcher(dto.getMobile());
            if (!matcher.matches()) {
                context.addRowError(dto.getRowNum(), "手机号不符合规则");
                return false;
            }
        }
        if (StringUtils.isNotBlank(dto.getEmail())) {
            Matcher matcher = Pattern.compile(EMAIL_PATTERN).matcher(dto.getEmail());
            if (!matcher.matches()) {
                context.addRowError(dto.getRowNum(), "邮箱不符合规则");
                return false;
            }
        }
        return true;
    }

    /**
     * 验证角色
     */
    private boolean validateRole(EmployeeImportDTO dto, ImportContext context) {
        if (StringUtils.isBlank(dto.getRoleName())) {
            context.addRowError(dto.getRowNum(), "角色名称不能为空");
            return false;
        }

        String normalizedRoleNames = dto.getRoleName().replace("，", ",");
        String[] roleNames = normalizedRoleNames.split(",");
        List<String> roleCodes = new ArrayList<>();
        boolean hasError = false;

        // 逐个验证每个角色名称
        for (String roleName : roleNames) {
            String trimmedRoleName = roleName.trim();
            if (StringUtils.isBlank(trimmedRoleName)) {
                continue;
            }

            String roleCode = RoleEnum.getCodeByName(trimmedRoleName);
            if (roleCode == null) {
                context.addRowError(dto.getRowNum(), String.format("角色 %s 不存在", trimmedRoleName));
                hasError = true;
            } else {
                roleCodes.add(roleCode);
            }
        }

        if (hasError || roleCodes.isEmpty()) {
            return false;
        }

        dto.setRoleCodes(roleCodes);
        return true;
    }

    /**
     * 验证账号类型
     */
    private boolean validateAccountType(EmployeeImportDTO dto, ImportContext context) {
        String accountTypeLabel = dto.getAccountTypeLabel();
        if (StringUtils.isBlank(accountTypeLabel)) {
            context.addRowError(dto.getRowNum(), "总分机不能为空");
            return false;
        }

        return Arrays.stream(AccountTypeEnum.values())
                .filter(typeEnum -> StringUtils.equals(typeEnum.getDescription(), accountTypeLabel))
                .findFirst()
                .map(typeEnum -> {
                    dto.setAccountType(typeEnum.getCode());
                    return true;
                })
                .orElseGet(() -> {
                    context.addRowError(dto.getRowNum(), "总分机值必须为总机或分机");
                    return false;
                });
    }

    private boolean validateTicketAssignFlag(EmployeeImportDTO dto, ImportContext context) {
        String ticketAssignmentFlagLabel = dto.getTicketAssignmentFlagLabel();
        if (StringUtils.isBlank(ticketAssignmentFlagLabel)) {
            context.addRowError(dto.getRowNum(), "接单表不能为空");
            return false;
        }
        if (StringUtils.equals("是",ticketAssignmentFlagLabel)){
            dto.setTicketAssignmentFlag(1);
            return true;
        }
        if (StringUtils.equals("否",ticketAssignmentFlagLabel)){
            dto.setTicketAssignmentFlag(0);
            return true;
        }
        context.addRowError(dto.getRowNum(), "接单表必须为是或者否");
        return false;
    }

    /**
     * 验证门店
     */
    private boolean validateHotel(EmployeeImportDTO dto, ImportContext context) {
        // 如果门店编码为空，则从headerInfo获取hotelCode
        if (StringUtils.isNotBlank(dto.getHotelCode())) {
            return true;
        } else {
            context.addRowError(dto.getRowNum(), "门店编码不能为空");
            return false;
        }
    }

    /**
     * 验证工单类型
     */
    private boolean validateTicketTypes(EmployeeImportDTO dto, ImportContext context) {
        if (dto.getRoleCodes().contains(RoleEnum.HOTEL_STAFF.getCode())
                && Objects.equals(dto.getAccountType(), AccountTypeEnum.EXTENSION.getCode())
                && StringUtils.isBlank(dto.getTicketTypes())) {
            context.addRowError(dto.getRowNum(), "工单类型不能为空");
            return false;
        }

        if (StringUtils.isNotBlank(dto.getTicketTypes())) {
            List<String> tickets = Arrays.asList(dto.getTicketTypes().replaceAll("[,，]", ",").split(","));
            List<String> ticketCodes = tickets.stream()
                    .map(ticket -> Arrays.stream(ServiceCategory.values())
                            .filter(category -> StringUtils.equals(category.getChineseName(), ticket))
                            .findFirst()
                            .map(ServiceCategory::getCode)
                            .orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(ticketCodes)) {
                context.addRowError(dto.getRowNum(), "工单类型无效");
                return false;
            }
            dto.setTickets(ticketCodes);
        }
        return true;
    }

    /**
     * 检查员工与门店的关联关系
     */
    private Mono<Boolean> validateEmployeeHotelRelation(EmployeeImportDTO dto, ImportContext context) {
        if (!dto.isExists() || dto.getEmployeeId() == null || StringUtils.isBlank(dto.getHotelCode())) {
            return Mono.just(true);
        }
        return hdsEmployeeHotelService.existsByEmployeeIdAndHotelCode(dto.getEmployeeId(), dto.getHotelCode())
                .map(exists -> {
                    if (exists) {
                        context.addRowError(dto.getRowNum(),
                                String.format("员工Id:[%d]与门店Code:[%s]的关联关系已存在",
                                        dto.getEmployeeId(), dto.getHotelCode()));
                        return false;
                    }
                    return true;
                })
                .onErrorResume(error -> {
                    log.error("验证员工与门店关联关系失败: dto={}, error={}", dto, error.getMessage(), error);
                    context.addRowError(dto.getRowNum(), "验证员工与门店关联关系失败: " + error.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * 验证门店是否存在
     */
    private boolean validateHotelExists(EmployeeImportDTO dto, Map<String, HdsHotelInfoEntity> hotelMap, ImportContext context) {
        // 如果门店编码不在hotelMap中或者对应的值为null，表示门店不存在
        if (!hotelMap.containsKey(dto.getHotelCode()) || hotelMap.get(dto.getHotelCode()) == null) {
            context.addRowError(dto.getRowNum(), String.format("门店编码[%s]不存在", dto.getHotelCode()));
            return false;
        }

        return true;
    }

    @Override
    public Flux<EmployeeImportDTO> convertData(Flux<Map<String, String>> rawData, ImportContext context) {
        return rawData.map(row -> {
            // 只负责基础的数据转换，不做业务验证
            EmployeeImportDTO dto = new EmployeeImportDTO();
            dto.setRowNum(Integer.parseInt(row.getOrDefault("_excelRowNum", "0")));

            // 基础字段映射
            dto.setHotelCode(row.get(EmployeeValidationRule.HOTEL_CODE.getFieldName()));
            if (context.getHeaderInfo() != null && StringUtils.isNotBlank(context.getHeaderInfo().getHotelCode())) {
                dto.setHotelCode(context.getHeaderInfo().getHotelCode());
            }

            dto.setName(row.get(EmployeeValidationRule.EMPLOYEE_NAME.getFieldName()));
            dto.setUsername(row.get(EmployeeValidationRule.USERNAME.getFieldName()));
            dto.setMobile(row.get(EmployeeValidationRule.MOBILE.getFieldName()));
            dto.setEmail(row.get(EmployeeValidationRule.EMAIL.getFieldName()));
            dto.setRoleName(row.get(EmployeeValidationRule.ROLE.getFieldName()));

            // 账号类型字段映射 - 仅做字段提取，不做验证
            String accountTypeStr = row.get(EmployeeValidationRule.ACCOUNT_TYPE.getFieldName());
            if (StringUtils.isNotBlank(accountTypeStr)) {
                dto.setAccountTypeLabel(accountTypeStr);
            }
            String assignStr = row.get(EmployeeValidationRule.TICKET_ASSIGNMENT_FLAG.getFieldName());
            if (StringUtils.isNotBlank(assignStr)) {
                dto.setTicketAssignmentFlagLabel(assignStr);
            }

//            // 工单类型字段映射 - 仅保存原始字符串，不做解析转换
//            String ticketStr = row.get(EmployeeValidationRule.TICKET_TYPES.getFieldName());
//            if (StringUtils.isNotBlank(ticketStr)) {
//                dto.setTicketTypes(ticketStr);
//            }


            // 特殊逻辑
            String normalizedRoleNames = dto.getRoleName().replace("，", ",");
            List<String> roleNames = Arrays.asList(normalizedRoleNames.split(","));
            if (CollectionUtils.isNotEmpty(roleNames)) {
                // 如果是门店管理员，设置账号类型为总机并清空工单类型
                if (roleNames.contains(RoleEnum.HOTEL_ADMIN.getName())) {
                    dto.setAccountTypeLabel(AccountTypeEnum.MAIN_SWITCHBOARD.getDescription());
                    dto.setTicketTypes(null);
                }

                // 如果是门店员工且账号类型是总机，清空工单类型
                else if (roleNames.contains(RoleEnum.HOTEL_STAFF.getName())
                        && AccountTypeEnum.MAIN_SWITCHBOARD.getDescription().equals(accountTypeStr)) {
                    dto.setTicketTypes(null);
                }
            }

            return dto;
        });
    }

    @Override
    public Flux<EntityCollection> toEntities(Flux<EmployeeImportDTO> data, ImportContext context) {
        // 每条记录单独处理，保留每条记录的工单类型和角色信息
        return data.map(dto -> {
            EntityCollection collection = EntityCollection.builder()
                    .rowNum(dto.getRowNum())
                    .entities(new HashMap<>())
                    .build();
            HeaderUtils.HeaderInfo headerInfo = context.getHeaderInfo();
            // 构建员工保存请求
            HdsEmployeeSaveReq employeeSaveReq = new HdsEmployeeSaveReq();
            employeeSaveReq.setName(dto.getName())
                    .setUsername(dto.getUsername())
                    .setMobile(dto.getMobile())
                    .setEmail(dto.getEmail())
                    .setRoleCode(dto.getRoleCode())
                    .setRoleCodes(dto.getRoleCodes())
                    .setAccountType(dto.getAccountType())
//                .setTickets(dto.getTickets())
                    .setCreatedBy(headerInfo.getUserId())
                    .setCreatedByName(headerInfo.getName())
                    .setTicketAssignmentFlag(dto.getTicketAssignmentFlag())
                    .setHotelCodes(Collections.singletonList(dto.getHotelCode()));

            // 如果是已存在的员工，设置ID用于更新关联关系
            if (dto.isExists() && dto.getEmployeeId() != null) {
                employeeSaveReq.setId(dto.getEmployeeId());
            }
            employeeSaveReq.setFrom("excel");

            collection.addEntity("employeeSaveReq", employeeSaveReq);
            return collection;
        });
    }

    @Override
    public Mono<Integer> saveData(List<EntityCollection> collections, ImportContext context) {
        return Flux.fromIterable(collections)
                .flatMap(collection -> {
                    HdsEmployeeSaveReq req = collection.getEntity("employeeSaveReq");

                    return transactionalOperator.transactional(
                                    hdsEmployeeService.importEmployee(req)
                            ).doOnSuccess(savedPosition -> {
                                log.info("第{}行员工信息入库成功，请求参数：{}", collection.getRowNum(), JacksonUtils.writeValueAsString(req));
                                context.incrementSuccessCount();
                            }).thenReturn(true)
                            .onErrorResume(e -> {
                                log.error("第{}行员工信息入库失败: {}", collection.getRowNum(), e.getMessage());
                                context.addRowError(collection.getRowNum(),
                                        String.format("员工信息入库失败: %s", e.getMessage()));
                                return Mono.just(false);
                            });
                })
                .count()
                .map(Long::intValue);
    }

    @Override
    public Flux<EmployeeImportDTO> exportData(Map<String, Object> params) {
        // 导出模板时不需要返回数据
        return Flux.empty();
    }

    @Override
    public String getExportFileName(Map<String, Object> params) {
        return "员工导入模板.xlsx";
    }
}