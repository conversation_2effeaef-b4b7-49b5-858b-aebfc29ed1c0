package com.wormhole.hotelds.excel;

import reactor.core.publisher.Flux;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/8 10:41
 * @Description：DataExportProcessor
 */
public interface DataExportProcessor<T, R extends BaseExportReq> {
    /**
     * 获取此处理器支持的业务类型
     */
    String getBusinessType();

    /**
     * 获取Excel表头
     */
    List<String> getExcelHeaders();

    /**
     * 查询要导出的数据
     *
     * @param request 导出请求
     * @param context 导出上下文
     * @return 数据流
     */
    Flux<T> queryData(R request, ExportContext context);

    /**
     * 将数据转换为Excel行数据
     */
    List<String> convertToRow(T data);

    /**
     * 获取导出文件名
     */
    String getExportFileName(R request);

    /**
     * 获取请求类的Class
     */
    Class<R> getRequestClass();
}
