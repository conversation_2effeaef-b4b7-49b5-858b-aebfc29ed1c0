package com.wormhole.hotelds.excel;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * @Author：flx
 * @Date：2025/5/8 15:31
 * @Description：Excel格式提供者接口，导出处理器可以实现此接口来提供自定义的Excel格式设置
 */
public interface ExcelFormattingProvider {

    /**
     * 获取列宽映射（列名 -> 宽度字符数）
     * @return 列宽映射
     */
    Map<String, Integer> getColumnWidthMap();

    /**
     * 获取特殊格式的列（列名 -> 格式类型）
     * 格式类型可以是 "date", "number", "text" 等
     * @return 特殊格式列映射
     */
    default Map<String, String> getColumnFormatMap() {
        return null;
    }

    /**
     * 获取表头行高（单位：点）
     * @return 表头行高
     */
    default float getHeaderRowHeight() {
        return 25.0f;
    }

    /**
     * 获取数据行默认行高（单位：点）
     * @return 数据行高
     */
    default float getDataRowHeight() {
        return 18.0f;
    }

    /**
     * 获取列对齐方式映射（列名 -> 对齐方式）
     * 可以返回 "center", "left", "right" 等值
     * @return 列对齐方式映射
     */
    default Map<String, String> getColumnAlignmentMap() {
        return null;
    }

    /**
     * 获取冻结窗格配置
     * @return 冻结窗格配置数组 [冻结列数, 冻结行数, 右侧可见首列, 下方可见首行]
     * 默认冻结第一行: [0, 1, 0, 1]
     * 不冻结返回null
     */
    default int[] getFreezePaneConfig() {
        return new int[]{0, 1, 0, 1};
    }

    /**
     * 是否启用表头保护（锁定表头不可编辑）
     * @return 是否启用表头保护
     */
    default boolean enableHeaderProtection() {
        return true;
    }




    /**
     * 获取工作表保护密码
     * 如果返回null，则使用默认密码
     * @return 保护密码
     */
    default String getSheetProtectionPassword() {
        return "password";
    }

    /**
     * 获取需要隐藏的列名集合
     * @return 隐藏列名集合
     */
    default Set<String> getHiddenColumns() {
        return Collections.emptySet();
    }
}
