package com.wormhole.hotelds.excel.userlead;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.excel.BaseExportReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/5/28 09:43
 * @Description：UserLeadExportReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserLeadExportReq extends BaseExportReq {

    /**
     * 联系人手机号码
     */
    private String mobile;

    /**
     * 线索状态：1-有效，0-无效，null-全部
     */
    private Integer status;
}
