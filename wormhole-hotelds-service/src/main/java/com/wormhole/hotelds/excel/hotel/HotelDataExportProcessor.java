package com.wormhole.hotelds.excel.hotel;

import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.ExportTypeEnum;
import com.wormhole.hotelds.admin.model.vo.HotelVO;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoEntity;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoFieldEnum;
import com.wormhole.hotelds.core.model.entity.HdsMerchantEntity;
import com.wormhole.hotelds.core.model.entity.HdsMerchantFieldEnum;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExcelFormattingProvider;
import com.wormhole.hotelds.excel.ExportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

import static com.wormhole.hotelds.admin.constant.ExcelConstant.*;

/**
 * @Author：flx
 * @Date：2025/5/9 10:10
 * @Description：HotelDataExportProcessor
 */
@Slf4j
@Component
public class HotelDataExportProcessor implements DataExportProcessor<HotelExportDTO, HotelExportReq>, ExcelFormattingProvider {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.HOTEL.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return new ArrayList<>(Arrays.asList(
                FIELD_HOTEL_ID,
                FIELD_HOTEL_CODE,
                FIELD_HOTEL_LOGO,
                FIELD_HOTEL_NAME,
                FIELD_COUNTRY_NAME,
                FIELD_PROVINCE_NAME,
                FIELD_CITY_NAME,
                FIELD_DISTRICT_NAME,
                FIELD_ADDRESS,
                FIELD_LONGITUDE,
                FIELD_LATITUDE,
                FIELD_MERCHANT_NAME,
                FIELD_MERCHANT_SHORT_NAME,
                FIELD_BRAND_NAME,
                FIELD_SUBJECT_NAME,
                FIELD_MAIN_PERSON,
                FIELD_PHONE,
                FIELD_EMAIL,
                FIELD_TOTAL_ROOM,
                FIELD_CTRIP_EBK_URL
        ));
    }

    @Override
    public Flux<HotelExportDTO> queryData(HotelExportReq request, ExportContext context) {
        // 构建查询条件
        Mono<Criteria> criteriaMono = buildQueryCriteria(request);

        // 执行查询
        return criteriaMono.flatMapMany(criteria -> {
                    Query query = Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.DESC, HdsHotelInfoFieldEnum.created_at.name()));
                    return r2dbcEntityTemplate.select(query, HdsHotelInfoEntity.class);
                })
                .map(HotelExportDTO::toVo);
    }

    /**
     * 根据导出类型和请求参数构建查询条件
     */
    private Mono<Criteria> buildQueryCriteria(HotelExportReq request) {
        // 根据导出类型选择不同的查询策略
        ExportTypeEnum exportType = ExportTypeEnum.getByCode(request.getExportType());

        return switch (exportType) {
            case ALL -> Mono.just(Criteria.empty().and(HdsHotelInfoFieldEnum.status.name()).not(2)
                    .and(HdsHotelInfoFieldEnum.row_status.name()).is(1));
            case CURRENT -> {
                // 当前页导出 - 根据IDs列表
                if (CollectionUtils.isEmpty(request.getCurrentIds())) {
                    // 如果ID列表为空，返回一个永远为false的条件，确保不会有数据
                    yield Mono.just(Criteria.where(HdsHotelInfoFieldEnum.id.name()).is(-1));
                } else {
                    yield Mono.just(Criteria.where(HdsHotelInfoFieldEnum.id.name()).in(request.getCurrentIds()));
                }
            }
            default -> buildFilterCriteria(request);
        };
    }

    /**
     * 构建筛选查询条件
     */
    private Mono<Criteria> buildFilterCriteria(HotelExportReq request) {
        // 首先构建基础查询条件
        Criteria baseCriteria = buildBaseCriteria(request);

        // 然后处理需要预过滤的商户名称条件
        return preFilterByMerchantName(request, baseCriteria)
                .map(criteria -> {
                    // 处理协议状态和服务状态
//                    if (Objects.nonNull(request.getContractStatus())) {
//                        criteria = criteria.and(HdsHotelInfoFieldEnum.contract_status.name()).is(request.getContractStatus());
//                    }
//
//                    if (Objects.nonNull(request.getServiceStatus())) {
//                        criteria = criteria.and(HdsHotelInfoFieldEnum.service_status.name()).is(request.getServiceStatus());
//                    }

                    return criteria;
                });
    }

    /**
     * 构建基础查询条件
     */
    private Criteria buildBaseCriteria(HotelExportReq request) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(request.getBrandName())) {
            criteria = criteria.and(HdsHotelInfoFieldEnum.brand_name.name()).like("%" + request.getBrandName().trim() + "%");
        }

        if (StringUtils.isNotBlank(request.getHotelName())) {
            criteria = criteria.and(HdsHotelInfoFieldEnum.hotel_name.name()).like("%" + request.getHotelName().trim() + "%");
        }

        if (StringUtils.isNotBlank(request.getHotelCode())) {
            criteria = criteria.and(HdsHotelInfoFieldEnum.hotel_code.name()).like("%" + request.getHotelCode().trim() + "%");
        }

        return criteria;
    }

    /**
     * 根据商户名称预过滤
     */
    private Mono<Criteria> preFilterByMerchantName(HotelExportReq request, Criteria baseCriteria) {
        if (StringUtils.isBlank(request.getMerchantName())) {
            return Mono.just(baseCriteria);
        }

        String likeWord = "%" + request.getMerchantName().trim() + "%";

        // 根据商户名称查询符合条件的商户ID
        Criteria merchantCriteria = Criteria.where(HdsMerchantFieldEnum.merchant_name.name()).like(likeWord)
                .or(Criteria.where(HdsMerchantFieldEnum.subject_name.name()).like(likeWord));

        return r2dbcEntityTemplate.select(HdsMerchantEntity.class)
                .matching(Query.query(merchantCriteria))
                .all()
                .map(HdsMerchantEntity::getMerchantId)
                .collectList()
                .map(merchantIds -> {
                    if (merchantIds.isEmpty()) {
                        return Criteria.where(HdsHotelInfoFieldEnum.id.name()).is(-1);
                    }
                    // 将商户ID添加到查询条件中
                    return baseCriteria.and(HdsHotelInfoFieldEnum.merchant_id.name()).in(merchantIds);
                });
    }

    /**
     * 将数据转成excel行
     *
     * @param data
     * @return
     */
    @Override
    public List<String> convertToRow(HotelExportDTO data) {
        // 定义表头到数据提取器的映射
        Map<String, Function<HotelExportDTO, String>> extractors = new LinkedHashMap<>();
        extractors.put(FIELD_HOTEL_ID, hotelExportDTO -> String.valueOf(data.getId()));
        extractors.put(FIELD_HOTEL_CODE, HotelExportDTO::getHotelCode);
        extractors.put(FIELD_HOTEL_LOGO, HotelExportDTO::getHotelLogo);
        extractors.put(FIELD_HOTEL_NAME, HotelExportDTO::getHotelName);
        extractors.put(FIELD_MERCHANT_NAME, HotelExportDTO::getMerchantName);
        extractors.put(FIELD_MERCHANT_SHORT_NAME, HotelExportDTO::getMerchantShortName);
        extractors.put(FIELD_BRAND_NAME, HotelExportDTO::getBrandName);
        extractors.put(FIELD_SUBJECT_NAME, HotelExportDTO::getSubjectName);
        extractors.put(FIELD_COUNTRY_NAME, HotelExportDTO::getCountryName);
        extractors.put(FIELD_PROVINCE_NAME, HotelExportDTO::getProvinceName);
        extractors.put(FIELD_CITY_NAME, HotelExportDTO::getCityName);
        extractors.put(FIELD_DISTRICT_NAME, HotelExportDTO::getDistrictName);
        extractors.put(FIELD_ADDRESS, HotelExportDTO::getAddress);
        extractors.put(FIELD_LONGITUDE, HotelExportDTO::getLongitude);
        extractors.put(FIELD_LATITUDE, HotelExportDTO::getLatitude);
        extractors.put(FIELD_TOTAL_ROOM, dto -> dto.getTotalRoom() != null ? dto.getTotalRoom().toString() : "");
        extractors.put(FIELD_MAIN_PERSON, HotelExportDTO::getMainPerson);
        extractors.put(FIELD_PHONE, HotelExportDTO::getPhone);
        extractors.put(FIELD_EMAIL, HotelExportDTO::getEmail);
        extractors.put(FIELD_CTRIP_EBK_URL, HotelExportDTO::getCtripEbkUrl);

        // 按照表头顺序提取数据
        List<String> row = new ArrayList<>();
        for (String header : getExcelHeaders()) {
            Function<HotelExportDTO, String> extractor = extractors.get(header);
            if (extractor != null) {
                String value = extractor.apply(data);
                row.add(value != null ? value : "");
            } else {
                // 未知表头，添加空值
                row.add("");
            }
        }

        return row;
    }

    /**
     * 获取导出文件名
     *
     * @param request
     * @return
     */
    @Override
    public String getExportFileName(HotelExportReq request) {
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now());
        return "门店列表_" + dateStr + ".xlsx";
    }

    /**
     * 获取请求类
     *
     * @return
     */
    @Override
    public Class<HotelExportReq> getRequestClass() {
        return HotelExportReq.class;
    }

    @Override
    public Map<String, Integer> getColumnWidthMap() {
        Map<String, Integer> widthMap = new HashMap<>();
        widthMap.put(FIELD_HOTEL_CODE, 15);
        widthMap.put(FIELD_HOTEL_LOGO, 15);
        widthMap.put(FIELD_HOTEL_NAME, 25);
        widthMap.put(FIELD_MERCHANT_NAME, 25);
        widthMap.put(FIELD_MERCHANT_SHORT_NAME, 20);
        widthMap.put(FIELD_BRAND_NAME, 15);
        widthMap.put(FIELD_SUBJECT_NAME, 30);
        widthMap.put(FIELD_COUNTRY_NAME, 10);
        widthMap.put(FIELD_PROVINCE_NAME, 10);
        widthMap.put(FIELD_CITY_NAME, 10);
        widthMap.put(FIELD_DISTRICT_NAME, 10);
        widthMap.put(FIELD_ADDRESS, 30);
        widthMap.put(FIELD_LONGITUDE, 10);
        widthMap.put(FIELD_LATITUDE, 10);
        widthMap.put(FIELD_TOTAL_ROOM, 10);
        widthMap.put(FIELD_MAIN_PERSON, 15);
        widthMap.put(FIELD_PHONE, 15);
        widthMap.put(FIELD_EMAIL, 20);
        widthMap.put(FIELD_CTRIP_EBK_URL, 30);
        return widthMap;
    }

    @Override
    public Set<String> getHiddenColumns() {
        Set<String> hiddenColumns = new HashSet<>();
        hiddenColumns.add(FIELD_HOTEL_ID); // 隐藏ID列
        return hiddenColumns;
    }
}