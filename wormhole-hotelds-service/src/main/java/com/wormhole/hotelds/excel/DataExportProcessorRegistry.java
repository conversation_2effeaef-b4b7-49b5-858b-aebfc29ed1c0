package com.wormhole.hotelds.excel;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/5/8 10:44
 * @Description：DataExportProcessorRegistry
 */
@Slf4j
@Component
public class DataExportProcessorRegistry {

    /**
     * 按业务类型组织的处理器映射
     */
    private final Map<String, DataExportProcessor<?, ? extends BaseExportReq>> processorMap = new HashMap<>();

    /**
     * 所有可用的处理器列表
     */
    private final List<DataExportProcessor<?, ? extends BaseExportReq>> processors;

    /**
     * 构造函数，注入所有可用的处理器
     *
     * @param processors 处理器列表
     */
    public DataExportProcessorRegistry(List<DataExportProcessor<?, ? extends BaseExportReq>> processors) {
        this.processors = processors;
    }

    /**
     * 初始化处理器映射
     * 在Spring容器启动时自动执行
     */
    @PostConstruct
    public void init() {
        for (DataExportProcessor<?, ? extends BaseExportReq> processor : processors) {
            String businessType = processor.getBusinessType();
            log.info("注册数据导出处理器: businessType={}, processor={}",
                    businessType, processor.getClass().getSimpleName());
            processorMap.put(businessType, processor);
        }
    }

    /**
     * 检查是否支持指定的业务类型
     *
     * @param businessType 业务类型
     * @return 是否支持
     */
    public boolean supports(String businessType) {
        return processorMap.containsKey(businessType);
    }

    /**
     * 获取处理器实例
     *
     * @param businessType 业务类型
     * @param <T> 数据类型
     * @param <R> 请求类型，必须是BaseExportReq的子类
     * @return 处理器实例
     * @throws BusinessException 如果找不到对应的处理器
     */
    @SuppressWarnings("unchecked")
    public <T, R extends BaseExportReq> DataExportProcessor<T, R> getProcessor(String businessType) {
        DataExportProcessor<?, ? extends BaseExportReq> processor = processorMap.get(businessType);
        if (processor == null) {
            throw new BusinessException(ResultCode.NOT_ACCEPTABLE,
                    String.format("不支持的业务类型: %s", businessType));
        }
        try {
            return (DataExportProcessor<T, R>) processor;
        } catch (ClassCastException e) {
            log.error("处理器类型转换失败: businessType={}, processor={}",
                    businessType, processor.getClass().getName(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                    String.format("处理器类型不匹配: %s", businessType));
        }
    }

    /**
     * 获取所有支持的业务类型
     *
     * @return 支持的业务类型集合
     */
    public List<String> getSupportedBusinessTypes() {
        return List.copyOf(processorMap.keySet());
    }
}
