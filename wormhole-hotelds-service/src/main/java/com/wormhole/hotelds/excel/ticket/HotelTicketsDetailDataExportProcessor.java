package com.wormhole.hotelds.excel.ticket;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.hotelds.admin.constant.ExcelConstant;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.resp.TicketAdminListResp;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExcelFormattingProvider;
import com.wormhole.hotelds.excel.ExportContext;
import com.wormhole.hotelds.util.SimpleDateUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

import static com.wormhole.hotelds.admin.constant.ExcelConstant.*;

/**
 * @Author：AI Assistant
 * @Date：2025/09/02
 * @Description：门店工单明细导出处理器
 */
@Slf4j
@Component
public class HotelTicketsDetailDataExportProcessor implements DataExportProcessor<HotelTicketsDetailExportDTO, HotelTicketsDetailExportReq>, ExcelFormattingProvider {

    @Resource
    private HotelDsApiClient hotelDsApiClient;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.HOTEL_TICKETS_DETAIL.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return new ArrayList<>(Arrays.asList(
                FIELD_TICKET_NO,
                FIELD_TICKET_TYPE,
                FIELD_TICKET_POSITION_NAME,
                FIELD_TICKET_GUEST_REQUEST,
                FIELD_TICKET_CONVERSATION_TYPE,
                FIELD_TICKET_HANDLE_METHOD,
                FIELD_TICKET_STATUS,
                FIELD_TICKET_CREATOR,
                FIELD_TICKET_HANDLER,
                FIELD_TICKET_CREATE_TIME,
                FIELD_TICKET_HANDLE_TIME,
                FIELD_TICKET_HANDLE_DURATION,
                FIELD_TICKET_CHAT_RECORD
        ));
    }

    @Override
    public Flux<HotelTicketsDetailExportDTO> queryData(HotelTicketsDetailExportReq request, ExportContext context) {
        try {
            // 解析requestJson为TicketAdminPageReq
            TicketAdminPageReq ticketReq = parseRequestJson(request.getRequestJson());
            
            // 设置分页参数为导出模式
            ticketReq.setCurrent(1);
            ticketReq.setPageSize(2000);

            // 调用HotelDsApiClient获取工单数据
            return hotelDsApiClient.getTicketPageFeign(ticketReq)
                    .flatMapMany(result -> {
                        if (result.isSuccess()) {
                            PageResult<TicketAdminListResp> pageResult = result.getData();
                            if (pageResult != null && pageResult.getRecords() != null) {
                                return Flux.fromIterable(pageResult.getRecords())
                                        .map(this::convertToExportDTO);
                            }
                        }
                        return Flux.empty();
                    })
                    .onErrorResume(e -> {
                        log.error("查询门店工单数据失败", e);
                        return Flux.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "查询工单数据失败: " + e.getMessage()));
                    });
        } catch (Exception e) {
            log.error("解析请求参数失败", e);
            return Flux.error(new BusinessException(ResultCode.INVALID_PARAMETER, "请求参数解析失败: " + e.getMessage()));
        }
    }

    private TicketAdminPageReq parseRequestJson(String requestJson) throws Exception {
        if (StringUtils.isBlank(requestJson)) {
            return new TicketAdminPageReq();
        }
        return objectMapper.readValue(requestJson, TicketAdminPageReq.class);
    }

    private HotelTicketsDetailExportDTO convertToExportDTO(TicketAdminListResp resp) {
        HotelTicketsDetailExportDTO dto = new HotelTicketsDetailExportDTO();

        // 映射字段
        dto.setTicketNo(resp.getTicketNo())
           .setType(resp.getType())
           .setPositionName(resp.getPositionName())
           .setGuestRequest(resp.getGuestRequest())
           .setConversationType(resp.getConversationType())
           .setHandleMethod(resp.getHandleMethod())
           .setStatus(resp.getStatus())
           .setCreator(resp.getCreator())
           .setHandler(resp.getHandler())
           .setCreateTime(resp.getCreateTime())
           .setHandleTime(resp.getHandleTime())
           .setHandleDuration(resp.getHandleDuration())
           .setChatRecord(""); // 聊天记录暂时空置

        return dto;
    }

    @Override
    public List<String> convertToRow(HotelTicketsDetailExportDTO data) {
        Map<String, Function<HotelTicketsDetailExportDTO, String>> extractors = new HashMap<>();
        
        extractors.put(FIELD_TICKET_NO, HotelTicketsDetailExportDTO::getTicketNo);
        extractors.put(FIELD_TICKET_TYPE, HotelTicketsDetailExportDTO::getType);
        extractors.put(FIELD_TICKET_POSITION_NAME, HotelTicketsDetailExportDTO::getPositionName);
        extractors.put(FIELD_TICKET_GUEST_REQUEST, HotelTicketsDetailExportDTO::getGuestRequest);
        extractors.put(FIELD_TICKET_CONVERSATION_TYPE, HotelTicketsDetailExportDTO::getConversationType);
        extractors.put(FIELD_TICKET_HANDLE_METHOD, HotelTicketsDetailExportDTO::getHandleMethod);
        extractors.put(FIELD_TICKET_STATUS, HotelTicketsDetailExportDTO::getStatus);
        extractors.put(FIELD_TICKET_CREATOR, HotelTicketsDetailExportDTO::getCreator);
        extractors.put(FIELD_TICKET_HANDLER, HotelTicketsDetailExportDTO::getHandler);
        extractors.put(FIELD_TICKET_CREATE_TIME, dto -> SimpleDateUtils.formatDateTime(dto.getCreateTime()));
        extractors.put(FIELD_TICKET_HANDLE_TIME, dto -> SimpleDateUtils.formatDateTime(dto.getHandleTime()));
        extractors.put(FIELD_TICKET_HANDLE_DURATION, HotelTicketsDetailExportDTO::getHandleDuration);
        extractors.put(FIELD_TICKET_CHAT_RECORD, HotelTicketsDetailExportDTO::getChatRecord);

        // 按照表头顺序提取数据
        List<String> row = new ArrayList<>();
        for (String header : getExcelHeaders()) {
            Function<HotelTicketsDetailExportDTO, String> extractor = extractors.get(header);
            if (extractor != null) {
                String value = extractor.apply(data);
                row.add(value != null ? value : "");
            } else {
                row.add("");
            }
        }

        return row;
    }

    @Override
    public String getExportFileName(HotelTicketsDetailExportReq request) {
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now());
        return "门店工单明细_" + dateStr + ".xlsx";
    }

    @Override
    public Class<HotelTicketsDetailExportReq> getRequestClass() {
        return HotelTicketsDetailExportReq.class;
    }

    @Override
    public Map<String, Integer> getColumnWidthMap() {
        Map<String, Integer> widthMap = new HashMap<>();
        widthMap.put(FIELD_TICKET_NO, 15);
        widthMap.put(FIELD_TICKET_TYPE, 12);
        widthMap.put(FIELD_TICKET_POSITION_NAME, 15);
        widthMap.put(FIELD_TICKET_GUEST_REQUEST, 30);
        widthMap.put(FIELD_TICKET_CONVERSATION_TYPE, 12);
        widthMap.put(FIELD_TICKET_HANDLE_METHOD, 12);
        widthMap.put(FIELD_TICKET_STATUS, 10);
        widthMap.put(FIELD_TICKET_CREATOR, 12);
        widthMap.put(FIELD_TICKET_HANDLER, 12);
        widthMap.put(FIELD_TICKET_CREATE_TIME, 18);
        widthMap.put(FIELD_TICKET_HANDLE_TIME, 18);
        widthMap.put(FIELD_TICKET_HANDLE_DURATION, 12);
        widthMap.put(FIELD_TICKET_CHAT_RECORD, 25);
        return widthMap;
    }

    @Override
    public Map<String, String> getColumnFormatMap() {
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put(FIELD_TICKET_CREATE_TIME, "date");
        formatMap.put(FIELD_TICKET_HANDLE_TIME, "date");
        return formatMap;
    }
}
