package com.wormhole.hotelds.excel;

import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.storage.file.FileInput;
import lombok.Data;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/4/9 16:48
 * @Description：ProcessContext
 */
@Data
public class ImportContext {

    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * base path
     */
    private String basePath;

    /**
     * 上传文件
     */
    private FileInput fileInput;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 请求头
     */
    private HeaderUtils.HeaderInfo headerInfo;

    /**
     * 开始时间
     */
    private Instant startTime;

    /**
     * 基础统计 - 使用AtomicInteger确保线程安全
     */
    private final AtomicInteger totalCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failedCount = new AtomicInteger(0);

    /**
     * 错误信息收集: <行号, 错误信息>
     * 使用ConcurrentHashMap确保线程安全
     */
    private final ConcurrentMap<Integer, String> rowErrors = new ConcurrentHashMap<>();


    /**
     * 添加行错误信息
     *
     * @param rowNum 行号
     * @param error  错误信息
     */
    public void addRowError(Integer rowNum, String error) {
        rowErrors.put(rowNum, error);
        failedCount.incrementAndGet();
    }

    /**
     * 更新成功计数
     */
    public void incrementSuccessCount() {
        successCount.incrementAndGet();
    }

    /**
     * 设置总数
     */
    public void setTotalCount(int count) {
        totalCount.set(count);
    }

    /**
     * 获取错误摘要信息
     */
    public String getErrorSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("共计上传%d个数据，", totalCount.get()));

        int successNum = successCount.get();
        if (successNum > 0) {
            summary.append(String.format("成功导入%d条，", successNum));
        }

        int failedNum = failedCount.get();
        if (failedNum > 0) {
            summary.append(String.format("失败%d条", failedNum));
        }

        return summary.toString();
    }

    /**
     * 获取总数
     */
    public int getTotalCount() {
        return totalCount.get();
    }

    /**
     * 获取成功数
     */
    public int getSuccessCount() {
        return successCount.get();
    }

    /**
     * 获取失败数
     */
    public int getFailedCount() {
        return failedCount.get();
    }

    /**
     * 获取详细错误信息
     */
    public String getErrorDetails() {
        if (rowErrors.isEmpty()) {
            return "";
        }

        StringBuilder details = new StringBuilder();
        details.append(String.format("\n============ %s导入错误详情 ============\n", businessType));

        // 按行号排序展示错误信息
        rowErrors.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    int rowNum = entry.getKey();
                    String error = entry.getValue();

                    // 对于第0行（非特定行的错误），使用特殊格式
                    if (rowNum == 0) {
                        details.append("全局错误：").append(error).append("\n");
                    } else {
                        details.append(String.format("第%d行：%s\n", rowNum, error));
                    }
                });

        details.append("==========================================\n");

        // 添加统计信息
        details.append(String.format("总计：%d条错误\n", failedCount.get()));

        return details.toString();
    }

    /**
     * 获取简单错误信息（不包含详细格式）
     */
    public String getSimpleErrors() {
        if (rowErrors.isEmpty()) {
            return "";
        }

        return rowErrors.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> String.format("第%d行：%s", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining("\n"));
    }
}

