package com.wormhole.hotelds.excel.hotel;

import com.wormhole.hotelds.core.model.entity.HdsHotelInfoEntity;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/5/9 10:55
 * @Description：HotelExportDTO
 */
@Data
public class HotelExportDTO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 门店编码
     */
    private String hotelCode;

    /**
     * 门店logo
     */
    private String hotelLogo;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户简称
     */
    private String merchantShortName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 地址
     */
    private String address;

    /**
     * 高德坐标经度
     */
    private String longitude;

    /**
     * 高德坐标纬度
     */
    private String latitude;

    /**
     * 总房间数
     */
    private Integer totalRoom;

    /**
     * 主要负责人
     */
    private String mainPerson;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 携程EBK链接
     */
    private String ctripEbkUrl;

    /**
     * 将实体转换为VO
     */
    public static HotelExportDTO toVo(HdsHotelInfoEntity entity) {
        if (entity == null) {
            return null;
        }
        HotelExportDTO dto = new HotelExportDTO();
        BeanUtils.copyProperties(entity, dto);
        dto.setLongitude(entity.getGaodeLongitude());
        dto.setLatitude(entity.getGaodeLatitude());
        return dto;
    }
}
