package com.wormhole.hotelds.excel.hotel;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.excel.BaseExportReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/9 10:11
 * @Description：HotelExportReq
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelExportReq extends BaseExportReq {

    /**
     * 商户名称/商户简称
     */
    private String merchantName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 协议状态 [0未签约 1合作中 2已过期 3已解约]
     */
    private Integer contractStatus;

    /**
     * 服务状态 [0正常运行 1即将过期 2服务暂停 3服务终止]
     */
    private Integer serviceStatus;

    /**
     * 门店编号
     */
    private String hotelCode;
}
