package com.wormhole.hotelds.excel;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.IdUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.TaskStatusEnum;
import com.wormhole.hotelds.admin.repository.TaskRepository;
import com.wormhole.hotelds.config.CommonProperties;
import com.wormhole.hotelds.core.model.entity.HdsTaskEntity;
import com.wormhole.hotelds.storage.FileService;
import com.wormhole.hotelds.storage.config.BucketProperties;
import com.wormhole.hotelds.storage.file.FileInput;
import com.wormhole.hotelds.storage.model.FileUploadResultDTO;
import com.wormhole.storage.model.ObjectMetaData;
import com.wormhole.storage.model.StorageObject;
import com.wormhole.storage.model.StorageParams;
import com.wormhole.storage.service.CosObjectStorageService;
import com.wormhole.storage.service.OssObjectStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.wormhole.hotelds.admin.constant.ExcelConstant.ALLOWED_EXTENSIONS;
import static com.wormhole.hotelds.admin.constant.ExcelConstant.MAX_FILE_SIZE;

/**
 * @Author：flx
 * @Date：2025/4/1 09:13
 * @Description：任务管理器
 */
@Slf4j
@Component
public class TaskManager {

    /**
     * Excel导入任务类型
     */
    private static final Integer TASK_TYPE_EXCEL_IMPORT = 1;

    /**
     * Excel导出任务类型
     */
    private static final Integer TASK_TYPE_EXCEL_EXPORT = 2;

    @Resource
    private TaskRepository taskRepository;

    @Resource
    private BucketProperties bucketProperties;

    @Resource
    private OssObjectStorageService ossObjectStorageService;

    @Resource
    private CosObjectStorageService cosObjectStorageService;

    @Resource
    private FileService fileService;

    @Resource
    private CommonProperties commonProperties;

    // 模板配置Map：业务类型 -> 文件名
    private static final Map<String, String> TEMPLATE_FILE_NAMES = new ConcurrentHashMap<>();

    // 初始化模板文件名
    static {
        TEMPLATE_FILE_NAMES.put(BussinessTypeEnum.HOTEL.getBusinessType(), "门店导入模板.xlsx");
        TEMPLATE_FILE_NAMES.put(BussinessTypeEnum.ROOM.getBusinessType(), "房间导入模板.xlsx");
        TEMPLATE_FILE_NAMES.put(BussinessTypeEnum.MERCHANT.getBusinessType(), "商户导入模板.xlsx");
        TEMPLATE_FILE_NAMES.put(BussinessTypeEnum.DEVICE.getBusinessType(), "设备导入模板.xlsx");
        TEMPLATE_FILE_NAMES.put(BussinessTypeEnum.DEVICE_POSITION.getBusinessType(), "设备位置导入模板.xlsx");
        TEMPLATE_FILE_NAMES.put(BussinessTypeEnum.EMPLOYEE.getBusinessType(), "员工导入模板.xlsx");
    }

    /**
     * 创建导入任务
     *
     * @param headerInfo
     * @param fileName
     * @param businessType
     * @return
     */
    public Mono<Integer> createImportTask(HeaderUtils.HeaderInfo headerInfo, String fileName,
                                          String businessType) {
        HdsTaskEntity task = new HdsTaskEntity();
        task.setTaskName(generateTaskName(businessType, TASK_TYPE_EXCEL_IMPORT, headerInfo.getUsername()));
        task.setTaskType(TaskTypeEnum.IMPORT.getCode());
        task.setStatus(TaskStatusEnum.PENDING.getCode());
        task.setCreatedAt(LocalDateTime.now());
        task.setCreatedBy(headerInfo.getUserId());
        task.setCreatedByName(headerInfo.getUsername());
        task.setMessage("任务创建中");
        task.setBusinessType(businessType);
        Map<String, Object> taskParams = new HashMap<>();
        taskParams.put("fileName", fileName);
        taskParams.put("source", "web");
        taskParams.put("createdTime", LocalDateTime.now().toString());

        task.setHotelCode(headerInfo.getHotelCode());
        task.setParams(JacksonUtils.writeValueAsString(taskParams));
        return taskRepository.save(task)
                .map(savedTask -> {
                    log.info("创建导入任务成功: ID={}", savedTask.getId());
                    return savedTask.getId();
                });
    }

    public Mono<Boolean> updateTaskInfo(Integer taskId, String key, String value) {
        return taskRepository.findById(taskId)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "任务不存在")))
                .flatMap(task -> {
                    Map<String, Object> stringObjectMap = JacksonUtils.readValue(task.getParams());
                    stringObjectMap.put(key, value);
                    task.setParams(JacksonUtils.writeValueAsString(stringObjectMap));
                    return taskRepository.save(task)
                            .flatMap(updatedTask -> {
                                log.info("更新任务信息成功: ID={}", updatedTask.getId());
                                return Mono.just(true);
                            });
                });
    }

    /**
     * 处理任务成功场景
     * 根据处理结果判断是全部成功还是部分成功
     */
    public Mono<Boolean> handleTaskSuccess(ImportContext context) {
        Integer taskId = context.getTaskId();
        int totalCount = context.getTotalCount();
        int successCount = context.getSuccessCount();
        int failedCount = context.getFailedCount();
        String resultMessage = String.format(
                "处理完成。总计: %d, 成功: %d, 失败: %d",
                totalCount,
                successCount,
                failedCount
        );

        if (context.getFailedCount() == 0) {
            // 全部成功
            return updateTaskToSuccess(taskId, totalCount, successCount, failedCount, resultMessage)
                    .thenReturn(true);
        } else if (context.getSuccessCount() > 0) {
            // 部分成功
            return updateTaskToPartialSuccess(
                    taskId,
                    totalCount,
                    successCount,
                    failedCount,
                    resultMessage,
                    context.getErrorSummary(),
                    context.getErrorDetails()
            ).thenReturn(true);
        } else {
            // 全部失败
            return updateTaskToFailed(
                    taskId,
                    context.getTotalCount(),
                    0,
                    context.getFailedCount(),
                    resultMessage,
                    context.getErrorSummary(),
                    context.getErrorDetails()
            ).thenReturn(true);
        }
    }

    /**
     * 处理任务失败场景
     * 用于处理异常情况
     */
    public <T> Mono<T> handleTaskError(String errorMessage,
                                       ImportContext context,
                                       ResultCode resultCode,
                                       Throwable error) {
        Integer taskId = context.getTaskId();
        log.info("任务处理失败: taskId={}, error={}", taskId, errorMessage, error);

        int totalCount = context.getTotalCount();
        int successCount = context.getSuccessCount();
        int failedCount = context.getFailedCount();

        String resultMessage = String.format(
                "处理完成。总计: %d, 成功: %d, 失败: %d",
                totalCount,
                successCount,
                failedCount
        );

        String errorSummary = context.getErrorSummary();
        String errorDetails = context.getErrorDetails();

        return updateTaskToFailed(
                taskId,
                totalCount,
                successCount,
                failedCount,
                resultMessage,
                StringUtils.isBlank(errorSummary) ? errorMessage : errorSummary,
                StringUtils.isBlank(errorDetails) ? errorMessage : errorDetails
        ).then(Mono.error(new BusinessException(
                resultCode, errorMessage)));
    }


    /**
     * 更新任务为失败状态
     *
     * @param taskId       任务ID
     * @param errorMessage 错误消息
     * @return 更新后的任务实体
     */
    public Mono<Void> updateTaskToFailed(Integer taskId,
                                         Integer totalRows,
                                         Integer successRows,
                                         Integer failedRows,
                                         String resultMessage,
                                         String errorMessage,
                                         String errorDetails) {

        return updateTaskStatus(
                taskId,
                TaskStatusEnum.FAILED.getCode(),
                totalRows,
                successRows,
                failedRows,
                resultMessage,
                errorMessage,
                errorDetails
        );
    }

    /**
     * 从OSS下载文件
     */
    public Mono<File> downloadFileFromOss(String objectKey) {
        return Mono.fromCallable(() -> Files.createTempFile(IdUtils.generateDateTimePrefixId(), ".tmp").toFile())
                .flatMap(tempFile -> {
                    StorageParams storageParams = StorageParams.builder()
                            .bucketName(bucketProperties.getCommonBucketName())
                            .objectKey(objectKey)
                            .build();

                    return cosObjectStorageService.getObject(storageParams, tempFile)
                            .map(metaData -> {
                                log.info("从OSS下载文件: {}, 大小: {}",
                                        objectKey, metaData.getContentLength());
                                return tempFile;
                            })
                            .publishOn(Schedulers.boundedElastic())
                            .onErrorResume(e -> {
                                log.error("从OSS下载文件失败: {}", objectKey, e);
                                try {
                                    Files.deleteIfExists(tempFile.toPath());
                                } catch (IOException ex) {
                                    log.warn("下载失败后删除临时文件失败", ex);
                                }
                                return Mono.empty();
                            });
                })
                .subscribeOn(Schedulers.boundedElastic());
    }


    /**
     * 上传文件到OSS
     */
    public Mono<FileUploadResultDTO> uploadFileToOss(String basePath, FileInput fileInput) {
        log.info("上传文件到OSS: {}", fileInput.getFileName());
        return fileService.upload(basePath, fileInput);
    }

    /**
     * 验证Excel表头是否包含必要字段
     */
    public Set<String> validateHeaders(Map<Integer, String> headers, Set<String> requiredFields) {
        if (headers == null || requiredFields == null) {
            return Collections.emptySet();
        }

        // 检查必要字段是否都存在
        Set<String> headerValues = new HashSet<>(headers.values());
        Set<String> missingFields = new HashSet<>(requiredFields);
        missingFields.removeAll(headerValues);
        return missingFields;
    }

    /**
     * 根据业务类型获取模板文件名
     *
     * @param businessType 业务类型
     * @return 模板文件名
     */
    public String getTemplateFileName(String businessType) {
        String fileName = TEMPLATE_FILE_NAMES.get(businessType);
        if (fileName == null) {
            throw new BusinessException(ResultCode.NOT_ACCEPTABLE,
                    String.format("不支持的业务类型模板: 业务类型=%s", businessType));
        }
        return fileName;
    }

    /**
     * 获取对象存储键
     *
     * @param businessType 业务类型
     * @return 对象存储键
     */
    private String getObjectKey(String businessType, HeaderUtils.HeaderInfo headerInfo) {
        BussinessTypeEnum bussinessTypeEnum = BussinessTypeEnum.getByBusinessType(businessType);
        if (bussinessTypeEnum == null) {
            throw new BusinessException(ResultCode.NOT_ACCEPTABLE,
                    String.format("不支持的业务类型: 业务类型=%s", businessType));
        }
        if (bussinessTypeEnum == BussinessTypeEnum.ROOM) {
            return commonProperties.getRoomObjectKey();
        }
        if (bussinessTypeEnum == BussinessTypeEnum.DEVICE_POSITION) {
            return commonProperties.getDevicePositionObjectKey();
        }
        if (bussinessTypeEnum == BussinessTypeEnum.DEVICE) {
            return commonProperties.getDeviceObjectKey();
        }
        if (bussinessTypeEnum == BussinessTypeEnum.EMPLOYEE) {
            if (StringUtils.isBlank(headerInfo.getHotelCode())) {
                return commonProperties.getEmployeeObjectKey();
            } else {
                return commonProperties.getHotelEmployeeObjectKey();
            }
        }
        return commonProperties.getRoomObjectKey();
    }

    /**
     * 获取对象元数据
     *
     * @param objectKey 对象键
     * @return 对象元数据Mono
     */
    public Mono<ObjectMetaData> getObjectMetadata(String objectKey) {
        String bucketName = bucketProperties.getCommonBucketName();

        StorageParams storageParams = StorageParams.builder()
                .bucketName(bucketName)
                .objectKey(objectKey)
                .build();

        return cosObjectStorageService.getObjectMetadata(storageParams);
    }

    /**
     * 获取存储对象
     *
     * @param businessType 业务类型
     * @return 存储对象Mono
     */
    public Mono<StorageObject> getTemplateObject(String businessType, HeaderUtils.HeaderInfo headerInfo) {
        // 获取ObjectKey
        String objectKey = getObjectKey(businessType, headerInfo);
        String bucketName = bucketProperties.getCommonBucketName();

        StorageParams storageParams = StorageParams.builder()
                .bucketName(bucketName)
                .objectKey(objectKey)
                .build();

        return cosObjectStorageService.getObject(storageParams);
    }

    /**
     * 获取模板元数据
     *
     * @param businessType 业务类型
     * @return 模板元数据Mono
     */
    public Mono<ObjectMetaData> getTemplateMetadata(String businessType, HeaderUtils.HeaderInfo headerInfo) {
        String objectKey = getObjectKey(businessType, headerInfo);
        return getObjectMetadata(objectKey);
    }

    /**
     * 生成任务名称
     *
     * @param businessType 业务类型
     * @param taskType     任务类型(1-导入，2-导出)
     * @param suffix       附加信息(可选)
     * @return 格式化的任务名称
     */
    public String generateTaskName(String businessType, Integer taskType, String suffix) {
        // 获取业务类型对应的中文名称
        String businessName = getBusinessName(businessType);

        // 获取操作类型
        String operationType = taskType == 1 ? "导入" : "导出";

        // 生成时间标识 (格式：yyyyMMddxxx，xxx是序号)
        String timeId = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        return suffix + operationType + businessName + "任务 - " + timeId;
    }

    /**
     * 获取业务类型的中文名称
     */
    private String getBusinessName(String businessType) {
        return switch (businessType) {
            case "hotel" -> "门店信息";
            case "room" -> "房间信息";
            case "device" -> "设备信息";
            case "brand" -> "品牌信息";
            case "merchant" -> "商户信息";
            case "employee" -> "员工信息";
            default -> businessType;
        };
    }

    /**
     * 文件验证 - 使用临时文件
     */
    public Mono<Void> validateFile(FileInput fileInput, String businessType) {
        return Mono.defer(() -> {
            // 1. 基础验证
            if (fileInput == null) {
                return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                        "上传文件不能为空"));
            }

            String fileName = fileInput.getFileName();
            if (StringUtils.isBlank(fileName)) {
                return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                        "文件名不能为空"));
            }

            // 2. 验证文件扩展名
            String extension = getFileExtension(fileName).toLowerCase();
            if (!ALLOWED_EXTENSIONS.contains(extension)) {
                return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                        "不支持的文件格式，仅支持.xlsx或.xls格式"));
            }

            // 3. 在boundedElastic线程池中创建临时文件并验证
            return Mono.fromCallable(() -> Files.createTempFile(IdUtils.generateDateTimePrefixId(businessType), extension))
                    .subscribeOn(Schedulers.boundedElastic())
                    .flatMap(tempPath -> fileInput.transferToPath(tempPath)
                            .flatMap(path -> Mono.fromCallable(() -> {
                                try {
                                    long fileSize = Files.size(path);
                                    if (fileSize > MAX_FILE_SIZE) {
                                        throw new BusinessException(ResultCode.INVALID_PARAMETER,
                                                "文件大小不能超过5MB");
                                    }
                                    return path;
                                } finally {
                                    try {
                                        Files.deleteIfExists(path);
                                    } catch (IOException e) {
                                        log.warn("删除临时文件失败", e);
                                    }
                                }
                            }).subscribeOn(Schedulers.boundedElastic()))
                            .onErrorMap(IOException.class, e -> {
                                log.error("文件处理失败", e);
                                return new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                        "文件处理失败");
                            }))
                    .then();
        });
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(lastDotIndex);
        }
        return "";
    }

    /**
     * 更新任务为完全成功状态
     */
    public Mono<Void> updateTaskToSuccess(Integer taskId, Integer totalCount, Integer successCount, Integer failedCount, String resultMessage) {
        return updateTaskStatus(
                taskId,
                TaskStatusEnum.SUCCESS.getCode(),
                totalCount,
                successCount,
                failedCount,
                resultMessage,
                null,
                null
        );
    }

    /**
     * 更新任务为部分成功状态
     */
    public Mono<Void> updateTaskToPartialSuccess(
            Integer taskId,
            Integer totalCount,
            Integer successCount,
            Integer failedCount,
            String resultMessage,
            String errorSummary,
            String errorDetails) {
        // 构建错误详情JSON
        return updateTaskStatus(
                taskId,
                TaskStatusEnum.PARTIAL_SUCCESS.getCode(),
                totalCount,
                successCount,
                failedCount,
                resultMessage,
                errorSummary,
                errorDetails
        );
    }

    /**
     * 更新任务状态
     */
    private Mono<Void> updateTaskStatus(
            Integer taskId,
            Integer status,
            Integer totalCount,
            Integer successCount,
            Integer failedCount,
            String resultMessage,
            String errorSummary,
            String errorDetails) {

        return taskRepository.findById(taskId)
                .flatMap(task -> {
                    // 更新任务状态
                    task.setStatus(status);
                    task.setTotalRows(totalCount);
                    task.setSuccessRows(successCount);
                    task.setFailedRows(failedCount);
                    task.setMessage(resultMessage);
                    task.setErrorSummary(errorSummary);
                    task.setErrorDetails(errorDetails);
                    task.setUpdatedAt(LocalDateTime.now());
                    return taskRepository.save(task);
                })
                .doOnSuccess(task ->
                        log.info("任务状态更新成功: taskId={}, status={}", taskId, status))
                .doOnError(error ->
                        log.error("任务状态更新失败: taskId={}, error={}", taskId, error.getMessage()))
                .then();
    }
}
