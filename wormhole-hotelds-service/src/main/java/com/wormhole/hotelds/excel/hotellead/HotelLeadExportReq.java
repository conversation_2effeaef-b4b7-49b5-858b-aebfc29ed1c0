package com.wormhole.hotelds.excel.hotellead;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.excel.BaseExportReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/5/29 16:40
 * @Description：HotelLeadExportReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelLeadExportReq extends BaseExportReq {

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 门店线索id
     */
    private Integer userLeadId;
}
