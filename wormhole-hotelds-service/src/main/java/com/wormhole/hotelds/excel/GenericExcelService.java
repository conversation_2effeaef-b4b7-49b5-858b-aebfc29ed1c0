package com.wormhole.hotelds.excel;

import com.wormhole.common.result.PageResult;
import com.wormhole.hotelds.admin.model.req.TemplateDownloadReq;
import com.wormhole.hotelds.storage.file.FileInput;
import com.wormhole.hotelds.admin.model.req.TaskSearchReq;
import com.wormhole.hotelds.admin.model.vo.TaskVO;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/1 09:08
 * @Description：通用excel导入服务接口
 */
public interface GenericExcelService {

    /**
     * 导入Excel数据
     *
     * @param basePath     基础路径
     * @param fileInput    文件输入
     * @param businessType 业务类型
     * @return 任务ID
     */
    Mono<Integer> importExcel(String basePath, FileInput fileInput, String businessType, String businessId);


    /**
     * excel导出
     *
     * @param req
     * @return
     */
    Mono<ResponseEntity<InputStreamResource>> exportExcel(BaseExportReq req);

    /**
     * 下载Excel导入模板
     *
     * @param templateDownloadReq
     * @return
     */
    Mono<ResponseEntity<InputStreamResource>> downloadTemplate(TemplateDownloadReq templateDownloadReq);

    /**
     * 查询任务列表
     *
     * @param taskSearchReq
     * @return
     */
    Mono<List<TaskVO>> search(TaskSearchReq taskSearchReq);


    /**
     * 查询任务数量
     *
     * @param taskSearchReq
     * @return
     */
    Mono<Long> count(TaskSearchReq taskSearchReq);

    /**
     * 根据任务ID查询任务状态
     *
     * @param taskId
     * @return
     */
    Mono<TaskVO> queryTaskById(Integer taskId);
}
