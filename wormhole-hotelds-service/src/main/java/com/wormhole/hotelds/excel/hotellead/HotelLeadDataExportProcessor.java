package com.wormhole.hotelds.excel.hotellead;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.dto.LeadProgressDTO;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.ExportTypeEnum;
import com.wormhole.hotelds.admin.repository.HdsEmployeeRepository;
import com.wormhole.hotelds.admin.repository.UserHotelLeadMappingRepository;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExcelFormattingProvider;
import com.wormhole.hotelds.excel.ExportContext;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wormhole.hotelds.admin.constant.ExcelConstant.*;

/**
 * @Author：flx
 * @Date：2025/5/29 16:38
 * @Description：HotelLeadDataExportProcessor
 */
@Slf4j
@Component
public class HotelLeadDataExportProcessor implements DataExportProcessor<HotelLeadExportDTO, HotelLeadExportReq>, ExcelFormattingProvider {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.HOTEL_LEAD.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return new ArrayList<>(Arrays.asList(
                FIELD_HOTEL_LEAD_ID,
                FIELD_HOTEL_LEAD_CODE,
                FIELD_HOTEL_LEAD_HOTEL_NAME,
                FIELD_HOTEL_LEAD_EBK_URL,
                FIELD_HOTEL_LEAD_CREATE_BY,
                FIELD_HOTEL_LEAD_BIND_EBK,
                FIELD_HOTEL_LEAD_SET_RIVAL_HOTEL,
                FIELD_HOTEL_LEAD_SYNC_REVIEW,
                FIELD_HOTEL_LEAD_SYNC_STATIC_INFO,
                FIELD_HOTEL_LEAD_SYNC_RIVAL_REVIEW,
                FIELD_HOTEL_LEAD_SYNC_RIVAL_PRICE,
                FIELD_HOTEL_LEAD_GENERATE_REPORT,
                FIELD_HOTEL_LEAD_CREATED_AT,
                FIELD_HOTEL_LEAD_COMPLETE_TIME,
                FIELD_HOTEL_LEAD_DURATION
        ));
    }

    @Override
    public Flux<HotelLeadExportDTO> queryData(HotelLeadExportReq request, ExportContext context) {
        return buildSearchCriteria(request)
                .flatMapMany(criteria -> {
                    // 设置排序方式，默认按创建时间降序
                    Sort sort = Sort.by(Sort.Direction.DESC, HdsHotelLeadFieldEnum.created_at.name());

                    // 执行查询
                    return r2dbcEntityTemplate.select(Query.query(criteria)
                                    .sort(sort), HdsHotelLeadEntity.class)
                            .collectList()
                            .flatMapMany(hotelLeadEntities -> {
                                if (hotelLeadEntities.isEmpty()) {
                                    return Flux.empty();
                                }

                                // 提取创建者ID
                                Set<Integer> creatorIds = extractHotelLeadCreatorIds(hotelLeadEntities);

                                // 批量查询员工信息
                                return fetchEmployeeMap(creatorIds)
                                        .flatMapMany(employeeMap -> Flux.fromIterable(hotelLeadEntities)
                                                .map(entity -> buildHotelLeadExportDTO(entity, employeeMap)));
                            });
                });
    }

    /**
     * 构建门店线索导出DTO
     */
    private HotelLeadExportDTO buildHotelLeadExportDTO(
            HdsHotelLeadEntity entity,
            Map<Integer, HdsEmployeeEntity> employeeMap) {

        HotelLeadExportDTO dto = new HotelLeadExportDTO();

        // 设置基本信息
        dto.setId(entity.getId());
        dto.setLeadCode(entity.getLeadCode());
        dto.setHotelName(entity.getHotelName());
        dto.setCtripEbkUrl(entity.getCtripEbkUrl());

        // 设置创建者手机号
        if (StringUtils.isNotBlank(entity.getCreatedBy())) {
            try {
                Integer creatorId = Integer.parseInt(entity.getCreatedBy());
                HdsEmployeeEntity creator = employeeMap.get(creatorId);
                if (creator != null) {
                    dto.setCreateBy(creator.getMobile());
                }
            } catch (NumberFormatException e) {
                log.warn("无法解析创建者ID: {}", entity.getCreatedBy());
            }
        }


        // 解析并设置进度信息
        LeadProgressDTO leadProgressDTO = parseProgressJson(entity.getProgressJson());
        int completedTasks = countCompletedTasks(leadProgressDTO);
//        dto.setProgress(String.format("%d/6", completedTasks));

        // 设置是否完成各个任务
        dto.setBindEbk(StringUtils.isNotBlank(leadProgressDTO.getBindEbk()));
        dto.setSetRivalHotel(StringUtils.isNotBlank(leadProgressDTO.getSetRivalHotel()));
        dto.setSyncReview(StringUtils.isNotBlank(leadProgressDTO.getSyncReview()));
        dto.setSyncStaticInfo(StringUtils.isNotBlank(leadProgressDTO.getSyncStaticInfo()));
        dto.setSyncRivalReview(StringUtils.isNotBlank(leadProgressDTO.getSyncRivalReview()));
        dto.setGenerateReport(StringUtils.isNotBlank(leadProgressDTO.getGenerateReport()));

        // 设置时间信息
//        dto.setCreatedAt(SimpleDateUtils.formatLocalDateTimeToDateHour(entity.getCreatedAt()));
        if (entity.getCompleteTime() != null) {
            dto.setCompleteTime(SimpleDateUtils.formatLocalDateTimeToDateHour(entity.getCompleteTime()));
            // 计算创建时长
            dto.setCreationDurationFormatted(calculateCreationDuration(entity));
        }
        dto.setCreatedAt(SimpleDateUtils.formatLocalDateTimeToDateHour(entity.getCreatedAt()));

        return dto;
    }

    /**
     * 计算已完成任务数量
     */
    private int countCompletedTasks(LeadProgressDTO leadProgressDTO) {
        int count = 0;
        if (StringUtils.isNotBlank(leadProgressDTO.getBindEbk())) count++;
        if (StringUtils.isNotBlank(leadProgressDTO.getSetRivalHotel())) count++;
        if (StringUtils.isNotBlank(leadProgressDTO.getSyncReview())) count++;
        if (StringUtils.isNotBlank(leadProgressDTO.getSyncStaticInfo())) count++;
        if (StringUtils.isNotBlank(leadProgressDTO.getSyncRivalReview())) count++;
        if (StringUtils.isNotBlank(leadProgressDTO.getGenerateReport())) count++;
        return count;
    }

    /**
     * 解析进度JSON
     */
    private LeadProgressDTO parseProgressJson(String progressJson) {
        if (StringUtils.isBlank(progressJson)) {
            return new LeadProgressDTO();
        }
        try {
            return JacksonUtils.readValue(progressJson, LeadProgressDTO.class);
        } catch (Exception e) {
            log.error("解析进度JSON失败，将创建新对象: {}", e.getMessage());
            return new LeadProgressDTO();
        }
    }

    /**
     * 提取门店线索创建人id集合
     */
    private Set<Integer> extractHotelLeadCreatorIds(List<HdsHotelLeadEntity> entities) {
        return entities.stream()
                .map(HdsHotelLeadEntity::getCreatedBy)
                .filter(StringUtils::isNotBlank)
                .map(id -> {
                    try {
                        return Integer.parseInt(id);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 获取员工信息映射
     */
    private Mono<Map<Integer, HdsEmployeeEntity>> fetchEmployeeMap(Set<Integer> employeeIds) {
        if (employeeIds.isEmpty()) {
            return Mono.just(Collections.emptyMap());
        }

        return hdsEmployeeRepository.findByIdIn(employeeIds)
                .collectList()
                .map(employees -> employees.stream()
                        .collect(Collectors.toMap(
                                HdsEmployeeEntity::getId,
                                employee -> employee,
                                (e1, e2) -> e1
                        )))
                .defaultIfEmpty(Collections.emptyMap())
                .onErrorResume(e -> {
                    log.error("获取员工信息失败", e);
                    return Mono.just(Collections.emptyMap());
                });
    }

    /**
     * 计算创建时长
     */
    private String calculateCreationDuration(HdsHotelLeadEntity entity) {
        if (entity.getCreatedAt() != null && entity.getCompleteTime() != null) {
            // 计算创建时长（分钟）
            LocalDateTime startTime = entity.getCreatedAt();
            LocalDateTime endTime = entity.getCompleteTime();

            // 计算两个时间之间的分钟差
            long totalMinutes = Duration.between(startTime, endTime).toMinutes();

            // 格式化为小时，保留一位小数
            return formatDuration(totalMinutes);
        } else {
            return null;
        }
    }

    /**
     * 格式化时长为"X.X小时"，保留一位小数
     */
    private String formatDuration(long totalMinutes) {
        // 将分钟转换为小时（浮点数）
        double hours = totalMinutes / 60.0;

        // 格式化为一位小数
        return String.format("%.1f小时", hours);
    }

    /**
     * 构建搜索条件
     */
    private Mono<Criteria> buildSearchCriteria(HotelLeadExportReq req) {
        // 根据导出类型选择不同的查询策略
        ExportTypeEnum exportType = ExportTypeEnum.getByCode(req.getExportType());

        Criteria baseCriteria = Criteria.empty();

        switch (exportType) {
            case ALL:
                // 全部导出 - 使用空条件
                return Mono.just(baseCriteria);
            case CURRENT:
                // 当前页导出 - 根据IDs列表
                if (CollectionUtils.isEmpty(req.getCurrentIds())) {
                    // 如果ID列表为空，返回一个永远为false的条件，确保不会有数据
                    return Mono.just(Criteria.where(HdsHotelLeadFieldEnum.id.name()).is(-1));
                }
                return Mono.just(Criteria.where(HdsHotelLeadFieldEnum.id.name()).in(req.getCurrentIds()));
            default:
                // 筛选导出 - 使用过滤条件
                return buildFilterCriteria(req);
        }
    }

    /**
     * 构建筛选查询条件
     */
    private Mono<Criteria> buildFilterCriteria(HotelLeadExportReq req) {
        // 基础条件
        Criteria baseCriteria = buildBaseCriteria(req);

        return preFilterByContactPhone(req, baseCriteria)
                .flatMap(criteria -> preFilterByUserLeadId(req, criteria));
    }

    private Criteria buildBaseCriteria(HotelLeadExportReq req) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(req.getHotelName())) {
            criteria = criteria.and(HdsHotelLeadFieldEnum.hotel_name.name()).like("%" + req.getHotelName() + "%");
        }

        return criteria;
    }

    private Mono<Criteria> preFilterByContactPhone(HotelLeadExportReq req, Criteria baseCriteria) {
        if (StringUtils.isBlank(req.getMobile())) {
            return Mono.just(baseCriteria);
        }
        Criteria employeeCriteria = Criteria.where(HdsEmployeeFieldEnum.mobile.name()).is(req.getMobile());

        return r2dbcEntityTemplate.select(HdsEmployeeEntity.class)
                .matching(Query.query(employeeCriteria))
                .all()
                .map(HdsEmployeeEntity::getId)
                .collectList()
                .map(employeeIds -> {
                    if (CollectionUtils.isEmpty(employeeIds)) {
                        return Criteria.where(HdsHotelLeadFieldEnum.created_by.name()).is("-1");
                    }
                    List<String> employeeIdStrings = employeeIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.toList());
                    // 将酒店编码添加到查询条件中
                    return baseCriteria.and(HdsHotelLeadFieldEnum.created_by.name()).in(employeeIdStrings);
                });
    }

    private Mono<Criteria> preFilterByUserLeadId(HotelLeadExportReq req, Criteria baseCriteria) {
        if (Objects.isNull(req.getUserLeadId())) {
            return Mono.just(baseCriteria);
        }
        Criteria userLeadCriteria = Criteria.where(HdsUserHotelLeadMappingFieldEnum.user_lead_id.name()).is(req.getUserLeadId());

        return r2dbcEntityTemplate.select(HdsUserHotelLeadMappingEntity.class)
                .matching(Query.query(userLeadCriteria))
                .all()
                .map(HdsUserHotelLeadMappingEntity::getHotelLeadId)
                .collectList()
                .map(hotelLeadIds -> {
                    if (CollectionUtils.isEmpty(hotelLeadIds)) {
                        return Criteria.where(HdsHotelLeadFieldEnum.id.name()).is("-1");
                    }
                    // 将酒店编码添加到查询条件中
                    return baseCriteria.and(HdsHotelLeadFieldEnum.id.name()).in(hotelLeadIds);
                });
    }

    /**
     * 将数据转成excel行
     */
    @Override
    public List<String> convertToRow(HotelLeadExportDTO data) {
        // 定义表头到数据提取器的映射
        Map<String, Function<HotelLeadExportDTO, String>> extractors = new LinkedHashMap<>();
        extractors.put(FIELD_HOTEL_LEAD_ID, dto -> dto.getId() != null ? String.valueOf(dto.getId()) : "");
        extractors.put(FIELD_HOTEL_LEAD_CODE, HotelLeadExportDTO::getLeadCode);
        extractors.put(FIELD_HOTEL_LEAD_HOTEL_NAME, HotelLeadExportDTO::getHotelName);
        extractors.put(FIELD_HOTEL_LEAD_EBK_URL, HotelLeadExportDTO::getCtripEbkUrl);
        extractors.put(FIELD_HOTEL_LEAD_CREATE_BY, HotelLeadExportDTO::getCreateBy);
        extractors.put(FIELD_HOTEL_LEAD_BIND_EBK, dto -> dto.isBindEbk() ? "是" : "否");
        extractors.put(FIELD_HOTEL_LEAD_SET_RIVAL_HOTEL, dto -> dto.isSetRivalHotel() ? "是" : "否");
        extractors.put(FIELD_HOTEL_LEAD_SYNC_REVIEW, dto -> dto.isSyncReview() ? "是" : "否");
        extractors.put(FIELD_HOTEL_LEAD_SYNC_STATIC_INFO, dto -> dto.isSyncStaticInfo() ? "是" : "否");
        extractors.put(FIELD_HOTEL_LEAD_SYNC_RIVAL_REVIEW, dto -> dto.isSyncRivalReview() ? "是" : "否");
        extractors.put(FIELD_HOTEL_LEAD_SYNC_RIVAL_PRICE, dto -> dto.isSyncRivalPrice() ? "是" : "否");
        extractors.put(FIELD_HOTEL_LEAD_GENERATE_REPORT, dto -> dto.isGenerateReport() ? "是" : "否");
        extractors.put(FIELD_HOTEL_LEAD_CREATED_AT, HotelLeadExportDTO::getCreatedAt);
        extractors.put(FIELD_HOTEL_LEAD_COMPLETE_TIME, HotelLeadExportDTO::getCompleteTime);
        extractors.put(FIELD_HOTEL_LEAD_DURATION, HotelLeadExportDTO::getCreationDurationFormatted);

        // 按照表头顺序提取数据
        List<String> row = new ArrayList<>();
        for (String header : getExcelHeaders()) {
            Function<HotelLeadExportDTO, String> extractor = extractors.get(header);
            if (extractor != null) {
                String value = extractor.apply(data);
                row.add(value != null ? value : "");
            } else {
                // 未知表头，添加空值
                row.add("");
            }
        }

        return row;
    }

    /**
     * 获取导出文件名
     */
    @Override
    public String getExportFileName(HotelLeadExportReq request) {
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now());
        return "门店线索列表_" + dateStr + ".xlsx";
    }

    /**
     * 获取请求类
     */
    @Override
    public Class<HotelLeadExportReq> getRequestClass() {
        return HotelLeadExportReq.class;
    }

    @Override
    public Map<String, Integer> getColumnWidthMap() {
        Map<String, Integer> widthMap = new HashMap<>();
        widthMap.put(FIELD_HOTEL_LEAD_CODE, 15);
        widthMap.put(FIELD_HOTEL_LEAD_HOTEL_NAME, 20);
        widthMap.put(FIELD_HOTEL_LEAD_EBK_URL, 25);
        widthMap.put(FIELD_HOTEL_LEAD_CREATE_BY, 15);
        widthMap.put(FIELD_HOTEL_LEAD_BIND_EBK, 12);
        widthMap.put(FIELD_HOTEL_LEAD_SET_RIVAL_HOTEL, 12);
        widthMap.put(FIELD_HOTEL_LEAD_SYNC_REVIEW, 12);
        widthMap.put(FIELD_HOTEL_LEAD_SYNC_STATIC_INFO, 15);
        widthMap.put(FIELD_HOTEL_LEAD_SYNC_RIVAL_REVIEW, 12);
        widthMap.put(FIELD_HOTEL_LEAD_SYNC_RIVAL_PRICE, 12);
        widthMap.put(FIELD_HOTEL_LEAD_GENERATE_REPORT, 15);
        widthMap.put(FIELD_HOTEL_LEAD_CREATED_AT, 20);
        widthMap.put(FIELD_HOTEL_LEAD_COMPLETE_TIME, 20);
        widthMap.put(FIELD_HOTEL_LEAD_DURATION, 15);
        return widthMap;
    }

    @Override
    public Map<String, String> getColumnFormatMap() {
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put(FIELD_HOTEL_LEAD_CREATED_AT, "datetime");
        formatMap.put(FIELD_HOTEL_LEAD_COMPLETE_TIME, "datetime");
        return formatMap;
    }

    @Override
    public Set<String> getHiddenColumns() {
        Set<String> hiddenColumns = new HashSet<>();
        hiddenColumns.add(FIELD_HOTEL_LEAD_ID); // 隐藏ID列
        return hiddenColumns;
    }
}
