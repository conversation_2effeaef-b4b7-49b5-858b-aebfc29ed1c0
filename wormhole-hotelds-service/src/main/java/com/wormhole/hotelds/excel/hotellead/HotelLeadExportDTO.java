package com.wormhole.hotelds.excel.hotellead;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/5/29 16:39
 * @Description：HotelLeadExportDTO
 */
@Data
public class HotelLeadExportDTO {

    private Long id;

    /**
     * 门店线索编码
     */
    private String leadCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 携程EBK链接
     */
    private String ctripEbkUrl;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 确认绑定EBK
     */
    private boolean bindEbk;

    /**
     * 设置竞对酒店
     */
    private boolean setRivalHotel;

    /**
     * 同步全量点评
     */
    private boolean syncReview;

    /**
     * 同步酒店静态信息
     */
    private boolean syncStaticInfo;

    /**
     * 同步竞对评论
     */
    private boolean syncRivalReview;

    /**
     * 同步竞对价格
     */
    private boolean syncRivalPrice;

    /**
     * 生成初始化报告
     */
    private boolean generateReport;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 创建完成时间
     */
    private String completeTime;

    /**
     * 创建门店时长(格式化)
     * 格式: X小时X分钟
     */
    private String creationDurationFormatted;
}
