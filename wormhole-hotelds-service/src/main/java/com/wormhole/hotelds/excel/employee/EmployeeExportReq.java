package com.wormhole.hotelds.excel.employee;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.excel.BaseExportReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/9 13:43
 * @Description：EmployeeExportReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EmployeeExportReq extends BaseExportReq {

    /**
     * 用户关键词（姓名、手机号）
     */
    private String userKeyword;

    /**
     * 业务关键词（商户名称、品牌名称、门店名称、酒店code等）
     */
    private String groupKeyword;

    /**
     * 员工状态 1正常 2停用 3注销
     */
    private Integer status;

    /**
     * 角色code
     */
    private String roleCode;

    /**
     * 员工类型
     */
    private Integer type;
}
