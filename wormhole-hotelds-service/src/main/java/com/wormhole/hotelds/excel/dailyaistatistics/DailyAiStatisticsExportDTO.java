package com.wormhole.hotelds.excel.dailyaistatistics;

import com.wormhole.hotelds.admin.model.entity.HdsHotelDailyAiStatisticsEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.format.DateTimeFormatter;

/**
 * @Date: 2025-08-15
 * @Description: 门店日报数据导出DTO
 */
@Data
@Accessors(chain = true)
public class DailyAiStatisticsExportDTO {

    /**
     * 日期
     */
    private String date;

    /**
     * 使用房间数
     */
    private Integer roomUseCount;

    /**
     * AI语音通话数
     */
    private Integer aiCallCount;

    /**
     * 平均通话时长（秒）
     */
    private Double avgCallDurationSeconds;

    /**
     * 文字对话数
     */
    private Integer textDialogueCount;

    /**
     * 完成工单数
     */
    private Integer completedTicketCount;

    /**
     * AI解决数
     */
    private Integer aiSolveCount;

    /**
     * 人工回拨工单数
     */
    private Integer returnCallTicketCount;

    /**
     * 处理时长
     */
    private Double avgCompleteDurationSeconds;

    /**
     * 超时工单数
     */
    private Integer overdueCount;

    /**
     * 投诉工单数
     */
    private Integer complaintTicketCount;

    /**
     * 将实体转换为DTO
     */
    public static DailyAiStatisticsExportDTO fromEntity(HdsHotelDailyAiStatisticsEntity entity) {
        if (entity == null) {
            return null;
        }

        DailyAiStatisticsExportDTO dto = new DailyAiStatisticsExportDTO();
        dto.setDate(entity.getBusinessDate() != null ? 
            entity.getBusinessDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : "");
        dto.setRoomUseCount(entity.getRoomUseCount() != null ? entity.getRoomUseCount() : 0);
        dto.setAiCallCount(entity.getAiCallCount() != null ? entity.getAiCallCount() : 0);
        dto.setAvgCallDurationSeconds(entity.getAvgCallDurationSeconds() != null ? entity.getAvgCallDurationSeconds() : 0.0d);
        dto.setTextDialogueCount(entity.getTextDialogueCount() != null ? entity.getTextDialogueCount() : 0);
        dto.setCompletedTicketCount(entity.getCompletedTicketCount() != null ? entity.getCompletedTicketCount() : 0);
        dto.setAiSolveCount(entity.getAiSolveCount() != null ? entity.getAiSolveCount() : 0);
        dto.setReturnCallTicketCount(entity.getReturnCallTicketCount() != null ? entity.getReturnCallTicketCount() : 0);
        dto.setAvgCompleteDurationSeconds(entity.getAvgCompleteDurationSeconds() != null ? entity.getAvgCompleteDurationSeconds() : 0.0d);
        dto.setOverdueCount(entity.getOverdueCount() != null ? entity.getOverdueCount() : 0);
        dto.setComplaintTicketCount(entity.getComplaintTicketCount() != null ? entity.getComplaintTicketCount() : 0);

        return dto;
    }
}
