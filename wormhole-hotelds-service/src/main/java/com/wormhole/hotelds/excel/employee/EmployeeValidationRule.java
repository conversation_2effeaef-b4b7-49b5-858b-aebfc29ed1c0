package com.wormhole.hotelds.excel.employee;

import lombok.Getter;

import java.util.function.Function;

/**
 * @Author：huang<PERSON><PERSON>e
 * @Date：2025/5/15
 * @Description：员工数据验证规则
 */
@Getter
public enum EmployeeValidationRule {

    HOTEL_CODE("门店编码", EmployeeImportDTO::getHotelCode, false),
    EMPLOYEE_NAME("员工姓名", EmployeeImportDTO::getName, true),
    USERNAME("自定义账号", EmployeeImportDTO::getUsername, false),
    MOBILE("手机号码", EmployeeImportDTO::getMobile, false),
    EMAIL("邮箱", EmployeeImportDTO::getEmail, false),
    ROLE("角色", EmployeeImportDTO::getRoleName, true),
    ACCOUNT_TYPE("总分机", EmployeeImportDTO::getAccountTypeLabel, false),
//    TICKET_TYPES("工单类型", EmployeeImportDTO::getTicketTypes, false),
    TICKET_ASSIGNMENT_FLAG("接单表", EmployeeImportDTO::getTicketAssignmentFlagLabel, false),
    ;

    /**
     * 字段名称
     */
    private final String fieldName;

    /**
     * 字段值获取器
     */
    private final Function<EmployeeImportDTO, String> valueGetter;

    /**
     * 是否必填
     */
    private final boolean required;

    EmployeeValidationRule(String fieldName, Function<EmployeeImportDTO, String> valueGetter, boolean required) {
        this.fieldName = fieldName;
        this.valueGetter = valueGetter;
        this.required = required;
    }
}