package com.wormhole.hotelds.excel.device;

import lombok.Builder;
import lombok.Data;

/**
 * @Author：flx
 * @Date：2025/4/8 16:53
 * @Description：HdsDeviceEntity2
 */
@Data
@Builder
public class DeviceImportDTO {

    /**
     * 设备型号编码
     */
    private String modelCode;

    private Long modelId;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 设备IMEI号
     */
    private String imei;

    /**
     * 采购时间
     */
    private String purchaseTime;

    /**
     * 设备保修开始时间
     */
    private String warrantyStart;

    /**
     * 设备保修结束时间
     */
    private String warrantyEnd;

    /**
     * 备注
     */
    private String remark;

    /**
     * 行号
     */
    private Integer rowNum;
}
