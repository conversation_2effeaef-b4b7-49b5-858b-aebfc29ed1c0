package com.wormhole.hotelds.excel.device;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.excel.BaseExportReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/8 10:40
 * @Description：DeviceExportReq
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceExportReq extends BaseExportReq {

    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * 是否模糊查询门店code
     */
    private boolean hotelCodeLike;

    /**
     * 设备类型id
     */
    private Long modelId;

    /**
     * id
     */
    private Long id;

    /**
     * 设备状态
     */
    private Integer deviceStatus;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;
}
