package com.wormhole.hotelds.excel;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/4/1 09:06
 * @Description：数据注册处理器
 */
@Slf4j
@Component
public class DataImportProcessorRegistry {

    /**
     * 按业务类型组织的处理器映射
     */
    private final Map<String, DataImportProcessor<?, ?>> processorMap = new HashMap<>();

    /**
     * 所有可用的处理器列表
     */
    private final List<DataImportProcessor<?, ?>> processors;

    /**
     * 构造函数，注入所有可用的处理器
     *
     * @param processors 处理器列表
     */
    public DataImportProcessorRegistry(List<DataImportProcessor<?, ?>> processors) {
        this.processors = processors;
    }

    /**
     * 初始化处理器映射
     * 在Spring容器启动时自动执行
     */
    @PostConstruct
    public void init() {
        for (DataImportProcessor<?, ?> processor : processors) {
            String businessType = processor.getBusinessType();
            log.info("注册数据处理器: businessType={}, processor={}",
                    businessType, processor.getClass().getSimpleName());
            processorMap.put(businessType, processor);
        }
    }

    /**
     * 检查是否支持指定的业务类型
     *
     * @param businessType 业务类型
     * @return 是否支持
     */
    public boolean supports(String businessType) {
        return processorMap.containsKey(businessType);
    }

    /**
     * 获取处理器实例
     *
     * @param businessType 业务类型
     * @param <T> DTO类型
     * @param <E> 实体类型
     * @return 处理器实例
     * @throws BusinessException 如果找不到对应的处理器
     */
    @SuppressWarnings("unchecked")
    public <T, E> DataImportProcessor<T, E> getProcessor(String businessType) {
        DataImportProcessor<?, ?> processor = processorMap.get(businessType);
        if (processor == null) {
            throw new BusinessException(ResultCode.NOT_ACCEPTABLE,
                    String.format("不支持的业务类型: %s", businessType));
        }
        try {
            return (DataImportProcessor<T, E>) processor;
        } catch (ClassCastException e) {
            log.error("处理器类型转换失败: businessType={}, processor={}",
                    businessType, processor.getClass().getName(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                    String.format("处理器类型不匹配: %s", businessType));
        }
    }

    /**
     * 获取所有支持的业务类型
     *
     * @return 支持的业务类型集合
     */
    public List<String> getSupportedBusinessTypes() {
        return List.copyOf(processorMap.keySet());
    }
}