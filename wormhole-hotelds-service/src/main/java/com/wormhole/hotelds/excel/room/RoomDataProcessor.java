//package com.wormhole.hotelds.excel.room;
//
//import com.alibaba.cloud.commons.lang.StringUtils;
//import com.wormhole.common.exception.BusinessException;
//import com.wormhole.common.result.ResultCode;
//import com.wormhole.common.util.HeaderUtils;
//import com.wormhole.hotelds.core.model.entity.*;
//import com.wormhole.hotelds.excel.DataProcessor;
//import com.wormhole.hotelds.excel.RowErrorInfo;
//import com.wormhole.hotelds.admin.constant.ExcelConstant;
//import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
//import com.wormhole.hotelds.admin.repository.HotelRepository;
//import com.wormhole.hotelds.admin.repository.RoomRepository;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.dao.DuplicateKeyException;
//import org.springframework.stereotype.Component;
//import reactor.core.publisher.Flux;
//import reactor.core.publisher.Mono;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.*;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
///**
// * @Author：flx
// * @Date：2025/4/1 09:35
// * @Description：RoomDataProcessor
// */
//@Slf4j
//@Component
//public class RoomDataProcessor implements DataProcessor<HdsRoomEntity> {
//
//    @Resource
//    private RoomRepository roomRepository;
//
//    @Resource
//    private HotelRepository hotelRepository;
//
//    @Override
//    public String getBusinessType() {
//        return BussinessTypeEnum.ROOM.getBusinessType();
//    }
//
//    @Override
//    public Set<String> getRequiredHeaders() {
//        return new HashSet<>(Arrays.asList(
//                ExcelConstant.FIELD_HOTEL_CODE,
//                ExcelConstant.FIELD_FLOOR,
//                ExcelConstant.FIELD_ROOM_TYPE,
//                ExcelConstant.FIELD_ROOM_CODE,
//                ExcelConstant.FIELD_ROOM_NAME,
//                ExcelConstant.FIELD_WIFI_NAME,
//                ExcelConstant.FIELD_WIFI_PASSWORD
//        ));
//    }
//
//    @Override
//    public Flux<HdsRoomEntity> validateData(Flux<HdsRoomEntity> data) {
//        return data.flatMap(room -> {
//            List<String> errors = new ArrayList<>();
//
//            // 验证楼层
//            if (Objects.isNull(room.getFloor())) {
//                errors.add("楼层不能为空");
//            }
//
//            // 验证房间号
//            if (StringUtils.isBlank(room.getRoomNum())) {
//                errors.add("房间号不能为空");
//            }
//
//            // 验证房型
//            if (StringUtils.isBlank(room.getRoomType())) {
//                errors.add("房型不能为空");
//            }
//
//            // 如果有错误，返回错误信息
////            if (!errors.isEmpty()) {
////                return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
////                        String.format("第%d行数据验证失败: %s",
////                                room.getRowNum(), String.join("; ", errors))));
////            }
//
//            return Mono.just(room);
//        });
//    }
//
//    @Override
//    public Flux<HdsRoomEntity> convertData(Flux<Map<String, String>> rawData,
//                                           HeaderUtils.HeaderInfo headerInfo) {
//        return rawData.flatMap(map -> {
//            try {
//                // 获取门店编码
//                String hotelCode = map.get(ExcelConstant.FIELD_HOTEL_CODE);
//                if (StringUtils.isBlank(hotelCode)) {
//                    return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
//                            String.format("第%s行门店编码不能为空",
//                                    map.get("_excelRowNum"))));
//                }
//
//                // 查询门店信息并转换数据
//                return hotelRepository.findByHotelCode(hotelCode)
//                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND,
//                                String.format("第%s行门店编码「%s」不存在",
//                                        map.get("_excelRowNum"), hotelCode))))
//                        .map(hotel -> convertToEntity(map, hotel, headerInfo));
//            } catch (Exception e) {
//                return Mono.error(new BusinessException(ResultCode.PARSE_ERROR,
//                        String.format("第%s行数据转换失败: %s",
//                                map.get("_excelRowNum"), e.getMessage())));
//            }
//        });
//    }
//
//    /**
//     * 转换为实体对象
//     */
//    private HdsRoomEntity convertToEntity(Map<String, String> map,
//                                          HdsHotelInfoEntity hotel, HeaderUtils.HeaderInfo headerInfo) {
//        HdsRoomEntity room = new HdsRoomEntity();
//
//        // 设置基本信息
//        room.setHotelCode(hotel.getHotelCode());
////        room.setRowNum(Integer.parseInt(map.get("_excelRowNum")));
//
//        // 设置房间信息
//        room.setFloor(Integer.valueOf(map.get(ExcelConstant.FIELD_FLOOR).trim()));
//        room.setRoomNum(map.get(ExcelConstant.FIELD_ROOM_CODE));
//        room.setRoomType(map.get(ExcelConstant.FIELD_ROOM_TYPE));
//        room.setRoomName(map.get(ExcelConstant.FIELD_ROOM_NAME));
//
//        // 设置WiFi信息
//        room.setWifiSsid(map.get(ExcelConstant.FIELD_WIFI_NAME));
//        room.setWifiPassword(map.get(ExcelConstant.FIELD_WIFI_PASSWORD).trim());
//
//        // 设置审计字段
//        room.setCreatedAt(LocalDateTime.now());
//        room.setCreatedBy(headerInfo.getUserId());
//
//        return room;
//    }
//
//    @Override
//    public Mono<Integer> saveData(List<HdsRoomEntity> entities) {
//        if (entities == null || entities.isEmpty()) {
//            return Mono.just(0);
//        }
//        return roomRepository.saveAll(entities)
//                .collectList()
//                .map(List::size);
//    }
//
//    /**
//     * 解析数据库错误，返回包含行号和错误信息的RowErrorInfo对象
//     *
//     * @param error 数据库错误
//     * @param entityList 已转换的实体列表
//     * @param convertedRows 原始Excel数据行
//     * @return 包含错误行号和错误信息的RowErrorInfo
//     */
//    @Override
//    public RowErrorInfo parseDbErrorWithRow(Throwable error, List<?> entityList,
//                                                List<Map<String, String>> convertedRows) {
//        String errorMessage;
//        int errorRow = 1;
//
//        if (error instanceof DuplicateKeyException) {
//            String duplicateInfo = error.getMessage();
//            log.debug("处理重复键错误: {}", duplicateInfo);
//
//            if (duplicateInfo.contains("Duplicate entry")) {
//                try {
//                    Pattern pattern = Pattern.compile("Duplicate entry '(.+)' for key '([^']+)'");
//                    Matcher matcher = pattern.matcher(duplicateInfo);
//                    if (matcher.find()) {
//                        // 重复的值
//                        String duplicateValue = matcher.group(1);
//                        // 键名
//                        String keyName = matcher.group(2);
//
//                        log.debug("重复键分析 - 值: {}, 键名: {}", duplicateValue, keyName);
//
//                        // 提取错误信息
//                        errorMessage = formatDuplicateKeyError(duplicateValue, keyName);
//
//                        // 查找匹配的行号
//                        if (duplicateValue.contains("-")) {
//                            String[] values = duplicateValue.split("-", 2);
//                            if (values.length == 2) {
//                                String hotelDbCode = values[0];
//                                String roomNum = values[1];
//
//                                // 查找匹配的行
//                                errorRow = findMatchingRow(entityList, convertedRows, hotelDbCode, roomNum);
//                            }
//                        } else {
//                            // 尝试根据重复值查找
//                            errorRow = findRowByValue(convertedRows, duplicateValue);
//                        }
//
//                        return new RowErrorInfo(errorRow, errorMessage);
//                    }
//                } catch (Exception e) {
//                    log.warn("解析重复键信息失败", e);
//                }
//
//                return new RowErrorInfo(errorRow, "存在重复数据，无法导入");
//            }
//
//            return new RowErrorInfo(errorRow, "数据已存在，不能重复导入");
//        }
//
//        // 处理外键约束错误
//        if (error instanceof org.springframework.dao.DataIntegrityViolationException) {
//            String dbErrorMessage = error.getMessage();
//
//            // 提取错误信息
//            if (dbErrorMessage.contains("foreign key constraint")) {
//                if (dbErrorMessage.contains("hotel_id")) {
//                    errorMessage = "引用的门店不存在，请先创建门店";
//                    // 尝试从错误信息中查找酒店ID
//                    errorRow = findRowByColumnValue(convertedRows, ExcelConstant.FIELD_HOTEL_CODE,
//                            extractValueFromError(dbErrorMessage, "hotel_id"));
//                } else {
//                    errorMessage = "违反外键约束，引用的数据不存在";
//                }
//            } else if (dbErrorMessage.contains("Data too long")) {
//                if (dbErrorMessage.contains("room_num")) {
//                    errorMessage = "房间号超出长度限制";
//                    errorRow = findRowByLongValue(convertedRows, ExcelConstant.FIELD_ROOM_CODE);
//                } else if (dbErrorMessage.contains("room_type_name")) {
//                    errorMessage = "房型名称超出长度限制";
//                    errorRow = findRowByLongValue(convertedRows, ExcelConstant.FIELD_ROOM_TYPE);
//                } else if (dbErrorMessage.contains("wifi_ssid")) {
//                    errorMessage = "WiFi名称超出长度限制";
//                    errorRow = findRowByLongValue(convertedRows, ExcelConstant.FIELD_WIFI_NAME);
//                } else if (dbErrorMessage.contains("wifi_password")) {
//                    errorMessage = "WiFi密码超出长度限制";
//                    errorRow = findRowByLongValue(convertedRows, ExcelConstant.FIELD_WIFI_PASSWORD);
//                } else {
//                    errorMessage = "数据超出长度限制";
//                }
//            } else if (dbErrorMessage.contains("NULL")) {
//                errorMessage = "必填字段不能为空";
//            } else {
//                errorMessage = "数据格式不符合要求";
//            }
//
//            return new RowErrorInfo(errorRow, errorMessage);
//        }
//
//        // 处理数据类型错误
//        if (error instanceof org.springframework.dao.DataAccessException) {
//            String dbErrorMessage = error.getMessage();
//            if (dbErrorMessage.contains("Incorrect integer value")) {
//                errorMessage = "数字字段包含非法值";
//                // 尝试查找包含非法数字的行
//                errorRow = findRowWithInvalidNumber(convertedRows);
//            } else if (dbErrorMessage.contains("Incorrect datetime value")) {
//                errorMessage = "日期字段格式不正确";
//            } else {
//                errorMessage = "数据访问异常";
//            }
//
//            return new RowErrorInfo(errorRow, errorMessage);
//        }
//
//        // 处理事务异常
//        if (error instanceof org.springframework.transaction.TransactionException) {
//            return new RowErrorInfo(errorRow, "事务处理失败，请稍后重试");
//        }
//
//        // 默认错误处理
//        return new RowErrorInfo(errorRow, "保存数据失败: " + error.getMessage());
//    }
//
//    /**
//     * 格式化重复键错误信息
//     */
//    private String formatDuplicateKeyError(String duplicateValue, String keyName) {
//        // 检查值是否包含分隔符"-"，这可能表示是组合键
//        if (duplicateValue.contains("-")) {
//            String[] values = duplicateValue.split("-", 2);
//            if (values.length == 2) {
//                // 无论键名如何，如果值是"X-Y"格式，大概率是酒店ID和房间号的组合
//                return String.format("门店ID '%s' 下已存在房间号 '%s'", values[0], values[1]);
//            }
//        }
//
//        // 基于键名的分析
//        if (keyName.contains("uk_hotel_room") || keyName.contains("uk_hotel_id_room_num")) {
//            // 明确是酒店+房间联合唯一键
//            return String.format("该门店下已存在同编号房间: %s", duplicateValue);
//        } else if ("hotel_id".equalsIgnoreCase(keyName) || keyName.contains("_hotel_id_")) {
//            // 如果键名就是hotel_id，或包含hotel_id
//            // 但考虑到值可能是复合的，尝试再次解析
//            if (duplicateValue.contains("-")) {
//                String[] parts = duplicateValue.split("-", 2);
//                return String.format("门店ID '%s' 下已存在房间号 '%s'", parts[0], parts[1]);
//            }
//            return String.format("门店ID已存在: %s", duplicateValue);
//        } else if (keyName.contains("room_num") || keyName.contains("_room_")) {
//            return String.format("房间号重复: %s", duplicateValue);
//        }
//
//        // 通用回退处理
//        return String.format("数据重复: %s (键: %s)", duplicateValue, keyName);
//    }
//
//    /**
//     * 在实体列表和原始数据行中查找匹配的Excel行号
//     */
//    private int findMatchingRow(List<?> entityList, List<Map<String, String>> convertedRows,
//                                String hotelCode, String roomNum) {
//        // 首先从实体列表查找
//        if (entityList != null) {
//            for (int i = 0; i < entityList.size(); i++) {
//                Object entity = entityList.get(i);
//                // 检查是否为HdsRoomEntity类型
//                if (entity instanceof HdsRoomEntity room) {
//                    // 检查房间号是否匹配
//                    if (room.getRoomNum() != null && room.getRoomNum().equals(roomNum)) {
//                        // 如果实体中有匹配的门店ID和房间号，直接返回
//                        if (room.getHotelCode() != null && room.getHotelCode().equals(hotelCode)) {
//                            return i + 1;
//                        }
//                    }
//                }
//            }
//        }
//
//        // 如果实体列表中没找到，尝试从原始数据行查找
//        if (convertedRows != null) {
//            for (Map<String, String> row : convertedRows) {
//                String excelRoomNum = row.get(ExcelConstant.FIELD_ROOM_CODE);
//
//                // 检查房间号是否匹配
//                if (excelRoomNum != null && excelRoomNum.trim().equals(roomNum)) {
//                    // 找到了匹配的房间号，返回Excel行号
//                    return Integer.parseInt(row.get("_excelRowNum"));
//                }
//            }
//        }
//
//        // 如果找不到匹配项，返回1作为默认行号
//        return 1;
//    }
//
//    /**
//     * 查找包含指定值的行
//     */
//    private int findRowByValue(List<Map<String, String>> rows, String value) {
//        if (rows != null && value != null) {
//            for (Map<String, String> row : rows) {
//                for (String fieldValue : row.values()) {
//                    if (fieldValue != null && fieldValue.contains(value)) {
//                        return Integer.parseInt(row.get("_excelRowNum"));
//                    }
//                }
//            }
//        }
//        return 1;
//    }
//
//    /**
//     * 查找指定列包含指定值的行
//     */
//    private int findRowByColumnValue(List<Map<String, String>> rows, String columnName, String value) {
//        if (rows != null && columnName != null && value != null) {
//            for (Map<String, String> row : rows) {
//                String fieldValue = row.get(columnName);
//                if (fieldValue != null && fieldValue.equals(value)) {
//                    return Integer.parseInt(row.get("_excelRowNum"));
//                }
//            }
//        }
//        return 1;
//    }
//
//    /**
//     * 查找指定列值超长的行
//     */
//    private int findRowByLongValue(List<Map<String, String>> rows, String columnName) {
//        if (rows != null && columnName != null) {
//            // 找出该列值最长的行
//            int maxLength = 0;
//            int rowWithMaxLength = 1;
//
//            for (Map<String, String> row : rows) {
//                String value = row.get(columnName);
//                if (value != null && value.length() > maxLength) {
//                    maxLength = value.length();
//                    rowWithMaxLength = Integer.parseInt(row.get("_excelRowNum"));
//                }
//            }
//
//            return rowWithMaxLength;
//        }
//        return 1;
//    }
//
//    /**
//     * 查找包含无效数字的行
//     */
//    private int findRowWithInvalidNumber(List<Map<String, String>> rows) {
//        if (rows != null) {
//            for (Map<String, String> row : rows) {
//                String floorStr = row.get(ExcelConstant.FIELD_FLOOR);
//                if (floorStr != null) {
//                    try {
//                        Integer.parseInt(floorStr.trim());
//                    } catch (NumberFormatException e) {
//                        return Integer.parseInt(row.get("_excelRowNum"));
//                    }
//                }
//            }
//        }
//        return 1;
//    }
//
//    /**
//     * 从错误信息中提取相关值
//     */
//    private String extractValueFromError(String errorMessage, String fieldName) {
//        // 这个方法需要根据实际错误信息格式进行定制
//        // 这里只是一个示例实现
//        try {
//            if (errorMessage.contains(fieldName)) {
//                Pattern pattern = Pattern.compile(fieldName + "[^0-9]+(\\d+)");
//                Matcher matcher = pattern.matcher(errorMessage);
//                if (matcher.find()) {
//                    return matcher.group(1);
//                }
//            }
//        } catch (Exception e) {
//            log.warn("从错误信息提取字段值失败", e);
//        }
//        return "";
//    }
//
//    @Override
//    public Flux<HdsRoomEntity> exportData(Map<String, Object> params) {
//        return null;
//    }
//
//    @Override
//    public String getExportFileName(Map<String, Object> params) {
//        return "";
//    }
//}
