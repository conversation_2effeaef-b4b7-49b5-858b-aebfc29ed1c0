package com.wormhole.hotelds.excel.device;

import com.wormhole.hotelds.admin.constant.ExcelConstant;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.ExportTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceFieldEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceInfoEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceInfoFieldEnum;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExcelFormattingProvider;
import com.wormhole.hotelds.excel.ExportContext;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

import static com.wormhole.hotelds.admin.constant.ExcelConstant.*;

/**
 * @Author：flx
 * @Date：2025/5/8 10:50
 * @Description：DeviceExportDataProcessor
 */
@Slf4j
@Component
public class DeviceDataExportProcessor implements DataExportProcessor<DeviceExportDTO, DeviceExportReq>, ExcelFormattingProvider {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.DEVICE.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return new ArrayList<>(Arrays.asList(
                FIELD_DEVICE_ID,
                FIELD_DEVICE_SN,
                FIELD_DEVICE_IMEI,
                FIELD_WARRANTY_START_TIME,
                FIELD_WARRANTY_END_TIME,
                FIELD_PURCHASE_TIME,
                FIELD_REMARK
        ));
    }

    @Override
    public Flux<DeviceExportDTO> queryData(DeviceExportReq request, ExportContext context) {
        // 构建查询条件
        Criteria criteria = buildQueryCriteria(request);

        // 执行查询
        return r2dbcEntityTemplate.select(Query.query(criteria)
                        .sort(Sort.by(Sort.Direction.DESC, HdsDeviceFieldEnum.created_at.name())), HdsDeviceEntity.class)
                .collectList()
                .flatMapMany(deviceList -> {
                    if (deviceList.isEmpty()) {
                        return Flux.empty();
                    }

                    // 提取所有device_sn和imei
                    Set<String> deviceSns = new HashSet<>();
                    Set<String> imeis = new HashSet<>();

                    for (HdsDeviceEntity device : deviceList) {
                        if (StringUtils.isNotBlank(device.getDeviceSn())) {
                            deviceSns.add(device.getDeviceSn());
                        }
                        if (StringUtils.isNotBlank(device.getImei())) {
                            imeis.add(device.getImei());
                        }
                    }

                    // 构建批量查询条件
                    Criteria infoCriteria = Criteria.empty();
                    if (!deviceSns.isEmpty()) {
                        infoCriteria = infoCriteria.or(Criteria.where(HdsDeviceInfoFieldEnum.device_sn.name()).in(deviceSns));
                    }
                    if (!imeis.isEmpty()) {
                        infoCriteria = infoCriteria.or(Criteria.where(HdsDeviceInfoFieldEnum.imei.name()).in(imeis));
                    }

                    // 如果没有可用的关联字段，直接返回基本DTO列表
                    if (infoCriteria.isEmpty()) {
                        return Flux.fromIterable(deviceList)
                                .map(DeviceExportDTO::toDTO);
                    }

                    // 批量查询设备信息
                    return r2dbcEntityTemplate.select(Query.query(infoCriteria), HdsDeviceInfoEntity.class)
                            .collectList()
                            .flatMapMany(infoList -> {
                                // 创建查询映射（根据device_sn和imei）
                                Map<String, HdsDeviceInfoEntity> snInfoMap = new HashMap<>();
                                Map<String, HdsDeviceInfoEntity> imeiInfoMap = new HashMap<>();

                                for (HdsDeviceInfoEntity info : infoList) {
                                    if (StringUtils.isNotBlank(info.getDeviceSn())) {
                                        snInfoMap.put(info.getDeviceSn(), info);
                                    }
                                    if (StringUtils.isNotBlank(info.getImei())) {
                                        imeiInfoMap.put(info.getImei(), info);
                                    }
                                }

                                // 合并数据并创建DTO
                                List<DeviceExportDTO> resultList = new ArrayList<>();
                                for (HdsDeviceEntity device : deviceList) {
                                    DeviceExportDTO dto = DeviceExportDTO.toDTO(device);

                                    // 优先使用device_sn匹配，如果找不到则使用imei匹配
                                    HdsDeviceInfoEntity infoEntity = null;
                                    if (StringUtils.isNotBlank(device.getDeviceSn())) {
                                        infoEntity = snInfoMap.get(device.getDeviceSn());
                                    }
                                    if (infoEntity == null && StringUtils.isNotBlank(device.getImei())) {
                                        infoEntity = imeiInfoMap.get(device.getImei());
                                    }

                                    // 设置额外信息
                                    if (infoEntity != null) {
                                        setAdditionalInfo(dto, infoEntity);
                                    }

                                    resultList.add(dto);
                                }

                                return Flux.fromIterable(resultList);
                            });
                });
    }

    /**
     * 根据导出类型和请求参数构建查询条件
     */
    private Criteria buildQueryCriteria(DeviceExportReq request) {
        // 根据导出类型选择不同的查询策略
        ExportTypeEnum exportType = ExportTypeEnum.getByCode(request.getExportType());

        return switch (exportType) {
            case ALL ->
                // 全部导出 - 使用空条件
                    Criteria.empty();
            case CURRENT -> {
                // 当前页导出 - 根据IDs列表
                if (CollectionUtils.isEmpty(request.getCurrentIds())) {
                    // 如果ID列表为空，返回一个永远为false的条件，确保不会有数据
                    yield Criteria.where(HdsDeviceFieldEnum.id.name()).is(-1);
                }
                yield Criteria.where(HdsDeviceFieldEnum.id.name()).in(request.getCurrentIds());
            }
            default ->
                // 筛选导出 - 使用过滤条件
                    buildFilterCriteria(request);
        };
    }

    /**
     * 构建筛选查询条件
     */
    private Criteria buildFilterCriteria(DeviceExportReq request) {
        Criteria criteria = Criteria.empty();

        // 门店编码
        if (StringUtils.isNotBlank(request.getHotelCode())) {
            if (request.isHotelCodeLike()) {
                criteria = criteria.and(HdsDeviceFieldEnum.hotel_code.name()).like("%" + request.getHotelCode().trim() + "%");
            } else {
                criteria = criteria.and(HdsDeviceFieldEnum.hotel_code.name()).is(request.getHotelCode());
            }
        }

        // 根据设备类型id
        if (Objects.nonNull(request.getModelId())) {
            criteria = criteria.and(HdsDeviceFieldEnum.model_id.name()).is(request.getModelId());
        }

        // 设备id
        if (Objects.nonNull(request.getId())) {
            criteria = criteria.and(HdsDeviceFieldEnum.id.name()).is(request.getId());
        }

        // 设备状态
        if (Objects.nonNull(request.getDeviceStatus())) {
            criteria = criteria.and(HdsDeviceFieldEnum.device_status.name())
                    .is(request.getDeviceStatus());
        }

        // 时间范围
        if (StringUtils.isNotBlank(request.getStartTime())) {
            criteria = criteria.and(HdsDeviceFieldEnum.created_at.name())
                    .greaterThanOrEquals(SimpleDateUtils.slashStringToStartDateTime(request.getStartTime()));
        }
        if (StringUtils.isNotBlank(request.getEndTime())) {
            criteria = criteria.and(HdsDeviceFieldEnum.created_at.name())
                    .lessThanOrEquals(SimpleDateUtils.slashStringToEndDateTime(request.getEndTime()));
        }

        return criteria;
    }

    /**
     * 将设备信息表的数据设置到DTO中
     *
     * @param dto        导出DTO
     * @param infoEntity 设备信息实体
     */
    private void setAdditionalInfo(DeviceExportDTO dto, HdsDeviceInfoEntity infoEntity) {
        // 设置保修开始时间
        dto.setWarrantyStart(SimpleDateUtils.formatLocalDateTimeToDate(infoEntity.getWarrantyStart()));

        // 设置保修结束时间
        dto.setWarrantyEnd(SimpleDateUtils.formatLocalDateTimeToDate(infoEntity.getWarrantyEnd()));

        // 设置采购时间
        dto.setPurchaseTime(SimpleDateUtils.formatLocalDateTimeToDate(infoEntity.getPurchaseTime()));

        // 设置remark
        dto.setRemark(infoEntity.getRemark());
    }

    /**
     * 将数据转成excel行
     *
     * @param data
     * @return
     */
    @Override
    public List<String> convertToRow(DeviceExportDTO data) {
        // 定义表头到数据提取器的映射
        Map<String, Function<DeviceExportDTO, String>> extractors = new LinkedHashMap<>();
//        extractors.put(FIELD_DEVICE_TYPE_CODE, dto -> dto.getModelCode() != null ? dto.getModelCode() : "");
        extractors.put(FIELD_DEVICE_ID, deviceExportDTO -> String.valueOf(deviceExportDTO.getId()));
        extractors.put(FIELD_DEVICE_SN, DeviceExportDTO::getDeviceSn);
        extractors.put(FIELD_DEVICE_IMEI, DeviceExportDTO::getImei);
        extractors.put(FIELD_WARRANTY_START_TIME, DeviceExportDTO::getWarrantyStart);
        extractors.put(FIELD_WARRANTY_END_TIME, DeviceExportDTO::getWarrantyEnd);
        extractors.put(FIELD_PURCHASE_TIME, DeviceExportDTO::getPurchaseTime);
        extractors.put(FIELD_REMARK, DeviceExportDTO::getRemark);

        // 按照表头顺序提取数据
        List<String> row = new ArrayList<>();
        for (String header : getExcelHeaders()) {
            Function<DeviceExportDTO, String> extractor = extractors.get(header);
            if (extractor != null) {
                row.add(extractor.apply(data));
            } else {
                // 未知表头，添加空值
                row.add("");
            }
        }

        return row;
    }

    /**
     * 获取导出文件名
     *
     * @param request
     * @return
     */
    @Override
    public String getExportFileName(DeviceExportReq request) {
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now());
        return "设备列表_" + dateStr + ".xlsx";
    }

    /**
     * 获取请求类
     *
     * @return
     */
    @Override
    public Class<DeviceExportReq> getRequestClass() {
        return DeviceExportReq.class;
    }

    @Override
    public Map<String, Integer> getColumnWidthMap() {
        Map<String, Integer> widthMap = new HashMap<>();
        // 使用常量定义列宽配置
        widthMap.put(FIELD_DEVICE_SN, 15);
        widthMap.put(FIELD_DEVICE_IMEI, 15);
        widthMap.put(FIELD_PURCHASE_TIME, 15);
        widthMap.put(FIELD_WARRANTY_START_TIME, 15);
        widthMap.put(FIELD_WARRANTY_END_TIME, 15);
        widthMap.put(FIELD_REMARK, 20);
        return widthMap;
    }

    @Override
    public Map<String, String> getColumnFormatMap() {
        Map<String, String> formatMap = new HashMap<>();
        // 使用常量定义日期格式配置
        formatMap.put(FIELD_PURCHASE_TIME, "date");
        formatMap.put(FIELD_WARRANTY_START_TIME, "date");
        formatMap.put(FIELD_WARRANTY_END_TIME, "date");
        return formatMap;
    }

    @Override
    public Set<String> getHiddenColumns() {
        Set<String> hiddenColumns = new HashSet<>();
        hiddenColumns.add(FIELD_DEVICE_ID); // 隐藏ID列
        return hiddenColumns;
    }
}
