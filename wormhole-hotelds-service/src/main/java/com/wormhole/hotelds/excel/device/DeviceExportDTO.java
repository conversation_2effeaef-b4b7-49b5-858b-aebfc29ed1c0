package com.wormhole.hotelds.excel.device;

import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/5/8 10:54
 * @Description：DeviceExportDTO
 */
@Data
public class DeviceExportDTO {

    private Long id;

    private String deviceSn;

    private String imei;

    /**
     * 保修开始时间
     */
    private String warrantyStart;

    /**
     * 保修结束时间
     */
    private String warrantyEnd;

    /**
     * 采购时间
     */
    private String purchaseTime;

    /**
     * 备注
     */
    private String remark;

    public static DeviceExportDTO toDTO(HdsDeviceEntity hdsDeviceEntity) {
        DeviceExportDTO deviceExportDTO = new DeviceExportDTO();
        BeanUtils.copyProperties(hdsDeviceEntity, deviceExportDTO);
        return deviceExportDTO;
    }
}
