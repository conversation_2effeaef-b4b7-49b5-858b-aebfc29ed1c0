package com.wormhole.hotelds.excel.userlead;

import lombok.Data;

/**
 * @Author：flx
 * @Date：2025/5/28 09:42
 * @Description：UserLeadExportDTO
 */
@Data
public class UserLeadExportDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 线索编码
     */
    private String leadCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 线索状态: 1-有效, 0-无效
     */
    private String status;

    /**
     * 获取EBK数
     */
    private Integer ebkCount;

    /**
     * 创建门店数
     */
    private Integer hotelCount;

    /**
     * 邀请人
     */
    private String invitedBy;

    /**
     * 被邀请码
     */
    private String inviteCode;

    /**
     * 注册时间
     */
    private String registeredAt;
}
