package com.wormhole.hotelds.excel.ticket;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.excel.BaseExportReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author：AI Assistant
 * @Date：2025/09/02
 * @Description：门店工单明细导出请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelTicketsDetailExportReq extends BaseExportReq {

    // 这个类将使用BaseExportReq中的requestJson字段来传递TicketAdminPageReq参数
    // 不需要额外的字段，因为所有查询参数都通过requestJson传递
}
