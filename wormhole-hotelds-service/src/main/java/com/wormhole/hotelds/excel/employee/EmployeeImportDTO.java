package com.wormhole.hotelds.excel.employee;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Date：2025/5/15
 * @Description：员工导入DTO
 */
@Data
@Accessors(chain = true)
public class EmployeeImportDTO {
    /**
     * Excel行号，用于错误提示
     */
    private Integer rowNum;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 门店编码（导入时根据门店名称查询）
     */
    private String hotelCode;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 账号类型，1总机 2分机
     */
    private String accountTypeLabel;

    /**
     * 账号类型，1总机 2分机
     */

    private String ticketAssignmentFlagLabel;

    private Integer ticketAssignmentFlag;
    private Integer accountType;

    /**
     * 账号
     */
    private String username;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 邮箱
     */
    private String email;

    /**
     * 角色
     */
    private String roleName;
    
    /**
     * 角色编码
     */
    private String roleCode;

    private List<String> roleCodes;

    /**
     * 工单类型名称（英文逗号分隔）
     */
    private String ticketTypes;
    
    /**
     * 工单类型列表
     */
    private List<String> tickets;
    
    /**
     * 是否已存在
     */
    private boolean exists;
    
    /**
     * 员工ID（用于已存在员工的关联）
     */
    private Integer employeeId;
}