package com.wormhole.hotelds.excel.deviceposition;

import com.wormhole.common.model.entity.BaseEntity;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.constant.ExcelConstant;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.repository.DevicePositionRepository;
import com.wormhole.hotelds.admin.service.HdsWechatQrCodeService;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.utils.HoteldsApiUtils;
import com.wormhole.hotelds.excel.DataImportProcessor;
import com.wormhole.hotelds.excel.EntityCollection;
import com.wormhole.hotelds.excel.ImportContext;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.OperationLogUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/4/14 10:10
 * @Description：DevicePositionDataProcessor
 */
@Slf4j
@Component
public class DevicePositionDataImportProcessor implements DataImportProcessor<DevicePositionImportDTO, EntityCollection> {

    @Resource
    private DevicePositionRepository devicePositionRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private OperationLogUtil operationLogUtil;

    @Resource
    private HdsWechatQrCodeService hdsWechatQrCodeService;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.DEVICE_POSITION.getBusinessType();
    }

    @Override
    public Set<String> getRequiredHeaders(ImportContext context) {
        return new HashSet<>(Arrays.asList(
                ExcelConstant.FIELD_DEVICE_APP_TYPE,
                ExcelConstant.FIELD_BLOCK,
                ExcelConstant.FIELD_AREA,
                ExcelConstant.FIELD_POSITION_NAME
        ));
    }

    @Override
    public Flux<DevicePositionImportDTO> validateData(Flux<DevicePositionImportDTO> data, ImportContext context) {
        return data.collectList()
                .flatMapMany(dtoList -> {
                    // 1. 设置总数
                    context.setTotalCount(dtoList.size());

                    // 2. 检查当前批次内的重复
                    Map<String, List<DevicePositionImportDTO>> duplicatesInBatch = checkDuplicatesInBatch(dtoList);

                    // 3. 获取所有位置标识
                    Set<String> allPositionKeys = dtoList.stream()
                            .map(this::getPositionKey)
                            .collect(Collectors.toSet());

                    // 4. 如果没有有效的位置，直接返回空结果
                    if (allPositionKeys.isEmpty()) {
                        return Flux.empty();
                    }

                    // 5. 处理每条数据
                    return Flux.fromIterable(dtoList)
                            .filterWhen(dto -> validatePosition(dto, duplicatesInBatch, context));
                })
                .doOnError(error -> {
                    log.error("数据验证过程发生错误: {}", error.getMessage(), error);
                    context.addRowError(0, "数据验证过程发生错误：" + error.getMessage());
                });
    }

    private String getPositionKey(DevicePositionImportDTO dto) {
        return String.format("%s_%s_%s_%s", dto.getHotelCode(), dto.getBlock(), dto.getArea(), dto.getPositionName());
    }

    private Map<String, List<DevicePositionImportDTO>> checkDuplicatesInBatch(List<DevicePositionImportDTO> dtoList) {
        return dtoList.stream()
                .collect(Collectors.groupingBy(
                        this::getPositionKey,
                        Collectors.toList()
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private Mono<Boolean> validatePosition(
            DevicePositionImportDTO dto,
            Map<String, List<DevicePositionImportDTO>> duplicatesInBatch,
            ImportContext context) {

        // 1. 基础字段验证
        if (!validateBasicFields(dto, context)) {
            return Mono.just(false);
        }

        // 2. 重复性验证
        if (!validateDuplicates(dto, duplicatesInBatch, context)) {
            return Mono.just(false);
        }

        return validateDataDuplicate(dto, context);
    }

    private boolean validateBasicFields(DevicePositionImportDTO dto, ImportContext context) {

        if (StringUtils.isBlank(dto.getHotelCode())) {
            context.addRowError(dto.getRowNum(), "门店编码不能为空");
            return false;
        }
        // 验证必填字段
        if (StringUtils.isBlank(dto.getDeviceAppType())) {
            context.addRowError(dto.getRowNum(), "设备App类型不能为空");
            return false;
        }
        if (StringUtils.isBlank(dto.getBlock())) {
            context.addRowError(dto.getRowNum(), "区域不能为空");
            return false;
        }
        if (dto.getArea() == null) {
            context.addRowError(dto.getRowNum(), "区域不能为空");
            return false;
        }
        if (StringUtils.isBlank(dto.getPositionName())) {
            context.addRowError(dto.getRowNum(), "位置名称不能为空");
            return false;
        }
        return true;
    }

    private boolean validateDuplicates(
            DevicePositionImportDTO dto,
            Map<String, List<DevicePositionImportDTO>> duplicatesInBatch,
            ImportContext context) {

        String positionKey = getPositionKey(dto);
        List<DevicePositionImportDTO> duplicates = duplicatesInBatch.get(positionKey);
        if (duplicates != null && duplicates.get(0) != dto) {
            context.addRowError(dto.getRowNum(),
                    String.format("位置信息在本次导入中重复，已采用第%d行的数据",
                            duplicates.get(0).getRowNum()));
            return false;
        }
        return true;
    }

    private Mono<Boolean> validateDataDuplicate(DevicePositionImportDTO dto, ImportContext context) {
        return devicePositionRepository.existsByHotelCodeAndBlockAndAreaAndPositionName(
                        dto.getHotelCode(),
                        dto.getBlock(),
                        dto.getArea(),
                        dto.getPositionName())
                .map(exists -> {
                    if (exists) {
                        context.addRowError(dto.getRowNum(),
                                String.format("位置信息[%s-%s-%s]已存在于数据库中",
                                        dto.getBlock(), dto.getArea(), dto.getPositionName()));
                        return false;
                    }
                    return true;
                });
    }

    @Override
    public Flux<DevicePositionImportDTO> convertData(Flux<Map<String, String>> rawData, ImportContext context) {
        return rawData.map(map -> DevicePositionImportDTO.builder()
                .hotelCode(context.getHeaderInfo().getHotelCode())
                .block(map.get(ExcelConstant.FIELD_BLOCK))
                .area(map.get(ExcelConstant.FIELD_AREA))
                .positionName(map.get(ExcelConstant.FIELD_POSITION_NAME))
                .deviceAppType(map.get(ExcelConstant.FIELD_DEVICE_APP_TYPE))
                .rowNum(Integer.parseInt(map.getOrDefault("_excelRowNum", "0")))
                .build());
    }

    @Override
    public Flux<EntityCollection> toEntities(Flux<DevicePositionImportDTO> data, ImportContext context) {
        return data.collectList()
                .flatMap(dtoList -> toBatchEntities(dtoList, context))
                .flatMapMany(Flux::fromIterable);
    }

    @Override
    public Mono<List<EntityCollection>> toBatchEntities(List<DevicePositionImportDTO> dtoList, ImportContext context) {
        if (dtoList == null || dtoList.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }

        HeaderUtils.HeaderInfo headerInfo = context.getHeaderInfo();

        return codePoolManager.getBatchCodesFromPool(
                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
                dtoList.size()
        ).map(positionCodes -> {
            List<EntityCollection> collections = new ArrayList<>();
            Iterator<String> codeIterator = positionCodes.iterator();

            for (DevicePositionImportDTO dto : dtoList) {
                try {
                    String positionCode = codeIterator.next();
                    HdsDevicePositionEntity position = buildDevicePosition(dto, positionCode, headerInfo);

                    EntityCollection collection = EntityCollection.builder()
                            .entities(new HashMap<>())
                            .rowNum(dto.getRowNum())
                            .build();

                    collection.addEntity("position", position);
                    collections.add(collection);
                } catch (Exception e) {
                    log.error("实体转换失败: dto={}, error={}", dto, e.getMessage());
                    context.addRowError(dto.getRowNum(), String.format("实体转换失败: %s", e.getMessage()));
                }
            }
            return collections;
        });
    }

    @Override
    public Mono<Integer> saveData(List<EntityCollection> collections, ImportContext context) {
        return Flux.fromIterable(collections)
                .flatMap(collection -> {
                    HdsDevicePositionEntity position = collection.getEntity("position");
                    ExcelSaveReq excelSaveReq = buildExcelSaveReq(position);

                    return transactionalOperator.transactional(
                                    devicePositionRepository.save(position)
                            ).doOnSuccess(savedPosition -> {
                                handleSuccess(savedPosition, excelSaveReq, context.getHeaderInfo());
                                context.incrementSuccessCount();
                                // 异步生成二维码，不影响主逻辑
                                hdsWechatQrCodeService.tryAsyncGenerate(savedPosition);
                            }).thenReturn(true)
                            .onErrorResume(e -> {
                                handleError(e, excelSaveReq, context.getHeaderInfo());
                                log.error("第{}行位置信息入库失败: {}", collection.getRowNum(), e.getMessage());
                                context.addRowError(collection.getRowNum(),
                                        String.format("位置信息入库失败: %s", e.getMessage()));
                                return Mono.just(false);
                            });
                })
                .count()
                .map(Long::intValue);
    }

    private HdsDevicePositionEntity buildDevicePosition(
            DevicePositionImportDTO dto,
            String positionCode,
            HeaderUtils.HeaderInfo headerInfo) {
        HdsDevicePositionEntity entity = new HdsDevicePositionEntity();
        BeanUtils.copyProperties(dto, entity);
        entity.setHotelCode(dto.getHotelCode());
        entity.setPositionCode(positionCode);
        entity.setAreaCode(HoteldsApiUtils.generateAreaCode(headerInfo.getHotelCode(), dto.getBlock(), dto.getArea()));
        setAuditFields(entity, headerInfo);
        entity.setRowStatus(1);
        return entity;
    }

    private ExcelSaveReq buildExcelSaveReq(HdsDevicePositionEntity position) {
        ExcelSaveReq req = new ExcelSaveReq();
        req.setHotelCode(position.getHotelCode());
        req.setBlock(position.getBlock());
        req.setArea(position.getArea());
        req.setPositionName(position.getPositionName());
        req.setDeviceAppType(position.getDeviceAppType());
        return req;
    }

    private void handleSuccess(
            HdsDevicePositionEntity position,
            ExcelSaveReq req,
            HeaderUtils.HeaderInfo headerInfo) {
//        operationLogUtil.recordSuccess(
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                position.getId(),
//                String.format("%s-%s-%s", position.getBlock(), position.getArea(), position.getPositionName()),
//                OperationTypeEnum.ADD.getCode(),
//                String.format("位置信息导入成功: %s-%s-%s",
//                        position.getBlock(), position.getArea(), position.getPositionName()),
//                req,
//                headerInfo
//        ).subscribe();
    }

    private void handleError(Throwable error, ExcelSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
//        operationLogUtil.recordFail(
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                null,
//                String.format("%s-%s-%s", req.getBlock(), req.getArea(), req.getPositionName()),
//                OperationTypeEnum.ADD.getCode(),
//                String.format("位置信息导入失败: %s-%s-%s",
//                        req.getBlock(), req.getArea(), req.getPositionName()),
//                req,
//                error.getMessage(),
//                headerInfo
//        ).subscribe();
    }

    private void setAuditFields(BaseEntity entity, HeaderUtils.HeaderInfo headerInfo) {
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
    }

    @Override
    public Flux<DevicePositionImportDTO> exportData(Map<String, Object> params) {
        return null;
    }

    @Override
    public String getExportFileName(Map<String, Object> params) {
        return "";
    }

    @Data
    static class ExcelSaveReq {
        private String hotelCode;
        private String block;
        private String area;
        private String positionName;
        private String deviceAppType;
    }
}
