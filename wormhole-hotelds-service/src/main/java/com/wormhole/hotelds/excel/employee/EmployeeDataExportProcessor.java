package com.wormhole.hotelds.excel.employee;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.vo.HdsEmployeeVO;
import com.wormhole.hotelds.admin.repository.HdsEmployeeRepository;
import com.wormhole.hotelds.admin.service.HdsEmployeeHotelService;
import com.wormhole.hotelds.admin.service.HdsEmployeeTicketMappingService;
import com.wormhole.hotelds.constant.RoleEnum;
import com.wormhole.hotelds.core.enums.AccountTypeEnum;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.model.entity.HdsEmployeeHotelEntity;
import com.wormhole.hotelds.core.model.entity.HdsEmployeeTicketMappingEntity;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExcelFormattingProvider;
import com.wormhole.hotelds.excel.ExportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wormhole.hotelds.admin.constant.ExcelConstant.*;

/**
 * @Author：flx
 * @Date：2025/5/9 13:43
 * @Description：EmployeeDataExportProcessor
 */
@Slf4j
@Component
public class EmployeeDataExportProcessor implements DataExportProcessor<EmployeeExportDTO, EmployeeExportReq>, ExcelFormattingProvider {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsEmployeeTicketMappingService hdsEmployeeTicketMappingService;

    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.EMPLOYEE.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return new ArrayList<>(Arrays.asList(
                FIELD_EMPLOYEE_ID,
                FIELD_RELATION_HOTEL,
                FIELD_EMPLOYEE_NAME,
                FIELD_EMPLOYEE_USERNAME,
                FIELD_EMPLOYEE_MOBILE,
                FIELD_EMPLOYEE_EMAIL,
                FIELD_EMPLOYEE_ROLE_TYPE,
                FIELD_EMPLOYEE_ACCOUNT_TYPE,
                FIELD_EMPLOYEE_TICKET_ASSIGNMENT_FLAG
//                FIELD_EMPLOYEE_CREATE_BY,
//                FIELD_EMPLOYEE_POSITION_DETAILS
        ));
    }

    @Override
    public Flux<EmployeeExportDTO> queryData(EmployeeExportReq request, ExportContext context) {
        // 构建查询条件
//        Mono<Criteria> criteriaMono = buildQueryCriteria(request);
        if (Objects.isNull(request.getType())) {
            request.setType(3);
        }

        // 执行查询
        return HeaderUtils.getHeaderInfo().flatMapMany(headerInfo -> {
            String hotelCode = headerInfo.getHotelCode();
            String userId = headerInfo.getUserId();

            // 获取当前登录员工关联的所有角色
            Mono<Set<String>> roleCodesMono = StringUtils.isBlank(hotelCode) ? Mono.just(Collections.emptySet()) : hdsEmployeeHotelService.listByEmployeeId(Integer.valueOf(userId), null, List.of(hotelCode))
                    .map(hotelEntities -> {
                        if (CollectionUtils.isEmpty(hotelEntities)) {
                            return Collections.emptySet();
                        }

                        // 使用流式处理提取和汇总roleCodes
                        return hotelEntities.stream()
                                .map(HdsEmployeeHotelEntity::getRoleCode)
                                .filter(StringUtils::isNotBlank)
                                .map(roleCodesStr -> roleCodesStr.split(","))
                                .flatMap(Arrays::stream)
                                .map(String::trim)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toSet());
                    });

            return roleCodesMono.flatMapMany(roleCodes -> {
                // 判断是否需要OTA过滤
                boolean isOtaAdminOnly = CollectionUtils.isNotEmpty(roleCodes) &&
                        roleCodes.contains(RoleEnum.OTA_AGENT_ADMIN.getCode()) &&
                        !roleCodes.contains(RoleEnum.HOTEL_ADMIN.getCode());

                // 使用带角色过滤的查询方法
                int otaFilterFlag = isOtaAdminOnly ? 1 : 0;

                return hdsEmployeeRepository.exportList(hotelCode,
                                request.getStatus(), request.getRoleCode(), request.getUserKeyword(), request.getGroupKeyword(),
                                request.getType(),otaFilterFlag)
                        .collectList()
                        .flatMapMany(this::enrichEmployeesWithTickets);
            });
        });
    }

    /**
     * 为员工数据批量添加工单信息
     */
    private Flux<EmployeeExportDTO> enrichEmployeesWithTickets(List<HdsEmployeeVO> employees) {
        if (employees.isEmpty()) {
            return Flux.empty();
        }

        // 提取所有员工ID
        List<Integer> employeeIds = employees.stream()
                .map(HdsEmployeeVO::getId)
                .collect(Collectors.toList());

        // 批量获取所有工单映射
        return hdsEmployeeTicketMappingService.findByEmployeeIds(employeeIds)
                .flatMapMany(mappingMap -> Flux.fromIterable(employees)
                        .flatMap(employee -> enrichSingleEmployee(employee, mappingMap.get(employee.getId())))
                );
    }

    /**
     * 为单个员工添加工单信息
     */
    private Mono<EmployeeExportDTO> enrichSingleEmployee(HdsEmployeeVO employee, List<HdsEmployeeTicketMappingEntity> mappings) {
        EmployeeExportDTO employeeExportDTO = new EmployeeExportDTO();
        BeanUtils.copyProperties(employee, employeeExportDTO);
        if (CollectionUtils.isEmpty(mappings) || StringUtils.isBlank(employee.getHotelCode())) {
            employeeExportDTO.setTicketLabels("");
            return Mono.just(employeeExportDTO);
        }

        // 找到与当前员工和门店匹配的工单映射
        return Mono.justOrEmpty(
                        mappings.stream()
                                .filter(m -> StringUtils.equals(employee.getHotelCode(), m.getHotelCode()))
                                .findFirst()
                ).map(mapping -> {
                    // 设置账号类型
                    employee.setAccountType(mapping.getEmployeeType());
                    employee.setTicketAssignmentFlag(mapping.getTicketAssignmentFlag());
                    employeeExportDTO.setAccountType(mapping.getEmployeeType());
                    employeeExportDTO.setTicketAssignmentLabel(ObjectUtil.equal(mapping.getTicketAssignmentFlag(),1)? "是":"否");

                    // 处理工单类别
                    Optional.ofNullable(mapping.getTicketCategories())
                            .filter(StringUtils::isNotBlank)
                            .map(str -> Arrays.asList(str.split(",")))
                            .ifPresent(tickets -> {
                                employee.setTickets(tickets);
                                employee.setTicketLabels(tickets.stream()
                                        .map(code -> Optional.ofNullable(ServiceCategory.getByCode(code, false))
                                                .map(ServiceCategory::getChineseName)
                                                .orElse(code))
                                        .collect(Collectors.toList()));

                                employeeExportDTO.setTicketLabels(String.join(",", employee.getTicketLabels()));
                            });

                    return employeeExportDTO;
                }).defaultIfEmpty(employeeExportDTO)
                .flatMap(employeeExportDTO1 -> {
                    if (StringUtils.equals(RoleEnum.HOTEL_ADMIN.getCode(), employeeExportDTO1.getRoleCode())) {
                        employeeExportDTO1.setAccountType(null);
                        employeeExportDTO1.setTicketLabels(null);
                    }
                    if (StringUtils.equals(RoleEnum.HOTEL_STAFF.getCode(), employeeExportDTO1.getRoleCode())
                            && Objects.equals(AccountTypeEnum.MAIN_SWITCHBOARD.getCode(), employeeExportDTO1.getAccountType())) {
                        employeeExportDTO1.setTicketLabels(null);
                    }
                    return Mono.just(employeeExportDTO1);
                });
    }


//    /**
//     * 根据导出类型和请求参数构建查询条件
//     */
//    private Mono<Criteria> buildQueryCriteria(EmployeeExportReq request) {
//        // 根据导出类型选择不同的查询策略
//        ExportTypeEnum exportType = ExportTypeEnum.getByCode(request.getExportType());
//
//        switch (exportType) {
//            case ALL:
//                // 全部导出 - 使用空条件
//                return Mono.just(Criteria.empty());
//            case CURRENT:
//                // 当前页导出 - 根据IDs列表
//                if (CollectionUtils.isEmpty(request.getCurrentIds())) {
//                    // 如果ID列表为空，返回一个永远为false的条件，确保不会有数据
//                    return Mono.just(Criteria.where(HdsEmployeeFieldEnum.id.name()).is(-1));
//                }
//                return Mono.just(Criteria.where(HdsEmployeeFieldEnum.id.name()).in(request.getCurrentIds()));
//            default:
//                // 筛选导出 - 使用过滤条件
//                return Mono.just(buildFilterCriteria(request));
//        }
//    }

    /**
     * 将数据转成excel行
     *
     * @param data 员工数据
     * @return 转换后的Excel行数据
     */
    @Override
    public List<String> convertToRow(EmployeeExportDTO data) {
        // 定义表头到数据提取器的映射
        Map<String, Function<EmployeeExportDTO, String>> extractors = new LinkedHashMap<>();
        extractors.put(FIELD_EMPLOYEE_ID, dto -> dto.getId() != null ? dto.getId().toString() : "");
        extractors.put(FIELD_RELATION_HOTEL, EmployeeExportDTO::getHotelName);
        extractors.put(FIELD_EMPLOYEE_NAME, EmployeeExportDTO::getName);
        extractors.put(FIELD_EMPLOYEE_USERNAME, EmployeeExportDTO::getUsername);
        extractors.put(FIELD_EMPLOYEE_MOBILE, EmployeeExportDTO::getMobile);
        extractors.put(FIELD_EMPLOYEE_EMAIL, EmployeeExportDTO::getEmail);
        extractors.put(FIELD_EMPLOYEE_ROLE_TYPE, dto -> formatRoleType(dto.getRoleCode()));
        extractors.put(FIELD_EMPLOYEE_ACCOUNT_TYPE, dto -> formatAccountType(dto.getAccountType()));
//        extractors.put(FIELD_EMPLOYEE_TICKET_TYPE, EmployeeExportDTO::getTicketLabels);
        extractors.put(FIELD_EMPLOYEE_TICKET_ASSIGNMENT_FLAG, EmployeeExportDTO::getTicketAssignmentLabel);
//        extractors.put(FIELD_EMPLOYEE_POSITION_DETAILS, EmployeeExportDTO::getPositionDetails);
//        extractors.put(FIELD_EMPLOYEE_CREATE_BY, EmployeeExportDTO::getCreatedBy);

        // 按照表头顺序提取数据
        List<String> row = new ArrayList<>();
        for (String header : getExcelHeaders()) {
            Function<EmployeeExportDTO, String> extractor = extractors.get(header);
            if (extractor != null) {
                String value = extractor.apply(data);
                row.add(value != null ? value : "");
            } else {
                // 未知表头，添加空值
                row.add("");
            }
        }

        return row;
    }

    /**
     * 格式化用户类型
     */
    private String formatRoleType(String roleCode) {
        return Optional.ofNullable(roleCode)
                .map(RoleEnum::fromCode)
                .map(RoleEnum::getName)
                .orElse("");
    }

    /**
     * 格式化账号类型
     */
    private String formatAccountType(Integer accountType) {
        return Optional.ofNullable(accountType)
                .map(AccountTypeEnum::getByCode)
                .map(AccountTypeEnum::getDescription)
                .orElse("");
    }

    /**
     * 获取导出文件名
     *
     * @param request 导出请求
     * @return 文件名
     */
    @Override
    public String getExportFileName(EmployeeExportReq request) {
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now());
        return "员工列表_" + dateStr + ".xlsx";
    }

    /**
     * 获取请求类
     *
     * @return 请求类Class
     */
    @Override
    public Class<EmployeeExportReq> getRequestClass() {
        return EmployeeExportReq.class;
    }

    @Override
    public Map<String, Integer> getColumnWidthMap() {
        Map<String, Integer> widthMap = new HashMap<>();
        widthMap.put(FIELD_EMPLOYEE_ID, 8);
        widthMap.put(FIELD_EMPLOYEE_NAME, 12);
        widthMap.put(FIELD_EMPLOYEE_USERNAME, 15);
        widthMap.put(FIELD_EMPLOYEE_MOBILE, 15);
        widthMap.put(FIELD_EMPLOYEE_EMAIL, 15);
        widthMap.put(FIELD_EMPLOYEE_ROLE_TYPE, 20);
        widthMap.put(FIELD_EMPLOYEE_ACCOUNT_TYPE, 15);
        widthMap.put(FIELD_EMPLOYEE_TICKET_ASSIGNMENT_FLAG, 20);
//        widthMap.put(FIELD_EMPLOYEE_CREATE_BY, 20);
//        widthMap.put(FIELD_EMPLOYEE_POSITION_DETAILS, 20);
        return widthMap;
    }


    @Override
    public Set<String> getHiddenColumns() {
        Set<String> hiddenColumns = new HashSet<>();
        hiddenColumns.add(FIELD_EMPLOYEE_ID); // 隐藏ID列
        return hiddenColumns;
    }
}
