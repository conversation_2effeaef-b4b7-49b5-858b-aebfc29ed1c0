package com.wormhole.hotelds.excel.device;

/**
 * @Author：flx
 * @Date：2025/4/10 14:19
 * @Description：设备基础校验规则
 */

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Function;

/**
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DeviceValidationRule {

    MODEL_CODE("型号编码", DeviceImportDTO::getModelCode, 20, false),
    DEVICE_SN("设备SN", DeviceImportDTO::getDeviceSn, 50, false),
    DEVICE_IMEI("设备imei", DeviceImportDTO::getImei, 50, false),
    WARRANTY_START("保修开始时间", DeviceImportDTO::getWarrantyStart, null, true),
    WARRANTY_END("保修结束时间", DeviceImportDTO::getWarrantyEnd, null, true),
    PURCHASE_TIME("采购时间", DeviceImportDTO::getPurchaseTime, null, true),
    REMARK("备注", DeviceImportDTO::getRemark, 255, false);

    private final String fieldName;
    private final Function<DeviceImportDTO, String> valueGetter;
    private final Integer maxLength;
    private final boolean required;
}
