package com.wormhole.hotelds.excel.employee;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wormhole.hotelds.core.model.entity.HdsEmployeeEntity;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/9 13:44
 * @Description：EmployeeExportDTO
 */
@Data
public class EmployeeExportDTO {

    private Integer id;

    /**
     * 账号
     */
    private String username;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 性别 1男 2女
     */
    private Integer gender;

    /**
     * 状态 1正常 2停用 3注销
     */
    private Integer status;

    /**
     * 员工类型 1-集团 2-服务商 3-酒店
     */
    private Integer type;

    private String roleCode;

    private String roleName;

    /**
     * 账号类型，1总机 2分机
     */
    private Integer accountType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 工单类型标签列表
     */
    private String ticketLabels;

    private String ticketAssignmentLabel;

    /**
     * 工单类型列表
     */
    private String tickets;

    /**
     * 酒店编码
     */
    private String hotelCode;


    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 位置
     */
    private List<String> positionDetails;

    /**
     * 创建人
     */
    private String createdBy;
}
