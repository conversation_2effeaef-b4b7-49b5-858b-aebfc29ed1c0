package com.wormhole.hotelds.excel;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author：flx
 * @Date：2025/4/1 09:04
 * @Description：数据处理器接口
 */
public interface DataImportProcessor<T,E> {

    /**
     * 获取此处理器支持的业务类型
     *
     * @return 业务类型标识符
     */
    String getBusinessType();

    /**
     * 获取Excel必需的表头字段
     *
     * @return 必需的表头字段集合
     */
    Set<String> getRequiredHeaders(ImportContext context);

    /**
     * 验证数据有效性
     *
     * @param data 待验证的数据流
     * @return 验证后的数据流
     */
    Flux<T> validateData(Flux<T> data, ImportContext context);

    /**
     * 将原始数据转换为实体对象
     *
     * @param rawData    原始数据流
     * @param context 用户上下文信息
     * @return 实体对象流
     */
    Flux<T> convertData(Flux<Map<String, String>> rawData, ImportContext context);


    /**
     * 将DTO转换为实体对象集合
     * 用于处理一个DTO需要转换为多个关联实体的场景
     *
     * @param context 用户上下文信息
     * @return 转换后的实体对象集合
     */
    Flux<E> toEntities(Flux<T> data, ImportContext context);

    /**
     * 批量转换实体对象
     * 默认实现是将List转换为Flux进行处理
     * 子类可以覆盖此方法实现批量优化
     *
     * @param dtoList DTO对象列表
     * @param context 用户上下文信息
     * @return 转换后的实体对象列表
     */
    default Mono<List<E>> toBatchEntities(List<T> dtoList, ImportContext context) {
        if (dtoList == null || dtoList.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }
        return toEntities(Flux.fromIterable(dtoList), context).collectList();
    }

    /**
     * 批量保存实体
     *
     * @param entities 实体列表
     * @return 保存成功的数量
     */
    Mono<Integer> saveData(List<E> entities, ImportContext context);

    /**
     * 导出数据
     *
     * @param params 查询参数
     * @return 导出的实体数据流
     */
    Flux<T> exportData(Map<String, Object> params);

    /**
     * 获取导出文件名
     *
     * @param params 查询参数
     * @return 文件名
     */
    String getExportFileName(Map<String, Object> params);
}
