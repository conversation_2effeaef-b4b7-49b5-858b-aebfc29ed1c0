package com.wormhole.hotelds.excel;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/4/9 17:54
 * @Description：EntityCollection
 */
@Data
@Builder
public class EntityCollection {

    private Map<String, Object> entities;
    private Integer rowNum;

    public <E> E getEntity(String key) {
        return (E) entities.get(key);
    }

    public void addEntity(String key, Object entity) {
        if (entities == null) {
            entities = new HashMap<>();
        }
        entities.put(key, entity);
    }
}
