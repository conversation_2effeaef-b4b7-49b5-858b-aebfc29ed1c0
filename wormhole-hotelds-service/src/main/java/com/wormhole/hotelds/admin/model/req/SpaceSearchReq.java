package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/3/26 19:37
 * @Description：SpaceSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SpaceSearchReq extends QueryCondition {

    /**
     * 所属门店ID
     */
    private Long hotelId;

    /**
     * 楼层
     */
    private Integer floor;

    /**
     * 空间类型，如：客房/前台/餐厅/健身房等
     */
    private String spaceType;

    /**
     * 空间编号（房号或自定义编码）
     */
    private String spaceCode;
}
