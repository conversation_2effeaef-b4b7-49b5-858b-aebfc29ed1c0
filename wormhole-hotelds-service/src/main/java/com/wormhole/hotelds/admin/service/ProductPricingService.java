package com.wormhole.hotelds.admin.service;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.PeriodTypeEnum;
import com.wormhole.hotelds.admin.model.req.PackageSaveReq;
import com.wormhole.hotelds.admin.repository.HdsProductPricingRepository;
import com.wormhole.hotelds.core.model.entity.HdsProductPricingEntity;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/6/16 14:14
 * @Description：ProductPricingService
 */
@Service
@Slf4j
public class ProductPricingService {

    @Resource
    private HdsProductPricingRepository hdsProductPricingRepository;

    /**
     * 为产品创建定价
     */
    public Mono<Boolean> createPricingForProduct(Integer productId,
                                                 List<PackageSaveReq.PricingSaveReq> pricingList,
                                                 HeaderUtils.HeaderInfo headerInfo,
                                                 Integer discountMode,
                                                 List<HdsProductPricingEntity> entities) {
        if (CollectionUtils.isEmpty(pricingList)) {
            return Mono.just(true);
        }

        // 根据优惠方式计算完整的定价数据
        List<HdsProductPricingEntity> calculatedPricingList = calculatePricingByDiscountMode(productId, pricingList, headerInfo, discountMode, entities);

        return Flux.fromIterable(calculatedPricingList)
                .flatMap(hdsProductPricingRepository::save)
                .then(Mono.just(true))
                .onErrorResume(e -> {
                    log.error("创建产品定价失败: {}", e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建产品定价失败"));
                });
    }

    /**
     * 根据优惠方式计算定价
     */
    private List<HdsProductPricingEntity> calculatePricingByDiscountMode(Integer productId,
                                                                         List<PackageSaveReq.PricingSaveReq> pricingList,
                                                                         HeaderUtils.HeaderInfo headerInfo,
                                                                         Integer discountMode,
                                                                         List<HdsProductPricingEntity> entities) {
        // 将定价按周期类型分组
        Map<Integer, PackageSaveReq.PricingSaveReq> pricingMap = pricingList.stream()
                .collect(Collectors.toMap(PackageSaveReq.PricingSaveReq::getPeriodType, p -> p));

        if (discountMode == 1) {
            // 一口价模式
            return calculateFixedPricing(productId, pricingMap, headerInfo, entities);
        } else {
            // 折扣模式
            return calculateDiscountPricing(productId, pricingMap, headerInfo, entities);
        }
    }


    /**
     * 一口价模式计算
     */
    private List<HdsProductPricingEntity> calculateFixedPricing(Integer productId,
                                                                Map<Integer, PackageSaveReq.PricingSaveReq> pricingMap,
                                                                HeaderUtils.HeaderInfo headerInfo,
                                                                List<HdsProductPricingEntity> entities) {
        List<HdsProductPricingEntity> result = new ArrayList<>();

        // 获取月度定价
        PackageSaveReq.PricingSaveReq monthlyPricing = pricingMap.get(PeriodTypeEnum.MONTHLY.getCode());
        BigDecimal monthlyMarketPrice = new BigDecimal(monthlyPricing.getMarketPrice());
        BigDecimal monthlyDiscountPrice = new BigDecimal(monthlyPricing.getDiscountPrice());

        Map<Integer, HdsProductPricingEntity> entityMap = entities.stream()
                .collect(Collectors.toMap(HdsProductPricingEntity::getId, Function.identity()));

        // 处理所有周期
        for (PeriodTypeEnum periodType : PeriodTypeEnum.values()) {
            PackageSaveReq.PricingSaveReq pricing = getOrCreatePricing(pricingMap, periodType);

            HdsProductPricingEntity hdsProductPricingEntity;
            if (PeriodTypeEnum.MONTHLY.equals(periodType)) {
                hdsProductPricingEntity = buildPricingEntity(productId, pricing, headerInfo, entityMap, 1);
            } else {
                // 季度/年度：基于月度计算
                int multiplier = getMultiplier(periodType);
                calculateFixedPricingForPeriod(pricing, monthlyMarketPrice, monthlyDiscountPrice, multiplier);
                hdsProductPricingEntity = buildPricingEntity(productId, pricing, headerInfo, entityMap, 1);
            }
            if (Objects.nonNull(hdsProductPricingEntity)) {
                result.add(hdsProductPricingEntity);
            }
        }

        return result;
    }

    /**
     * 获取或创建定价对象
     */
    private PackageSaveReq.PricingSaveReq getOrCreatePricing(Map<Integer, PackageSaveReq.PricingSaveReq> pricingMap, PeriodTypeEnum periodType) {
        PackageSaveReq.PricingSaveReq pricing = pricingMap.get(periodType.getCode());
        if (pricing == null) {
            pricing = new PackageSaveReq.PricingSaveReq();
            pricing.setPeriodType(periodType.getCode());
        }
        return pricing;
    }

    /**
     * 获取周期倍数
     */
    private int getMultiplier(PeriodTypeEnum periodType) {
        return switch (periodType) {
            case QUARTERLY -> 3;
            case YEARLY -> 12;
            default -> 1;
        };
    }

    /**
     * 计算一口价模式的周期定价
     */
    private void calculateFixedPricingForPeriod(PackageSaveReq.PricingSaveReq pricing,
                                                BigDecimal monthlyMarketPrice,
                                                BigDecimal monthlyDiscountPrice,
                                                int multiplier) {
        // 门市价 = 月度门市价 * 倍数
        BigDecimal marketPrice = monthlyMarketPrice.multiply(BigDecimal.valueOf(multiplier));
        pricing.setMarketPrice(marketPrice.toString());

        // 优惠价计算
        BigDecimal discountPrice;
        if (StringUtils.isNotBlank(pricing.getDiscountPrice()) && !pricing.getDiscountPrice().equals("0")) {
            // 有自定义一口价，直接使用
            discountPrice = new BigDecimal(pricing.getDiscountPrice());
        } else {
            // 没有自定义，基于月度计算：月度优惠价 * 倍数
            discountPrice = monthlyDiscountPrice.multiply(BigDecimal.valueOf(multiplier));
        }
        pricing.setDiscountPrice(discountPrice.toString());
    }

    /**
     * 折扣模式计算
     */
    private List<HdsProductPricingEntity> calculateDiscountPricing(Integer productId,
                                                                   Map<Integer, PackageSaveReq.PricingSaveReq> pricingMap,
                                                                   HeaderUtils.HeaderInfo headerInfo,
                                                                   List<HdsProductPricingEntity> entities) {
        List<HdsProductPricingEntity> result = new ArrayList<>();

        // 获取月度定价（必填）
        PackageSaveReq.PricingSaveReq monthlyPricing = pricingMap.get(PeriodTypeEnum.MONTHLY.getCode());
        BigDecimal monthlyMarketPrice = new BigDecimal(monthlyPricing.getMarketPrice());
        BigDecimal monthlyDiscountPrice = new BigDecimal(monthlyPricing.getDiscountPrice());

        Map<Integer, HdsProductPricingEntity> entityMap = entities.stream()
                .collect(Collectors.toMap(HdsProductPricingEntity::getId, Function.identity()));

        for (PeriodTypeEnum periodType : PeriodTypeEnum.values()) {
            PackageSaveReq.PricingSaveReq pricing = getOrCreatePricing(pricingMap, periodType);

            HdsProductPricingEntity hdsProductPricingEntity;
            if (PeriodTypeEnum.MONTHLY.equals(periodType)) {
                // 月度：直接使用原始定价
                hdsProductPricingEntity = buildPricingEntity(productId, pricing, headerInfo, entityMap, 0);
            } else {
                // 季度/年度：基于月度计算
                int multiplier = getMultiplier(periodType);
                calculateDiscountPricingForPeriod(pricing, monthlyMarketPrice, monthlyDiscountPrice, multiplier);
                hdsProductPricingEntity = buildPricingEntity(productId, pricing, headerInfo, entityMap, 0);
            }

            if (Objects.nonNull(hdsProductPricingEntity)) {
                result.add(hdsProductPricingEntity);
            }
        }

        return result;
    }

    /**
     * 计算折扣模式的周期定价
     */
    private void calculateDiscountPricingForPeriod(PackageSaveReq.PricingSaveReq pricing,
                                                   BigDecimal monthlyMarketPrice,
                                                   BigDecimal monthlyDiscountPrice,
                                                   int multiplier) {
        // 门市价 = 月度门市价 * 倍数
        BigDecimal marketPrice = monthlyMarketPrice.multiply(BigDecimal.valueOf(multiplier));
        pricing.setMarketPrice(marketPrice.toString());

        // 获取额外折扣比例
        BigDecimal extraDiscountRate = StringUtils.isNotBlank(pricing.getDiscountRate())
                ? new BigDecimal(pricing.getDiscountRate())
                : BigDecimal.ZERO;

        // 优惠价计算：月度优惠价 * 倍数 * (1 - 额外折扣比例/100)
        BigDecimal discountPrice = monthlyDiscountPrice
                .multiply(BigDecimal.valueOf(multiplier))
                .multiply(BigDecimal.ONE.subtract(extraDiscountRate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP)))
                .setScale(2, RoundingMode.HALF_UP);
        pricing.setDiscountPrice(discountPrice.toString());

        // 折扣比例
        pricing.setDiscountRate(extraDiscountRate.toString());
    }

    /**
     * 构建定价实体
     */
    private HdsProductPricingEntity buildPricingEntity(Integer productId,
                                                       PackageSaveReq.PricingSaveReq pricingReq,
                                                       HeaderUtils.HeaderInfo headerInfo,
                                                       Map<Integer, HdsProductPricingEntity> entityMap,
                                                       Integer discountMode) {
        HdsProductPricingEntity entity = getOrCreateEntity(pricingReq, entityMap);
        if (entity == null) {
            return null;
        }

        // 2. 检查是否需要更新（仅对已存在实体）
        if (pricingReq.getId() != null && !checkPricingNeedUpdate(entity, pricingReq, discountMode)) {
            return null;
        }

        entity.setProductId(productId);
        entity.setPeriodType(pricingReq.getPeriodType());
        entity.setMarketPrice(new BigDecimal(pricingReq.getMarketPrice()));

        // 一口价模式
        if (StringUtils.isNotBlank(pricingReq.getDiscountPrice())) {
            entity.setDiscountPrice(new BigDecimal(pricingReq.getDiscountPrice()));
            entity.setFinalPrice(new BigDecimal(pricingReq.getDiscountPrice()));
        }

        // 折扣模式
        if (StringUtils.isNotBlank(pricingReq.getDiscountRate())) {
            entity.setDiscountRate(new BigDecimal(pricingReq.getDiscountRate()));
        }

        // 设置审计字段
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setRowStatus(1);
        return entity;
    }

    /**
     * 获取或创建实体
     */
    private HdsProductPricingEntity getOrCreateEntity(PackageSaveReq.PricingSaveReq pricingReq,
                                                      Map<Integer, HdsProductPricingEntity> entityMap) {
        if (pricingReq.getId() == null) {
            return new HdsProductPricingEntity();
        }

        return entityMap.get(pricingReq.getId());
    }

    public Flux<HdsProductPricingEntity> findByProductId(Integer productId) {
        return hdsProductPricingRepository.findByProductId(productId);
    }

    public Flux<HdsProductPricingEntity> findAllByProductIdIn(List<Integer> productIds) {
        return hdsProductPricingRepository.findAllByProductIdIn(productIds);
    }

    public Flux<Boolean> deletePricingByProductIds(List<Integer> ids) {
        return hdsProductPricingRepository.deleteAllByProductIdIn(ids);
    }

    /**
     * 更新产品定价信息
     */
    public Mono<Boolean> updatePricingForProduct(Integer productId, List<PackageSaveReq.PricingSaveReq> pricingList, HeaderUtils.HeaderInfo headerInfo, Integer discountMode) {
        if (CollectionUtils.isEmpty(pricingList)) {
            return Mono.just(true);
        }

        Set<Integer> priceIds = pricingList.stream().map(PackageSaveReq.PricingSaveReq::getId).collect(Collectors.toSet());
        return hdsProductPricingRepository.findByIdIn(priceIds)
                .collectList()
                .flatMap(entities ->
                        createPricingForProduct(productId, pricingList, headerInfo, discountMode, entities));
    }

    /**
     * 检查价格信息是否需要更新
     */
    private boolean checkPricingNeedUpdate(HdsProductPricingEntity entity, PackageSaveReq.PricingSaveReq req, Integer discountMode) {
        // 检查门市价是否变化
        BigDecimal reqMarketPrice = new BigDecimal(req.getMarketPrice());
        if (entity.getMarketPrice().compareTo(reqMarketPrice) != 0) {
            return true;
        }

        // 按照折扣模式检查
        if (Objects.equals(discountMode, 1)) {
            // 一口价模式，检查折扣价
            if (StringUtils.isNotBlank(req.getDiscountPrice())) {
                BigDecimal reqDiscountPrice = new BigDecimal(req.getDiscountPrice());
                return entity.getDiscountPrice() == null || entity.getDiscountPrice().compareTo(reqDiscountPrice) != 0;
            }
        } else {
            // 折扣模式，检查折扣率
            if (StringUtils.isNotBlank(req.getDiscountRate())) {
                BigDecimal reqDiscountRate = new BigDecimal(req.getDiscountRate());
                return entity.getDiscountRate() == null || entity.getDiscountRate().compareTo(reqDiscountRate) != 0;
            }
        }

        return false;
    }

    /**
     * 更新价格实体信息
     */
    private void updatePricingEntity(HdsProductPricingEntity entity, PackageSaveReq.PricingSaveReq req, Integer discountMode, HeaderUtils.HeaderInfo headerInfo) {
        // 设置门市价
        entity.setMarketPrice(new BigDecimal(req.getMarketPrice()));

        // 根据折扣模式设置不同的价格
        if (Objects.equals(discountMode, 1)) {
            // 一口价模式
            BigDecimal discountPrice = new BigDecimal(req.getDiscountPrice());
            entity.setDiscountPrice(discountPrice);
            entity.setDiscountRate(null);
            entity.setFinalPrice(discountPrice);
        } else {
            // 折扣模式
            BigDecimal discountRate = new BigDecimal(req.getDiscountRate());
            entity.setDiscountRate(discountRate);

            // 计算最终价格 = 门市价 * 折扣比例 / 100
            BigDecimal finalPrice = entity.getMarketPrice()
                    .multiply(discountRate)
                    .divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
            entity.setDiscountPrice(finalPrice);
            entity.setFinalPrice(finalPrice);
        }

        // 设置更新信息
        entity.setUpdatedBy(headerInfo.getUserId());
        entity.setUpdatedByName(headerInfo.getUsername());
        entity.setUpdatedAt(LocalDateTime.now());
    }

    public Mono<HdsProductPricingEntity> findByProductIdAndPeriodType(Integer productId, Integer periodType) {
        return hdsProductPricingRepository.findByProductIdAndPeriodType(productId, periodType);
    }
}