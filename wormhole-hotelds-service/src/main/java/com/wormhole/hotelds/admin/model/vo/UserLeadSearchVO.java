package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/5/20 10:28
 * @Description：MerchantLeadSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserLeadSearchVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 线索编码
     */
    private String leadCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 线索状态: 1-有效, 0-无效
     */
    private Integer status;

    /**
     * 获取EBK数
     */
    private Integer ebkCount;

    /**
     * 创建门店数
     */
    private Integer hotelCount;

    /**
     * 邀请人
     */
    private String invitedBy;

    /**
     * 被邀请码
     */
    private String inviteCode;

    /**
     * 注册时间
     */
    private String registeredAt;
}
