package com.wormhole.hotelds.admin.model.dto;

import lombok.Data;

/**
 * @Author：flx
 * @Date：2025/5/22 14:43
 * @Description：TaskProgressDTO
 */
@Data
public class LeadProgressDTO {

    /**
     * 确认绑定EBK完成时间
     */
    private String bindEbk;

    /**
     * 设置竞对酒店完成时间
     */
    private String setRivalHotel;

    /**
     * 同步全量点评完成时间
     */
    private String syncReview;

    /**
     * 同步酒店静态信息完成时间
     */
    private String syncStaticInfo;

    /**
     * 同步竞对评论完成时间
     */
    private String syncRivalReview;

    /**
     * 同步竞对价格完成时间
     */
    private String syncRivalPrice;

    /**
     * 生成初始化报告完成时间
     */
    private String generateReport;
}
