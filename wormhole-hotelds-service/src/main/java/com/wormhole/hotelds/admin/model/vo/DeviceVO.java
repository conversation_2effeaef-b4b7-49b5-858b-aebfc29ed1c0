package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import lombok.*;
import lombok.experimental.*;
import org.springframework.beans.BeanUtils;

import java.io.*;
import java.time.LocalDateTime;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:57:52
 * @Description: Device
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceVO {

    private Long id;

    /**
     * 设备类型id
     */
    private Long modelId;

    /**
     * 设备code
     */
    private String deviceCode;

    /**
     * sn
     */
    private String deviceSn;

    /**
     * 设备IMEI号
     */
    private String imei;

    /**
     * 设备状态
     */
    private Integer deviceStatus;

    /**
     * 设备类型code
     */
    private String modelCode;

    /**
     * 设备类型名称
     */
    private String modelName;

    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 楼栋
     */
    private String block;

    /**
     * 区域
     */
    private String area;

    /**
     * 位置名称
     */
    private String positionName;

    /**
     * 设备类型
     */
    private String deviceAppType;

    /**
     * 保修开始时间
     */
    private String warrantyStart;

    /**
     * 保修结束时间
     */
    private String warrantyEnd;

    /**
     * 采购时间
     */
    private String purchaseTime;

    /**
     * 备注
     */
    private String remark;

    public static DeviceVO toVo(HdsDeviceEntity hdsDeviceEntity) {
        DeviceVO deviceVO = new DeviceVO();
        BeanUtils.copyProperties(hdsDeviceEntity, deviceVO);
        return deviceVO;
    }
}