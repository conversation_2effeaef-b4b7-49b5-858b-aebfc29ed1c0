package com.wormhole.hotelds.admin.dao;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.admin.model.qo.HdsHotelMappingQO;
import com.wormhole.hotelds.admin.repository.HdsHotelMappingRepository;
import com.wormhole.hotelds.core.model.entity.HdsHotelMappingEntity;
import com.wormhole.hotelds.core.model.entity.HdsHotelMappingFieldEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

@Repository
public class HdsHotelMappingDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Autowired
    private HdsHotelMappingRepository hotelMappingRepository;


    public Mono<HdsHotelMappingEntity> findOne(HdsHotelMappingQO hotelMappingQO){
        Criteria criteria = getCriteria(hotelMappingQO);
        return r2dbcEntityTemplate.selectOne(Query.query(criteria).limit(1),HdsHotelMappingEntity.class);
    }


    public Mono<List<HdsHotelMappingEntity>> findList(HdsHotelMappingQO hotelMappingQO){
        Criteria criteria = getCriteria(hotelMappingQO);
        return r2dbcEntityTemplate.select(Query.query(criteria).limit(1),HdsHotelMappingEntity.class).collectList();
    }

    public Mono<HdsHotelMappingEntity> save(HdsHotelMappingEntity hotelMappingEntity){
        return hotelMappingRepository.save(hotelMappingEntity);
    }

    private Criteria getCriteria(HdsHotelMappingQO hotelMappingQO){
        Criteria criteria = Criteria.empty();
        if(StringUtils.isNotBlank(hotelMappingQO.getHotelCode())) {
            criteria = criteria.and(Criteria.where(HdsHotelMappingFieldEnum.hotel_code.name()).is(hotelMappingQO.getHotelCode()));
        }
        if(StringUtils.isNotBlank(hotelMappingQO.getChannel())) {
            criteria = criteria.and(Criteria.where(HdsHotelMappingFieldEnum.channel.name()).is(hotelMappingQO.getChannel()));
        }
        if(StringUtils.isNotBlank(hotelMappingQO.getPlatform())) {
            criteria = criteria.and(Criteria.where(HdsHotelMappingFieldEnum.platform.name()).is(hotelMappingQO.getPlatform()));
        }
        if(StringUtils.isNotBlank(hotelMappingQO.getExternalId())) {
            criteria = criteria.and(Criteria.where(HdsHotelMappingFieldEnum.external_id.name()).is(hotelMappingQO.getExternalId()));
        }
        if(CollUtil.isNotEmpty(hotelMappingQO.getExternalIds())) {
            criteria = criteria.and(Criteria.where(HdsHotelMappingFieldEnum.external_id.name()).in(hotelMappingQO.getExternalIds()));
        }
        if(CollUtil.isNotEmpty(hotelMappingQO.getHotelCodes())) {
            criteria = criteria.and(Criteria.where(HdsHotelMappingFieldEnum.hotel_code.name()).in(hotelMappingQO.getHotelCodes()));
        }
        if(Objects.nonNull(hotelMappingQO.getId())) {
            criteria = criteria.and(Criteria.where(HdsHotelMappingFieldEnum.id.name()).is(hotelMappingQO.getId()));
        }

        criteria = criteria.and(Criteria.where(HdsHotelMappingFieldEnum.status.name()).is(RowStatusEnum.VALID.getId()));
        return criteria;
    }
}
