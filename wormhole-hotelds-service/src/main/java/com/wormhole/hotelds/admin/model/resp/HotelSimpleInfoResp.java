package com.wormhole.hotelds.admin.model.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelSimpleInfoResp implements Serializable {
    /**
     * 酒店代码
     */
    private String hotelCode;
    /**
     * 酒店全称
     */
    private String hotelName;
    /**
     * 英文名称
     */
    private String hotelNameEng;


    public static HotelSimpleInfoResp toSimpleInfoResp(HotelInfoResp hotelInfoResp) {
        HotelSimpleInfoResp simpleInfoResp = new HotelSimpleInfoResp();
        simpleInfoResp.setHotelCode(hotelInfoResp.getHotelCode());
        simpleInfoResp.setHotelName(hotelInfoResp.getHotelName());
        simpleInfoResp.setHotelNameEng(hotelInfoResp.getHotelNameEng());
        return simpleInfoResp;
    }
}
