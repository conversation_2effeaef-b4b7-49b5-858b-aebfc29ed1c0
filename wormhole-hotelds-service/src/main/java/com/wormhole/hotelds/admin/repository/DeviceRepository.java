package com.wormhole.hotelds.admin.repository;

import aj.org.objectweb.asm.commons.Remapper;
import com.wormhole.hotelds.admin.model.dto.DeviceDistributionDTO;
import com.wormhole.hotelds.admin.model.dto.DeviceStatisticsDTO;
import com.wormhole.hotelds.admin.model.dto.HotelStatisticsDTO;
import com.wormhole.hotelds.core.model.entity.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:57:52
 * @Description: Device
 */
@Repository
public interface DeviceRepository extends ReactiveCrudRepository<HdsDeviceEntity, Long> {

    /**
     * 检查设备编码是否存在
     * @param deviceId 设备编码
     * @return 是否存在
     */
    Mono<Boolean> existsByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 查询指定酒店列表下的活跃设备
     */
    Flux<HdsDeviceEntity> findByHotelCodeInAndDeviceStatusEquals(List<String> hotelCodes, Integer status);

    /**
     * 批量更新设备状态
     */
    @Modifying
    @Query("""
        UPDATE hds_device 
        SET device_status = :status,
            updated_by = :updatedBy,
            updated_by_name = :updatedByName,
            updated_at = NOW()
        WHERE id IN (:ids)
    """)
    Mono<Integer> updateDeviceStatus(
            @Param("ids") List<Long> ids,
            @Param("status") Integer status,
            @Param("updatedBy") String updatedBy,
            @Param("updatedByName") String updatedByName
    );

    /**
     * 根据位置编码查询设备
     */
    @Query("SELECT * FROM hds_device WHERE position_code = :positionCode AND device_status = 1")
    Flux<HdsDeviceEntity> findByPositionCode(@Param("positionCode") String positionCode);



    Flux<HdsDeviceEntity> findByHotelCode(String hotelCode);

    @Query("SELECT * FROM hds_device WHERE (device_sn IN (:deviceSns) OR imei IN (:imeis)) AND device_status != 0")
    Flux<HdsDeviceEntity> findByDeviceSnInOrImeiIn(
            @Param("deviceSns") Collection<String> deviceSns,
            @Param("imeis") Collection<String> imeis);

    @Query("SELECT * FROM hds_device WHERE imei IN (:imeis)")
    Flux<HdsDeviceEntity> findByImeiIn(
            @Param("imeis") Collection<String> allImeis);

    @Query("SELECT * FROM hds_device WHERE device_sn IN (:deviceSns)")
    Flux<HdsDeviceEntity> findByDeviceSnIn(
            @Param("deviceSns") Collection<String> deviceSns);


    Flux<HdsDeviceEntity> findByHotelCodeAndUserId(String hotelCode, String userId);

    /**
     * 根据门店code统计门店下的设备数量
     * @param hotelCode
     * @return
     */
    Mono<Long> countByHotelCode(String hotelCode);

    Flux<HdsDeviceEntity> findByDeviceSnInAndModelId(Collection<String> deviceSns, Long modelId);

    Flux<HdsDeviceEntity> findByImeiInAndModelId(Collection<String> imeis, Long modelId);
}