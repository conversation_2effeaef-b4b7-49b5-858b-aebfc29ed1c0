package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsMerchantLeadEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;

/**
 * @Author：flx
 * @Date：2025/5/20 10:54
 * @Description：MerchantLeadRepository
 */
@Repository
public interface MerchantLeadRepository extends ReactiveCrudRepository<HdsMerchantLeadEntity, Long> {

    Mono<Boolean> existsByInviteeId(Integer inviteeId);

    Mono<HdsMerchantLeadEntity> findByInviteeId(Integer inviteeId);

    Flux<HdsMerchantLeadEntity> findByInviteCodeIn(Collection<String> inviteCodes);
}
