package com.wormhole.hotelds.admin.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class CtripHotelPlaceInfoResponse {

    @JsonProperty("Result")
    private Boolean result;

    @JsonProperty("ErrorCode")
    private Integer errorCode;

    @JsonProperty("Response")
    private Response response;

    /**
     * 响应数据内部类
     */
    @Data
    public static class Response {

        @JsonProperty("placeInfoList")
        private List<HotelPlaceInfo> placeInfoList;
    }

    /**
     * 酒店周边地点信息分类
     */
    @Data
    public static class HotelPlaceInfo {

        @JsonProperty("typeId")
        private Integer typeId;

        @JsonProperty("typeName")
        private String typeName;

        @JsonProperty("list")
        private List<PlaceItem> list;
    }

    /**
     * 具体地点信息项
     */
    @Data
    public static class PlaceItem {

        @JsonProperty("distance")
        private String distance;

        @JsonProperty("placeName")
        private String placeName;

        @JsonProperty("lat")
        private Double lat;

        @JsonProperty("lng")
        private Double lng;

        @JsonProperty("arrivalWay")
        private String arrivalWay;

        @JsonProperty("transportType")
        private Integer transportType;

        @JsonProperty("distanceNumber")
        private Integer distanceNumber;
    }
}
