package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/3/26 14:42
 * @Description：BrandSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BrandSearchReq extends QueryCondition {

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌logo
     */
    private String brandLogo;

    /**
     * 品牌描述
     */
    private String brandDesc;

    /**
     * 公司编码
     */
    private String merchantId;

    /**
     * 公司名称
     */
    private String merchantName;

    /**
     * 星级类型
     */
    private Integer starType;

    /**
     * 状态
     */
    private Integer status;
}
