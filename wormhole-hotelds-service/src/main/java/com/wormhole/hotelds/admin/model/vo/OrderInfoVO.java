package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/7/2 16:25
 * @Description：OrderInfoVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderInfoVO {

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 周期类型
     */
    private Integer periodType;

    /**
     * 原价
     */
    private String originalAmount;

    /**
     * 优惠金额
     */
    private String discountAmount;

    /**
     * 订单金额
     */
    private String orderAmount;

    /**
     * 到期时间
     */
    private String expireTime;
}
