package com.wormhole.hotelds.admin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:59:28
 * @Description: 协议实体
 */
@Data
@Table("hds_agreement")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsAgreementEntity extends BaseEntity {

    /**
     * 协议ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 协议编号
     */
    private String agreementCode;

    /**
     * 协议类型：electronic电子协议/manual手工录入
     */
    private String agreementType;

    /**
     * 协议名称
     */
    private String agreementName;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 签约服务商ID（用于商户协议中）
     */
    private Long providerId;

    /**
     * 企业联系人姓名
     */
    private String contactName;

    /**
     * 企业联系人手机号
     */
    private String contactPhone;

    /**
     * 协议对象类型：merchant商户 / provider服务商
     */
    private String agreementSubjectType;

    /**
     * 协议对象ID（商户ID或服务商ID）
     */
    private Long agreementSubjectId;

    /**
     * 设备名称与型号，多个可用JSON或分隔
     */
    private String deviceModels;

    /**
     * 设备实施周期说明
     */
    private String implementationPeriod;

    /**
     * 设备试行周期（单位：天）
     */
    private Integer deviceTrialDays;

    /**
     * 云电话数量
     */
    private Integer devicePhoneNum;

    /**
     * 云电话设备单价
     */
    private BigDecimal devicePhonePrice;

    /**
     * 云电话设备总价
     */
    private BigDecimal totalPhonePrice;

    /**
     * 智能座机数量
     */
    private Integer deviceSeatNum;

    /**
     * 智能座机设备单价
     */
    private BigDecimal deviceSeatPrice;

    /**
     * 智能座机设备总价
     */
    private BigDecimal totalSeatPrice;

    /**
     * 设备总金额
     */
    private BigDecimal totalDevicePrice;

    /**
     * 云客服价格
     */
    private BigDecimal cloudServicePrice;

    /**
     * 计费周期类型：monthly/quarterly/yearly
     */
    private String payCycleType;

    /**
     * 协议金额（计费周期）
     */
    private BigDecimal payCyclePrice;

    /**
     * 总协议金额
     */
    private BigDecimal totalPrice;

    /**
     * 协议开始日期
     */
    private LocalDate startDate;

    /**
     * 协议结束日期
     */
    private LocalDate endDate;

    /**
     * 协议状态（pending:未签约, active:合作中, expired:已过期, terminated:已解约）
     */
    private String status;

    /**
     * 营业执照图片地址
     */
    private String licenseUrl;

    /**
     * 统一信用代码
     */
    private String creditCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业地址
     */
    private String companyAddress;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 法人身份证号码
     */
    private String legalIdNumber;

    /**
     * 法人身份证正面
     */
    private String legalIdFront;

    /**
     * 法人身份证反面
     */
    private String legalIdBack;

    /**
     * 开户名称
     */
    private String bankAccountName;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 开户支行
     */
    private String bankBranch;

    /**
     * 银行账号
     */
    private String bankAccountNumber;

    /**
     * 备注信息
     */
    private String remark;
}