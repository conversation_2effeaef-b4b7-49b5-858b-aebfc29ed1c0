package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/6/19 15:13
 * @Description：OrderSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderSearchVO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 周期类型
     */
    private Integer periodType;

    /**
     * 订单金额
     */
    private String orderAmount;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 产品到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 支付方式
     */
    private Integer paymentMethod;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
}
