package com.wormhole.hotelds.admin.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.model.entity.BaseEntity;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.annotation.*;
import com.wormhole.hotelds.admin.aop.LoggingContext;
import com.wormhole.hotelds.admin.model.dto.DeviceDistributionDTO;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.DeviceEventTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.repository.*;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.core.enums.DeviceStatusEnum;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.OperationLogUtil;
import com.wormhole.hotelds.util.SimpleDateUtils;
import com.wormhole.hotelds.util.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.*;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:57:52
 * @Description: Device
 */
@Service
@Slf4j
public class DeviceService {

    @Resource
    private DeviceInfoRepository deviceInfoRepository;

    @Resource
    private DeviceRepository deviceRepository;

    @Resource
    private DeviceModelRepository deviceModelRepository;

    @Resource
    private DevicePositionRepository devicePositionRepository;

    @Resource
    private HotelRepository hotelRepository;

    @Resource
    private TransactionalOperator transactionalOperator;
    @Resource
    private OperationLogUtil operationLogUtil;

    @Resource
    private DeviceLogRepository deviceLogRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HotelDsApiClient hotelDsApiClient;

    /**
     * 设备入库
     *
     * @param deviceSaveReq 请求参数
     * @return Mono<Boolean>
     */
    @OperationLog(businessType = BussinessTypeEnum.DEVICE,
            operationType = OperationTypeEnum.ADD)
    public Mono<Boolean> create(DeviceSaveReq deviceSaveReq) {
        ValidatorUtils.validateDeviceSaveReq(deviceSaveReq, false);
        return deviceModelRepository.findById(deviceSaveReq.getModelId())
                .flatMap(hdsDeviceModelEntity ->
                        existDevice(deviceSaveReq.getModelId(), deviceSaveReq.getDeviceSn(), deviceSaveReq.getImei(), null)
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "设备已存在"));
                                    }
                                    return HeaderUtils.getHeaderInfo();
                                })
                                .flatMap(headerInfo -> executeTransferWithinTransactionCreate(deviceSaveReq, headerInfo)))
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备类型不存在")));
    }

    private Mono<Boolean> existDevice(Long modelId, String deviceSn, String imei, Long id) {
        Criteria criteria = buildCriteria(modelId, deviceSn, imei, id);
        return r2dbcEntityTemplate.count(Query.query(criteria), HdsDeviceInfoEntity.class)
                .flatMap(count -> Mono.just(count > 0));
    }

    private Criteria buildCriteria(Long modelId, String deviceSn, String imei, Long id) {
        // 构建基础条件：model_id 和可能的 id 条件
        Criteria baseCriteria = Criteria.where(HdsDeviceInfoFieldEnum.model_id.name()).is(modelId);
        if (Objects.nonNull(id)) {
            baseCriteria = baseCriteria.and(HdsDeviceInfoFieldEnum.id.name()).not(id);
        }

        // 构建 deviceSn 和 imei 的 OR 条件组
        List<Criteria> orCriterias = new ArrayList<>();

        if (StringUtils.isNotBlank(deviceSn)) {
            orCriterias.add(Criteria.where(HdsDeviceInfoFieldEnum.device_sn.name()).is(deviceSn));
        }

        if (StringUtils.isNotBlank(imei)) {
            orCriterias.add(Criteria.where(HdsDeviceInfoFieldEnum.imei.name()).is(imei));
        }

        // 如果有OR条件，则与baseCriteria进行AND连接
        if (!orCriterias.isEmpty()) {
            // 使用or连接所有条件
            Criteria orCombined = orCriterias.get(0);
            for (int i = 1; i < orCriterias.size(); i++) {
                orCombined = orCombined.or(orCriterias.get(i));
            }

            // 与baseCriteria进行AND连接
            return baseCriteria.and(orCombined);
        }

        // 如果没有OR条件，则直接返回baseCriteria
        return baseCriteria;
    }

    /**
     * 事务创建
     *
     * @param deviceSaveReq 请求参数
     * @param headerInfo    请求头信息
     * @return Mono<Boolean>
     */
    private Mono<Boolean> executeTransferWithinTransactionCreate(DeviceSaveReq deviceSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        Mono<HdsDeviceModelEntity> deviceModelMono = getAndUpdateDeviceModel(deviceSaveReq.getModelId(), headerInfo);
        Mono<HdsDeviceInfoEntity> deviceInfoMono = getOrPrepareDeviceInfo(deviceSaveReq, headerInfo, deviceModelMono);
        Mono<HdsDeviceEntity> deviceMono = getOrPrepareDevice(deviceSaveReq, headerInfo, deviceModelMono);

        // 4、组合所有前置条件
        return Mono.zip(deviceInfoMono, deviceMono, deviceModelMono)
                .flatMap(tuple -> {
                    HdsDeviceInfoEntity deviceInfoEntity = tuple.getT1();
                    HdsDeviceEntity deviceEntity = tuple.getT2();
                    HdsDeviceModelEntity deviceModelEntity = tuple.getT3();

                    // 6、执行事务操作
                    return executeTransaction(deviceInfoEntity, deviceEntity, deviceModelEntity, deviceSaveReq, headerInfo);
                });
    }

    /**
     * 执行事务操作
     *
     * @param deviceInfo  设备信息实体
     * @param device      设备实体
     * @param deviceModel 设备型号实体
     * @return 操作结果
     */
    private Mono<Boolean> executeTransaction(
            HdsDeviceInfoEntity deviceInfo,
            HdsDeviceEntity device,
            HdsDeviceModelEntity deviceModel,
            DeviceSaveReq deviceSaveReq,
            HeaderUtils.HeaderInfo headerInfo) {

        // 创建事务操作列表
        Mono<Tuple3<HdsDeviceInfoEntity, HdsDeviceEntity, HdsDeviceModelEntity>> transactionMono = tuple3Mono(deviceInfo, device, deviceModel, deviceInfoRepository, deviceRepository, deviceModelRepository);

        // 使用事务操作符包装执行
        return transactionalOperator.transactional(transactionMono)
                .doOnSuccess(result -> handleSuccess(result, deviceSaveReq, headerInfo))
                .thenReturn(true)
                .onErrorResume(e -> {
//                    handleError(e, deviceSaveReq, headerInfo);
                    log.error("事务执行失败: {}", e.getMessage(), e);
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "设备入库事务失败"));
                });
    }

    public static Mono<Tuple3<HdsDeviceInfoEntity, HdsDeviceEntity, HdsDeviceModelEntity>> tuple3Mono(HdsDeviceInfoEntity deviceInfo, HdsDeviceEntity device, HdsDeviceModelEntity deviceModel, DeviceInfoRepository deviceInfoRepository, DeviceRepository deviceRepository, DeviceModelRepository deviceModelRepository) {
        return Mono.defer(() -> {
            // 所有需要在事务中执行的数据库操作
            Mono<HdsDeviceInfoEntity> saveDeviceInfo = deviceInfoRepository.save(deviceInfo);
            Mono<HdsDeviceEntity> saveDevice = deviceRepository.save(device);
            Mono<HdsDeviceModelEntity> saveDeviceModel = deviceModelRepository.save(deviceModel);

            // 将所有操作组合成一个事务
            return Mono.zip(saveDeviceInfo, saveDevice, saveDeviceModel);
        });
    }

    /**
     * 构建设备实体
     *
     * @param deviceSaveReq
     * @param headerInfo
     * @return
     */
    private Mono<HdsDeviceInfoEntity> getOrPrepareDeviceInfo(DeviceSaveReq deviceSaveReq, HeaderUtils.HeaderInfo headerInfo, Mono<HdsDeviceModelEntity> deviceModelMono) {
        return deviceModelMono.flatMap(deviceModelEntity -> {
            HdsDeviceInfoEntity deviceInfoEntity = new HdsDeviceInfoEntity();

            if (StringUtils.isNotBlank(deviceSaveReq.getDeviceSn())) {
                deviceInfoEntity.setDeviceSn(deviceSaveReq.getDeviceSn().trim());
            }
            if (StringUtils.isNotBlank(deviceSaveReq.getImei())) {
                deviceInfoEntity.setImei(deviceSaveReq.getImei().trim());
            }
            deviceInfoEntity.setModelId(deviceSaveReq.getModelId());

            LocalDateTime warrantyStart = SimpleDateUtils.slashStringToStartDateTime(deviceSaveReq.getWarrantyStart());
            LocalDateTime warrantyEnd = SimpleDateUtils.slashStringToStartDateTime(deviceSaveReq.getWarrantyEnd());
            deviceInfoEntity.setWarrantyStart(warrantyStart);
            deviceInfoEntity.setWarrantyEnd(warrantyEnd);
            deviceInfoEntity.setStorageTime(LocalDateTime.now());
            deviceInfoEntity.setDeviceAppType(deviceModelEntity.getDeviceAppType());
            deviceInfoEntity.setPurchaseTime(SimpleDateUtils.slashStringToStartDateTime(deviceSaveReq.getPurchaseTime()));

            deviceInfoEntity.setRemark(deviceSaveReq.getRemark());

            // 设置审计字段
            setAuditFields(deviceInfoEntity, headerInfo);
            deviceInfoEntity.setRowStatus(1);
            return Mono.just(deviceInfoEntity);
        });
    }

    /**
     * 构建设备实体
     *
     * @param deviceSaveReq
     * @param headerInfo
     * @return
     */
    private Mono<HdsDeviceEntity> getOrPrepareDevice(DeviceSaveReq deviceSaveReq, HeaderUtils.HeaderInfo headerInfo, Mono<HdsDeviceModelEntity> deviceModelMono) {
        return deviceModelMono.flatMap(deviceModelEntity -> {
            HdsDeviceEntity deviceEntity = new HdsDeviceEntity();
            deviceEntity.setModelId(deviceSaveReq.getModelId());

            deviceEntity.setDeviceAppType(deviceModelEntity.getDeviceAppType());

            if (StringUtils.isNotBlank(deviceSaveReq.getDeviceSn())) {
                deviceEntity.setDeviceSn(deviceSaveReq.getDeviceSn().trim());
            }
            if (StringUtils.isNotBlank(deviceSaveReq.getImei())) {
                deviceEntity.setImei(deviceSaveReq.getImei().trim());
            }
            deviceEntity.setDeviceStatus(DeviceStatusEnum.PENDING_ACTIVATION.getCode());
            deviceEntity.setRemark(deviceSaveReq.getRemark());

            // 设置审计字段
            setAuditFields(deviceEntity, headerInfo);
            deviceEntity.setRowStatus(1);
            return Mono.just(deviceEntity);
        });
    }

    /**
     * 获取并更新设备型号信息
     *
     * @param modelId    型号ID
     * @param headerInfo 请求头信息
     * @return 更新后的设备型号实体
     */
    private Mono<HdsDeviceModelEntity> getAndUpdateDeviceModel(Long modelId, HeaderUtils.HeaderInfo headerInfo) {
        return deviceModelRepository.findById(modelId)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备型号不存在")))
                .map(model -> {
                    // 更新库存
                    model.setTotalInventory(model.getTotalInventory() + 1);
                    // 可分配库存也+1
                    if (model.getInventory() != null) {
                        model.setInventory(model.getInventory() + 1);
                    } else {
                        model.setInventory(1);
                    }
                    // 设置更新审计字段
                    model.setUpdatedAt(LocalDateTime.now());
                    model.setUpdatedBy(headerInfo.getUserId());
                    model.setUpdatedByName(headerInfo.getUsername());
                    return model;
                });
    }


    /**
     * 设置审计字段
     *
     * @param entity     实体对象
     * @param headerInfo 请求头信息
     */
    private void setAuditFields(BaseEntity entity, HeaderUtils.HeaderInfo headerInfo) {
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
    }

    /**
     * 成功之后记录设备生命周期日志
     *
     * @param result
     */
    private void handleSuccess(Tuple3<HdsDeviceInfoEntity, HdsDeviceEntity, HdsDeviceModelEntity> result, DeviceSaveReq deviceSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        HdsDeviceInfoEntity deviceInfo = result.getT1();
        HdsDeviceEntity device = result.getT2();
        HdsDeviceModelEntity deviceModelEntity = result.getT3();

        HdsDeviceLogEntity hdsDeviceLogEntity = new HdsDeviceLogEntity();
        hdsDeviceLogEntity.setDeviceId(device.getDeviceId());
        hdsDeviceLogEntity.setDeviceSn(device.getDeviceSn());
        hdsDeviceLogEntity.setEventType(DeviceEventTypeEnum.INBOUND.getCode());
        hdsDeviceLogEntity.setEventTime(LocalDateTime.now());

        // 设置更新审计字段
        hdsDeviceLogEntity.setCreatedAt(LocalDateTime.now());
        hdsDeviceLogEntity.setCreatedBy(deviceInfo.getCreatedBy());
        hdsDeviceLogEntity.setRemark(String.format("设备[%s]入库，型号[%s]，型号名称[%s]",
                device.getDeviceSn(), deviceModelEntity.getModelCode(), deviceModelEntity.getModelName()));


        // 2. 记录操作成功日志
//        Mono.zip(
        deviceLogRepository.save(hdsDeviceLogEntity)
//                operationLogUtil.recordSuccess(
//                        BussinessTypeEnum.DEVICE.getBusinessType(),
//                        BussinessTypeEnum.DEVICE.getBusinessType(),
//                        device.getId(),
//                        device.getDeviceSn(),
//                        OperationTypeEnum.ADD.getCode(),
//                        String.format("设备入库成功: SN=%s, 型号=%s, 型号名称=%s", deviceSaveReq.getDeviceSn(), deviceModelEntity.getModelCode(), deviceModelEntity.getModelName()),
//                        deviceSaveReq,
//                        headerInfo
//                )
                .subscribe(
                        success -> log.info("设备入库成功: device=[id={}, deviceCode={}, deviceSn={}], model=[id={}, name={}]",
                                device.getId(), device.getDeviceId(), device.getDeviceSn(),
                                deviceModelEntity.getId(), deviceModelEntity.getModelName()),
                        error -> log.error("设备入库成功但日志记录失败: {}", error.getMessage())
                );
    }

    /**
     * 记录失败日志
     *
     * @param error 异常信息
     * @param req   请求参数
     */
//    private void handleError(Throwable error, DeviceSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
//        operationLogUtil.recordFail(
//                BussinessTypeEnum.DEVICE_MODEL.getBusinessType(),
//                BussinessTypeEnum.DEVICE_MODEL.getBusinessType(),
//                null,
//                req.getDeviceSn(),
//                OperationTypeEnum.ADD.getCode(),
//                String.format("设备入库失败: %s", req.getDeviceSn()),
//                req,
//                error.getMessage(),
//                headerInfo
//        ).subscribe();
//    }

    /**
     * 更新记录
     */
    @OperationLog(businessType = BussinessTypeEnum.DEVICE,
            operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> update(DeviceSaveReq req) {
        ValidatorUtils.validateDeviceSaveReq(req, true);
        if (req.getId() == null) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "id不能为空"));
        }
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionUpdate(req, headerInfo)
                        .onErrorResume(e -> {
                                    log.error("更新设备失败: {}", e.getMessage());
                                    if (e instanceof BusinessException) {
                                        return Mono.error(e);
                                    }
                                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device update error"));
                                }
                        )
                );
    }

    /**
     * 执行更新事务
     */
    private Mono<Boolean> executeTransferWithinTransactionUpdate(DeviceSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 1. 首先查询设备是否存在
        return deviceRepository.findById(req.getId())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备不存在")))
                .flatMap(device -> {
                    // 2. 检查设备状态
                    if (device.getDeviceStatus() != DeviceStatusEnum.PENDING_ACTIVATION.getCode()) {
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "只有待激活状态的设备可以修改"));
                    }
                    // 3.查询设备类型是否存在
                    return deviceModelRepository.findById(req.getModelId())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备类型不存在")))
                            .flatMap(hdsDeviceModelEntity ->
                                    // 4.查询设备信息是否存在
                                    findDevice(device.getModelId(), device.getDeviceSn(), device.getImei())
                                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备信息不存在")))
                                            .flatMap(hdsDeviceInfoEntity -> {
                                                // 3. 检查更新的设备sn和imei是否已存在
                                                return existDeviceBySnOrImeiExcludeSelf(hdsDeviceInfoEntity, req)
                                                        .flatMap(exist -> {
                                                            // 4.如果存在则报错
                                                            if (exist) {
                                                                return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "设备已存在"));
                                                            }
                                                            // 5. 更新设备信息
                                                            return executeUpdateTransaction(device, hdsDeviceInfoEntity, req, headerInfo);
                                                        });
                                            }));
                });
    }

    /**
     * 检查SN或IMEI是否存在
     */
    private Mono<Boolean> existDeviceBySnOrImeiExcludeSelf(HdsDeviceInfoEntity hdsDeviceInfoEntity, DeviceSaveReq req) {
        // 如果设备类型 device sn或者imei都没变，则直接跳过
        if (checkDeviceInfoNotUpdate(hdsDeviceInfoEntity, req)) {
            return Mono.just(false);
        }
        // 再根据设备类型，devicen sn和imei判断设备是否已存在
        return existDevice(req.getModelId(), req.getDeviceSn(), req.getImei(), hdsDeviceInfoEntity.getId());
    }

    /**
     * 判断设备信息是否发生变化
     *
     * @param hdsDeviceInfoEntity
     * @param req
     * @return
     */
    private boolean checkDeviceInfoNotUpdate(HdsDeviceInfoEntity hdsDeviceInfoEntity, DeviceSaveReq req) {
        if (StringUtils.isNotBlank(req.getDeviceSn())) {
            req.setDeviceSn(req.getDeviceSn().trim());
        }
        if (StringUtils.isNotBlank(req.getImei())) {
            req.setImei(req.getImei().trim());
        }

        if (!Objects.equals(hdsDeviceInfoEntity.getModelId(), req.getModelId())) {
            return false;
        }
        if (!StringUtils.equals(hdsDeviceInfoEntity.getDeviceSn(), req.getDeviceSn())) {
            return false;
        }
        return StringUtils.equals(hdsDeviceInfoEntity.getImei(), req.getImei());
    }

    /**
     * 执行更新事务操作
     */
    private Mono<Boolean> executeUpdateTransaction(
            HdsDeviceEntity existDevice,
            HdsDeviceInfoEntity existDeviceInfo,
            DeviceSaveReq req,
            HeaderUtils.HeaderInfo headerInfo) {

        // 1. 更新设备基本信息
        if (StringUtils.isNotBlank(req.getDeviceSn())) {
            req.setDeviceSn(req.getDeviceSn().trim());
            req.setDeviceSn(req.getDeviceSn());

        }
        existDevice.setDeviceSn(req.getDeviceSn());
        existDeviceInfo.setDeviceSn(req.getDeviceSn());

        if (StringUtils.isNotBlank(req.getImei())) {
            req.setImei(req.getImei().trim());
            req.setImei(req.getImei());
        }

        existDevice.setImei(req.getImei());
        existDeviceInfo.setImei(req.getImei());

        existDevice.setRemark(req.getRemark());
        existDeviceInfo.setRemark(req.getRemark());

        existDevice.setUpdatedAt(LocalDateTime.now());
        existDevice.setUpdatedBy(headerInfo.getUserId());
        existDevice.setUpdatedByName(headerInfo.getUsername());

        // 2. 更新设备详细信息
        existDevice.setModelId(req.getModelId());
        existDeviceInfo.setModelId(req.getModelId());

        if (StringUtils.isNotBlank(req.getPurchaseTime())) {
            existDeviceInfo.setPurchaseTime(SimpleDateUtils.slashStringToStartDateTime(req.getPurchaseTime()));
        }
        LocalDateTime warrantyStart = SimpleDateUtils.slashStringToStartDateTime(req.getWarrantyStart());
        LocalDateTime warrantyEnd = SimpleDateUtils.slashStringToStartDateTime(req.getWarrantyEnd());
        existDeviceInfo.setWarrantyStart(warrantyStart);
        existDeviceInfo.setWarrantyEnd(warrantyEnd);
        existDeviceInfo.setUpdatedAt(LocalDateTime.now());
        existDeviceInfo.setUpdatedBy(headerInfo.getUserId());
        existDeviceInfo.setUpdatedByName(headerInfo.getUsername());

        // 3. 创建事务操作
        Mono<Tuple2<HdsDeviceEntity, HdsDeviceInfoEntity>> transactionMono = Mono.defer(() -> {
            Mono<HdsDeviceEntity> saveDevice = deviceRepository.save(existDevice);
            Mono<HdsDeviceInfoEntity> saveDeviceInfo = deviceInfoRepository.save(existDeviceInfo);
            return Mono.zip(saveDevice, saveDeviceInfo);
        });

        // 4. 执行事务
        return transactionalOperator.transactional(transactionMono)
                .doOnSuccess(result -> handleUpdateSuccess(result, req, headerInfo))
                .thenReturn(true)
                .onErrorResume(e -> {
                    handleUpdateError(e, req, headerInfo);
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    log.error("设备更新事务执行失败: {}", e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "设备更新事务失败"));
                });
    }

    /**
     * 处理更新成功
     */
    private void handleUpdateSuccess(Tuple2<HdsDeviceEntity, HdsDeviceInfoEntity> result, DeviceSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
//        operationLogUtil.recordSuccess(
//                BussinessTypeEnum.DEVICE.getBusinessType(),
//                BussinessTypeEnum.DEVICE.getBusinessType(),
//                req.getId(),
//                req.getDeviceSn(),
//                OperationTypeEnum.UPDATE.getCode(),
//                String.format("设备更新成: %s", req.getDeviceSn()),
//                req,
//                headerInfo
//        ).subscribe();
    }

    /**
     * 处理更新失败
     */
    private void handleUpdateError(Throwable error, DeviceSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
//        operationLogUtil.recordFail(
//                BussinessTypeEnum.DEVICE.getBusinessType(),
//                BussinessTypeEnum.DEVICE.getBusinessType(),
//                req.getId(),
//                req.getDeviceSn(),
//                OperationTypeEnum.UPDATE.getCode(),
//                String.format("设备更新失败: %s", req.getDeviceSn()),
//                req,
//                error.getMessage(),
//                headerInfo
//        ).subscribe();
    }


    /**
     * 删除设备
     *
     * @param deviceDeleteReq 删除请求参数
     * @return Mono<Boolean>
     */
    @OperationLog(businessType = BussinessTypeEnum.DEVICE,
            operationType = OperationTypeEnum.DELETE)
    public Mono<Boolean> delete(DeviceDeleteReq deviceDeleteReq) {
        ValidatorUtils.validateDeviceDeleteReq(deviceDeleteReq);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionDelete(deviceDeleteReq, headerInfo)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            log.error("设备删除事务执行失败: {}", ex.getMessage(), ex);
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device delete error"));
                        })
                );
    }

    /**
     * 执行删除事务
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(DeviceDeleteReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 1. 查询设备是否存在
        return deviceRepository.findById(req.getId())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备不存在")))
                .flatMap(existDevice -> {
                    // 2. 检查设备状态
                    if (existDevice.getDeviceStatus() != DeviceStatusEnum.PENDING_ACTIVATION.getCode()) {
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "只有待激活状态的设备可以删除"));
                    }
                    // 3. 查询设备信息
                    return findDevice(existDevice.getModelId(), existDevice.getDeviceSn(), existDevice.getImei())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备信息不存在")))
                            .flatMap(deviceInfo -> {
                                // 4. 查询设备型号
                                return deviceModelRepository.findById(deviceInfo.getModelId())
                                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备型号不存在")))
                                        .flatMap(deviceModel -> {
                                            // 5. 执行删除事务
                                            return executeDeleteTransaction(existDevice, deviceInfo, deviceModel, req, headerInfo);
                                        });
                            });
                });
    }

    /**
     * 执行删除事务操作
     */
    private Mono<Boolean> executeDeleteTransaction(
            HdsDeviceEntity existDevice,
            HdsDeviceInfoEntity deviceInfo,
            HdsDeviceModelEntity deviceModel,
            DeviceDeleteReq req,
            HeaderUtils.HeaderInfo headerInfo) {

        // 1. 更新设备型号库存
        deviceModel.setTotalInventory(deviceModel.getTotalInventory() - 1);
        if (deviceModel.getInventory() != null) {
            deviceModel.setInventory(deviceModel.getInventory() - 1);
        }
        deviceModel.setUpdatedAt(LocalDateTime.now());
        deviceModel.setUpdatedBy(headerInfo.getUserId());
        deviceModel.setUpdatedByName(headerInfo.getUsername());

        // 3. 创建事务操作
        Mono<HdsDeviceModelEntity> transactionMono = Mono.defer(() -> {
            // 先执行删除操作
            return deviceRepository.deleteById(existDevice.getId())
                    .then(deviceInfoRepository.deleteById(deviceInfo.getId()))
                    // 然后执行更新和保存操作
                    .then(deviceModelRepository.save(deviceModel));
        });

        // 4. 执行事务
        return transactionalOperator.transactional(transactionMono)
                .doOnSuccess(result -> handleDeleteSuccess(existDevice, req, headerInfo))
                .thenReturn(true)
                .onErrorResume(e -> {
                    handleDeleteError(e, req, headerInfo);
                    log.error("设备删除事务执行失败: {}", e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "设备删除事务失败"));
                });
    }

    private Mono<HdsDeviceInfoEntity> findDevice(Long modelId, String deviceSn, String imei) {
        Criteria criteria = buildCriteria(modelId, deviceSn, imei, null);
        return r2dbcEntityTemplate.selectOne(Query.query(criteria), HdsDeviceInfoEntity.class);
    }

    /**
     * 处理删除成功
     */
    private void handleDeleteSuccess(HdsDeviceEntity device, DeviceDeleteReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 记录操作日志
//        operationLogUtil.recordSuccess(
//                BussinessTypeEnum.DEVICE,
//                OperationTypeEnum.DELETE,
//                req,
//                device,
//                null,
//                null,
//                headerInfo
//        ).subscribe(
//                success -> log.info("设备删除成功: device=[id={}, code={}, sn={}]",
//                        device.getId(), device.getDeviceId(), device.getDeviceSn()),
//                error -> log.error("设备删除成功但日志记录失败: {}", error.getMessage())
//        );
    }

    /**
     * 处理删除失败
     */
    private void handleDeleteError(Throwable error, DeviceDeleteReq req, HeaderUtils.HeaderInfo headerInfo) {
//        operationLogUtil.recordFail(
//                BussinessTypeEnum.DEVICE.getBusinessType(),
//                BussinessTypeEnum.DEVICE.getBusinessType(),
//                req.getId(),
//                null,
//                OperationTypeEnum.DELETE.getCode(),
//                String.format("设备删除失败: id=%s", req.getId()),
//                req,
//                error.getMessage(),
//                headerInfo
//        ).subscribe();
    }

    /**
     * 查询设备列表
     *
     * @param deviceSearchReq
     * @return
     */
    public Mono<Long> searchCount(DeviceSearchReq deviceSearchReq) {
        // 使用相同的预过滤逻辑构建查询条件
        return preFilterAndBuildCriteria(deviceSearchReq)
                .flatMap(criteria -> r2dbcEntityTemplate.count(Query.query(criteria), HdsDeviceEntity.class))
                .doOnSuccess(count -> log.info("设备总数查询完成: count={}, req={}",
                        count, deviceSearchReq))
                .doOnError(e -> log.error("设备总数查询失败: req={}, error={}",
                        deviceSearchReq, e.getMessage(), e));
    }

    public Mono<List<DeviceSearchVO>> search(DeviceSearchReq deviceSearchReq) {
        // 1. 根据查询条件预先过滤相关数据
        return preFilterAndBuildCriteria(deviceSearchReq)
                .flatMap(criteria -> {
                    // 2. 使用构建好的条件查询设备
                    Query query = Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.DESC, HdsDeviceFieldEnum.created_at.name())
                                    .and(Sort.by(Sort.Direction.DESC, HdsDeviceFieldEnum.id.name())))
                            .limit(deviceSearchReq.getPageSize())
                            .offset((long) (deviceSearchReq.getCurrent() - 1) * deviceSearchReq.getPageSize());

                    return r2dbcEntityTemplate.select(query, HdsDeviceEntity.class)
                            .collectList()
                            .flatMap(this::fetchRelatedData)
                            .map(this::buildDeviceSearchVOList);
                })
                .doOnSuccess(list -> log.info("设备查询完成: size={}, req={}",
                        list.size(), deviceSearchReq))
                .doOnError(e -> log.error("设备查询失败: req={}, error={}",
                        deviceSearchReq, e.getMessage(), e));
    }

    /**
     * 根据查询条件预先过滤相关数据，构建更精确的查询条件
     */
    private Mono<Criteria> preFilterAndBuildCriteria(DeviceSearchReq deviceSearchReq) {
        // 基础条件
        Criteria baseCriteria = buildBaseCriteria(deviceSearchReq);
        return Mono.just(baseCriteria);

        // 根据条件预过滤并构建更精确的查询
//        Mono<Criteria> hotelFilterMono = preFilterByHotelName(deviceSearchReq, baseCriteria);
//        Mono<Criteria> modelFilterMono = preFilterByModel(deviceSearchReq, baseCriteria);

//        return Mono.zip(hotelFilterMono, modelFilterMono)
//                .map(tuple -> {
//                    Criteria hotelCriteria = tuple.getT1();
//                    Criteria modelCriteria = tuple.getT2();
//
//                    if (hotelCriteria == baseCriteria && modelCriteria == baseCriteria) {
//                        return baseCriteria;
//                    }
//
//                    if (modelCriteria == baseCriteria) {
//                        return hotelCriteria;
//                    }
//
//                    if (hotelCriteria == baseCriteria) {
//                        return modelCriteria;
//                    }
//
//                    return hotelCriteria.and(modelCriteria);
//                });
    }

    /**
     * 根据酒店名称预过滤
     */
    private Mono<Criteria> preFilterByHotelName(DeviceSearchReq deviceSearchReq, Criteria baseCriteria) {
        if (StringUtils.isBlank(deviceSearchReq.getHotelName())) {
            return Mono.just(baseCriteria);
        }

        // 根据酒店名称查询符合条件的酒店编码
        Criteria hotelCriteria = Criteria.where(HdsHotelInfoFieldEnum.hotel_name.name())
                .like("%" + deviceSearchReq.getHotelName().trim() + "%");

        return r2dbcEntityTemplate.select(HdsHotelInfoEntity.class)
                .matching(Query.query(hotelCriteria))
                .all()
                .map(HdsHotelInfoEntity::getHotelCode)
                .collectList()
                .map(hotelCodes -> {
                    if (hotelCodes.isEmpty()) {
                        return Criteria.where(HdsDeviceFieldEnum.id.name()).is(-1);
                    }
                    // 将酒店编码添加到查询条件中
                    return baseCriteria.and(HdsDeviceFieldEnum.hotel_code.name()).in(hotelCodes);
                });
    }

    /**
     * 根据设备型号信息预过滤
     */
    private Mono<Criteria> preFilterByModel(DeviceSearchReq deviceSearchReq, Criteria baseCriteria) {
        if (StringUtils.isBlank(deviceSearchReq.getModelCode()) &&
                StringUtils.isBlank(deviceSearchReq.getModelName())) {
            return Mono.just(baseCriteria);
        }

        // 构建模型查询条件
        Criteria modelCriteria = Criteria.empty();
        if (StringUtils.isNotBlank(deviceSearchReq.getModelCode())) {
            modelCriteria = modelCriteria.and(HdsDeviceModelFieldEnum.model_code.name())
                    .is(deviceSearchReq.getModelCode());
        }
        if (StringUtils.isNotBlank(deviceSearchReq.getModelName())) {
            modelCriteria = modelCriteria.and(HdsDeviceModelFieldEnum.model_name.name())
                    .like("%" + deviceSearchReq.getModelName().trim() + "%");
        }

        // 查询符合条件的模型ID
        return r2dbcEntityTemplate.select(HdsDeviceModelEntity.class)
                .matching(Query.query(modelCriteria))
                .all()
                .map(HdsDeviceModelEntity::getId)
                .collectList()
                .flatMap(modelIds -> {
                    if (modelIds.isEmpty()) {
                        // 如果没有符合条件的模型，返回一个永不满足的条件
                        return Mono.just(Criteria.where(HdsDeviceFieldEnum.id.name()).is(-1));
                    }

                    // 根据模型ID查询设备信息，获取设备标识符
                    return r2dbcEntityTemplate.select(HdsDeviceInfoEntity.class)
                            .matching(Query.query(Criteria.where(HdsDeviceInfoFieldEnum.model_id.name()).in(modelIds)))
                            .all()
                            .collectList()
                            .map(deviceInfoList -> {
                                Set<String> deviceSns = deviceInfoList.stream()
                                        .map(HdsDeviceInfoEntity::getDeviceSn)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toSet());

                                Set<String> imeis = deviceInfoList.stream()
                                        .map(HdsDeviceInfoEntity::getImei)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toSet());

                                if (deviceSns.isEmpty() && imeis.isEmpty()) {
                                    // 如果没有符合条件的设备标识符，返回一个永不满足的条件
                                    return Criteria.where(HdsDeviceFieldEnum.id.name()).is(-1);
                                }

                                // 构建设备标识符条件
                                Criteria deviceIdentifierCriteria = Criteria.empty();
                                if (!deviceSns.isEmpty()) {
                                    deviceIdentifierCriteria = deviceIdentifierCriteria.or(HdsDeviceFieldEnum.device_sn.name()).in(deviceSns);
                                }
                                if (!imeis.isEmpty()) {
                                    deviceIdentifierCriteria = deviceIdentifierCriteria.or(HdsDeviceFieldEnum.imei.name()).in(imeis);
                                }

                                return baseCriteria.and(deviceIdentifierCriteria);
                            });
                });
    }

    /**
     * 获取关联数据
     */
    private Mono<Tuple6<List<HdsDeviceEntity>,
            Map<String, HdsHotelInfoEntity>,
            Map<Long, HdsDeviceInfoEntity>,
            Map<Long, HdsDeviceModelEntity>,
            Map<String, HdsDevicePositionEntity>,
            Map<String, HdsDeviceLatestInfoEntity>>> fetchRelatedData(List<HdsDeviceEntity> devices) {
        if (devices.isEmpty()) {
            return Mono.just(Tuples.of(
                    devices,
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap()
            ));
        }

        Set<String> hotelCodes = devices.stream()
                .map(HdsDeviceEntity::getHotelCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> positionCodes = devices.stream()
                .map(HdsDeviceEntity::getPositionCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> deviceIds = devices.stream()
                .map(HdsDeviceEntity::getDeviceId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

//        Map<Long, HdsDeviceEntity> deviceMap = new HashMap<>();
        Map<Long, HdsDeviceInfoEntity> deviceToInfoMap = new HashMap<>();
        Set<Long> modelIds = new HashSet<>();


//        Map<String, HdsDeviceEntity> deviceBySn = new HashMap<>();
//        Map<String, HdsDeviceEntity> deviceByImei = new HashMap<>();

//        Set<String> deviceSns = new HashSet<>();
//        Set<String> imeis = new HashSet<>();

        for (HdsDeviceEntity device : devices) {
//            if (StringUtils.isNotBlank(device.getDeviceSn())) {
//                deviceSns.add(device.getDeviceSn());
//                deviceBySn.put(device.getDeviceSn(), device);
//            }
//            if (StringUtils.isNotBlank(device.getImei())) {
//                imeis.add(device.getImei());
//                deviceByImei.put(device.getImei(), device);
//            }
            modelIds.add(device.getModelId());
//            deviceMap.put(device.getModelId(),device);
        }

        // 构建查询条件
        Criteria hotelCriteria = !hotelCodes.isEmpty() ?
                Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).in(hotelCodes) : Criteria.empty();

        Criteria deviceInfoCriteria = Criteria.empty();
        deviceInfoCriteria.and(HdsDeviceInfoFieldEnum.model_id.name()).in(modelIds);
//        if (!deviceSns.isEmpty()) {
//            deviceInfoCriteria = deviceInfoCriteria.or(HdsDeviceInfoFieldEnum.device_sn.name()).in(deviceSns);
//        }
//        if (!imeis.isEmpty()) {
//            deviceInfoCriteria = deviceInfoCriteria.or(HdsDeviceInfoFieldEnum.imei.name()).in(imeis);
//        }

        Criteria positionCriteria = !positionCodes.isEmpty() ?
                Criteria.where(HdsDevicePositionFieldEnum.position_code.name()).in(positionCodes) : Criteria.empty();

        Criteria appVersionCriteria = !deviceIds.isEmpty() ?
                Criteria.where(HdsDeviceLatestInfoFieldEnum.device_id.name()).in(deviceIds) : Criteria.empty();

        // 执行并行查询
        Mono<Map<String, HdsHotelInfoEntity>> hotelMapMono = r2dbcEntityTemplate
                .select(HdsHotelInfoEntity.class)
                .matching(Query.query(hotelCriteria))
                .all()
                .collectMap(HdsHotelInfoEntity::getHotelCode);

        Mono<List<HdsDeviceInfoEntity>> deviceInfoListMono = r2dbcEntityTemplate
                .select(HdsDeviceInfoEntity.class)
                .matching(Query.query(deviceInfoCriteria))
                .all()
                .collectList();

        Mono<Map<String, HdsDevicePositionEntity>> positionMapMono = r2dbcEntityTemplate
                .select(HdsDevicePositionEntity.class)
                .matching(Query.query(positionCriteria))
                .all()
                .collectMap(HdsDevicePositionEntity::getPositionCode);

        Mono<Map<String, HdsDeviceLatestInfoEntity>> deviceAppVersionMapMono = r2dbcEntityTemplate
                .select(HdsDeviceLatestInfoEntity.class)
                .matching(Query.query(appVersionCriteria))
                .all()
                .collectMap(HdsDeviceLatestInfoEntity::getDeviceId);

        // 合并结果
        return Mono.zip(hotelMapMono, deviceInfoListMono, positionMapMono,deviceAppVersionMapMono)
                .flatMap(tuple -> {
                    Map<String, HdsHotelInfoEntity> hotelMap = tuple.getT1();
                    List<HdsDeviceInfoEntity> deviceInfoList = tuple.getT2();
                    Map<String, HdsDevicePositionEntity> positionMap = tuple.getT3();
                    Map<String, HdsDeviceLatestInfoEntity> latestInfoEntityMap = tuple.getT4();

                    // 创建设备到设备信息的映射
//                    Map<HdsDeviceEntity, HdsDeviceInfoEntity> deviceToInfoMap = new HashMap<>();
//                    Set<Long> modelIds = new HashSet<>();

                    for (HdsDeviceInfoEntity hdsDeviceInfoEntity : deviceInfoList) {
//                        HdsDeviceEntity device = null;
//                        if (StringUtils.isNotBlank(info.getDeviceSn())) {
//                            device = deviceBySn.get(info.getDeviceSn());
//                        }
//                        if (device == null && StringUtils.isNotBlank(info.getImei())) {
//                            device = deviceByImei.get(info.getImei());
//                        }
//
//                        if (device != null) {
//                            deviceToInfoMap.put(device, info);
//                            if (info.getModelId() != null) {
//                                modelIds.add(info.getModelId());
//                            }
//                        }
                        deviceToInfoMap.put(hdsDeviceInfoEntity.getModelId(), hdsDeviceInfoEntity);
                    }

                    // 查询模型信息
                    if (modelIds.isEmpty()) {
                        return Mono.just(Tuples.of(
                                devices,
                                hotelMap,
                                deviceToInfoMap,
                                Collections.emptyMap(),
                                positionMap,
                                latestInfoEntityMap
                        ));
                    }

                    return r2dbcEntityTemplate.select(HdsDeviceModelEntity.class)
                            .matching(Query.query(Criteria.where(HdsDeviceModelFieldEnum.id.name()).in(modelIds)))
                            .all()
                            .collectMap(HdsDeviceModelEntity::getId)
                            .map(modelMap -> Tuples.of(
                                    devices,
                                    hotelMap,
                                    deviceToInfoMap,
                                    modelMap,
                                    positionMap,
                                    latestInfoEntityMap
                            ));
                });
    }

    /**
     * 构建设备查询结果
     */
    private List<DeviceSearchVO> buildDeviceSearchVOList(
            Tuple6<
                                List<HdsDeviceEntity>,
                                Map<String, HdsHotelInfoEntity>,
                                Map<Long, HdsDeviceInfoEntity>,
                                Map<Long, HdsDeviceModelEntity>,
                                Map<String, HdsDevicePositionEntity>,
                                Map<String, HdsDeviceLatestInfoEntity>> data) {

        List<HdsDeviceEntity> devices = data.getT1();
        Map<String, HdsHotelInfoEntity> hotelMap = data.getT2();
        Map<Long, HdsDeviceInfoEntity> deviceInfoMap = data.getT3();
        Map<Long, HdsDeviceModelEntity> modelMap = data.getT4();
        Map<String, HdsDevicePositionEntity> positionMap = data.getT5();
        Map<String, HdsDeviceLatestInfoEntity> appVersionMap = data.getT6();

        return devices.stream()
                .map(device -> {
                    DeviceSearchVO vo = new DeviceSearchVO();
                    // 设置设备基本信息
                    vo.setId(device.getId())
                            .setDeviceCode(device.getDeviceId())
                            .setDeviceSn(device.getDeviceSn())
                            .setImei(device.getImei())
                            .setDeviceStatus(device.getDeviceStatus())
                            .setDeviceAppType(device.getDeviceAppType())
                            .setHotelCode(device.getHotelCode());

                    // 设置酒店信息
                    HdsHotelInfoEntity hotel = hotelMap.get(device.getHotelCode());
                    if (hotel != null) {
                        vo.setHotelName(hotel.getHotelName());
                    }

                    // 设置位置信息
                    HdsDevicePositionEntity position = positionMap.get(device.getPositionCode());
                    if (position != null) {
                        vo.setBlock(position.getBlock())
                                .setArea(position.getArea())
                                .setPositionName(position.getPositionName())
                                .setRoomNum(position.getPositionName());
                    }

                    // 设置设备信息和型号信息
                    HdsDeviceInfoEntity deviceInfo = deviceInfoMap.get(device.getModelId());
                    if (deviceInfo != null) {
                        vo.setWarrantyStart(SimpleDateUtils.formatLocalDateTimeToDate(deviceInfo.getWarrantyStart()))
                                .setWarrantyEnd(SimpleDateUtils.formatLocalDateTimeToDate(deviceInfo.getWarrantyEnd()))
                                .setPurchaseTime(SimpleDateUtils.formatLocalDateTimeToDate(deviceInfo.getPurchaseTime()));

                        // 设置设备型号信息
                        HdsDeviceModelEntity model = modelMap.get(deviceInfo.getModelId());
                        if (model != null) {
                            vo.setModelCode(model.getModelCode())
                                    .setModelName(model.getModelName());

                            if (StringUtils.isBlank(vo.getDeviceAppType())) {
                                vo.setDeviceAppType(model.getDeviceAppType());
                            }
                        }
                    }

                    String appVersion = null;
                    if (StringUtils.isNotBlank(device.getDeviceId())) {
                        HdsDeviceLatestInfoEntity hdsDeviceLatestInfoEntity = appVersionMap.get(device.getDeviceId());
                        appVersion = hdsDeviceLatestInfoEntity != null && device.getDeviceStatus() != DeviceStatusEnum.PENDING_ACTIVATION.getCode() ? hdsDeviceLatestInfoEntity.getAppVersion() : null;
                    }
                    vo.setAppVersion(appVersion);

                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     *
     * @param deviceSearchReq
     * @return
     */
    private Criteria buildBaseCriteria(DeviceSearchReq deviceSearchReq) {
        Criteria criteria = Criteria.empty();

        // 门店编码
        if (StringUtils.isNotBlank(deviceSearchReq.getHotelCode())) {
            if (deviceSearchReq.isHotelCodeLike()) {
                criteria = criteria.and(HdsDeviceFieldEnum.hotel_code.name()).like("%" + deviceSearchReq.getHotelCode().trim() + "%");
            } else {
                criteria = criteria.and(HdsDeviceFieldEnum.hotel_code.name()).is(deviceSearchReq.getHotelCode());
            }
        }

        // 根据设备类型id
        if (Objects.nonNull(deviceSearchReq.getModelId())) {
            criteria = criteria.and(HdsDeviceFieldEnum.model_id.name()).is(deviceSearchReq.getModelId());
        }

        // 设备id
        if (Objects.nonNull(deviceSearchReq.getId())) {
            criteria = criteria.and(HdsDeviceFieldEnum.id.name()).is(deviceSearchReq.getId());
        }

        // 设备编码
//        if (StringUtils.isNotBlank(deviceSearchReq.getDeviceCode())) {
//            criteria = criteria.and(HdsDeviceFieldEnum.device_id.name())
//                    .like("%" + deviceSearchReq.getDeviceCode().trim() + "%");
//        }

        // 设备状态
        if (Objects.nonNull(deviceSearchReq.getDeviceStatus())) {
            criteria = criteria.and(HdsDeviceFieldEnum.device_status.name())
                    .is(deviceSearchReq.getDeviceStatus());
        }

        // 时间范围
        if (StringUtils.isNotBlank(deviceSearchReq.getStartTime())) {
            criteria = criteria.and(HdsDeviceFieldEnum.created_at.name())
                    .greaterThanOrEquals(SimpleDateUtils.slashStringToStartDateTime(deviceSearchReq.getStartTime()));
        }
        if (StringUtils.isNotBlank(deviceSearchReq.getEndTime())) {
            criteria = criteria.and(HdsDeviceFieldEnum.created_at.name())
                    .lessThanOrEquals(SimpleDateUtils.slashStringToEndDateTime(deviceSearchReq.getEndTime()));
        }
        criteria = criteria.and(HdsDeviceFieldEnum.device_app_type.name()).in(Lists.newArrayList(DeviceTypeEnum.ROOM.getCode(), DeviceTypeEnum.FRONT.getCode()));
        return criteria;
    }


    /**
     * 根据ID查询设备详情
     *
     * @param id 设备ID
     * @return Mono<DeviceVO>
     */
    @RequireHotelAccess(resourceType = RequireHotelAccess.ResourceType.DEVICE)
    public Mono<DeviceVO> findById(Long id) {
        return deviceRepository.findById(id)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备不存在")))
                .flatMap(device -> {
                    // 2. 根据 deviceSn或imei 查询设备信息
                    Mono<HdsDeviceInfoEntity> deviceInfoMono = findDevice(device.getModelId(), device.getDeviceSn(), device.getImei());

                    // 3. 并行查询关联数据
                    return Mono.zip(
                            // 查询设备信息
                            deviceInfoMono.defaultIfEmpty(new HdsDeviceInfoEntity()),
                            // 查询设备型号（通过设备信息中的model_id）
                            deviceInfoMono.flatMap(info ->
                                    info.getModelId() != null
                                            ? deviceModelRepository.findById(info.getModelId())
                                            : Mono.just(new HdsDeviceModelEntity())
                            ).defaultIfEmpty(new HdsDeviceModelEntity()),
                            // 查询酒店信息
                            hotelRepository.findByHotelCode(device.getHotelCode())
                                    .defaultIfEmpty(new HdsHotelInfoEntity()),

                            devicePositionRepository.findByPositionCode(device.getPositionCode())
                                    .defaultIfEmpty(new HdsDevicePositionEntity())
                    ).map(tuple -> {
                        HdsDeviceInfoEntity deviceInfo = tuple.getT1();
                        HdsDeviceModelEntity deviceModel = tuple.getT2();
                        HdsHotelInfoEntity hotel = tuple.getT3();
                        HdsDevicePositionEntity position = tuple.getT4();

                        // 4. 组装VO对象
                        return convertToDeviceVO(device, deviceInfo, deviceModel, hotel, position);
                    });
                })
                .onErrorResume(e -> {
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "查询设备详情失败"));
                });
    }

    /**
     * 转换为VO对象
     */
    private DeviceVO convertToDeviceVO(
            HdsDeviceEntity device,
            HdsDeviceInfoEntity deviceInfo,
            HdsDeviceModelEntity deviceModel,
            HdsHotelInfoEntity hotel,
            HdsDevicePositionEntity position) {

        DeviceVO vo = new DeviceVO();

        // 设置设备基本信息
        vo.setId(device.getId());
        vo.setDeviceCode(device.getDeviceId());
        vo.setDeviceSn(device.getDeviceSn());
        vo.setImei(device.getImei());
        vo.setHotelCode(device.getHotelCode());
        vo.setDeviceStatus(device.getDeviceStatus());
        vo.setRemark(device.getRemark());

        // 设置设备信息
        if (deviceInfo != null) {
            vo.setWarrantyStart(SimpleDateUtils.formatLocalDateTimeToDate(deviceInfo.getWarrantyStart()));
            vo.setWarrantyEnd(SimpleDateUtils.formatLocalDateTimeToDate(deviceInfo.getWarrantyEnd()));
            vo.setPurchaseTime(SimpleDateUtils.formatLocalDateTimeToDate(deviceInfo.getPurchaseTime()));
        }

        // 设置型号信息
        if (deviceModel != null) {
            vo.setModelId(deviceModel.getId());
            vo.setModelCode(deviceModel.getModelCode());
            vo.setModelName(deviceModel.getModelName());
        }

        // 设置酒店信息
        if (hotel != null) {
            vo.setHotelName(hotel.getHotelName());
        }
        if (position != null) {
            vo.setBlock(position.getBlock());
            vo.setArea(position.getArea());
            vo.setPositionName(position.getPositionName());
        }
        vo.setDeviceAppType(device.getDeviceAppType());
        return vo;
    }

    /**
     * 获取酒店统计信息
     *
     * @return
     */
    public Mono<DeviceStatisticsVO> getStatistics(DeviceStatisticsReq deviceStatisticsReq) {
        // 1. 创建基础查询条件
        Criteria criteria = Criteria.where(HdsDeviceFieldEnum.hotel_code.name()).is(deviceStatisticsReq.getHotelCode());
        criteria = criteria.and(HdsDeviceFieldEnum.device_app_type.name()).in(Lists.newArrayList(DeviceTypeEnum.ROOM.getCode(), DeviceTypeEnum.FRONT.getCode()));
        // 2. 查询所有符合条件的设备
        return r2dbcEntityTemplate.select(HdsDeviceEntity.class)
                .matching(Query.query(criteria))
                .all()
                // 3. 过滤掉位置不匹配的设备
                .filterWhen(device -> devicePositionRepository
                        .findByPositionCode(device.getPositionCode())
                        .map(position -> position.getDeviceAppType().equals(device.getDeviceAppType()))
                        .defaultIfEmpty(false))
                .collectList()
                .map(devices -> {
                    long totalCount = devices.size();
                    long onlineCount = devices.stream()
                            .filter(d -> d.getDeviceStatus() == 1)
                            .count();
                    long offlineCount = devices.stream()
                            .filter(d -> d.getDeviceStatus() == 2)
                            .count();
                    long disabledCount = devices.stream()
                            .filter(d -> d.getDeviceStatus() == 3)
                            .count();

                    return DeviceStatisticsVO.builder()
                            .totalCount(totalCount)
                            .onlineCount(onlineCount)
                            .offlineCount(offlineCount)
                            .disabledCount(disabledCount)
                            .activeCount(onlineCount)  // 与 onlineCount 相同
                            .build();
                })
                .doOnSuccess(vo -> log.info("设备统计完成: hotelCode={}, statistics={}",
                        deviceStatisticsReq.getHotelCode(), vo))
                .doOnError(e -> log.error("设备统计失败: hotelCode={}, error={}",
                        deviceStatisticsReq.getHotelCode(), e.getMessage(), e));
    }


    /**
     * 根据设备APP类型过滤
     */
    private boolean filterByAppType(HdsDeviceEntity device, String deviceAppType) {
        return "all".equals(deviceAppType) || deviceAppType.equals(device.getDeviceAppType());
    }

    /**
     * 根据设备状态过滤
     */
    private boolean filterByStatus(HdsDeviceEntity device, Integer deviceStatus) {
        return deviceStatus == 0 || deviceStatus.equals(device.getDeviceStatus());
    }

    /**
     * 获取设备分布信息（分步查询处理）
     */
    public Mono<DeviceDistributionCountVO> getDeviceDistributionCount(DeviceDistributionReq req) {
        Preconditions.checkArgument(req != null, "参数不能为空");
        Preconditions.checkArgument(req.getHotelCode() != null, "门店编码不能为空");
        Preconditions.checkArgument(req.getDeviceStatus() != null, "设备状态不能为空");

        return deviceRepository.findByHotelCode(req.getHotelCode())
                .filter(device -> filterByStatus(device, req.getDeviceStatus()))
                .filter(device -> StringUtils.isNotBlank(device.getPositionCode()))
                // 添加位置和设备类型匹配的过滤条件
                .filterWhen(device -> devicePositionRepository
                        .findByPositionCode(device.getPositionCode())
                        .map(position -> position.getDeviceAppType().equals(device.getDeviceAppType()))
                        .defaultIfEmpty(false))
                .collectList()
                .flatMap(devices -> {
                    if (devices.isEmpty()) {
                        return Mono.just(DeviceDistributionCountVO.builder()
                                .deviceAppTypes(List.of(DeviceDistributionCountVO.DeviceTypeCount.builder()
                                        .deviceAppType("all")
                                        .deviceCount(0)
                                        .build()))
                                .build());
                    }

                    // 1. 获取所有设备类型
                    Set<String> deviceAppTypes = devices.stream()
                            .map(HdsDeviceEntity::getDeviceAppType)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    // 2. 查询设备位置信息以获取排序值
                    return devicePositionRepository.findByHotelCodeAndDeviceAppTypeIn(
                                    req.getHotelCode(), deviceAppTypes)
                            .collectList()
                            .map(positions -> {
                                // 3. 创建设备类型到排序值的映射
                                Map<String, Integer> appTypeSortOrders = positions.stream()
                                        .collect(Collectors.toMap(
                                                HdsDevicePositionEntity::getDeviceAppType,
                                                HdsDevicePositionEntity::getAppTypeSortOrder,
                                                (v1, v2) -> v1  // 如果有重复的设备类型，保留第一个排序值
                                        ));

                                List<DeviceDistributionCountVO.DeviceTypeCount> deviceTypeCounts = new ArrayList<>();

                                // 4. 添加总数统计（始终排在第一位）
                                deviceTypeCounts.add(DeviceDistributionCountVO.DeviceTypeCount.builder()
                                        .deviceAppType("all")
                                        .deviceCount(devices.size())
                                        .appTypeSortOrder(null)
                                        .build());

                                // 5. 按设备类型分组统计
                                Map<String, Long> deviceTypeGroups = devices.stream()
                                        .collect(Collectors.groupingBy(
                                                HdsDeviceEntity::getDeviceAppType,
                                                Collectors.counting()
                                        ));

                                // 6. 构建每种设备类型的统计并排序
                                List<DeviceDistributionCountVO.DeviceTypeCount> typeCountList = deviceTypeGroups.entrySet()
                                        .stream()
                                        .map(entry -> DeviceDistributionCountVO.DeviceTypeCount.builder()
                                                .deviceAppType(entry.getKey())
                                                .deviceCount(entry.getValue().intValue())
                                                .appTypeSortOrder(appTypeSortOrders.get(entry.getKey()))
                                                .build())
                                        .sorted(Comparator.comparing(
                                                DeviceDistributionCountVO.DeviceTypeCount::getAppTypeSortOrder,
                                                Comparator.nullsLast(Comparator.naturalOrder())))
                                        .toList();

                                deviceTypeCounts.addAll(typeCountList);

                                return DeviceDistributionCountVO.builder()
                                        .deviceAppTypes(deviceTypeCounts)
                                        .build();
                            });
                })
                .doOnSuccess(vo -> log.info("设备分布统计查询完成: hotelCode={}, req={}",
                        req.getHotelCode(), req))
                .doOnError(e -> log.error("设备分布统计查询失败: hotelCode={}, req={}, error={}",
                        req.getHotelCode(), req, e.getMessage(), e));
    }

    /**
     * 获取设备分布信息（分步查询处理）
     */
    public Mono<DeviceDistributionVO> getDeviceDistribution(DeviceDistributionReq req) {
        Preconditions.checkArgument(req != null, "参数不能为空");
        Preconditions.checkArgument(req.getHotelCode() != null, "门店编码不能为空");
        Preconditions.checkArgument(req.getDeviceAppType() != null, "设备App类型不能为空");
        Preconditions.checkArgument(req.getDeviceStatus() != null, "设备状态不能为空");
        // 1. 查询设备基本信息
        return deviceRepository.findByHotelCode(req.getHotelCode())
                .filter(device -> filterByAppType(device, req.getDeviceAppType()))
                .filter(device -> filterByStatus(device, req.getDeviceStatus()))
                .filterWhen(device -> devicePositionRepository
                        .findByPositionCode(device.getPositionCode())
                        .map(position -> position.getDeviceAppType().equals(device.getDeviceAppType()))
                        .defaultIfEmpty(false))
                .collectList()
                .flatMap(devices -> {
                    if (devices.isEmpty()) {
                        return Mono.just(DeviceDistributionVO.builder()
                                .distributions(Collections.emptyList())
                                .build());
                    }

                    // 2. 获取设备位置信息
                    Set<String> positionCodes = devices.stream()
                            .map(HdsDeviceEntity::getPositionCode)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    if (positionCodes.isEmpty()) {
                        return Mono.just(DeviceDistributionVO.builder()
                                .distributions(Collections.emptyList())
                                .build());
                    }

                    // 3. 查询位置信息
                    return devicePositionRepository.findByHotelCodeAndPositionCodeIn(
                                    req.getHotelCode(), positionCodes)
                            .collectMap(HdsDevicePositionEntity::getPositionCode, Function.identity())
                            .map(positionMap -> buildDeviceDistributionDTO(devices, positionMap))
                            .map(deviceDistributionDTOS -> buildDeviceDistribution(deviceDistributionDTOS));
                })
                .doOnSuccess(vo -> log.info("设备分布查询完成: hotelCode={}, req={}",
                        req.getHotelCode(), req))
                .doOnError(e -> log.error("设备分布查询失败: hotelCode={}, req={}, error={}",
                        req.getHotelCode(), req, e.getMessage(), e));
    }

    /**
     * 构建设备分布DTO列表
     */
    private List<DeviceDistributionDTO> buildDeviceDistributionDTO(
            List<HdsDeviceEntity> devices,
            Map<String, HdsDevicePositionEntity> positionMap) {

        return devices.stream()
                .map(device -> {
                    DeviceDistributionDTO dto = new DeviceDistributionDTO();
                    dto.setId(device.getId());
                    dto.setDeviceId(device.getDeviceId());
                    dto.setHotelCode(device.getHotelCode());
                    dto.setDeviceAppType(device.getDeviceAppType());
                    dto.setDeviceStatus(device.getDeviceStatus());

                    // 设置位置信息
                    HdsDevicePositionEntity position = positionMap.get(device.getPositionCode());
                    if (position == null) {
                        return null;
                    } else {
                        dto.setBlock(position.getBlock());
                        dto.setArea(position.getArea());
                        dto.setPositionName(position.getPositionName());

                        dto.setAppTypeSortOrder(position.getAppTypeSortOrder());
                        dto.setBlockFloorSortOrder(position.getBlockAreaSortOrder());
                        dto.setPositionSortOrder(position.getPositionSortOrder());
                    }
                    return dto;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator
                        // 首先按设备类型排序值降序
                        .comparing(DeviceDistributionDTO::getAppTypeSortOrder,
                                Comparator.nullsLast(Comparator.naturalOrder()))
                        // 然后按楼栋楼层排序值降序
                        .thenComparing(DeviceDistributionDTO::getBlockFloorSortOrder,
                                Comparator.nullsLast(Comparator.naturalOrder()))
                        // 最后按位置排序值降序
                        .thenComparing(DeviceDistributionDTO::getPositionSortOrder,
                                Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }

    private DeviceDistributionVO buildDeviceDistribution(List<DeviceDistributionDTO> dtos) {
        // 2. 构建分布信息，先按设备类型分组
        List<DeviceDistributionVO.DeviceTypeDistribution> distributions = dtos.stream()
                .collect(Collectors.groupingBy(
                        DeviceDistributionDTO::getDeviceAppType,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                devicesByType -> {
                                    // 2.1 按楼栋分组
                                    Map<String, List<DeviceDistributionDTO>> buildingGroups = devicesByType.stream()
                                            .collect(Collectors.groupingBy(DeviceDistributionDTO::getBlock));

                                    // 2.2 构建楼栋设备列表
                                    List<DeviceDistributionVO.BuildingFloorDevices> buildingFloors = buildingGroups.entrySet().stream()
                                            .map(entry -> {
                                                String block = entry.getKey();
                                                List<DeviceDistributionDTO> buildingDevices = entry.getValue();

                                                // 2.3 按楼层分组
                                                Map<String, List<DeviceDistributionDTO>> floorGroups = buildingDevices.stream()
                                                        .collect(Collectors.groupingBy(DeviceDistributionDTO::getArea));

                                                // 2.4 构建楼层设备列表
                                                List<DeviceDistributionVO.FloorDevices> floors = floorGroups.entrySet().stream()
                                                        .map(floorEntry -> {
                                                            String area = floorEntry.getKey();
                                                            List<DeviceDistributionDTO> floorDevices = floorEntry.getValue();

                                                            return DeviceDistributionVO.FloorDevices.builder()
                                                                    .floorName(area)
                                                                    .deviceCount(floorDevices.size())
                                                                    .rooms(buildRoomDevices(floorDevices))
                                                                    .build();
                                                        })
                                                        .collect(Collectors.toList());

                                                return DeviceDistributionVO.BuildingFloorDevices.builder()
                                                        .buildingName(block)
                                                        .floors(floors)
                                                        .blockFloorSortOrder(buildingDevices.get(0).getBlockFloorSortOrder())
                                                        .build();
                                            })
                                            .sorted(Comparator.comparing(
                                                    DeviceDistributionVO.BuildingFloorDevices::getBlockFloorSortOrder,
                                                    Comparator.nullsLast(Comparator.naturalOrder())))
                                            .collect(Collectors.toList());

                                    return DeviceDistributionVO.DeviceTypeDistribution.builder()
                                            .deviceAppType(devicesByType.get(0).getDeviceAppType())
                                            .deviceCount(devicesByType.size())
                                            .buildingFloors(buildingFloors)
                                            .appTypeSortOrder(devicesByType.get(0).getAppTypeSortOrder())
                                            .build();
                                }
                        )
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(
                        DeviceDistributionVO.DeviceTypeDistribution::getAppTypeSortOrder,
                        Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());

        return DeviceDistributionVO.builder()
                .distributions(distributions)
                .build();
    }

    private List<DeviceDistributionVO.RoomDevice> buildRoomDevices(List<DeviceDistributionDTO> devices) {
        return devices.stream()
                .map(device -> DeviceDistributionVO.RoomDevice.builder()
                        .roomNumber(device.getPositionName())
                        .deviceStatus(device.getDeviceStatus())
                        .deviceId(device.getId())
                        .positionSortOrder(device.getPositionSortOrder())
                        .build())
                .sorted(Comparator.comparing(
                        DeviceDistributionVO.RoomDevice::getPositionSortOrder,
                        Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }

    /**
     * 批量设备解绑
     */
    public Mono<Boolean> unbind(DeviceUnbindReq req) {
        Instant startTime = Instant.now();
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionUnbind(startTime, req, headerInfo)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            log.error("设备批量解绑失败: {}", ex.getMessage());
                            return Mono.error(new BusinessException(
                                    ResultCode.INTERNAL_SERVER_ERROR, "设备批量解绑失败"));
                        }));
    }

    /**
     * 事务执行批量解绑操作
     */
    /**
     * 事务执行批量解绑操作
     */
    private Mono<Boolean> executeTransferWithinTransactionUnbind(
            Instant startTime,
            DeviceUnbindReq req,
            HeaderUtils.HeaderInfo headerInfo) {

        if (CollectionUtils.isEmpty(req.getDeviceIds())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "设备ID列表不能为空"));
        }

        return transactionalOperator.transactional(
                deviceRepository.findAllById(req.getDeviceIds())
                        .collectList()
                        .flatMap(devices -> {
                            if (CollectionUtils.isEmpty(devices)) {
                                return Mono.error(new BusinessException(
                                        ResultCode.NOT_FOUND, "设备不存在"));
                            }

                            // 过滤出device_status != 3的设备
                            List<HdsDeviceEntity> validDevices = devices.stream()
                                    .filter(d -> DeviceStatusEnum.PENDING_ACTIVATION.getCode() != d.getDeviceStatus())
                                    .toList();

                            // 如果没有可解绑的设备，返回错误
                            if (validDevices.isEmpty()) {
                                return Mono.error(new BusinessException(
                                        ResultCode.NOT_ACCEPTABLE, "没有可解绑的设备，所有设备已经处于解绑状态"));
                            }

                            List<HdsDeviceEntity> beforeDevices = validDevices.stream()
                                    .map(device -> {
                                        HdsDeviceEntity copy = new HdsDeviceEntity();
                                        BeanUtils.copyProperties(device, copy);
                                        return copy;
                                    })
                                    .toList();

                            // 更新设备信息
                            List<HdsDeviceEntity> updatedDevices = validDevices.stream()
                                    .peek(device -> {
                                        device.setDeviceStatus(DeviceStatusEnum.PENDING_ACTIVATION.getCode());
                                        device.setHotelCode(null);
                                        device.setPositionCode(null);
                                        device.setActiveTime(null);
                                        device.setRtcUserId(null);
                                        device.setUserId(null);
                                        device.setUserName(null);
                                        device.setUpdatedBy(headerInfo.getUserId());
                                        device.setUpdatedByName(headerInfo.getUsername());
                                        device.setUpdatedAt(LocalDateTime.now());
                                    }).toList();

                            // 1. 更新设备状态的操作
                            Mono<List<HdsDeviceEntity>> updateDevicesMono = deviceRepository.saveAll(updatedDevices)
                                    .collectList();

                            // 2. 处理所有设备的登出操作
                            List<Mono<Boolean>> logoutMonos = validDevices.stream()
                                    .map(device -> hotelDsApiClient.deviceLogOut(device.getDeviceId())
                                            .defaultIfEmpty(Boolean.FALSE)
                                            .doOnNext(result -> {
                                                log.info("device[{}]logout result: {}", device.getId(), result);
                                                if (!result) {
                                                    throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                                            "设备[" + device.getId() + "]登出失败");
                                                }
                                            }))
                                    .toList();
                            Mono<List<Boolean>> logoutResultsMono = Flux.concat(logoutMonos).collectList();

                            // 3. 并行执行两个操作：更新设备和登出设备
                            return Mono.zip(updateDevicesMono, logoutResultsMono)
                                    .map(tuple -> {
                                        List<HdsDeviceEntity> afterDevices = tuple.getT1();
                                        List<Boolean> logoutResults = tuple.getT2();

                                        log.info("Device logout batch result: All successful, total {} devices", logoutResults.size());

                                        // 记录操作日志
                                        handleBindSuccess(startTime, beforeDevices, afterDevices, req, headerInfo);
                                        return Boolean.TRUE;
                                    });
                        })
                        .doOnError(error -> {
                            log.error("Device unbinding transaction failed: {}", error.getMessage(), error);
                            handleBindError(startTime, error, req, headerInfo);
                        }));
    }

    /**
     * 记录日志
     *
     * @param startTime:开始时间
     * @param validDevices:更新前
     * @param afterDevices:更新后
     * @param req:请求参数
     * @param headerInfo:请求头
     */
    public void handleBindSuccess(Instant startTime,
                                  List<HdsDeviceEntity> validDevices,
                                  List<HdsDeviceEntity> afterDevices,
                                  DeviceUnbindReq req,
                                  HeaderUtils.HeaderInfo headerInfo) {
        String deviceIds = req.getDeviceIds().stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        // 1. 创建设备生命周期日志
        try {
            // 1. 创建设备生命周期日志
            List<HdsDeviceLogEntity> deviceLogs = afterDevices.stream()
                    .map(device -> {
                        HdsDeviceLogEntity log = new HdsDeviceLogEntity();
                        log.setDeviceId(device.getDeviceId());
                        log.setDeviceSn(device.getDeviceSn());
                        log.setEventType(DeviceEventTypeEnum.UNBIND.getCode());
                        log.setEventTime(LocalDateTime.now());
                        log.setRemark("批量解绑设备");
                        setAuditFields(log, headerInfo);
                        return log;
                    })
                    .toList();

            // 2. 创建每个设备对应的操作日志上下文
            List<LoggingContext> loggingContexts = new ArrayList<>();
            for (int i = 0; i < afterDevices.size(); i++) {
                HdsDeviceEntity beforeDevice = validDevices.get(i);
                HdsDeviceEntity afterDevice = afterDevices.get(i);

                try {
                    LoggingContext context = LoggingContext.builder()
                            .oprParams(req)
                            .businessId(afterDevice.getId())
                            .oprTime(LocalDateTime.ofInstant(startTime, ZoneId.systemDefault()))
                            .businessType(BussinessTypeEnum.DEVICE)
                            .operationType(OperationTypeEnum.UPDATE)
                            .methodName("unbindDevices")
                            .headerInfo(headerInfo)
                            .oprResult("SUCCESS")
                            .beforeObj(beforeDevice)
                            .afterObj(afterDevice)
                            .oprContent("批量解绑设备")
                            .build();

                    loggingContexts.add(context);
                } catch (Exception e) {
                    log.error("创建日志上下文失败，设备ID: {}, 错误: {}",
                            afterDevice.getDeviceId(), e.getMessage(), e);
                }
            }

            // 3. 批量保存设备日志和操作日志
            Mono.zip(
                            deviceLogRepository.saveAll(deviceLogs)
                                    .onErrorResume(e -> {
                                        log.error("保存设备生命周期日志失败: {}", e.getMessage(), e);
                                        return Mono.empty();
                                    })
                                    .collectList(),
                            Flux.fromIterable(loggingContexts)
                                    .flatMap(context ->
                                            operationLogUtil.recordSuccess(context)
                                                    .onErrorResume(e -> {
                                                        log.error("记录操作日志失败，设备ID: {}, 错误: {}",
                                                                context.getBusinessId(), e.getMessage(), e);
                                                        return Mono.empty();
                                                    })
                                    )
                                    .collectList()
                    )
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe(
                            success -> log.info("设备批量解绑日志记录成功 - 设备Ids: [{}]", deviceIds),
                            error -> log.error("设备批量解绑日志记录失败: {}", error.getMessage(), error)
                    );
        } catch (Exception e) {
            log.error("处理日志记录时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理解绑失败的操作
     * 记录失败日志
     */
    public void handleBindError(Instant startTime, Throwable error, DeviceUnbindReq req, HeaderUtils.HeaderInfo headerInfo) {
//        String errorMessage = error instanceof BusinessException ?
//                error.getMessage() : "系统异常，设备解绑失败";
//
//        LoggingContext context = getLoggingContext(req, startTime, "failure", errorMessage, headerInfo);
//
//        operationLogUtil.recordFail(context).subscribe(
//                success -> log.info("设备解绑失败日志记录成功"),
//                logError -> log.error("设备解绑失败日志记录失败: {}", logError.getMessage())
//        );
    }

    public Mono<HdsDeviceEntity> getByHotelAndUserId(String hotelCode, String userId) {
        return deviceRepository.findByHotelCodeAndUserId(hotelCode, userId)
                .next().switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "Device not found")));
    }

    public Flux<HdsDeviceEntity> findByHotelCode(String hotelCode) {
        return deviceRepository.findByHotelCode(hotelCode);
    }

    public Mono<List<HdsDeviceEntity>> saveAll(List<HdsDeviceEntity> updatedDevices) {
        if (CollectionUtils.isEmpty(updatedDevices)) {
            return Mono.just(Collections.emptyList());
        }
        return deviceRepository.saveAll(updatedDevices).collectList();
    }

    public Mono<List<HdsDeviceEntity>> findByHotelCodeInAndDeviceStatusEquals(List<String> hotelCodes, Integer status) {
        return deviceRepository.findByHotelCodeInAndDeviceStatusEquals(hotelCodes, status).collectList()
                .defaultIfEmpty(Collections.emptyList());
    }

    public Mono<Integer> updateDeviceStatus(
            List<Long> ids,
            Integer status,
            String updatedBy,
            String updatedByName) {
        if (CollectionUtils.isEmpty(ids)) {
            return Mono.just(0);
        }
        return deviceRepository.updateDeviceStatus(ids, status, updatedBy, updatedByName);
    }

    Mono<List<HdsDeviceEntity>> findAllById(List<Long> deviceIds) {
        return deviceRepository.findAllById(deviceIds).collectList()
                .defaultIfEmpty(Collections.emptyList());
    }

    public Mono<Void> sendDeviceRtcMessage(List<String> deviceIds, String isEnable) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            log.warn("设备ID列表为空，跳过发送RTC消息");
            return Mono.empty();
        }

        log.info("开始发送RTC消息，状态: {}, 设备数量: {}", isEnable, deviceIds.size());

        // 使用Flux.fromIterable并发处理，有限制并发数
        return Flux.fromIterable(deviceIds)
                .flatMap(deviceId ->
                                hotelDsApiClient.sendDeviceRtcMessage(deviceId, isEnable)
                                        .doOnSuccess(result -> log.info("设备[{}] RTC消息发送成功: {}", deviceId, result))
                                        .onErrorResume(e -> {
                                            log.error("设备[{}] RTC消息发送失败: {}", deviceId, e.getMessage(), e);
                                            return Mono.just(false);
                                        })
                                        .defaultIfEmpty(false),
                        10) // 限制最大并发数为10
                .collectList()
                .doOnNext(results -> {
                    long successCount = results.stream().filter(Boolean::booleanValue).count();
                    log.info("RTC消息发送完成，成功: {}/{}，状态: {}",
                            successCount, deviceIds.size(), isEnable);
                })
                .then();
    }

    public Mono<List<HdsDeviceEntity>> getDeviceInfo(String hotelCode, String userId, String deviceId, String deviceType) {
        Criteria criteria = Criteria.empty();
        if (StringUtils.isNotBlank(deviceId)){
            criteria = criteria.and(HdsDeviceFieldEnum.device_id.name()).is(deviceId);
        }
        if (StringUtils.isNotBlank(deviceType)){
            criteria = criteria.and(HdsDeviceFieldEnum.device_app_type.name()).is(deviceType);
        }
        if (StringUtils.isNotBlank(hotelCode)){
            criteria = criteria.and(HdsDeviceFieldEnum.hotel_code.name()).is(hotelCode);
        }
        if (StringUtils.isNotBlank(userId)){
            criteria = criteria.and(HdsDeviceFieldEnum.user_id.name()).is(userId);
        }
        Sort sort = Sort.by(Sort.Direction.DESC, HdsDeviceFieldEnum.id.name());
        return r2dbcEntityTemplate.select(Query.query(criteria).sort(sort), HdsDeviceEntity.class).collectList();
    }
}