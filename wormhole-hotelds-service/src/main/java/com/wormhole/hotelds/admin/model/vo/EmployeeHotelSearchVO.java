package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/5/26 17:14
 * @Description：EmployeeHotelSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EmployeeHotelSearchVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 门店编号
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * ota到期时间
     */
    private String otaEndTime;

    /**
     * ai产品
     */
    private List<String> aiProductTypes;

    public static EmployeeHotelSearchVO toVO(HdsHotelInfoEntity entity) {
        EmployeeHotelSearchVO vo = new EmployeeHotelSearchVO();

        // 计算ota过期时间
        LocalDateTime initFinishedAt = entity.getInitFinishedAt();
        if (Objects.nonNull(initFinishedAt)) {
            if (Objects.nonNull(entity.getOtaExtendMonths())) {
                initFinishedAt = initFinishedAt.plusMonths(entity.getOtaExtendMonths());
            }
            if (Objects.nonNull(entity.getOtaRewardMonths())) {
                initFinishedAt = initFinishedAt.plusMonths(entity.getOtaRewardMonths());
            }
        }


        vo.setId(entity.getId())
                .setHotelCode(entity.getHotelCode())
                .setHotelName(entity.getHotelName())
                .setOtaEndTime(SimpleDateUtils.formatLocalDateTimeToDate(initFinishedAt));

        if (StringUtils.isNotBlank(entity.getAiProductTypes())) {
            vo.setAiProductTypes(Arrays.stream(entity.getAiProductTypes().split(",")).toList());
        }
        return vo;
    }
}
