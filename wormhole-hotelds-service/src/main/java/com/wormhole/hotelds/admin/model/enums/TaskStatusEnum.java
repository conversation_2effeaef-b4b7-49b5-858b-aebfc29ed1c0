package com.wormhole.hotelds.admin.model.enums;

import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/3/29 21:40
 * @Description：ImportStatusEnum
 */
@Getter
public enum TaskStatusEnum {

    /**
     * 等待处理
     */
    PENDING(0, "等待处理"),

    /**
     * 处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 完成
     */
    SUCCESS(2, "完成"),

    /**
     * 失败
     */
    FAILED(3, "失败"),

    PARTIAL_SUCCESS(4, "部分成功");

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 状态描述
     */
    private String message;

    /**
     * 构造函数
     *
     * @param code 状态码
     * @param message 状态描述
     */
    TaskStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 获取状态码
     *
     * @return 状态码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举实例，如果不存在则返回null
     */
    public static TaskStatusEnum getByCode(Integer code) {
        for (TaskStatusEnum status : TaskStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断状态码是否合法
     *
     * @param code 状态码
     * @return 是否是合法的状态码
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    @Override
    public String toString() {
        return this.message;
    }
}
