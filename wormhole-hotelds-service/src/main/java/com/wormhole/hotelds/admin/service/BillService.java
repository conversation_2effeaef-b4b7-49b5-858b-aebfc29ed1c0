package com.wormhole.hotelds.admin.service;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.hotelds.admin.model.req.BillSearchReq;
import com.wormhole.hotelds.admin.model.resp.OrderStatusResp;
import com.wormhole.hotelds.admin.model.vo.BillSearchVO;
import com.wormhole.hotelds.admin.repository.HdsBillRepository;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/19 17:47
 * @Description：PaymentBillService
 */
@Service
@Slf4j
public class BillService {

    @Resource
    private HdsBillRepository hdsBillRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private PackageService packageService;

    @Resource
    private ProductService productService;

    public Mono<Boolean> create(OrderStatusResp orderStatusResp, HdsOrderEntity orderEntity) {
        HdsBillEntity billEntity = new HdsBillEntity()
                .setThirdOrderNo(orderStatusResp.getChannelTrxId())
                .setTransactionNo(orderStatusResp.getUniqueOrderNo())
                .setOrderNo(orderEntity.getOrderNo())
                .setHotelCode(orderEntity.getHotelCode())
                .setPayAmount(orderEntity.getOrderAmount())
                .setPayMethod(orderEntity.getPayMethod())
                .setTransactionStatus(1) // 交易成功
                .setTransactionAt(SimpleDateUtils.parseDateTime(orderStatusResp.getPaySuccessDate()))
                .setIsInvoiceApplied(0); // 未申请发票
        billEntity.setRowStatus(1);

        return hdsBillRepository.save(billEntity)
                .thenReturn(true);
    }

    /**
     * 账单列表查询总数
     */
    public Mono<Long> searchCount(BillSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 如果存在ID列表查询
        if (req.getBillIds() != null && !req.getBillIds().isEmpty()) {
            return buildSearchCriteria(req, headerInfo)
                    .flatMap(criteria -> r2dbcEntityTemplate.count(Query.query(criteria), HdsBillEntity.class));
        }
        // 如果包含套餐名称查询，需要特殊处理
        else if (StringUtils.isNotBlank(req.getPackageName())) {
            return searchWithPackageName(req, headerInfo).count();
        }
        // 如果包含套餐编码查询，需要特殊处理
        else if (StringUtils.isNotBlank(req.getPackageCode())) {
            return searchWithPackageCode(req, headerInfo).count();
        } else {
            return buildSearchCriteria(req, headerInfo)
                    .flatMap(criteria -> r2dbcEntityTemplate.count(Query.query(criteria), HdsBillEntity.class));
        }
    }

    /**
     * 账单列表查询
     */
    public Mono<List<BillSearchVO>> search(BillSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 如果存在ID列表查询
        if (req.getBillIds() != null && !req.getBillIds().isEmpty()) {
            return buildSearchCriteria(req, headerInfo)
                    .flatMap(criteria -> r2dbcEntityTemplate.select(Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.DESC, HdsBillFieldEnum.created_at.name())), HdsBillEntity.class)
                            .collectList()
                            .flatMap(this::toSortedVOList));
        }
        // 如果包含套餐名称查询，需要特殊处理
        else if (StringUtils.isNotBlank(req.getPackageName())) {
            return searchWithPackageName(req, headerInfo).collectList();
        }
        // 如果包含套餐编码查询，需要特殊处理
        else if (StringUtils.isNotBlank(req.getPackageCode())) {
            return searchWithPackageCode(req, headerInfo).collectList();
        } else {
            return buildSearchCriteria(req, headerInfo)
                    .flatMap(criteria -> r2dbcEntityTemplate.select(Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.DESC, HdsBillFieldEnum.created_at.name())), HdsBillEntity.class)
                            .collectList()
                            .flatMap(this::toSortedVOList));
        }
    }

    /**
     *  转换为BillSearchVO
     */
    private Mono<List<BillSearchVO>> toSortedVOList(List<HdsBillEntity> billList) {
        return Flux.fromIterable(billList)
                // 并发异步查关联表
                .flatMap(this::convertToBillSearchVO)
                .collectList()
                .map(list -> {
                    list.sort(
                            Comparator.comparing(BillSearchVO::getCreatedAt)
                                    // 二级键
                                    .thenComparing(BillSearchVO::getId)
                                    .reversed()
                    );
                    return list;
                });
    }

    /**
     * 根据套餐名称查询账单（模糊查询）
     */
    private Flux<BillSearchVO> searchWithPackageName(BillSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 1. 先查询符合条件的套餐信息
        return r2dbcEntityTemplate.select(Query.query(
                                Criteria.where(HdsPackageFieldEnum.package_name.name()).like("%" + req.getPackageName() + "%")),
                        HdsPackageEntity.class)
                .flatMap(packageEntity -> {
                    // 2. 根据套餐编码查询相关订单
                    return r2dbcEntityTemplate.select(Query.query(
                                            Criteria.where(HdsPackageFieldEnum.package_code.name()).is(packageEntity.getPackageCode())),
                                    HdsOrderEntity.class)
                            .flatMap(orderEntity -> {
                                // 3. 根据订单号查询账单
                                return buildSearchCriteria(req, headerInfo)
                                        .flatMapMany(criteria -> {
                                            // 添加订单号条件
                                            Criteria finalCriteria = criteria.and(HdsBillFieldEnum.order_no.name()).is(orderEntity.getOrderNo());
                                            return r2dbcEntityTemplate.select(Query.query(finalCriteria)
                                                    .sort(Sort.by(Sort.Direction.DESC, HdsBillFieldEnum.created_at.name())), HdsBillEntity.class);
                                        });
                            });
                })
                .flatMap(this::convertToBillSearchVO);
    }

    /**
     * 根据套餐编码查询账单（精确查询）
     */
    private Flux<BillSearchVO> searchWithPackageCode(BillSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 1. 根据套餐编码查询相关订单
        return r2dbcEntityTemplate.select(Query.query(
                                Criteria.where(HdsOrderFieldEnum.package_code.name()).is(req.getPackageCode())),
                        HdsOrderEntity.class)
                .flatMap(orderEntity -> {
                    // 2. 根据订单号查询账单
                    return buildSearchCriteria(req, headerInfo)
                            .flatMapMany(criteria -> {
                                // 添加订单号条件
                                Criteria finalCriteria = criteria.and(HdsBillFieldEnum.order_no.name()).is(orderEntity.getOrderNo());
                                return r2dbcEntityTemplate.select(Query.query(finalCriteria)
                                        .sort(Sort.by(Sort.Direction.DESC, HdsBillFieldEnum.created_at.name())), HdsBillEntity.class);
                            });
                })
                .flatMap(this::convertToBillSearchVO);
    }

    /**
     * 构建账单查询条件
     */
    private Mono<Criteria> buildSearchCriteria(BillSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        Criteria criteria = buildBaseCriteria(req);
        
        // 如果请求头中包含酒店编码，添加酒店编码条件
        if (StringUtils.isNotBlank(headerInfo.getHotelCode())) {
            criteria = criteria.and(HdsBillFieldEnum.hotel_code.name()).is(headerInfo.getHotelCode());
        }
        
        return Mono.just(criteria);
    }

    /**
     * 构建基础查询条件
     */
    private Criteria buildBaseCriteria(BillSearchReq req) {
        Criteria criteria = Criteria.empty();
        if (StringUtils.isNotBlank(req.getOrderNo())) {
            criteria = criteria.and(HdsBillFieldEnum.order_no.name()).is(req.getOrderNo());
        }
        if (StringUtils.isNotBlank(req.getTransactionNo())) {
            criteria = criteria.and(HdsBillFieldEnum.transaction_no.name()).is(req.getTransactionNo());
        }
        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(HdsBillFieldEnum.hotel_code.name()).is(req.getHotelCode());
        }
        if (req.getBillIds() != null && !req.getBillIds().isEmpty()) {
            criteria = criteria.and(HdsBillFieldEnum.id.name()).in(req.getBillIds());
        }

        return criteria;
    }

    /**
     * 根据酒店编码列表构建查询条件
     */
    private Criteria buildCriteriaWithHotelCodes(BillSearchReq req, List<String> hotelCodes) {
        Criteria criteria = buildBaseCriteria(req);

        if (hotelCodes.isEmpty()) {
            // 如果没有匹配的酒店，返回不可能匹配的条件
            return criteria.and(HdsBillFieldEnum.hotel_code.name()).is("__NO_MATCH__");
        }
        
        return criteria.and(HdsBillFieldEnum.hotel_code.name()).in(hotelCodes);
    }

    /**
     * 转换为账单搜索VO（包含详细信息）
     */
    private Mono<BillSearchVO> convertToBillSearchVO(HdsBillEntity entity) {
        BillSearchVO vo = new BillSearchVO();
        BeanUtils.copyProperties(entity, vo);
        // 查询订单信息补充套餐名、产品名等
        return r2dbcEntityTemplate.selectOne(Query.query(Criteria.where(HdsOrderFieldEnum.order_no.name()).is(entity.getOrderNo())), HdsOrderEntity.class)
                .flatMap(orderEntity -> {
                    vo.setPackageName("");
                    vo.setProductName("");
                    if (orderEntity != null) {
                        vo.setPeriodType(orderEntity.getPeriodType());
                        vo.setOtaEndTime(orderEntity.getExpireTime());
                        vo.setOrderCreatedAt(orderEntity.getCreatedAt());
                        // 查询套餐名
                        return packageService.findByPackageCode(orderEntity.getPackageCode())
                                .map(pkg -> {
                                    vo.setPackageName(pkg.getPackageName());
                                    return vo;
                                })
                                .defaultIfEmpty(vo)
                                .flatMap(v -> productService.findById(orderEntity.getProductId())
                                        .map(product -> {
                                            v.setProductName(product.getProductName());
                                            return v;
                                        })
                                        .defaultIfEmpty(v));
                    }
                    return Mono.just(vo);
                })
                .defaultIfEmpty(vo)
                // 查询酒店信息填充酒店名称
                .flatMap(v -> {
                    if (StringUtils.isNotBlank(entity.getHotelCode())) {
                        return r2dbcEntityTemplate.selectOne(
                                Query.query(Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name())
                                        .is(entity.getHotelCode())),
                                HdsHotelInfoEntity.class)
                                .map(hotel -> {
                                    v.setHotelCode(entity.getHotelCode());
                                    v.setHotelName(hotel.getHotelName());
                                    return v;
                                })
                                .defaultIfEmpty(v);
                    }
                    return Mono.just(v);
                });
    }

    /**
     * 根据ID查询账单
     *
     * @param billId 账单ID
     * @return 账单信息
     */
    public Mono<HdsBillEntity> findById(Integer billId) {
        return hdsBillRepository.findById(billId.longValue())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "账单不存在: " + billId)));
    }

    /**
     * 批量根据ID查询账单
     *
     * @param billIds 账单ID列表
     * @return 账单信息列表
     */
    public Mono<List<HdsBillEntity>> findByIds(List<Integer> billIds) {
        return Flux.fromIterable(billIds)
                .flatMap(this::findById)
                .collectList();
    }

    /**
     * 检查账单是否已申请发票
     *
     * @param billIds 账单ID列表
     * @return 已申请发票的账单ID列表
     */
    public Mono<List<Integer>> checkInvoiceAppliedBills(List<Integer> billIds) {
        return Flux.fromIterable(billIds)
                .flatMap(this::findById)
                .filter(bill -> bill.getIsInvoiceApplied() != null && bill.getIsInvoiceApplied() == 1)
                .map(HdsBillEntity::getId)
                .collectList();
    }
}
