package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/6/19 17:39
 * @Description：YeeOrderVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class YeeOrderVO {

    /**
     * 易宝收款订单号
     */
    private String uniqueOrderNo;

    /**
     * 收银台链接
     */
    private String cashierUrl;
}
