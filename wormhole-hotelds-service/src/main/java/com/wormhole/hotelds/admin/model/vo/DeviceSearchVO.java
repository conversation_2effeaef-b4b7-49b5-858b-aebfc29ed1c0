package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/4/8 19:14
 * @Description：DeviceSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceSearchVO {

    /**
     * 设备id
     */
    private Long id;

    /**
     * 设备code
     */
    private String deviceCode;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备imei
     */
    private String imei;

    /**
     * 设备类型
     */
    private String deviceAppType;

    /**
     * 设备类型code
     */
    private String modelCode;

    /**
     * 设备类型名称
     */
    private String modelName;

    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 楼栋
     */
    private String block;

    /**
     * 区域
     */
    private String area;

    /**
     * 位置名称
     */
    private String positionName;

    /**
     * 房间号
     */
    private String roomNum;

    /**
     * 保修开始时间
     */
    private String warrantyStart;

    /**
     * 保修结束时间
     */
    private String warrantyEnd;

    /**
     * 设备状态
     */
    private Integer deviceStatus;

    /**
     * 采购时间
     */
    private String purchaseTime;

    /**
     * 当前APP版本
     */
    private String appVersion;
}
