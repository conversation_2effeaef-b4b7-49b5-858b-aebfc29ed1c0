package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.annotation.OperationLog;
import com.wormhole.hotelds.admin.model.enums.AiProductTypeEnum;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.InvitationCodeLogSearchVO;
import com.wormhole.hotelds.admin.model.vo.InvitationCodeSearchVO;
import com.wormhole.hotelds.admin.model.vo.InvitationCodeVO;
import com.wormhole.hotelds.admin.repository.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/5/19 09:45
 * @Description：InvitationCodeService
 */
@Slf4j
@Service
public class InvitationCodeService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private InvitationCodeRepository invitationCodeRepository;

    @Resource
    private HotelRepository hotelRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;

    @Resource
    private UserLeadRepository userLeadRepository;

    @Resource
    private UserHotelLeadMappingRepository userHotelLeadMappingRepository;

    @Resource
    private HdsEmployeeService hdsEmployeeService;

    /**
     * 创建邀请码
     */
//    @OperationLog(businessType = BussinessTypeEnum.INVITATION_CODE, operationType = OperationTypeEnum.ADD)
    public Mono<InvitationCodeSearchVO> create(InvitationCodeSaveReq invitationCodeSaveReq) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    String hotelCode = headerInfo.getHotelCode();
                    if (StringUtils.isBlank(hotelCode)) {
                        return invitationCodeRepository.existsByName(invitationCodeSaveReq.getName())
                                .flatMap(exists -> {
                                    if (Boolean.TRUE.equals(exists)) {
                                        // 名称已存在，返回错误
                                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "邀请码名称已存在"));
                                    }
                                    // 名称不存在，继续处理
                                    return generateCodeAndSave(invitationCodeSaveReq, headerInfo);
                                });
                    } else {
                        return hotelRepository.findByHotelCode(hotelCode)
                                .flatMap(hotelInfoEntity -> {
                                    List<String> aiProductTypes = Arrays.stream(hotelInfoEntity.getAiProductTypes().split(",")).toList();
                                    if (!aiProductTypes.contains(AiProductTypeEnum.OTA_AGENT.getCode())) {
                                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "当前门店未开通OTA智能体功能,不能生成邀请码"));
                                    }
                                    return invitationCodeRepository.findByHotelCode(headerInfo.getHotelCode())
                                            .flatMap(hdsInvitationCodeEntity -> toVo(hdsInvitationCodeEntity, null))
                                            .switchIfEmpty(generateCodeAndSave(invitationCodeSaveReq, headerInfo));
                                });
                    }
                });
    }

    private Mono<InvitationCodeSearchVO> generateCodeAndSave(InvitationCodeSaveReq invitationCodeSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        return codePoolManager.getCodeFromPool(BussinessTypeEnum.INVITATION_CODE.getBusinessType())
                .flatMap(invitationCode -> {
                    Mono<Tuple2<HdsHotelInfoEntity, HdsInvitationCodeEntity>> transactionalOperations = Mono.defer(() -> {
                        String hotelCode = headerInfo.getHotelCode();
                        if (StringUtils.isNotBlank(hotelCode)) {
                            return invitationCodeRepository.findByHotelCode(hotelCode)
                                    .flatMap(existingCode -> {
                                        // 如果已存在，直接返回现有的数据
                                        return Mono.zip(
                                                Mono.just(new HdsHotelInfoEntity()), // 不需要更新酒店信息
                                                Mono.just(existingCode)
                                        );
                                    })
                                    .switchIfEmpty(Mono.defer(() -> {
                                        // 不存在，创建新的邀请码
                                        Mono<HdsHotelInfoEntity> hdsHotelInfoEntityMono = updateHotelInfo(headerInfo, invitationCode);
                                        Mono<HdsInvitationCodeEntity> hdsInvitationCodeEntityMono = createInvitationCodeEntity(invitationCodeSaveReq, invitationCode, headerInfo);
                                        return Mono.zip(hdsHotelInfoEntityMono, hdsInvitationCodeEntityMono);
                                    }));
                        } else {
                            // 非酒店场景，直接创建
                            Mono<HdsHotelInfoEntity> hdsHotelInfoEntityMono = updateHotelInfo(headerInfo, invitationCode);
                            Mono<HdsInvitationCodeEntity> hdsInvitationCodeEntityMono = createInvitationCodeEntity(invitationCodeSaveReq, invitationCode, headerInfo);
                            return Mono.zip(hdsHotelInfoEntityMono, hdsInvitationCodeEntityMono);
                        }
                    });

                    // 保存邀请码
                    return transactionalOperator.transactional(transactionalOperations)
                            .flatMap(tuple -> toVo(tuple.getT2(), null))
                            .onErrorResume(error -> {
                                log.error("创建邀请码失败: {}", error.getMessage(), error);
                                return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建邀请码失败: " + error.getMessage()));
                            });
                });
    }

    private Mono<HdsHotelInfoEntity> updateHotelInfo(HeaderUtils.HeaderInfo headerInfo, String invitationCode) {
        if (StringUtils.isBlank(headerInfo.getHotelCode())) {
            return Mono.just(new HdsHotelInfoEntity());
        }
        return hotelRepository.findByHotelCode(headerInfo.getHotelCode())
                .flatMap(hotelInfoEntity -> {
                    hotelInfoEntity.setInvitationCode(invitationCode);
                    return hotelRepository.save(hotelInfoEntity);
                });
    }

    /**
     * 构建邀请码实体
     */
    private Mono<HdsInvitationCodeEntity> createInvitationCodeEntity(InvitationCodeSaveReq invitationCodeSaveReq, String invitationCode, HeaderUtils.HeaderInfo headerInfo) {
        HdsInvitationCodeEntity entity = new HdsInvitationCodeEntity();

        entity.setInvitationCode(invitationCode);
        entity.setName(invitationCodeSaveReq.getName());

        if (StringUtils.isNotBlank(headerInfo.getHotelCode())) {
            entity.setSource(2);
            entity.setHotelCode(headerInfo.getHotelCode());
        }
        // 默认启用
        entity.setStatus(1);
        entity.setCreatedAt(LocalDateTime.now());
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setRowStatus(1);
        return invitationCodeRepository.save(entity);
    }

    /**
     * 更新邀请码
     */
//    @OperationLog(businessType = BussinessTypeEnum.INVITATION_CODE, operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> update(InvitationCodeSaveReq req) {
        // 参数验证
        Preconditions.checkArgument(Objects.nonNull(req), "请求参数不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "邀请码ID不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getName()), "邀请码名称不能为空");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> invitationCodeRepository.findById(req.getId())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "邀请码不存在")))
                        .flatMap(existingEntity -> {
                            // 3. 更新实体属性
                            updateInvitationCodeEntity(existingEntity, req, headerInfo);

                            // 4. 保存更新后的实体
                            return transactionalOperator.transactional(invitationCodeRepository.save(existingEntity))
                                    .doOnSuccess(updatedEntity -> log.info("更新邀请码成功: id={}", updatedEntity.getId()))
                                    .map(updatedEntity -> true)
                                    .onErrorResume(error -> {
                                        log.error("更新邀请码失败: {}", error.getMessage(), error);
                                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新邀请码失败: " + error.getMessage()));
                                    });
                        }));
    }

    /**
     * 更新邀请码实体
     */
    private void updateInvitationCodeEntity(HdsInvitationCodeEntity entity, InvitationCodeSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        entity.setName(req.getName());
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setUpdatedBy(headerInfo.getUserId());
        entity.setUpdatedByName(headerInfo.getName());
    }

    /**
     * 删除邀请码
     */
//    @OperationLog(businessType = BussinessTypeEnum.INVITATION_CODE, operationType = OperationTypeEnum.DELETE)
    public Mono<Boolean> delete(InvitationCodeDeleteReq req) {
        // 参数验证
        Preconditions.checkArgument(Objects.nonNull(req), "请求参数不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "邀请码ID不能为空");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> invitationCodeRepository.findById(req.getId())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "邀请码不存在")))
                        .flatMap(entity -> {
                            if (StringUtils.isNotBlank(entity.getHotelCode())) {
                                return Mono.error(new BusinessException(ResultCode.NOT_ACCEPTABLE,
                                        "门店端邀请码不能删除"));
                            }
                            // 执行删除
                            return transactionalOperator.transactional(invitationCodeRepository.deleteById(req.getId()))
                                    .doOnSuccess(v -> log.info("删除邀请码成功: id={}", req.getId()))
                                    .then(Mono.just(true))
                                    .onErrorResume(error -> {
                                        log.error("删除邀请码失败: {}", error.getMessage(), error);
                                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除邀请码失败: " + error.getMessage()));
                                    });
                        }));
    }

    /**
     * 更新邀请码状态
     */
    @OperationLog(businessType = BussinessTypeEnum.INVITATION_CODE, operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> updateStatus(InvitationCodeStatusReq req) {
        // 参数验证
        Preconditions.checkArgument(Objects.nonNull(req), "请求参数不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "邀请码ID不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getStatus()), "状态不能为空");
        Preconditions.checkArgument(req.getStatus() == 0 || req.getStatus() == 1, "状态值无效，应为0或1");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> invitationCodeRepository.findById(req.getId())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "邀请码不存在")))
                        .flatMap(entity -> {
                            // 如果状态相同，不需要更新
                            if (req.getStatus().equals(entity.getStatus())) {
                                return Mono.just(true);
                            }

                            // 更新状态
                            entity.setStatus(req.getStatus());
                            entity.setUpdatedAt(LocalDateTime.now());
                            entity.setUpdatedBy(headerInfo.getUserId());
                            entity.setUpdatedByName(headerInfo.getUsername());

                            return transactionalOperator.transactional(invitationCodeRepository.save(entity))
                                    .doOnSuccess(updatedEntity -> log.info("更新邀请码状态成功: id={}, status={}", updatedEntity.getId(), updatedEntity.getStatus()))
                                    .map(updatedEntity -> true)
                                    .onErrorResume(error -> {
                                        log.error("更新邀请码状态失败: {}", error.getMessage(), error);
                                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新邀请码状态失败: " + error.getMessage()));
                                    });
                        }));
    }

    /**
     * 统计邀请码数量
     */
    public Mono<Long> searchCount(InvitationCodeSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.count(query, HdsInvitationCodeEntity.class);
    }

    /**
     * 搜索邀请码列表
     */
    public Mono<List<InvitationCodeSearchVO>> search(InvitationCodeSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);

        // 设置排序方式，默认按创建时间降序
        Sort sort = Sort.by(Sort.Direction.DESC, HdsInvitationCodeFieldEnum.created_at.name());

        // 构建查询对象，设置分页参数
        Query query = Query.query(criteria)
                .sort(sort)
                .limit(req.getPageSize())
                .offset((long) (req.getCurrent() - 1) * req.getPageSize());

        // 执行查询，返回VO列表
        return r2dbcEntityTemplate.select(query, HdsInvitationCodeEntity.class)
                .collectList()
                .flatMap(hdsInvitationCodeEntities -> {
                    // 如果没有查询到数据，直接返回空列表
                    if (hdsInvitationCodeEntities.isEmpty()) {
                        return Mono.just(Collections.emptyList());
                    }

                    // 提取所有非空的酒店代码
                    Set<String> hotelCodes = hdsInvitationCodeEntities.stream()
                            .map(HdsInvitationCodeEntity::getHotelCode)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    // 提取所有非空的邀请码
                    Set<String> invitationCodes = hdsInvitationCodeEntities.stream()
                            .map(HdsInvitationCodeEntity::getInvitationCode)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    // 查询用户线索，然后查询关联的酒店线索
                    Mono<Map<String, Integer>> invitationCodeCountMapMono = invitationCodes.isEmpty()
                            ? Mono.just(Collections.emptyMap())
                            : userLeadRepository.findByInviteCodeInAndStatus(invitationCodes, 2)
                            .collectList()
                            .defaultIfEmpty(Collections.emptyList())
                            .flatMap(userLeads -> {
                                if (userLeads.isEmpty()) {
                                    return Mono.just(Collections.emptyMap());
                                }

                                // 获取用户线索ID列表
                                List<Long> userLeadIds = userLeads.stream()
                                        .map(HdsUserLeadEntity::getId)
                                        .collect(Collectors.toList());

                                // 查询用户-酒店线索映射关系
                                return userHotelLeadMappingRepository.findByUserLeadIdIn(userLeadIds)
                                        .collectList()
                                        .defaultIfEmpty(Collections.emptyList())
                                        .flatMap(mappings -> {
                                            if (mappings.isEmpty()) {
                                                return Mono.just(Collections.emptyMap());
                                            }

                                            // 创建用户线索ID到邀请码的映射
                                            Map<Long, String> userLeadToInviteCode = userLeads.stream()
                                                    .collect(Collectors.toMap(
                                                            HdsUserLeadEntity::getId,
                                                            HdsUserLeadEntity::getInviteCode,
                                                            (e1, e2) -> e1
                                                    ));

                                            // 创建邀请码到酒店数量的映射
                                            Map<String, Set<Long>> inviteCodeToHotelLeadIds = new HashMap<>();

                                            for (HdsUserHotelLeadMappingEntity mapping : mappings) {
                                                Long userLeadId = mapping.getUserLeadId();
                                                Long hotelLeadId = mapping.getHotelLeadId();
                                                String inviteCode = userLeadToInviteCode.get(userLeadId);

                                                if (StringUtils.isNotBlank(inviteCode)) {
                                                    inviteCodeToHotelLeadIds
                                                            .computeIfAbsent(inviteCode, k -> new HashSet<>())
                                                            .add(hotelLeadId);
                                                }
                                            }

                                            // 计算每个邀请码关联的不同酒店数量
                                            Map<String, Integer> invitationCodeHotelCountMap = new HashMap<>();
                                            inviteCodeToHotelLeadIds.forEach((inviteCode, hotelLeadIds) ->
                                                    invitationCodeHotelCountMap.put(inviteCode, hotelLeadIds.size()));

                                            return Mono.just(invitationCodeHotelCountMap);
                                        });
                            });

                    Mono<List<HdsHotelInfoEntity>> hotelsMono = hotelCodes.isEmpty()
                            ? Mono.just(Collections.emptyList())
                            : hotelRepository.findByHotelCodeIn(hotelCodes)
                            .collectList()
                            .defaultIfEmpty(Collections.emptyList());

                    return Mono.zip(invitationCodeCountMapMono, hotelsMono)
                            .flatMap(tuple -> {
                                Map<String, Integer> invitationCodeHotelCountMap = tuple.getT1();
                                List<HdsHotelInfoEntity> hotels = tuple.getT2();

                                // 构建酒店代码到酒店名称的映射
                                Map<String, String> hotelMap = hotels.stream()
                                        .collect(Collectors.toMap(
                                                HdsHotelInfoEntity::getHotelCode,
                                                HdsHotelInfoEntity::getHotelName,
                                                (existing, replacement) -> existing
                                        ));

                                // 为每个邀请码实体设置对应的酒店名称和邀请门店数量，并转换为VO
                                List<Mono<InvitationCodeSearchVO>> voMonos = hdsInvitationCodeEntities.stream()
                                        .map(entity -> {
                                            String invitationCode = entity.getInvitationCode();
                                            // 获取对应邀请码的门店数量，不存在则为0
                                            Integer hotelCount = invitationCodeHotelCountMap.getOrDefault(invitationCode, 0);
                                            // 转换为VO并设置门店数量
                                            return toVo(entity, hotelMap.get(entity.getHotelCode()))
                                                    .map(vo -> {
                                                        vo.setInvitationCount(hotelCount);
                                                        return vo;
                                                    });
                                        })
                                        .collect(Collectors.toList());

                                return Flux.fromIterable(voMonos)
                                        .concatMap(mono -> mono)
                                        .collectList();
                            });
                });
    }

    /**
     * 将实体对象转换为搜索结果视图对象
     */
    public Mono<InvitationCodeSearchVO> toVo(HdsInvitationCodeEntity entity, String hotelName) {
        if (entity == null) {
            return Mono.empty();
        }

        InvitationCodeSearchVO vo = new InvitationCodeSearchVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setCreateTime(SimpleDateUtils.formatLocalDateTimeToDateHour(entity.getCreatedAt()));
        vo.setHotelName(hotelName);

        return userLeadRepository.findByInviteCodeInAndStatus(Collections.singleton(entity.getInvitationCode()), 2)
                .collectList()
                .defaultIfEmpty(Collections.emptyList())
                .flatMap(hdsUserLeadEntities -> {
                    if (hdsUserLeadEntities.isEmpty()) {
                        vo.setInvitationCount(0);
                        return Mono.just(vo);
                    }

                    Set<Long> userLeadIds = hdsUserLeadEntities.stream()
                            .map(HdsUserLeadEntity::getId)
                            .collect(Collectors.toSet());

                    return userHotelLeadMappingRepository.findByUserLeadIdIn(userLeadIds)
                            .collectList()
                            .defaultIfEmpty(Collections.emptyList())
                            .map(mappings -> {
                                vo.setInvitationCount(mappings.size());
                                return vo;
                            });
                })
                .onErrorResume(e -> {
                    log.warn("获取邀请码VO时发生错误: {}", e.getMessage(), e);
                    vo.setInvitationCount(0);
                    return Mono.just(vo);
                });
    }

    /**
     * 构建搜索条件
     */
    private Criteria buildSearchCriteria(InvitationCodeSearchReq req) {
        // 基础条件：row_status = 1
        Criteria criteria = Criteria.where(HdsInvitationCodeFieldEnum.row_status.name()).is(1);

        // 根据请求参数添加查询条件
        if (StringUtils.isNotBlank(req.getInvitationCode())) {
            criteria = criteria.and(HdsInvitationCodeFieldEnum.invitation_code.name())
                    .like("%" + req.getInvitationCode() + "%");
        }

        if (StringUtils.isNotBlank(req.getName())) {
            criteria = criteria.and(HdsInvitationCodeFieldEnum.name.name())
                    .like("%" + req.getName() + "%");
        }
        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(HdsInvitationCodeFieldEnum.hotel_code.name())
                    .is(req.getHotelCode());
        }

        if (Objects.nonNull(req.getStatus())) {
            criteria = criteria.and(HdsInvitationCodeFieldEnum.status.name())
                    .is(req.getStatus());
        }

        return criteria;
    }

    /**
     * 根据ID查询邀请码详情
     */
    public Mono<InvitationCodeVO> findById(Integer id) {
        // 参数验证
        Preconditions.checkArgument(Objects.nonNull(id), "邀请码ID不能为空");
        return invitationCodeRepository.findById(id)
                .flatMap(entity -> userLeadRepository.findByInviteCodeInAndStatus(Collections.singleton(entity.getInvitationCode()), 2)
                        .collectList()
                        .defaultIfEmpty(Collections.emptyList())
                        .map(leads -> {

                            // 计算邀请门店数量（不同的酒店代码）
                            int invitedHotelCount = leads.size();

                            InvitationCodeVO vo = InvitationCodeVO.toVo(entity);
                            vo.setInvitationCount(invitedHotelCount);

                            return vo;
                        }))
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "邀请码不存在")));
    }

    /**
     * 统计邀请码数量
     */
    public Mono<Long> logSearchCount(HeaderUtils.HeaderInfo headerInfo, InvitationCodeLogSearchReq req) {
        return operationLogService.searchCount(headerInfo, buildOperationLogSearchReq(req));
    }

    /**
     * 搜索邀请码列表
     */
    /**
     * 搜索邀请码日志列表
     */
    public Mono<List<InvitationCodeLogSearchVO>> logSearch(HeaderUtils.HeaderInfo headerInfo, InvitationCodeLogSearchReq req) {
        return operationLogService.search(headerInfo, buildOperationLogSearchReq(req))
                .flatMap(operationLogVos -> {
                    if (operationLogVos.isEmpty()) {
                        return Mono.just(Collections.emptyList());
                    }

                    // 提取业务ID列表
                    List<Integer> businessIds = operationLogVos.stream()
                            .map(logSearchVO -> Math.toIntExact(logSearchVO.getBusinessId()))
                            .collect(Collectors.toList());

                    // 查询邀请码实体
                    return invitationCodeRepository.findByIdIn(businessIds)
                            .collectList()
                            .flatMap(invitationCodes -> {
                                if (invitationCodes.isEmpty()) {
                                    return Mono.just(Collections.emptyList());
                                }

                                // 创建ID到实体的映射
                                Map<Integer, HdsInvitationCodeEntity> idToEntityMap = invitationCodes.stream()
                                        .collect(Collectors.toMap(
                                                HdsInvitationCodeEntity::getId,
                                                entity -> entity,
                                                (e1, e2) -> e1
                                        ));

                                // 提取酒店代码和员工ID集合
                                Set<String> hotelCodes = invitationCodes.stream()
                                        .map(HdsInvitationCodeEntity::getHotelCode)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toSet());

                                Set<Integer> employeeIds = invitationCodes.stream()
                                        .map(HdsInvitationCodeEntity::getCreatedBy)
                                        .filter(StringUtils::isNotBlank)
                                        .map(str -> {
                                            try {
                                                return Integer.parseInt(str);
                                            } catch (NumberFormatException e) {
                                                return null;
                                            }
                                        })
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toSet());

                                // 并行查询酒店和员工信息
                                Mono<Map<String, String>> hotelMapMono = hotelCodes.isEmpty()
                                        ? Mono.just(Collections.emptyMap())
                                        : hotelRepository.findByHotelCodeIn(hotelCodes)
                                        .collectList()
                                        .map(hotels -> hotels.stream()
                                                .filter(hotel -> hotel.getHotelCode() != null) // 过滤null键
                                                .collect(Collectors.toMap(
                                                        HdsHotelInfoEntity::getHotelCode,
                                                        hotel -> hotel.getHotelName() != null ? hotel.getHotelName() : "",
                                                        (e1, e2) -> e1
                                                )));

                                Mono<Map<Integer, HdsEmployeeEntity>> employeeMapMono = employeeIds.isEmpty()
                                        ? Mono.just(Collections.emptyMap())
                                        : hdsEmployeeRepository.findByIdIn(employeeIds)
                                        .collectList()
                                        .map(employees -> employees.stream()
                                                .collect(Collectors.toMap(
                                                        HdsEmployeeEntity::getId,
                                                        hdsEmployeeEntity -> hdsEmployeeEntity,
                                                        (e1, e2) -> e1
                                                )));

                                // 组合查询结果并构建最终 VO 列表
                                return Mono.zip(hotelMapMono, employeeMapMono)
                                        .map(tuple -> {
                                            Map<String, String> hotelMap = tuple.getT1();
                                            Map<Integer, HdsEmployeeEntity> employeeMap = tuple.getT2();

                                            // 构建最终结果
                                            return operationLogVos.stream()
                                                    .map(logVO -> {
                                                        Integer businessId = Math.toIntExact(logVO.getBusinessId());
                                                        HdsInvitationCodeEntity entity = idToEntityMap.get(businessId);


                                                        // 构建日志VO对象
                                                        InvitationCodeLogSearchVO vo = new InvitationCodeLogSearchVO()
                                                                .setId(logVO.getId())
                                                                .setOprDesc(logVO.getOprDesc())
                                                                .setOprTime(logVO.getOprTime());

                                                        if (Objects.nonNull(entity)) {
                                                            vo.setInvitationCode(entity.getInvitationCode());

                                                            if (StringUtils.isNotBlank(entity.getHotelCode())) {
                                                                vo.setHotelName(hotelMap.getOrDefault(entity.getHotelCode(), ""));
                                                            }

                                                            // 设置邀请人
                                                            if (StringUtils.isNotBlank(entity.getCreatedBy())) {
                                                                try {
                                                                    Integer createdById = Integer.parseInt(entity.getCreatedBy());
                                                                    vo.setCreatedBy(hdsEmployeeService.generateCreatedByInfo(employeeMap.get(createdById)));
                                                                } catch (NumberFormatException e) {
                                                                    vo.setCreatedBy(entity.getCreatedByName());
                                                                }
                                                            }
                                                        }
                                                        return vo;
                                                    })
                                                    .collect(Collectors.toList());
                                        });
                            });
                });
    }

    private OperationLogSearchReq buildOperationLogSearchReq(InvitationCodeLogSearchReq req) {
        OperationLogSearchReq operationLogSearchReq = new OperationLogSearchReq();

        if (Objects.nonNull(req.getId())) {
            operationLogSearchReq.setBusinessId(req.getId());
        }

        operationLogSearchReq.setBusinessType(BussinessTypeEnum.INVITATION_CODE.getBusinessType());
        operationLogSearchReq.setCurrent(req.getCurrent());
        operationLogSearchReq.setPageSize(req.getPageSize());
        return operationLogSearchReq;
    }

    public Mono<InvitationCodeVO> findByHotelCode(String hotelCode) {
        Preconditions.checkArgument(org.apache.commons.lang.StringUtils.isNotEmpty(hotelCode), "hotelCode must not be null");
        return invitationCodeRepository.findByHotelCode(hotelCode)
                .map(InvitationCodeVO::toVo)
                .switchIfEmpty(Mono.just(new InvitationCodeVO()));
    }
}
