package com.wormhole.hotelds.admin.service.strategy;

import com.wormhole.common.config.WormholeTranslationConfig;
import com.wormhole.common.enums.*;
import com.wormhole.common.exception.*;
import com.wormhole.common.result.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.model.req.GetAuthInfoReq;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.*;
import com.wormhole.hotelds.constant.*;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.*;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: huangweijie
 * @date: 2025/5/15
 * @Description: 酒店来源认证策略实现
 */
@Component
@Slf4j
public class HotelAuthStrategy implements AuthSourceStrategy<HotelAuthVO> {

    @Resource
    private HdsEmployeeTicketMappingService hdsEmployeeTicketMappingService;

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private DevicePositionService devicePositionService;

    @Resource
    private WormholeTranslationConfig translationConfig;

    @Override
    public Mono<HotelAuthVO> processAuthInfo(GetAuthInfoReq getAuthInfoReq,AuthVO authVO, HeaderUtils.HeaderInfo headerInfo) {
        if (StringUtils.isEmpty(headerInfo.getHotelCode())){
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "酒店编码不能为空"));
        }

        HotelAuthVO hotelAuthVO = new HotelAuthVO();
        BeanUtils.copyProperties(authVO, hotelAuthVO);
        return hdsEmployeeTicketMappingService.findByEmployeeIdAndHotelCode(hotelAuthVO.getId(), headerInfo.getHotelCode())
                .flatMap(ticketInfo -> {
                    hotelAuthVO.setAccountType(ticketInfo.getEmployeeType());
                    hotelAuthVO.setTicketAssignmentFlag(ticketInfo.getTicketAssignmentFlag());
                    hotelAuthVO.setAcceptTicketStatus(ticketInfo.getAcceptTicketStatus());
                    return enrichWithEmployeeHotels(hotelAuthVO, headerInfo, getAuthInfoReq.getDeviceType(),getAuthInfoReq.getDeviceId());
                })
                .switchIfEmpty(Mono.just(hotelAuthVO));
    }

    private Mono<HotelAuthVO> enrichWithEmployeeHotels(HotelAuthVO hotelAuthVO, HeaderUtils.HeaderInfo headerInfo, String deviceType, String deviceId) {
        return hdsEmployeeHotelService.listByEmployeeId(hotelAuthVO.getId(),
                        EmployeeStatusEnum.ACTIVE.getCode(), List.of(headerInfo.getHotelCode()))
                .flatMap(employeeHotels -> {
                    updateAuthVOWithRole(hotelAuthVO, employeeHotels,headerInfo.getLanguage());;
                    // 前台app直接放回,无需设备信息
                    if (DeviceTypeEnum.FRONT_APP.getCode().equals(deviceType)) {
                        return  Mono.just(hotelAuthVO);
                    }
                    return enrichWithDeviceInfo(hotelAuthVO, headerInfo,deviceId,deviceType);
                });
    }

    private Mono<HotelAuthVO> enrichWithDeviceInfo(HotelAuthVO hotelAuthVO, HeaderUtils.HeaderInfo headerInfo, String deviceId, String deviceType) {
        return deviceService.getDeviceInfo(headerInfo.getHotelCode(), hotelAuthVO.getId().toString(),deviceId,deviceType)
                .flatMap(positions -> {
                    log.info("positions {}",JacksonUtils.writeValueAsString(positions));
                    if (CollectionUtils.isEmpty(positions)){
                        return Mono.just(hotelAuthVO);
                    }
                    HdsDeviceEntity position = positions.get(0);
                    return devicePositionService.findByPositionCode(position.getPositionCode())
                            .map(devicePosition->{
                                hotelAuthVO.setPositionCode(devicePosition.getPositionCode());
                                hotelAuthVO.setPositionName(devicePosition.getPositionName());
                                return hotelAuthVO;
                            });
                });
    }

    private void updateAuthVOWithRole(HotelAuthVO hotelAuthVO, List<HdsEmployeeHotelEntity> employeeHotels, String language) {
        Optional.ofNullable(employeeHotels)
                .filter(CollectionUtils::isNotEmpty)
                .map(hotels -> hotels.get(0))
                .ifPresent(hotel -> {
//                    hotelAuthVO.setRoleCode(hotel.getRoleCode());

                    List<String> roleCodeList = Optional.ofNullable(hotel.getRoleCode())
                            .filter(StringUtils::isNotBlank)
                            .map(roleCodesStr -> Arrays.stream(roleCodesStr.split(","))
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList()))
                            .orElse(Collections.emptyList());
                    hotelAuthVO.setRoleCodes(roleCodeList);
                    hotelAuthVO.setRoleName(translationConfig.translateSync(RoleEnum.fromCode(roleCodeList.get(0)).getCode(),language));
                });
    }

    @Override
    public String getSourceCode() {
        return SourcePlatform.HOTEL.getCode();
    }
}