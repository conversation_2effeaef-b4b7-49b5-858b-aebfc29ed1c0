package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.relational.core.mapping.Column;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/16
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsHotelMappingSaveReq implements Serializable {
    private String hotelCode;
    private String hotelName;
    private String externalId;
    private String externalName;
    private String channel;
    private String platform;
}
