package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsWechatQrCodeEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/21 10:19
 */
public interface HdsWechatQrCodeRepository extends ReactiveCrudRepository<HdsWechatQrCodeEntity, Long> {
    /**
     * 根据酒店编码查询
     *
     * @param hotelCode 酒店编码
     * @return HdsWechatQrcodeEntity
     */
    Mono<HdsWechatQrCodeEntity> findByHotelCodeAndPositionCode(String hotelCode, String positionCode);


    Flux<HdsWechatQrCodeEntity> findByIdIn(List<String> idList);
}
