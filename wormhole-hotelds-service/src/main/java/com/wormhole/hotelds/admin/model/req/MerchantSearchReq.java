package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/3/26 11:02
 * @Description：商户搜索
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MerchantSearchReq extends QueryCondition {

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户类型(1:酒店商户 2:服务商商户)
     */
    private Integer type;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 商户状态
     */
    private Integer status;

    /**
     * 联系人
     */
    private String merchantContacts;

    /**
     * 联系电话
     */
    private String merchantPhone;
}
