package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/17 10:46
 * @Description：HotelPackageDetailVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelPackageDetailVO {

    /**
     * 门店套餐ID
     */
    private Integer id;

    /**
     * 主套餐id
     */
    private Integer packageId;

    /**
     * 门店套餐编码
     */
    private String hotelPackageCode;

    /**
     * 状态：1-启用，0-停用
     */
    private Integer status;

    /**
     * 门店信息
     */
    private HotelVO hotelVo;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 付费方式：0-按房间数量收费，1-按门店收费
     */
    private Integer paymentMode;

    /**
     * 优惠方式：0-折扣，1-一口价
     */
    private Integer discountMode;

    /**
     * 调价原因
     */
    private String adjustPriceReason;

    /**
     * 产品列表
     */
    private List<HotelPackageProductDetailVO> products;

    /**
     * 门店信息VO
     */
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelVO {

        /**
         * 门店ID
         */
        private Integer id;

        /**
         * 门店名称
         */
        private String hotelName;

        /**
         * 门店编码
         */
        private String hotelCode;

        /**
         * 房间数量
         */
        private Integer roomCount;
    }

    /**
     * 门店套餐产品详情VO
     */
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelPackageProductDetailVO {

        /**
         * 产品ID
         */
        private Integer id;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * 排序
         */
        private Integer sortOrder;

        /**
         * 产品描述
         */
        private List<String> productDescription;

        /**
         * 定价信息
         */
        private List<HotelProductPricingDetailVO> pricing;
    }

    /**
     * 门店产品定价详情VO
     */
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelProductPricingDetailVO {

        private Integer id;

        /**
         * 周期类型：1-月度，2-季度，3-年度
         */
        private Integer periodType;

        /**
         * 门市价
         */
        private String marketPrice;

        /**
         * 优惠价
         */
        private String discountPrice;

        /**
         * 折扣比例
         */
        private String discountRate;

        /**
         * 最终价格
         */
        private String finalPrice;
    }
}
