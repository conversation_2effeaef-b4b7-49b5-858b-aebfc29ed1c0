package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

import java.io.*;

/**
 * @Author: flx
 * @Date: 2025-03-27 18:00:26
 * @Description: HdsRefundRecord
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsRefundRecordDeleteReq implements Serializable {
    
    private static final long serialVersionUID = 1L;

    private Long id;

}