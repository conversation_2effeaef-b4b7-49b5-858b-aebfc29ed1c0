package com.wormhole.hotelds.admin.model.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.bot.ObjectInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/2/28
 * @Description:
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserInfoResp implements Serializable {

    private String userId;

    private String username;

    private String nickname;

    private ObjectInfo imageInfo;

}
