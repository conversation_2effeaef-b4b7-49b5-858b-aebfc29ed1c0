package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.model.entity.BaseEntity;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.dto.LeadProgressDTO;
import com.wormhole.hotelds.admin.model.enums.AgentFlowTypeEnum;
import com.wormhole.hotelds.admin.model.enums.AiProductTypeEnum;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.req.HotelLeadSaveReq;
import com.wormhole.hotelds.admin.model.req.HotelLeadSearchReq;
import com.wormhole.hotelds.admin.model.req.HotelSaveReq;
import com.wormhole.hotelds.admin.model.vo.HotelLeadSearchVO;
import com.wormhole.hotelds.admin.model.vo.HotelLeadVO;
import com.wormhole.hotelds.admin.repository.*;
import com.wormhole.hotelds.constant.RoleEnum;
import com.wormhole.hotelds.core.enums.EmployeeStatusEnum;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/5/22 14:27
 * @Description：HotelLeadService
 */
@Slf4j
@Service
public class HotelLeadService {

    @Resource
    private HotelLeadRepository hotelLeadRepository;

    @Resource
    private UserLeadRepository userLeadRepository;

    @Resource
    private HdsEmployeeHotelRepository hdsEmployeeHotelRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private HotelRepository hotelRepository;

    @Resource
    private AgentTimeFlowRepository agentTimeFlowRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private UserHotelLeadMappingRepository userHotelLeadMappingRepository;

    /**
     * 更新门店线索
     *
     * @param hotelLeadSaveReq 门店线索更新请求
     * @return 更新结果
     */
    public Mono<Boolean> update(HotelLeadSaveReq hotelLeadSaveReq) {
        Preconditions.checkArgument(StringUtils.isNotBlank(hotelLeadSaveReq.getHotelName()), "门店名称不能为空");
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 1. 查询门店线索
                    return hotelLeadRepository.findByHotelCode(hotelLeadSaveReq.getHotelCode())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "门店线索不存在")))
                            .flatMap(hotelLeadEntity -> {
                                // 2. 查询用户线索
                                return userLeadRepository.findByInviteeId(Integer.valueOf(headerInfo.getUserId()))
                                        .switchIfEmpty(Mono.just(new HdsUserLeadEntity()))
                                        .flatMap(userLeadEntity -> {
                                            // 3. 执行事务操作
                                            return executeAllInTransaction(hotelLeadEntity, userLeadEntity,
                                                    hotelLeadSaveReq, headerInfo);
                                        });
                            });
                });
    }

    /**
     * 在单一事务中执行所有操作
     */
    private Mono<Boolean> executeAllInTransaction(
            HdsHotelLeadEntity hotelLeadEntity,
            HdsUserLeadEntity userLeadEntity,
            HotelLeadSaveReq hotelLeadSaveReq,
            HeaderUtils.HeaderInfo headerInfo) {

        return Mono.defer(() -> {
                    try {
                        // 1. 检查当前任务状态
                        boolean currentlyCompleted = checkAllTasksCompleted(hotelLeadEntity.getProgressJson());

                        // 2. 解析并更新进度JSON
                        LeadProgressDTO leadProgressDTO = parseProgressJson(hotelLeadEntity.getProgressJson());
                        String updatedJson = updateProgressJson(leadProgressDTO, hotelLeadSaveReq);

                        // 3. 检查更新后的任务状态
                        boolean nowCompleted = checkAllTasksCompleted(updatedJson);

                        // 4. 检测状态变化
                        boolean becameCompleted = !currentlyCompleted && nowCompleted;

                        // 5. 更新门店线索实体
                        Mono<HdsHotelLeadEntity> hotelLeadEntityMono = updateHotelLeadEntity(hotelLeadEntity, updatedJson, headerInfo, nowCompleted);

                        // 6. 根据不同情况构建操作链
                        if (currentlyCompleted) {
                            // 如果当前已完成，创建员工-酒店关联（OTA_AGENT_STAFF角色）
                            Mono<HdsEmployeeHotelEntity> employeeHotelEntityMono = updateOrCreateEmployeeHotelEntity(
                                    headerInfo, hotelLeadEntity, RoleEnum.OTA_AGENT_STAFF.getCode());

                            return Flux.concat(hotelLeadEntityMono, employeeHotelEntityMono)
                                    .then(Mono.just(true));
                        } else {
                            // 基础操作：更新门店线索
                            Flux<Object> operations = Flux.just(hotelLeadEntityMono).flatMap(mono -> mono);

                            // 更新用户线索状态
                            if (userLeadEntity.getId() != null) {
                                Mono<HdsUserLeadEntity> userLeadEntityMono = updateUserLeadStatus(userLeadEntity, headerInfo, nowCompleted);
                                operations = Flux.concat(operations, userLeadEntityMono);
                            }

                            // 如果任务状态变为已完成，处理完成相关操作
                            if (becameCompleted) {
                                // 创建员工-酒店关联（OTA_AGENT_ADMIN角色）
                                Mono<HdsEmployeeHotelEntity> employeeHotelEntityMono = updateOrCreateEmployeeHotelEntity(headerInfo, hotelLeadEntity, RoleEnum.OTA_AGENT_ADMIN.getCode());
                                operations = Flux.concat(operations, employeeHotelEntityMono);

                                // 更新其他员工-门店关联（移除了 saveOperations 参数）
                                Mono<List<HdsEmployeeHotelEntity>> updateExistEmployeeHotelEntity =
                                        updateExistEmployeeHotelEntity(headerInfo, hotelLeadSaveReq, RoleEnum.OTA_AGENT_STAFF.getCode());
                                operations = Flux.concat(operations, updateExistEmployeeHotelEntity.flatMapMany(Flux::fromIterable));

                                // 奖励逻辑
                                Mono<Void> rewardMono = hotelRepository.findByHotelCode(hotelLeadEntity.getHotelCode())
                                        .flatMap(hotel -> {
                                            if (Objects.equals(hotel.getSource(), 1)) {
                                                // 给被邀请人所在门店赠送OTA一个月
                                                Mono<HdsAgentTimeFlowEntity> targetHotelRewardAndFlow = rewardTargetHotelOtaRewardMonths(hotelLeadEntity.getHotelCode(), headerInfo, employeeHotelEntityMono)
                                                        .flatMap(hdsHotelInfoEntity -> createOTATimeFlowEntity(headerInfo, hdsHotelInfoEntity, AgentFlowTypeEnum.INITIAL_GIFT.getCode()));

                                                Flux<HdsAgentTimeFlowEntity> rewardOperations = Flux.just(targetHotelRewardAndFlow).flatMap(mono -> mono);

                                                // 给邀请人所在门店奖励OTA一个月
                                                if (userLeadEntity.getId() != null) {
                                                    Mono<HdsAgentTimeFlowEntity> inviterHotelRewardAndFlow = rewardInviterHotelOtaRewardMonths(userLeadEntity, headerInfo)
                                                            .flatMap(inviterHotel -> createOTATimeFlowEntity(headerInfo, inviterHotel, AgentFlowTypeEnum.INVITE_REWARD.getCode()));
                                                    rewardOperations = Flux.concat(rewardOperations, inviterHotelRewardAndFlow);
                                                }

                                                return rewardOperations.then();
                                            }
                                            return Mono.empty();
                                        })
                                        .then();

                                operations = Flux.concat(operations, rewardMono);
                            }

                            return operations.then(Mono.just(true));
                        }
                    } catch (Exception e) {
                        log.error("更新门店线索事务准备失败: hotelName={}, error={}",
                                hotelLeadEntity.getHotelName(), e.getMessage(), e);
                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                "更新门店线索失败: " + e.getMessage()));
                    }
                })
                .as(transactionalOperator::transactional)
                .doOnSuccess(result -> {
                    log.info("门店线索更新成功: hotelName={}", hotelLeadEntity.getHotelName());
                })
                .doOnError(e -> log.error("门店线索更新事务执行失败: hotelName={}, error={}",
                        hotelLeadEntity.getHotelName(), e.getMessage(), e));
    }

    /**
     * 更新员工与酒店的关联关系
     *
     * @param hotelLeadSaveReq 酒店线索保存请求
     * @return 更新后的员工-酒店关联实体列表
     */
    private Mono<List<HdsEmployeeHotelEntity>> updateExistEmployeeHotelEntity(HeaderUtils.HeaderInfo headerInfo,
                                                                              HotelLeadSaveReq hotelLeadSaveReq,
                                                                              String roleCode) {
        return hotelLeadRepository.findByHotelCode(hotelLeadSaveReq.getHotelCode())
                .flatMap(hdsHotelLeadEntity -> {
                    // 1. 查找与酒店线索相关的用户-酒店线索映射
                    return userHotelLeadMappingRepository.findByHotelLeadId(hdsHotelLeadEntity.getId())
                            .collectList()
                            .flatMap(userHotelLeadMappingEntities -> {
                                // 如果没有映射关系，返回空列表
                                if (userHotelLeadMappingEntities.isEmpty()) {
                                    return Mono.just(Collections.<HdsEmployeeHotelEntity>emptyList());
                                }

                                // 2. 提取用户线索ID集合
                                Set<Long> userLeadIds = userHotelLeadMappingEntities.stream()
                                        .map(HdsUserHotelLeadMappingEntity::getUserLeadId)
                                        .collect(Collectors.toSet());

                                // 3. 查询用户线索实体
                                return userLeadRepository.findAllById(userLeadIds)
                                        .collectList()
                                        .flatMap(userLeadEntities -> {
                                            // 如果没有用户线索，返回空列表
                                            if (userLeadEntities.isEmpty()) {
                                                return Mono.just(Collections.<HdsEmployeeHotelEntity>emptyList());
                                            }

                                            // 4. 提取被邀请人ID集合
                                            Set<Integer> inviteeIds = userLeadEntities.stream()
                                                    .map(HdsUserLeadEntity::getInviteeId)
                                                    .filter(Objects::nonNull)
                                                    .collect(Collectors.toSet());

                                            // 如果没有被邀请人，返回空列表
                                            if (inviteeIds.isEmpty()) {
                                                return Mono.just(Collections.<HdsEmployeeHotelEntity>emptyList());
                                            }

                                            // 5. 查询员工实体
                                            return hdsEmployeeRepository.findAllById(inviteeIds)
                                                    .collectList()
                                                    .flatMap(employeeEntities -> {
                                                        // 6. 为每个员工创建或更新与酒店的关联
                                                        List<HdsEmployeeHotelEntity> employeeHotelEntities = new ArrayList<>();

                                                        for (HdsEmployeeEntity employee : employeeEntities) {
                                                            String userId = headerInfo.getUserId();
                                                            if (Objects.equals(Integer.valueOf(userId), employee.getId())) {
                                                                continue;
                                                            }
                                                            // 使用员工ID创建关联实体
                                                            HdsEmployeeHotelEntity entity = createNewEmployeeHotelEntity(
                                                                    headerInfo,
                                                                    employee.getId(), // 传入员工ID
                                                                    hotelLeadSaveReq.getHotelCode(),
                                                                    roleCode
                                                            );
                                                            employeeHotelEntities.add(entity);
                                                        }

                                                        // 如果没有员工-酒店关联实体，返回空列表
                                                        if (employeeHotelEntities.isEmpty()) {
                                                            return Mono.just(Collections.<HdsEmployeeHotelEntity>emptyList());
                                                        }

                                                        // 7. 批量保存员工-酒店关联
                                                        // 7. 批量保存员工-酒店关联
                                                        return hdsEmployeeHotelRepository.saveAll(employeeHotelEntities)
                                                                .collectList();
                                                    });
                                        });
                            });
                })
                .defaultIfEmpty(Collections.emptyList());
    }

    /**
     * 创建员工-酒店关联实体
     */
    private Mono<HdsEmployeeHotelEntity> updateOrCreateEmployeeHotelEntity(
            HeaderUtils.HeaderInfo headerInfo,
            HdsHotelLeadEntity hotelLeadEntity,
            String roleCode) {
        return hdsEmployeeHotelRepository.findByEmployeeIdAndHotelCode(Integer.valueOf(headerInfo.getUserId()), hotelLeadEntity.getHotelCode())
                .flatMap(employeeHotelEntity -> {
                    Set<String> roleCodeList = new HashSet<>(Optional.ofNullable(employeeHotelEntity.getRoleCode())
                            .filter(StringUtils::isNotBlank)
                            .map(roleCodesStr -> Arrays.stream(roleCodesStr.split(","))
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toSet()))
                            .orElse(Collections.emptySet()));
                    // 如果是角色是OTA_AGENT_ADMIN，并且存在的记录不包含OTA_AGENT_ADMIN，则添加
                    if (StringUtils.equals(roleCode, RoleEnum.OTA_AGENT_ADMIN.getCode()) && !roleCodeList.contains(RoleEnum.OTA_AGENT_ADMIN.getCode())) {
                        roleCodeList.add(roleCode);
                        employeeHotelEntity.setRoleCode(String.join(",", roleCodeList));
                        return hdsEmployeeHotelRepository.save(employeeHotelEntity);
                    } else {
                        return Mono.just(employeeHotelEntity);
                    }
                }).switchIfEmpty(saveEmployeeHotelEntity(headerInfo, Integer.parseInt(headerInfo.getUserId()), hotelLeadEntity.getHotelCode(), roleCode));

    }

    private Mono<HdsEmployeeHotelEntity> saveEmployeeHotelEntity(HeaderUtils.HeaderInfo headerInfo,
                                                                 Integer employeeId,
                                                                 String hotelCode,
                                                                 String roleCode) {
        HdsEmployeeHotelEntity newEmployeeHotelEntity = createNewEmployeeHotelEntity(headerInfo, employeeId, hotelCode, roleCode);
        return hdsEmployeeHotelRepository.save(newEmployeeHotelEntity);
    }

    private HdsEmployeeHotelEntity createNewEmployeeHotelEntity(HeaderUtils.HeaderInfo headerInfo,
                                                                Integer employeeId,
                                                                String hotelCode,
                                                                String roleCode) {
        HdsEmployeeHotelEntity entity = new HdsEmployeeHotelEntity();
        entity.setHotelCode(hotelCode);
        entity.setEmployeeId(employeeId);
        entity.setRoleCode(roleCode);
        entity.setStatus(EmployeeStatusEnum.ACTIVE.getCode());
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setRowStatus(1);
        return entity;
    }

    /**
     * 更新门店线索实体
     */
    private Mono<HdsHotelLeadEntity> updateHotelLeadEntity(
            HdsHotelLeadEntity hotelLeadEntity,
            String updatedJson,
            HeaderUtils.HeaderInfo headerInfo,
            boolean isCompleted) {

        // 设置基本更新信息
        hotelLeadEntity.setProgressJson(updatedJson);

        // 如果任务全部完成，设置完成时间和管理员ID
        if (isCompleted && hotelLeadEntity.getCompleteTime() == null) {
            hotelLeadEntity.setAdminUserId(Integer.valueOf(headerInfo.getUserId()));
            hotelLeadEntity.setCompleteTime(LocalDateTime.now());
            // 设置为已归属状态
            hotelLeadEntity.setStatus(1);
        }
        setAuditField(hotelLeadEntity, headerInfo, false);

        return hotelLeadRepository.save(hotelLeadEntity);
    }

    /**
     * 更新用户线索状态
     */
    private Mono<HdsUserLeadEntity> updateUserLeadStatus(HdsUserLeadEntity userLeadEntity, HeaderUtils.HeaderInfo headerInfo, boolean isCompleted) {
        if (isCompleted) {
            userLeadEntity.setStatus(2); // 已完成
        } else {
            userLeadEntity.setStatus(1); // 发展中
        }
        userLeadEntity.setUpdatedAt(LocalDateTime.now());
        userLeadEntity.setUpdatedBy(headerInfo.getUserId());
        userLeadEntity.setUpdatedByName(headerInfo.getUsername());
        return userLeadRepository.save(userLeadEntity);
    }

    /**
     * 给被邀请人赠送OTA 时间
     *
     * @param hotelCode  门店编码
     * @param headerInfo 请求头信息
     * @return 更新后的酒店实体
     */
    private Mono<HdsHotelInfoEntity> rewardTargetHotelOtaRewardMonths(String hotelCode, HeaderUtils.HeaderInfo headerInfo, Mono<HdsEmployeeHotelEntity> employeeHotelEntityMono) {
        return getHotelEntityByHotelCode(hotelCode)
                .flatMap(hotel -> {
                    hotel.setStatus(1);
                    if (StringUtils.isNotBlank(hotel.getAiProductTypes())) {
                        List<String> aiProductTypes = Arrays.stream(hotel.getAiProductTypes().split(",")).toList();
                        Set<String> aiProductTypesSet = new HashSet<>(aiProductTypes);
                        aiProductTypesSet.add(AiProductTypeEnum.OTA_AGENT.getCode());
                        hotel.setAiProductTypes(String.join(",", aiProductTypesSet));
                    } else {
                        hotel.setAiProductTypes(AiProductTypeEnum.OTA_AGENT.getCode());
                    }
                    hotel.setInitFinishedAt(LocalDateTime.now());

                    return hdsEmployeeRepository.findById(Integer.valueOf(headerInfo.getUserId()))
                            .doOnNext(employee -> {
                                hotel.setPhone(employee.getMobile());
                            })
                            .then(Mono.just(hotel))
                            .defaultIfEmpty(hotel)
                            .flatMap(updatedHotel -> incrementHotelOtaRewardMonths(updatedHotel, headerInfo));
                });
    }

    /**
     * 给邀请人所在的门店发放OTA奖励月份
     *
     * @param userLeadEntity
     * @return 更新后的酒店实体
     */
    private Mono<HdsHotelInfoEntity> rewardInviterHotelOtaRewardMonths(HdsUserLeadEntity userLeadEntity, HeaderUtils.HeaderInfo headerInfo) {
        if (userLeadEntity == null) {
            return null;
        }
        return hotelRepository.findByInvitationCode(userLeadEntity.getInviteCode())
                .switchIfEmpty(Mono.empty())
                .flatMap(hotel -> incrementHotelOtaRewardMonths(hotel, headerInfo));
    }

    /**
     * 创建OTA时间流记录实体
     *
     * @param headerInfo  请求头信息
     * @param hotelEntity 酒店实体
     * @param flowType    流类型 (0:目标酒店, 1:邀请人酒店)
     * @return OTA时间流记录实体
     */
    private Mono<HdsAgentTimeFlowEntity> createOTATimeFlowEntity(
            HeaderUtils.HeaderInfo headerInfo,
            HdsHotelInfoEntity hotelEntity,
            Integer flowType) {

        HdsAgentTimeFlowEntity timeFlowEntity = new HdsAgentTimeFlowEntity();
        // 设置基本信息
        timeFlowEntity.setType(0);
        timeFlowEntity.setHotelCode(hotelEntity.getHotelCode());
        timeFlowEntity.setFlowType(flowType);
        timeFlowEntity.setOperationType(1);
        timeFlowEntity.setMonthsChanged(1);

        // 获取OTA奖励月数的前后值
        Integer rewardMonthsBefore = hotelEntity.getOtaRewardMonths() != null ? hotelEntity.getOtaRewardMonths() - 1 : 0;
        Integer rewardMonthsAfter = hotelEntity.getOtaRewardMonths() != null ? hotelEntity.getOtaRewardMonths() : 1;

        // 获取OTA扩展月数
        Integer extendMonths = hotelEntity.getOtaExtendMonths() != null ? hotelEntity.getOtaExtendMonths() : 0;

        // 设置奖励月数前后值
        timeFlowEntity.setOtaRewardMonthsBefore(rewardMonthsBefore);
        timeFlowEntity.setOtaRewardMonthsAfter(rewardMonthsAfter);

        // 设置扩展月数前后值
        timeFlowEntity.setOtaExtendMonthsBefore(extendMonths);
        timeFlowEntity.setOtaExtendMonthsAfter(extendMonths);

        // 计算总有效月数(奖励月数 + 扩展月数)
        int totalMonthsBefore = rewardMonthsBefore + extendMonths;
        int totalMonthsAfter = rewardMonthsAfter + extendMonths;

        // 计算过期时间
        timeFlowEntity.setExpirationBefore(hotelEntity.getInitFinishedAt() != null ? hotelEntity.getInitFinishedAt().plusMonths(totalMonthsBefore) : null);
        timeFlowEntity.setExpirationAfter(hotelEntity.getInitFinishedAt() != null ? hotelEntity.getInitFinishedAt().plusMonths(totalMonthsAfter) : null);

        // 设置创建信息
        setAuditField(timeFlowEntity, headerInfo, true);

        return agentTimeFlowRepository.save(timeFlowEntity);
    }

    /**
     * 增加酒店OTA奖励月份
     *
     * @param hotel      酒店实体
     * @param headerInfo 请求头信息
     * @return 更新后的酒店实体
     */
    private Mono<HdsHotelInfoEntity> incrementHotelOtaRewardMonths(HdsHotelInfoEntity hotel, HeaderUtils.HeaderInfo headerInfo) {
        // 获取当前月数
        Integer currentMonths = hotel.getOtaRewardMonths();
        // 设置新的月数值(+1或设为1)
        Integer newMonths = Objects.isNull(currentMonths) ? 1 : currentMonths + 1;

        // 设置奖励月数
        hotel.setOtaRewardMonths(newMonths);
        // 设置审计字段
        setAuditField(hotel, headerInfo, false);
        return hotelRepository.save(hotel);
    }

    /**
     * 根据酒店编码获取酒店实体
     *
     * @param hotelCode 酒店编码
     * @return 酒店实体
     */
    private Mono<HdsHotelInfoEntity> getHotelEntityByHotelCode(String hotelCode) {
        return hotelRepository.findByHotelCode(hotelCode)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND,
                        String.format("未找到门店, hotelCode=%s", hotelCode))));
    }

    /**
     * 设置审计字段
     *
     * @param entity     实体对象
     * @param headerInfo 请求头信息
     */
    private void setAuditField(BaseEntity entity, HeaderUtils.HeaderInfo headerInfo, boolean isCreate) {
        if (isCreate) {
            entity.setCreatedAt(LocalDateTime.now());
            entity.setCreatedBy(headerInfo.getUserId());
            entity.setCreatedByName(headerInfo.getUsername());
        } else {
            entity.setUpdatedBy(headerInfo.getUserId());
            entity.setUpdatedByName(headerInfo.getUsername());
            entity.setUpdatedAt(LocalDateTime.now());
        }
    }

    /**
     * 更新进度JSON
     *
     * @param leadProgressDTO 当前线索进度对象
     * @param req             更新请求
     * @return 更新后的JSON字符串
     */
    private String updateProgressJson(LeadProgressDTO leadProgressDTO, HotelLeadSaveReq req) {
        // 当前时间，用于记录完成时间
        String nowStr = SimpleDateUtils.formatLocalDateTimeToDateHour(LocalDateTime.now());

        // 处理确认绑定EBK任务
        if (StringUtils.isBlank(leadProgressDTO.getBindEbk()) && Boolean.TRUE.equals(req.getBindEbk())) {
            leadProgressDTO.setBindEbk(nowStr);
            log.info("任务'确认绑定EBK'标记为完成");
        }

        // 处理设置竞对酒店任务
        if (StringUtils.isBlank(leadProgressDTO.getSetRivalHotel()) && Boolean.TRUE.equals(req.getSetRivalHotel())) {
            leadProgressDTO.setSetRivalHotel(nowStr);
            log.info("任务'设置竞对酒店'标记为完成");
        }

        // 处理同步全量点评任务
        if (StringUtils.isBlank(leadProgressDTO.getSyncReview()) && Boolean.TRUE.equals(req.getSyncReview())) {
            leadProgressDTO.setSyncReview(nowStr);
            log.info("任务'同步全量点评'标记为完成");
        }

        // 处理同步酒店静态信息任务
        if (StringUtils.isBlank(leadProgressDTO.getSyncStaticInfo()) && Boolean.TRUE.equals(req.getSyncStaticInfo())) {
            leadProgressDTO.setSyncStaticInfo(nowStr);
            log.info("任务'同步酒店静态信息'标记为完成");
        }

        // 处理同步竞对评论任务
        if (StringUtils.isBlank(leadProgressDTO.getSyncRivalReview()) && Boolean.TRUE.equals(req.getSyncRivalReview())) {
            leadProgressDTO.setSyncRivalReview(nowStr);
            log.info("任务'同步竞对评论'标记为完成");
        }

        // 处理同步竞对评论任务
        if (StringUtils.isBlank(leadProgressDTO.getSyncRivalPrice()) && Boolean.TRUE.equals(req.getSyncRivalPrice())) {
            leadProgressDTO.setSyncRivalPrice(nowStr);
            log.info("任务'同步竞对价格'标记为完成");
        }

        // 处理生成初始化报告任务
        if (StringUtils.isBlank(leadProgressDTO.getGenerateReport()) && Boolean.TRUE.equals(req.getGenerateReport())) {
            leadProgressDTO.setGenerateReport(nowStr);
            log.info("任务'生成初始化报告'标记为完成");
        }

        return JacksonUtils.writeValueAsString(leadProgressDTO);
    }

    /**
     * 解析进度JSON
     */
    private LeadProgressDTO parseProgressJson(String progressJson) {
        if (StringUtils.isBlank(progressJson)) {
            return new LeadProgressDTO();
        }
        try {
            return JacksonUtils.readValue(progressJson, LeadProgressDTO.class);
        } catch (Exception e) {
            log.error("解析进度JSON失败，将创建新对象: {}", e.getMessage());
            return new LeadProgressDTO();
        }
    }

    /**
     * 检查是否所有任务都已完成
     *
     * @param progressJson 进度JSON字符串
     * @return 是否全部完成
     */
    private boolean checkAllTasksCompleted(String progressJson) {
        if (StringUtils.isBlank(progressJson)) {
            return false;
        }

        try {
            // 解析JSON字符串为LeadProgressDTO对象
            LeadProgressDTO progressDTO = JacksonUtils.readValue(progressJson, LeadProgressDTO.class);

            return StringUtils.isNotBlank(progressDTO.getGenerateReport());
        } catch (Exception e) {
            log.error("解析进度JSON失败: {}", e.getMessage(), e);
            return false;
        }
    }

    public Mono<HotelLeadVO> findByHotelCode(String hotelCode) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(hotelCode), "hotelCode must not be null");
        return hotelLeadRepository.findByHotelCode(hotelCode)
                .map(this::toVo)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "hotel not found")));
    }

    public Mono<List<HdsHotelLeadEntity>> findByCreatedBy(String createBy) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(createBy), "createBy must not be null");
        return hotelLeadRepository.findByCreatedBy((createBy))
                .collectList()
                .defaultIfEmpty(Collections.emptyList());
    }

    private HotelLeadVO toVo(HdsHotelLeadEntity hdsHotelLeadEntity) {
        // 解析进度JSON获取LeadProgressDTO
        LeadProgressDTO leadProgressDTO = parseProgressJson(hdsHotelLeadEntity.getProgressJson());

        // 创建VO对象
        HotelLeadVO hotelLeadVO = new HotelLeadVO();

        hotelLeadVO.setId(hdsHotelLeadEntity.getId());

        // 设置任务完成状态，非空字符串表示已完成(true)，空字符串或null表示未完成(false)
        hotelLeadVO.setBindEbk(StringUtils.isNotBlank(leadProgressDTO.getBindEbk()));
        hotelLeadVO.setSetRivalHotel(StringUtils.isNotBlank(leadProgressDTO.getSetRivalHotel()));
        hotelLeadVO.setSyncReview(StringUtils.isNotBlank(leadProgressDTO.getSyncReview()));
        hotelLeadVO.setSyncStaticInfo(StringUtils.isNotBlank(leadProgressDTO.getSyncStaticInfo()));
        hotelLeadVO.setSyncRivalReview(StringUtils.isNotBlank(leadProgressDTO.getSyncRivalReview()));
        hotelLeadVO.setGenerateReport(StringUtils.isNotBlank(leadProgressDTO.getGenerateReport()));

        return hotelLeadVO;
    }

    /**
     * 统计商户线索数量
     */
    public Mono<Long> searchCount(HotelLeadSearchReq req) {
        return buildSearchCriteria(req).flatMap(criteria -> {
            Query query = Query.query(criteria);
            return r2dbcEntityTemplate.count(query, HdsHotelLeadEntity.class);
        });
    }

    /**
     * 搜索商户线索列表
     */
    public Mono<List<HotelLeadSearchVO>> search(HotelLeadSearchReq req) {
        return buildSearchCriteria(req).flatMap(criteria -> {
            // 设置排序方式，默认按创建时间降序
            Sort sort = Sort.by(Sort.Direction.DESC, HdsUserLeadFieldEnum.created_at.name());


            // 构建查询对象，设置分页参数
            Query query = Query.query(criteria)
                    .sort(sort)
                    .limit(req.getPageSize())
                    .offset((long) (req.getCurrent() - 1) * req.getPageSize());

            // 执行查询
            return r2dbcEntityTemplate.select(query, HdsHotelLeadEntity.class)
                    .collectList()
                    .flatMap(this::enrichHotelLeadEntities);
        });
    }

    /**
     * 构建搜索条件
     */
    private Mono<Criteria> buildSearchCriteria(HotelLeadSearchReq req) {
        // 基础条件
        Criteria baseCriteria = buildBaseCriteria(req);

        return preFilterByContactPhone(req, baseCriteria)
                .flatMap(criteria -> preFilterByUserLeadId(req, criteria));
    }

    private Criteria buildBaseCriteria(HotelLeadSearchReq req) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(req.getHotelName())) {
            criteria = criteria.and(HdsHotelLeadFieldEnum.hotel_name.name()).like("%" + req.getHotelName() + "%");
        }

        return criteria;
    }

    private Mono<Criteria> preFilterByContactPhone(HotelLeadSearchReq req, Criteria baseCriteria) {
        if (StringUtils.isBlank(req.getMobile())) {
            return Mono.just(baseCriteria);
        }
        Criteria employeeCriteria = Criteria.where(HdsEmployeeFieldEnum.mobile.name()).is(req.getMobile());

        return r2dbcEntityTemplate.select(HdsEmployeeEntity.class)
                .matching(Query.query(employeeCriteria))
                .all()
                .map(HdsEmployeeEntity::getId)
                .collectList()
                .map(employeeIds -> {
                    if (CollectionUtils.isEmpty(employeeIds)) {
                        return Criteria.where(HdsHotelLeadFieldEnum.created_by.name()).is("-1");
                    }
                    List<String> employeeIdStrings = employeeIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.toList());
                    // 将酒店编码添加到查询条件中
                    return baseCriteria.and(HdsHotelLeadFieldEnum.created_by.name()).in(employeeIdStrings);
                });
    }

    private Mono<Criteria> preFilterByUserLeadId(HotelLeadSearchReq req, Criteria baseCriteria) {
        if (Objects.isNull(req.getUserLeadId())) {
            return Mono.just(baseCriteria);
        }
        Criteria userLeadCriteria = Criteria.where(HdsUserHotelLeadMappingFieldEnum.user_lead_id.name()).is(req.getUserLeadId());

        return r2dbcEntityTemplate.select(HdsUserHotelLeadMappingEntity.class)
                .matching(Query.query(userLeadCriteria))
                .all()
                .map(HdsUserHotelLeadMappingEntity::getHotelLeadId)
                .collectList()
                .map(hotelLeadIds -> {
                    if (CollectionUtils.isEmpty(hotelLeadIds)) {
                        return Criteria.where(HdsHotelLeadFieldEnum.id.name()).is("-1");
                    }
                    return baseCriteria.and(HdsHotelLeadFieldEnum.id.name()).in(hotelLeadIds);
                });
    }

    /**
     * 丰富用户线索实体，添加关联信息
     */
    private Mono<List<HotelLeadSearchVO>> enrichHotelLeadEntities(List<HdsHotelLeadEntity> entities) {
        // 如果没有查询到数据，直接返回空列表
        if (entities.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }

        // 2. 并行查询关联数据
        Set<Integer> hotelLeadCreateByIds = extractHotelLeadCreateByIds(entities);
        Mono<Map<Integer, HdsEmployeeEntity>> employeeMapMono = fetchEmployeeMap(hotelLeadCreateByIds);

        // 3. 组合数据并构建VO
        return combineDataAndBuildVOs(entities, employeeMapMono);

    }

    /**
     * 提取门店线索创建人id集合
     */
    private Set<Integer> extractHotelLeadCreateByIds(List<HdsHotelLeadEntity> entities) {
        return entities.stream()
                .map(HdsHotelLeadEntity::getCreatedBy)
                .filter(Objects::nonNull)
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
    }

    /**
     * 获取员工信息映射
     */
    private Mono<Map<Integer, HdsEmployeeEntity>> fetchEmployeeMap(Set<Integer> employeeIds) {
        if (employeeIds.isEmpty()) {
            return Mono.just(Collections.emptyMap());
        }

        return hdsEmployeeRepository.findByIdIn(employeeIds)
                .collectList()
                .map(employees -> employees.stream()
                        .collect(Collectors.toMap(
                                HdsEmployeeEntity::getId,
                                employee -> employee,
                                (e1, e2) -> e1
                        )))
                .defaultIfEmpty(Collections.emptyMap())
                .onErrorResume(e -> {
                    log.error("获取员工信息失败", e);
                    return Mono.just(Collections.emptyMap());
                });
    }

    /**
     * 组合数据并构建VO列表
     */
    private Mono<List<HotelLeadSearchVO>> combineDataAndBuildVOs(
            List<HdsHotelLeadEntity> entities,
            Mono<Map<Integer, HdsEmployeeEntity>> employeeMapMono) {

        return employeeMapMono.map(employeeMap -> entities.stream()
                .map(entity -> buildHotelLeadSearchVO(entity, employeeMap))
                .collect(Collectors.toList()));
    }

    /**
     * 构建单个门店线索
     */
    private HotelLeadSearchVO buildHotelLeadSearchVO(
            HdsHotelLeadEntity entity,
            Map<Integer, HdsEmployeeEntity> employeeMap) {

        HotelLeadSearchVO vo = new HotelLeadSearchVO();

        // 复制基本属性
        BeanUtils.copyProperties(entity, vo);

        // 设置员工信息
        setEmployeeInfo(vo, entity, employeeMap);
        LeadProgressDTO leadProgressDTO = parseProgressJson(entity.getProgressJson());

        vo.setBindEbk(StringUtils.isNotBlank(leadProgressDTO.getBindEbk()));
        vo.setSetRivalHotel(StringUtils.isNotBlank(leadProgressDTO.getSetRivalHotel()));
        vo.setSyncReview(StringUtils.isNotBlank(leadProgressDTO.getSyncReview()));
        vo.setSyncStaticInfo(StringUtils.isNotBlank(leadProgressDTO.getSyncStaticInfo()));
        vo.setSyncRivalReview(StringUtils.isNotBlank(leadProgressDTO.getSyncRivalReview()));
        vo.setSyncRivalPrice(StringUtils.isNotBlank(leadProgressDTO.getSyncRivalPrice()));
        vo.setGenerateReport(StringUtils.isNotBlank(leadProgressDTO.getGenerateReport()));

        vo.setCreationDurationFormatted(calculateCreationDuration(entity));

        return vo;
    }

    /**
     * 设置员工信息
     */
    private void setEmployeeInfo(
            HotelLeadSearchVO vo,
            HdsHotelLeadEntity entity,
            Map<Integer, HdsEmployeeEntity> employeeMap) {

        // 设置被邀请人手机号
        if (StringUtils.isNotBlank(entity.getCreatedBy())) {
            HdsEmployeeEntity invitee = employeeMap.get(Integer.parseInt(entity.getCreatedBy()));
            if (invitee != null) {
                vo.setCreateBy(invitee.getMobile());
            }
        }
    }

    /**
     * 计算创建时长
     */
    private String calculateCreationDuration(HdsHotelLeadEntity entity) {
        if (entity.getCreatedAt() != null && entity.getCompleteTime() != null) {
            // 计算创建时长（分钟）
            LocalDateTime startTime = entity.getCreatedAt();
            LocalDateTime endTime = entity.getCompleteTime();

            // 计算两个时间之间的分钟差
            long totalMinutes = Duration.between(startTime, endTime).toMinutes();

            // 格式化为小时，保留一位小数
            return formatDuration(totalMinutes);
        } else {
            return null;
        }
    }

    /**
     * 格式化时长为"X.X小时"，保留一位小数
     */
    private String formatDuration(long totalMinutes) {

        // 将分钟转换为小时（浮点数）
        double hours = totalMinutes / 60.0;

        // 格式化为一位小数
        return String.format("%.1f小时", hours);
    }

    public Mono<HdsHotelLeadEntity> getOrPrepareHotelLead(HotelSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        String hotelCode = req.getHotelCode();
        return hotelLeadRepository.findByHotelCode(hotelCode)
                .flatMap(existingHotelLead -> {
                    if (Objects.equals(existingHotelLead.getStatus(), 1)) {
                        return hdsEmployeeHotelRepository.findByHotelCodeAndRoleCode(hotelCode, RoleEnum.OTA_AGENT_ADMIN.getCode())
                                .collectList()
                                .flatMap(hdsEmployeeHotelEntities -> {
                                    if (CollectionUtils.isEmpty(hdsEmployeeHotelEntities)) {
                                        return Mono.error(new BusinessException("DATA-ALREADY-INIT", "该门店已初始化完成"));
                                    }
                                    HdsEmployeeHotelEntity hdsEmployeeHotelEntity = hdsEmployeeHotelEntities.get(0);
                                    Integer employeeId = hdsEmployeeHotelEntity.getEmployeeId();
                                    return hdsEmployeeRepository.findById(employeeId)
                                            .flatMap(hdsEmployee -> Mono.error(new BusinessException("DATA-ALREADY-INIT", "请联系本店管理员开通权限" + hdsEmployee.getMobile())));
                                });
                    }
                    existingHotelLead.setHotelName(req.getHotelName());
                    existingHotelLead.setCtripEbkUrl(req.getCtripEbkUrl());
                    existingHotelLead.setUpdatedBy(headerInfo.getUserId());
                    existingHotelLead.setUpdatedByName(headerInfo.getUsername());
                    existingHotelLead.setUpdatedAt(LocalDateTime.now());
                    return Mono.just(existingHotelLead);
                })
                .switchIfEmpty(
                        // 创建新 lead
                        codePoolManager.getCodeFromPool(BussinessTypeEnum.HOTEL_LEAD.getBusinessType())
                                .flatMap(hotelLeadCode -> {
                                    HdsHotelLeadEntity hotelLeadEntity = new HdsHotelLeadEntity();
                                    hotelLeadEntity.setLeadCode(hotelLeadCode);
                                    hotelLeadEntity.setHotelName(req.getHotelName());
                                    hotelLeadEntity.setHotelCode(hotelCode);
                                    hotelLeadEntity.setCtripEbkUrl(req.getCtripEbkUrl());
                                    hotelLeadEntity.setStatus(0);
                                    hotelLeadEntity.setCreatedBy(headerInfo.getUserId());
                                    hotelLeadEntity.setCreatedByName(headerInfo.getUsername());
                                    hotelLeadEntity.setCreatedAt(LocalDateTime.now());

                                    LeadProgressDTO leadProgressDTO = new LeadProgressDTO();
                                    leadProgressDTO.setBindEbk(SimpleDateUtils.formatLocalDateTimeToDateHour(LocalDateTime.now()));
                                    hotelLeadEntity.setProgressJson(JacksonUtils.writeValueAsString(leadProgressDTO));
                                    hotelLeadEntity.setRowStatus(1);
                                    return Mono.just(hotelLeadEntity);
                                }));
    }

    public Mono<HdsHotelLeadEntity> save(HdsHotelLeadEntity hotelLeadEntity) {
        return hotelLeadRepository.save(hotelLeadEntity);
    }
}
