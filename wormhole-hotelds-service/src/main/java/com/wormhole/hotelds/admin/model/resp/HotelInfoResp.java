package com.wormhole.hotelds.admin.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class HotelInfoResp {
    /**
     * 酒店id
     */
    private Integer hotelId;
    /**
     * 门店id
     */
    private Integer merchantId;
    /**
     * 商户id
     */
    private Integer merchantBaseInfoId;
    /**
     * 品牌id
     */
    private Integer brandId;
    /**
     * 商户业态类型
     */
    private String merchantType;
    /**
     * 酒店代码
     */
    private String hotelCode;
    /**
     * 酒店全称
     */
    private String hotelName;
    /**
     * 英文名称
     */
    private String hotelNameEng;
    /**
     * 酒店简称
     */
    private String hotelNameShort;
    /**
     * 建筑面积
     */
    private BigDecimal buildingArea;
    /**
     * 营业面积
     */
    private BigDecimal businessArea;
    /**
     * 签约房量
     */
    private Integer roomCount;
    /**
     * 物理房量
     */
    private Integer physicsRoom;

    /**
     * 首次签约时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstSignTime;
    /**
     * 最新签约时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signTime;
    /**
     * 计划开业时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planOpenTime;
    /**
     * 正式开业时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date openingTime;
    /**
     * 产生营收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date openTime;
    /**
     * 合同开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartTime;
    /**
     * 合同结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;
    /**
     * 装修时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date spruceTime;
    /**
     * 星级
     */
    private Integer star;
    /**
     * 星级名称
     */
    private String starName;
    /**
     * 酒店缩略图
     */
    private String picsThumb;
    /**
     * 公安备案编码
     */
    private String psbHotelCode;
    /**
     * PM类型
     */
    private String pmsType;
    /**
     * PM类型名称
     */
    private String pmsTypeName;
    /**
     * 营业开始时段
     */
    private String openStartTime;
    /**
     * 营业结束时段
     */
    private String openEndTime;
    /**
     * 酒店特色
     */
    private String hotelFeature;
    /**
     * 酒店标签
     */
    private String hotelLabel;
    /**
     * 中文简介
     */
    private String hotelCnDesc;
    /**
     * 英文简介
     */
    private String hotelEngDesc;
    /**
     * 预订须知
     */
    private String shouldKnow;
    /**
     * 预订须知-钟点房
     */
    private String shouldKnowHourly;
    /**
     * 温馨提示
     */
    private String notice;
    /**
     * 签约主体
     */
    private String consignee;
    /**
     * 搜索词
     */
    private String searchWord;
    /**
     * 项目状态
     */
    private String itemStatus;
    /**
     * 运营状态
     */
    private String operatingStatus;
    /**
     * 运营性质
     */
    private String operatingType;
    /**
     * 门店电话
     */
    private String phone;
    /**
     * 酒店传真
     */
    private String fax;
    /**
     * 邮政编码
     */
    private String postCode;
    /**
     * 门店邮箱
     */
    private String mailbox;
    /**
     * 事业部
     */
    private String deptCode;

    private String timeZoneArea;

    private String timeZoneStr;

    private String timeFormatter;

    private String countryLanguage;

    private String currency;

    private String currencySymbol;

    private String country;

    private String countryName;
}