package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.HdsInvitationCodeEntity;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/5/19 09:44
 * @Description：InvitationCodeVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InvitationCodeVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 邀请码名称
     */
    private String name;

    /**
     * 状态: 1-启用, 0-关闭
     */
    private Integer status;

    /**
     * 邀请门店数
     */
    private Integer invitationCount;

    /**
     * 将实体对象转换为视图对象
     */
    public static InvitationCodeVO toVo(HdsInvitationCodeEntity entity) {
        InvitationCodeVO vo = new InvitationCodeVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
