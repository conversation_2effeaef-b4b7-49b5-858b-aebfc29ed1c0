package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsUserHotelLeadMappingEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/22 16:14
 * @Description：UserHoteLeadMappingRepository
 */
@Repository
public interface UserHotelLeadMappingRepository extends ReactiveCrudRepository<HdsUserHotelLeadMappingEntity,Long> {

    Flux<HdsUserHotelLeadMappingEntity> findByUserLeadIdIn(Collection<Long> userLeadIds);

    Mono<Boolean> existsByUserLeadIdAndHotelLeadId(Long userLeadId, Long hotelLeadId);

    Flux<HdsUserHotelLeadMappingEntity> findByHotelLeadId(Long hotelLeadId);
}
