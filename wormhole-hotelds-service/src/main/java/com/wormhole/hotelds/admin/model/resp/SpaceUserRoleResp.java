package com.wormhole.hotelds.admin.model.resp;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.bot.ObjectInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SpaceUserRoleResp implements Serializable {

    private String userId;

    private String nickname;

    private ObjectInfo imageInfo;

    private String username;

    private String roleCode;

    private String roleName;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createdAt;

    /**
     * 能否删除
     */
    private Boolean canRemove;

}
