package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceInfoSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceInfoSearchReq;
import com.wormhole.hotelds.admin.model.vo.HdsInvoiceInfoSearchVO;
import com.wormhole.hotelds.admin.service.HdsInvoiceInfoService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-24 11:50:55
 * @Description: 发票信息控制器
 */
@RestController
@RequestMapping("/invoice_info")
public class HdsInvoiceInfoController {

    @Resource
    private HdsInvoiceInfoService service;

    /**
     * 搜索发票信息列表
     * @param req 搜索请求
     * @return 发票信息列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<HdsInvoiceInfoSearchVO>>> search(@RequestBody HdsInvoiceInfoSearchReq req) {
        return Mono.zip(
                service.searchCount(req),
                service.search(req)
        )
        .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
        .flatMap(Result::success);
    }
    
    /**
     * 获取默认发票信息或最近更新的发票信息
     * @param hotelCode 酒店编码
     * @return 发票信息
     */
    @GetMapping("/default/{hotelCode}")
    public Mono<Result<HdsInvoiceInfoSearchVO>> getDefaultOrLatest(@PathVariable("hotelCode") String hotelCode) {
        return service.getDefaultOrLatest(hotelCode)
                .flatMap(Result::success)
                .switchIfEmpty(Result.success(null));
    }
    
    /**
     * 创建或更新发票信息
     * @param req 发票信息保存请求
     * @return 发票信息ID
     */
    @PostMapping("/create")
    public Mono<Result<Integer>> create(@RequestBody HdsInvoiceInfoSaveReq req) {
        return service.create(req)
                .flatMap(Result::success);
    }
}