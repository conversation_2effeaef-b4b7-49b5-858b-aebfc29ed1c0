package com.wormhole.hotelds.admin.service;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.dto.BillDetailDTO;
import com.wormhole.hotelds.admin.model.enums.BillInvoiceStatusEnum;
import com.wormhole.hotelds.admin.model.enums.InvoiceApplicationStatusEnum;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.admin.aop.LoggingContext;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationCancelReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationCreateReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationSearchReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationStatusUpdateReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationUpdateReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceInfoSaveReq;
import com.wormhole.hotelds.admin.model.vo.HdsInvoiceApplicationDetailVO;
import com.wormhole.hotelds.admin.model.vo.HdsInvoiceApplicationSearchVO;
import com.wormhole.hotelds.admin.repository.HdsInvoiceApplicationRepository;
import com.wormhole.hotelds.admin.repository.HdsInvoiceDetailRepository;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.AuditFieldsUtils;
import com.wormhole.hotelds.util.CodeGeneratorUtils;
import com.wormhole.hotelds.util.OperationLogUtil;
import com.wormhole.hotelds.util.UserUtils;
import com.wormhole.hotelds.util.ValidatorUtils;
import com.wormhole.common.constant.RowStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-25 17:59:55
 * @Description: HdsInvoiceApplyService
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HdsInvoiceApplyService {

    private final HdsInvoiceApplicationRepository repository;
    private final HdsInvoiceDetailRepository detailRepository;
    private final R2dbcEntityTemplate template;
    private final TransactionalOperator transactionalOperator;
    private final CodeGeneratorUtils codeGeneratorUtils;
    private final BillService billService;
    private final PackageService packageService;
    private final HdsInvoiceInfoService hdsInvoiceInfoService;
    private final HotelService hotelService;
    private final OperationLogUtil operationLogUtil;

    /**
     * 创建发票申请
     * @param req 发票申请创建请求
     * @return 创建结果
     */
    public Mono<Boolean> create(HdsInvoiceApplicationCreateReq req) {
        LocalDateTime startTime = LocalDateTime.now();
        // 参数校验
        return validateRequest(req)
                .then(validateBillInvoiceStatus(req.getBillIds()))
                .then(HeaderUtils.getHeaderInfo())
                .flatMap(headerInfo -> executeCreateInTransaction(req, headerInfo)
                        .flatMap(result -> {
                            if (result) {
                                // 记录成功日志
                                recordCreateSuccess(req, null, headerInfo, startTime)
                                        .subscribe();
                            }
                            return Mono.just(result);
                        }))
                .onErrorResume(ex -> {
                    if (ex instanceof BusinessException) {
                        return Mono.error(ex);
                    }
                    log.error("创建发票申请失败: {}", ex.getMessage(), ex);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建发票申请失败"));
                });
    }

    /**
     * 请求参数校验
     */
    private Mono<Void> validateRequest(HdsInvoiceApplicationCreateReq req) {
        return Mono.fromRunnable(() -> ValidatorUtils.validateInvoiceApplicationCreateReq(req))
                .onErrorMap(IllegalArgumentException.class, 
                    ex -> new BusinessException(ResultCode.INVALID_PARAMETER, ex.getMessage()))
                .then();
    }

    /**
     * 校验账单发票申请状态
     */
    private Mono<Void> validateBillInvoiceStatus(List<Integer> billIds) {
        return billService.checkInvoiceAppliedBills(billIds)
                .flatMap(appliedBillIds -> {
                    if (!appliedBillIds.isEmpty()) {
                        String billIdsStr = appliedBillIds.stream()
                                .map(String::valueOf)
                                .reduce((a, b) -> a + ", " + b)
                                .orElse("");
                        String errorMessage = String.format("账单ID [%s] 已申请过发票，不能重复申请", billIdsStr);
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, errorMessage));
                    }
                    return Mono.empty();
                });
    }

    /**
     * 在事务中执行创建操作
     */
    private Mono<Boolean> executeCreateInTransaction(HdsInvoiceApplicationCreateReq req, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                // 查询账单信息
                queryBillsData(req.getBillIds())
                        .flatMap(billsData -> {
                            // 计算总金额和账单数量
                            BigDecimal totalAmount = calculateTotalAmount(billsData);
                            int billCount = billsData.size();
                            
                            // 生成申请编号
                            return codeGeneratorUtils.generateInvoiceApplicationNo()
                                    .flatMap(applicationNo -> {
                                        // 创建发票申请记录
                                        return createInvoiceApplication(req, applicationNo, totalAmount, billCount, headerInfo)
                                                .flatMap(applicationEntity -> {
                                                    // 创建发票明细记录
                                                    return createInvoiceDetails(applicationEntity.getId(), billsData, headerInfo)
                                                            .then(updateBillsInvoiceStatus(req.getBillIds()))
                                                            .then(saveInvoiceInfo(req))
                                                            .thenReturn(true);
                                                });
                                    });
                        })
        );
    }

    /**
     * 查询账单数据
     */
    private Mono<List<BillDetailDTO>> queryBillsData(List<Integer> billIds) {
        return billService.findByIds(billIds)
                .flatMap(billEntities -> 
                    Flux.fromIterable(billEntities)
                            .flatMap(this::enrichBillWithOrderAndPackage)
                            .collectList()
                );
    }

    /**
     * 丰富账单信息，添加订单和套餐信息
     */
    private Mono<BillDetailDTO> enrichBillWithOrderAndPackage(HdsBillEntity billEntity) {
        // 查询订单信息
        return template.selectOne(
                Query.query(Criteria.where(HdsOrderFieldEnum.order_no.name()).is(billEntity.getOrderNo())),
                HdsOrderEntity.class
        )
        .flatMap(orderEntity -> {
            // 查询套餐信息
            return packageService.findByPackageCode(orderEntity.getPackageCode())
                    .map(packageEntity -> new BillDetailDTO()
                            .setBillEntity(billEntity)
                            .setOrderEntity(orderEntity)
                            .setPackageName(packageEntity.getPackageName())
                    );
        });
    }

    /**
     * 计算总金额
     */
    private BigDecimal calculateTotalAmount(List<BillDetailDTO> billsData) {
        return billsData.stream()
                .map(data -> data.getBillEntity().getPayAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 创建发票申请记录
     */
    private Mono<HdsInvoiceApplyEntity> createInvoiceApplication(HdsInvoiceApplicationCreateReq req,
                                                                        String applicationNo,
                                                                        BigDecimal totalAmount,
                                                                        int billCount,
                                                                        HeaderUtils.HeaderInfo headerInfo) {
        HdsInvoiceApplyEntity entity = new HdsInvoiceApplyEntity();
        entity.setHotelCode(req.getHotelCode())
                .setApplicationNo(applicationNo)
                .setInvoiceAmount(totalAmount)
                .setBillCount(billCount)
                // 待审核
                .setApplicationStatus(InvoiceApplicationStatusEnum.PENDING.getCode())
                .setInvoiceHeader(req.getInvoiceHeader())
                .setTaxNumber(req.getTaxNumber())
                .setReceiveEmail(req.getReceiveEmail())
                .setInvoiceContent(StringUtils.defaultIfBlank(req.getInvoiceContent(), "服务费"))
                .setMoreInfo(req.getMoreInfo());

        // 设置审计信息
        AuditFieldsUtils.setAuditFields(entity, headerInfo, true);
        
        return repository.save(entity);
    }

    /**
     * 创建发票明细记录
     */
    private Mono<Void> createInvoiceDetails(Long applicationId, List<BillDetailDTO> billsData, HeaderUtils.HeaderInfo headerInfo) {
        return Flux.fromIterable(billsData)
                .map(data -> createInvoiceDetailEntity(applicationId, data, headerInfo))
                .flatMap(detailRepository::save)
                .then();
    }

    /**
     * 创建发票明细实体
     */
    private HdsInvoiceDetailEntity createInvoiceDetailEntity(Long applicationId, BillDetailDTO data, HeaderUtils.HeaderInfo headerInfo) {
        HdsInvoiceDetailEntity entity = new HdsInvoiceDetailEntity();
        entity.setApplicationId(applicationId.intValue())
                .setBillId(data.getBillEntity().getId())
                .setOrderNo(data.getBillEntity().getOrderNo())
                .setPackageName(data.getPackageName())
                .setBillAmount(data.getBillEntity().getPayAmount());

        // 设置审计信息
        AuditFieldsUtils.setAuditFields(entity, headerInfo, true);
        
        return entity;
    }

    /**
     * 更新账单的发票申请状态
     */
    private Mono<Void> updateBillsInvoiceStatus(List<Integer> billIds) {
        return Flux.fromIterable(billIds)
                .flatMap(billId -> {
                    Update update = Update.update(HdsBillFieldEnum.is_invoice_applied.name(), BillInvoiceStatusEnum.APPLIED.getCode());
                    return template.update(HdsBillEntity.class)
                            .matching(Query.query(Criteria.where(HdsBillFieldEnum.id.name()).is(billId)))
                            .apply(update);
                })
                .then();
    }

    /**
     * 保存发票信息
     */
    private Mono<Void> saveInvoiceInfo(HdsInvoiceApplicationCreateReq req) {
        HdsInvoiceInfoSaveReq invoiceInfoReq = new HdsInvoiceInfoSaveReq();
        invoiceInfoReq.setHotelCode(req.getHotelCode());
        invoiceInfoReq.setInvoiceHeader(req.getInvoiceHeader());
        invoiceInfoReq.setTaxNumber(req.getTaxNumber());
        invoiceInfoReq.setReceiveEmail(req.getReceiveEmail());
        invoiceInfoReq.setInvoiceContent(req.getInvoiceContent());
        invoiceInfoReq.setMoreInfo(req.getMoreInfo());
        invoiceInfoReq.setIsDefault(req.getIsDefault());
        return hdsInvoiceInfoService.create(invoiceInfoReq).then();
    }

    /**
     * 搜索发票申请数量
     */
    public Mono<Long> searchCount(HdsInvoiceApplicationSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        return buildSearchCriteria(req, headerInfo)
                .flatMap(criteria -> template.count(Query.query(criteria), HdsInvoiceApplyEntity.class))
                .defaultIfEmpty(0L)
                .doOnSuccess(count -> log.info("发票申请计数成功: count={}, req={}", count, req))
                .onErrorResume(e -> {
                    log.error("发票申请计数失败: req={}, error={}", req, e.getMessage(), e);
                    return Mono.just(0L);
                });
    }

    /**
     * 搜索发票申请列表
     */
    public Mono<List<HdsInvoiceApplicationSearchVO>> search(HdsInvoiceApplicationSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        return buildSearchCriteria(req, headerInfo)
                .flatMap(criteria -> {
                    // 构建查询
                    Query query = Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.DESC, HdsInvoiceApplyFieldEnum.created_at.name()))
                            .limit(req.getPageSize())
                            .offset((long) (req.getCurrent() - 1) * req.getPageSize());

                    // 执行查询
                    return template.select(HdsInvoiceApplyEntity.class)
                            .matching(query)
                            .all()
                            .collectList()
                            .flatMap(this::enrichApplicationsWithHotelInfo);
                })
                .doOnSuccess(list -> log.info("发票申请查询成功: size={}, req={}", list.size(), req))
                .onErrorResume(e -> {
                    log.error("发票申请查询失败: req={}, error={}", req, e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "发票申请查询失败"));
                });
    }

    /**
     * 构建搜索条件
     */
    private Mono<Criteria> buildSearchCriteria(HdsInvoiceApplicationSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        Criteria criteria = buildBaseCriteria(req);
        
        // 如果请求头中包含酒店编码，添加酒店编码条件
        if (StringUtils.isNotBlank(headerInfo.getHotelCode())) {
            criteria = criteria.and(HdsInvoiceApplyFieldEnum.hotel_code.name()).is(headerInfo.getHotelCode());
        }
        
        return Mono.just(criteria);
    }

    /**
     * 构建基础搜索条件
     */
    private Criteria buildBaseCriteria(HdsInvoiceApplicationSearchReq req) {
        Criteria criteria = Criteria.empty();

        // 只查询有效记录（排除软删除的记录）
        criteria = criteria.and(HdsInvoiceApplyFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId());

        // 申请编号（精确搜索）
        if (StringUtils.isNotBlank(req.getApplicationNo())) {
            criteria = criteria.and(HdsInvoiceApplyFieldEnum.application_no.name()).is(req.getApplicationNo());
        }

        // 酒店编码（精确搜索）
        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(HdsInvoiceApplyFieldEnum.hotel_code.name()).is(req.getHotelCode());
        }

        // 申请状态
        if (req.getApplicationStatus() != null) {
            criteria = criteria.and(HdsInvoiceApplyFieldEnum.application_status.name()).is(req.getApplicationStatus());
        }

        return criteria;
    }

    /**
     * 根据酒店编码列表构建搜索条件
     */
    private Criteria buildCriteriaWithHotelCodes(HdsInvoiceApplicationSearchReq req, List<String> hotelCodes) {
        Criteria criteria = buildBaseCriteria(req);

        if (hotelCodes.isEmpty()) {
            // 如果没有匹配的酒店，返回不可能匹配的条件
            return criteria.and(HdsInvoiceApplyFieldEnum.hotel_code.name()).is("__NO_MATCH__");
        }
        
        return criteria.and(HdsInvoiceApplyFieldEnum.hotel_code.name()).in(hotelCodes);
    }

    /**
     * 丰富申请信息，添加酒店名称等信息
     */
    private Mono<List<HdsInvoiceApplicationSearchVO>> enrichApplicationsWithHotelInfo(List<HdsInvoiceApplyEntity> applications) {
        if (applications.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }

        // 提取所有酒店编码
        Set<String> hotelCodes = applications.stream()
                .map(HdsInvoiceApplyEntity::getHotelCode)
                .collect(Collectors.toSet());

        // 批量查询酒店信息
        return hotelService.batchQueryHotels(hotelCodes)
                .map(hotels -> hotels.stream()
                        .collect(Collectors.toMap(
                                HdsHotelInfoEntity::getHotelCode,
                                Function.identity(),
                                (existing, replacement) -> existing
                        )))
                .map(hotelMap -> applications.stream()
                        .map(app -> convertToSearchVO(app, hotelMap.get(app.getHotelCode())))
                        .collect(Collectors.toList()));
    }

    /**
     * 转换为搜索VO
     */
    private HdsInvoiceApplicationSearchVO convertToSearchVO(HdsInvoiceApplyEntity entity, HdsHotelInfoEntity hotelEntity) {
        HdsInvoiceApplicationSearchVO vo = new HdsInvoiceApplicationSearchVO();
        vo.setId(entity.getId())
                .setApplicationNo(entity.getApplicationNo())
                .setHotelCode(entity.getHotelCode())
                .setInvoiceHeader(entity.getInvoiceHeader())
                .setTaxNumber(entity.getTaxNumber())
                .setInvoiceAmount(entity.getInvoiceAmount())
                .setBillCount(entity.getBillCount())
                .setApplicationStatus(entity.getApplicationStatus())
                .setCreatedAt(entity.getCreatedAt())
                .setInvoicedAt(entity.getInvoicedAt())
                .setReviewedByName(entity.getReviewedByName())
                .setReviewRemark(entity.getReviewRemark())
                .setInvoiceNo(entity.getInvoiceNo())
                .setInvoiceCode(entity.getInvoiceCode())
                .setInvoiceUrl(entity.getInvoiceUrl())
                .setCreatedByName(entity.getCreatedByName());

        // 设置门店信息
        if (hotelEntity != null) {
            vo.setHotelName(hotelEntity.getHotelName());
        }

        return vo;
    }

    /**
     * 修改发票申请
     * @param req 修改请求
     * @return 修改结果
     */
    public Mono<Boolean> update(HdsInvoiceApplicationUpdateReq req) {
        LocalDateTime startTime = LocalDateTime.now();
        // 参数校验
        return validateUpdateRequest(req)
                .then(HeaderUtils.getHeaderInfo())
                .flatMap(headerInfo -> 
                    // 先查询修改前的对象
                    repository.findById(req.getId())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "发票申请记录不存在")))
                            .flatMap(beforeObj -> 
                                executeUpdateInTransaction(req, headerInfo)
                                        .flatMap(result -> {
                                            if (result) {
                                                // 查询修改后的对象并记录成功日志
                                                repository.findById(req.getId())
                                                        .flatMap(afterObj -> recordUpdateSuccess(req, beforeObj, afterObj, headerInfo, startTime))
                                                        .subscribe();
                                            }
                                            return Mono.just(result);
                                        })
                            )
                )
                .onErrorResume(ex -> {
                    if (ex instanceof BusinessException) {
                        return Mono.error(ex);
                    }
                    log.error("修改发票申请失败: {}", ex.getMessage(), ex);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "修改发票申请失败"));
                });
    }

    /**
     * 校验修改请求
     */
    private Mono<Void> validateUpdateRequest(HdsInvoiceApplicationUpdateReq req) {
        return Mono.fromRunnable(() -> {
            // 校验申请ID
            if (req.getId() == null) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "申请ID不能为空");
            }

            // 校验账单ID
            if (req.getBillIds() == null || req.getBillIds().isEmpty()) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "账单ID不能为空");
            }

            // 校验发票抬头
            if (StringUtils.isBlank(req.getInvoiceHeader())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "发票抬头不能为空");
            }

            // 校验纳税人识别号
            if (StringUtils.isBlank(req.getTaxNumber())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "纳税人识别号不能为空");
            }

            // 校验接收邮箱
            if (StringUtils.isBlank(req.getReceiveEmail())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "接收邮箱不能为空");
            }
            // 校验邮箱格式
            if (StringUtils.isNotBlank(req.getReceiveEmail()) && !ValidatorUtils.isValidEmail(req.getReceiveEmail())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "邮箱格式不正确");
            }
            
            // 校验纳税人识别号格式（只能包含数字和大写字母）
            if (StringUtils.isNotBlank(req.getTaxNumber()) && !req.getTaxNumber().matches("^[0-9A-Z]+$")) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "纳税人识别号只能包含数字和大写字母");
            }
        })
        // 校验账单发票申请状态
        .then(validateBillInvoiceStatusForUpdate(req.getBillIds()));
    }

    /**
     * 校验账单发票申请状态（修改时）
     */
    private Mono<Void> validateBillInvoiceStatusForUpdate(List<Integer> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return Mono.empty();
        }
        
        return Flux.fromIterable(billIds)
                .flatMap(billId -> billService.findById(billId)
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "账单不存在: " + billId)))
                        .flatMap(bill -> {
                            // 允许未申请或已申请的账单
                            if (!BillInvoiceStatusEnum.UNAPPLIED.getCode().equals(bill.getIsInvoiceApplied()) &&
                                !BillInvoiceStatusEnum.APPLIED.getCode().equals(bill.getIsInvoiceApplied())) {
                                return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, 
                                    String.format("账单[%s]状态不允许申请发票", billId)));
                            }
                            return Mono.empty();
                        }))
                .then();
    }

    /**
     * 在事务中执行修改操作
     */
    private Mono<Boolean> executeUpdateInTransaction(HdsInvoiceApplicationUpdateReq req, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                // 查询当前申请记录
                repository.findById(req.getId())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "发票申请记录不存在")))
                        .flatMap(applicationEntity -> validateUpdateApplicationStatus(applicationEntity)
                                .then(processUpdateWithBillChanges(applicationEntity, req, headerInfo))
                                .then(updateRelatedInvoiceInfo(req, applicationEntity.getHotelCode()))
                                .thenReturn(true)
                        )
        );
    }

    /**
     * 处理修改申请时的账单变更
     */
    private Mono<Void> processUpdateWithBillChanges(HdsInvoiceApplyEntity applicationEntity, 
                                                   HdsInvoiceApplicationUpdateReq req, 
                                                   HeaderUtils.HeaderInfo headerInfo) {
        // 如果没有传入账单ID，则只更新申请基本信息
        if (req.getBillIds() == null || req.getBillIds().isEmpty()) {
            return updateApplicationFields(applicationEntity, req, headerInfo).then();
        }

        // 查询当前申请的账单ID
        return queryRelatedBillIds(applicationEntity.getId())
                .flatMap(currentBillIds -> {
                    Set<Integer> currentSet = new HashSet<>(currentBillIds);
                    Set<Integer> newSet = new HashSet<>(req.getBillIds());
                    
                    // 找出需要移除的账单ID
                    Set<Integer> toRemove = new HashSet<>(currentSet);
                    toRemove.removeAll(newSet);
                    
                    // 找出需要添加的账单ID
                    Set<Integer> toAdd = new HashSet<>(newSet);
                    toAdd.removeAll(currentSet);
                    
                    // 处理账单变更
                    return processBillChanges(applicationEntity.getId(), toRemove, toAdd, headerInfo)
                            .then(queryBillsData(req.getBillIds()))
                            .flatMap(billsData -> {
                                // 重新计算总金额和账单数量
                                BigDecimal totalAmount = calculateTotalAmount(billsData);
                                applicationEntity.setInvoiceAmount(totalAmount);
                                applicationEntity.setBillCount(billsData.size());
                                
                                // 更新申请字段
                                return updateApplicationFields(applicationEntity, req, headerInfo);
                            })
                            .then();
                });
    }

    /**
     * 处理账单变更
     */
    private Mono<Void> processBillChanges(Long applicationId, Set<Integer> toRemove, Set<Integer> toAdd, HeaderUtils.HeaderInfo headerInfo) {
        Mono<Void> removeOp = Mono.empty();
        Mono<Void> addOp = Mono.empty();
        
        // 移除账单
        if (!toRemove.isEmpty()) {
            removeOp = removeBillsFromApplication(applicationId, toRemove, headerInfo);
        }
        
        // 添加账单
        if (!toAdd.isEmpty()) {
            addOp = addBillsToApplication(applicationId, toAdd, headerInfo);
        }
        
        return removeOp.then(addOp);
    }

    /**
     * 从申请中移除账单
     */
    private Mono<Void> removeBillsFromApplication(Long applicationId, Set<Integer> billIds, HeaderUtils.HeaderInfo headerInfo) {
        return Flux.fromIterable(billIds)
                .flatMap(billId -> {
                    // 软删除发票明细记录
                    Criteria criteria = Criteria.where(HdsInvoiceDetailFieldEnum.application_id.name()).is(applicationId.intValue())
                            .and(HdsInvoiceDetailFieldEnum.bill_id.name()).is(billId)
                            .and(HdsInvoiceDetailFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId());
                    
                    return template.select(Query.query(criteria), HdsInvoiceDetailEntity.class)
                            .flatMap(detailEntity -> {
                                detailEntity.setRowStatus(RowStatusEnum.DELETE.getId());
                                AuditFieldsUtils.setAuditFields(detailEntity, headerInfo, false);
                                return detailRepository.save(detailEntity);
                            })
                            .then();
                })
                .then(resetBillsInvoiceStatus(new ArrayList<>(billIds)))
                .doOnSuccess(unused -> log.info("成功从申请[{}]中移除账单: {}", applicationId, billIds));
    }

    /**
     * 向申请中添加账单
     */
    private Mono<Void> addBillsToApplication(Long applicationId, Set<Integer> billIds, HeaderUtils.HeaderInfo headerInfo) {
        return queryBillsData(new ArrayList<>(billIds))
                .flatMap(billsData -> createInvoiceDetails(applicationId, billsData, headerInfo))
                .then(updateBillsInvoiceStatus(new ArrayList<>(billIds)))
                .doOnSuccess(unused -> log.info("成功向申请[{}]中添加账单: {}", applicationId, billIds));
    }

    /**
     * 校验申请状态是否允许修改
     */
    private Mono<Void> validateUpdateApplicationStatus(HdsInvoiceApplyEntity applicationEntity) {
        return Mono.fromRunnable(() -> {
            // 只有待审核和已驳回状态才能修改
            if (!InvoiceApplicationStatusEnum.PENDING.getCode().equals(applicationEntity.getApplicationStatus()) &&
                !InvoiceApplicationStatusEnum.REJECTED.getCode().equals(applicationEntity.getApplicationStatus())) {
                InvoiceApplicationStatusEnum statusEnum = InvoiceApplicationStatusEnum.getByCode(applicationEntity.getApplicationStatus());
                String statusDesc = statusEnum != null ? statusEnum.getDesc() : "未知状态";
                throw new BusinessException(ResultCode.INVALID_PARAMETER, 
                    String.format("当前状态为[%s]，不允许修改", statusDesc));
            }
        });
    }

    /**
     * 更新申请字段
     */
    private Mono<HdsInvoiceApplyEntity> updateApplicationFields(HdsInvoiceApplyEntity applicationEntity, 
                                                                       HdsInvoiceApplicationUpdateReq req, 
                                                                       HeaderUtils.HeaderInfo headerInfo) {
        // 保存原始状态
        Integer originalStatus = applicationEntity.getApplicationStatus();
        
        // 更新可修改的字段
        applicationEntity.setInvoiceHeader(req.getInvoiceHeader())
                .setTaxNumber(req.getTaxNumber())
                .setInvoiceContent(req.getInvoiceContent())
                .setReceiveEmail(req.getReceiveEmail())
                .setMoreInfo(req.getMoreInfo());

        // 如果传入了发票金额，则更新
        if (req.getInvoiceAmount() != null) {
            applicationEntity.setInvoiceAmount(req.getInvoiceAmount());
        }

        // 如果原状态是已驳回，修改后自动变为待审核状态
        if (InvoiceApplicationStatusEnum.REJECTED.getCode().equals(originalStatus)) {
            applicationEntity.setApplicationStatus(InvoiceApplicationStatusEnum.PENDING.getCode());
            // 清空审核相关信息
            applicationEntity.setReviewedBy(null)
                    .setReviewedByName(null)
                    .setReviewedAt(null);
            log.info("发票申请[{}]从已驳回状态修改后自动变为待审核状态", applicationEntity.getId());
        }

        // 设置审计信息
        AuditFieldsUtils.setAuditFields(applicationEntity, headerInfo, false);

        return repository.save(applicationEntity)
                .doOnSuccess(entity -> log.info("成功修改发票申请: id={}, applicationNo={}, status={}", 
                    entity.getId(), entity.getApplicationNo(), 
                    InvoiceApplicationStatusEnum.getByCode(entity.getApplicationStatus()).getDesc()));
    }

    /**
     * 更新相关的发票信息
     */
    private Mono<Void> updateRelatedInvoiceInfo(HdsInvoiceApplicationUpdateReq req, String hotelCode) {
        HdsInvoiceInfoSaveReq saveReq = new HdsInvoiceInfoSaveReq();
        saveReq.setHotelCode(hotelCode)
                .setInvoiceHeader(req.getInvoiceHeader())
                .setTaxNumber(req.getTaxNumber())
                .setInvoiceContent(req.getInvoiceContent())
                .setReceiveEmail(req.getReceiveEmail())
                .setMoreInfo(req.getMoreInfo())
                .setIsDefault(req.getIsDefault());
        return hdsInvoiceInfoService.create(saveReq).then();
    }

    /**
     * 取消发票申请
     * @param req 取消申请请求
     * @return 取消结果
     */
    public Mono<Boolean> cancel(HdsInvoiceApplicationCancelReq req) {
        LocalDateTime startTime = LocalDateTime.now();
        // 参数校验
        return validateCancelRequest(req)
                .then(HeaderUtils.getHeaderInfo())
                .flatMap(headerInfo -> 
                    // 先查询取消前的对象
                    repository.findById(req.getId())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "发票申请记录不存在")))
                            .flatMap(beforeObj -> 
                                executeCancelInTransaction(req, headerInfo)
                                        .flatMap(result -> {
                                            if (result) {
                                                // 记录成功日志
                                                recordCancelSuccess(req, beforeObj, headerInfo, startTime)
                                                        .subscribe();
                                            }
                                            return Mono.just(result);
                                        })
                            )
                )
                .onErrorResume(ex -> {
                    if (ex instanceof BusinessException) {
                        return Mono.error(ex);
                    }
                    log.error("取消发票申请失败: {}", ex.getMessage(), ex);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "取消发票申请失败"));
                });
    }

    /**
     * 校验取消申请请求
     */
    private Mono<Void> validateCancelRequest(HdsInvoiceApplicationCancelReq req) {
        return Mono.fromRunnable(() -> {
            if (req == null || req.getId() == null) {
                throw new IllegalArgumentException("发票申请ID不能为空");
            }
        }).onErrorMap(IllegalArgumentException.class, 
            ex -> new BusinessException(ResultCode.INVALID_PARAMETER, ex.getMessage()))
        .then();
    }

    /**
     * 在事务中执行取消操作
     */
    private Mono<Boolean> executeCancelInTransaction(HdsInvoiceApplicationCancelReq req, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                // 1. 查询申请记录
                repository.findById(req.getId())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "发票申请记录不存在")))
                        .flatMap(applicationEntity -> {
                            // 2. 校验申请状态
                            return validateApplicationStatus(applicationEntity)
                                    .then(Mono.just(applicationEntity));
                        })
                        .flatMap(applicationEntity -> {
                            // 3. 查询关联的账单ID
                            return queryRelatedBillIds(applicationEntity.getId())
                                    .flatMap(billIds -> {
                                        // 4. 恢复账单的发票申请状态
                                        return resetBillsInvoiceStatus(billIds)
                                                .then(
                                                        // 5. 软删除发票明细记录
                                                        softDeleteInvoiceDetails(applicationEntity.getId(), headerInfo)
                                                )
                                                .then(
                                                        // 6. 软删除申请记录
                                                        softDeleteApplication(applicationEntity, headerInfo)
                                                )
                                                .thenReturn(true);
                                    });
                        })
        );
    }

    /**
     * 校验申请状态
     */
    private Mono<Void> validateApplicationStatus(HdsInvoiceApplyEntity applicationEntity) {
        return Mono.fromRunnable(() -> {
            if (!InvoiceApplicationStatusEnum.PENDING.getCode().equals(applicationEntity.getApplicationStatus())) {
                InvoiceApplicationStatusEnum status = InvoiceApplicationStatusEnum.getByCode(applicationEntity.getApplicationStatus());
                String statusDesc = status != null ? status.getDesc() : "未知状态";
                throw new BusinessException(ResultCode.INVALID_PARAMETER, 
                    String.format("只有待审核状态的申请可以取消，当前状态：%s", statusDesc));
            }
        }).then();
    }

    /**
     * 查询关联的账单ID
     */
    private Mono<List<Integer>> queryRelatedBillIds(Long applicationId) {
        return template.select(
                Query.query(Criteria.where(HdsInvoiceDetailFieldEnum.application_id.name()).is(applicationId.intValue())
                        .and(HdsInvoiceDetailFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId())),
                HdsInvoiceDetailEntity.class
        )
        .map(HdsInvoiceDetailEntity::getBillId)
        .collectList()
        .doOnNext(billIds -> log.info("发票申请[{}]关联的账单ID: {}", applicationId, billIds));
    }

    /**
     * 恢复账单的发票申请状态为未申请
     */
    private Mono<Void> resetBillsInvoiceStatus(List<Integer> billIds) {
        if (billIds.isEmpty()) {
            return Mono.empty();
        }
        
        return Flux.fromIterable(billIds)
                .flatMap(billId -> {
                    Update update = Update.update(HdsBillFieldEnum.is_invoice_applied.name(), BillInvoiceStatusEnum.UNAPPLIED.getCode());
                    return template.update(HdsBillEntity.class)
                            .matching(Query.query(Criteria.where(HdsBillFieldEnum.id.name()).is(billId)))
                            .apply(update);
                })
                .then()
                .doOnSuccess(unused -> log.info("成功恢复账单[{}]的发票申请状态为未申请", billIds));
    }

    /**
     * 软删除发票明细记录
     */
    private Mono<Void> softDeleteInvoiceDetails(Long applicationId, HeaderUtils.HeaderInfo headerInfo) {
        return template.select(
                Query.query(Criteria.where(HdsInvoiceDetailFieldEnum.application_id.name()).is(applicationId.intValue())
                        .and(HdsInvoiceDetailFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId())),
                HdsInvoiceDetailEntity.class
        )
        .flatMap(detailEntity -> {
            // 设置软删除标志
            detailEntity.setRowStatus(RowStatusEnum.DELETE.getId());
            
            // 设置审计信息
            AuditFieldsUtils.setAuditFields(detailEntity, headerInfo, false);
            
            return detailRepository.save(detailEntity);
        })
        .then()
        .doOnSuccess(unused -> log.info("成功软删除发票申请[{}]的所有明细记录", applicationId));
    }

    /**
     * 软删除申请记录
     */
    private Mono<Void> softDeleteApplication(HdsInvoiceApplyEntity applicationEntity, HeaderUtils.HeaderInfo headerInfo) {
        // 设置软删除标志
        applicationEntity.setRowStatus(RowStatusEnum.DELETE.getId());
        
        // 设置审计信息
        AuditFieldsUtils.setAuditFields(applicationEntity, headerInfo, false);
        
        return repository.save(applicationEntity)
                .then()
                .doOnSuccess(unused -> log.info("成功软删除发票申请[{}]", applicationEntity.getId()));
    }

    /**
     * 根据ID查询发票申请详情
     * @param id 发票申请ID
     * @return 发票申请详情
     */
    public Mono<HdsInvoiceApplicationDetailVO> findById(Long id) {
        return repository.findById(id)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "发票申请记录不存在")))
                .flatMap(this::enrichApplicationDetail)
                .doOnSuccess(detail -> log.info("成功查询发票申请详情: id={}", id))
                .onErrorResume(ex -> {
                    if (ex instanceof BusinessException) {
                        return Mono.error(ex);
                    }
                    log.error("查询发票申请详情失败: id={}, error={}", id, ex.getMessage(), ex);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "查询发票申请详情失败"));
                });
    }

    /**
     * 丰富申请详情信息
     */
    private Mono<HdsInvoiceApplicationDetailVO> enrichApplicationDetail(HdsInvoiceApplyEntity applicationEntity) {
        return Mono.zip(
                // 查询门店信息
                queryHotelInfo(applicationEntity.getHotelCode()),
                // 查询发票明细列表
                queryInvoiceDetails(applicationEntity.getId())
        ).map(tuple -> {
            HdsHotelInfoEntity hotelEntity = tuple.getT1();
            List<HdsInvoiceApplicationDetailVO.InvoiceDetailItemVO> billDetails = tuple.getT2();
            
            return convertToDetailVO(applicationEntity, hotelEntity, billDetails);
        });
    }

    /**
     * 查询门店信息
     */
    private Mono<HdsHotelInfoEntity> queryHotelInfo(String hotelCode) {
        return template.selectOne(
                Query.query(Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).is(hotelCode)),
                HdsHotelInfoEntity.class
        ).switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "门店信息不存在")));
    }

    /**
     * 查询发票明细列表
     */
    private Mono<List<HdsInvoiceApplicationDetailVO.InvoiceDetailItemVO>> queryInvoiceDetails(Long applicationId) {
        return template.select(
                Query.query(Criteria.where(HdsInvoiceDetailFieldEnum.application_id.name()).is(applicationId.intValue())
                        .and(HdsInvoiceDetailFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId())),
                HdsInvoiceDetailEntity.class
        )
        .flatMap(this::enrichInvoiceDetailItem)
        .collectList()
        .doOnNext(details -> log.info("查询到发票明细 {} 条", details.size()));
    }

    /**
     * 丰富发票明细信息
     */
    private Mono<HdsInvoiceApplicationDetailVO.InvoiceDetailItemVO> enrichInvoiceDetailItem(HdsInvoiceDetailEntity detailEntity) {
        return Mono.zip(
                // 查询账单信息
                queryBillInfo(detailEntity.getBillId()),
                // 查询订单信息
                queryOrderInfo(detailEntity.getOrderNo()),
                // 查询产品信息
                queryProductInfo(detailEntity.getOrderNo())
        ).map(tuple -> {
            HdsBillEntity billEntity = tuple.getT1();
            HdsOrderEntity orderEntity = tuple.getT2();
            HdsProductEntity productEntity = tuple.getT3();
            
            return convertToDetailItemVO(detailEntity, billEntity, orderEntity, productEntity);
        });
    }

    /**
     * 查询账单信息
     */
    private Mono<HdsBillEntity> queryBillInfo(Integer billId) {
        return template.selectOne(
                Query.query(Criteria.where(HdsBillFieldEnum.id.name()).is(billId)),
                HdsBillEntity.class
        ).switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "账单信息不存在")));
    }

    /**
     * 查询订单信息
     */
    private Mono<HdsOrderEntity> queryOrderInfo(String orderNo) {
        return template.selectOne(
                Query.query(Criteria.where(HdsOrderFieldEnum.order_no.name()).is(orderNo)),
                HdsOrderEntity.class
        ).switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "订单信息不存在")));
    }

    /**
     * 查询产品信息
     */
    private Mono<HdsProductEntity> queryProductInfo(String orderNo) {
        return template.selectOne(
                Query.query(Criteria.where(HdsOrderFieldEnum.order_no.name()).is(orderNo)),
                HdsOrderEntity.class
        )
        .flatMap(orderEntity -> 
                template.selectOne(
                        Query.query(Criteria.where(HdsProductFieldEnum.id.name()).is(orderEntity.getProductId())),
                        HdsProductEntity.class
                )
        ).switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "产品信息不存在")));
    }

    /**
     * 转换为详情VO
     */
    private HdsInvoiceApplicationDetailVO convertToDetailVO(HdsInvoiceApplyEntity applicationEntity,
                                                            HdsHotelInfoEntity hotelEntity,
                                                            List<HdsInvoiceApplicationDetailVO.InvoiceDetailItemVO> billDetails) {
        HdsInvoiceApplicationDetailVO vo = new HdsInvoiceApplicationDetailVO();
        
        // 基本信息
        vo.setId(applicationEntity.getId())
                .setApplicationNo(applicationEntity.getApplicationNo())
                .setHotelCode(applicationEntity.getHotelCode())
                .setHotelName(hotelEntity.getHotelName())
                .setInvoiceAmount(applicationEntity.getInvoiceAmount())
                .setBillCount(applicationEntity.getBillCount())
                .setApplicationStatus(applicationEntity.getApplicationStatus());

        // 发票信息
        vo.setInvoiceHeader(applicationEntity.getInvoiceHeader())
                .setTaxNumber(applicationEntity.getTaxNumber())
                .setReceiveEmail(applicationEntity.getReceiveEmail())
                .setInvoiceContent(applicationEntity.getInvoiceContent())
                .setMoreInfo(applicationEntity.getMoreInfo());

        // 审核信息
        vo.setReviewRemark(applicationEntity.getReviewRemark())
                .setReviewedBy(applicationEntity.getReviewedBy())
                .setReviewedByName(applicationEntity.getReviewedByName())
                .setReviewedAt(applicationEntity.getReviewedAt());

        // 开票信息
        vo.setInvoiceNo(applicationEntity.getInvoiceNo())
                .setInvoiceCode(applicationEntity.getInvoiceCode())
                .setInvoiceUrl(applicationEntity.getInvoiceUrl())
                .setInvoicedAt(applicationEntity.getInvoicedAt());

        // 操作信息
        vo.setCreatedByName(applicationEntity.getCreatedByName())
                .setCreatedAt(applicationEntity.getCreatedAt())
                .setUpdatedAt(applicationEntity.getUpdatedAt());

        // 账单明细
        vo.setBillDetails(billDetails);

        return vo;
    }

    /**
     * 转换为明细项VO
     */
    private HdsInvoiceApplicationDetailVO.InvoiceDetailItemVO convertToDetailItemVO(HdsInvoiceDetailEntity detailEntity,
                                                                                     HdsBillEntity billEntity,
                                                                                     HdsOrderEntity orderEntity,
                                                                                     HdsProductEntity productEntity) {
        HdsInvoiceApplicationDetailVO.InvoiceDetailItemVO itemVO = new HdsInvoiceApplicationDetailVO.InvoiceDetailItemVO();
        
        // 基本信息
        itemVO.setId(detailEntity.getId())
                .setBillId(detailEntity.getBillId())
                .setOrderNo(detailEntity.getOrderNo())
                .setPackageName(detailEntity.getPackageName())
                .setProductName(productEntity.getProductName())
                .setBillAmount(detailEntity.getBillAmount());

        // 订单信息
        itemVO.setPeriodType(orderEntity.getPeriodType())
                .setRoomCount(orderEntity.getRoomCount())
                .setExpireTime(orderEntity.getExpireTime());

        // 支付信息
        itemVO.setPayMethod(billEntity.getPayMethod())
                .setTransactionAt(billEntity.getTransactionAt());

        return itemVO;
    }

    /**
     * 更新发票申请状态
     * @param req 状态更新请求
     * @return 更新结果
     */
    public Mono<Boolean> updateStatus(HdsInvoiceApplicationStatusUpdateReq req) {
        LocalDateTime startTime = LocalDateTime.now();
        // 参数校验
        return validateStatusUpdateRequest(req)
                .then(HeaderUtils.getHeaderInfo())
                .flatMap(headerInfo -> 
                    // 先查询修改前的对象
                    repository.findById(req.getId())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "发票申请记录不存在")))
                            .flatMap(beforeObj -> 
                                executeStatusUpdateInTransaction(req, headerInfo)
                                        .flatMap(result -> {
                                            if (result) {
                                                // 查询修改后的对象并记录成功日志
                                                repository.findById(req.getId())
                                                        .flatMap(afterObj -> recordStatusUpdateSuccess(req, beforeObj, afterObj, headerInfo, startTime))
                                                        .subscribe();
                                            }
                                            return Mono.just(result);
                                        })
                            )
                )
                .onErrorResume(ex -> {
                    if (ex instanceof BusinessException) {
                        return Mono.error(ex);
                    }
                    log.error("更新发票申请状态失败: {}", ex.getMessage(), ex);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新发票申请状态失败"));
                });
    }

    /**
     * 校验状态更新请求
     */
    private Mono<Void> validateStatusUpdateRequest(HdsInvoiceApplicationStatusUpdateReq req) {
        return Mono.fromRunnable(() -> {
            // 校验目标状态是否合法
            if (!InvoiceApplicationStatusEnum.isValidCode(req.getTargetStatus())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "无效的目标状态");
            }
            
            // 如果目标状态是驳回，审核备注必填
            if (InvoiceApplicationStatusEnum.REJECTED.getCode().equals(req.getTargetStatus()) 
                && StringUtils.isBlank(req.getReviewRemark())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "驳回时审核备注为必填项");
            }
        });
    }

    /**
     * 在事务中执行状态更新操作
     */
    private Mono<Boolean> executeStatusUpdateInTransaction(HdsInvoiceApplicationStatusUpdateReq req, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                // 查询当前申请记录
                repository.findById(req.getId())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "发票申请记录不存在")))
                        .flatMap(applicationEntity -> {
                            // 保存原始状态，避免在校验过程中被修改
                            Integer originalStatus = applicationEntity.getApplicationStatus();
                            
                            // 校验状态转换是否合法
                            return validateStatusTransition(originalStatus, req.getTargetStatus())
                                    .then(updateApplicationStatus(applicationEntity, req, headerInfo))
                                    .thenReturn(true);
                        })
        );
    }

    /**
     * 校验状态转换是否合法（使用原始状态）
     */
    private Mono<Void> validateStatusTransition(Integer originalStatus, Integer targetStatus) {
        return Mono.fromRunnable(() -> {
            
            InvoiceApplicationStatusEnum currentEnum = InvoiceApplicationStatusEnum.getByCode(originalStatus);
            InvoiceApplicationStatusEnum targetEnum = InvoiceApplicationStatusEnum.getByCode(targetStatus);
            
            if (currentEnum == null || targetEnum == null) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "无效的状态");
            }
            
            // 定义状态转换规则
            boolean isValidTransition = switch (currentEnum) {
                case PENDING ->
                    // 待审核 -> 审核中
                        targetEnum == InvoiceApplicationStatusEnum.AUDITING;
                case AUDITING ->
                    // 审核中 -> 已开票 或 已驳回
                        targetEnum == InvoiceApplicationStatusEnum.INVOICED
                                || targetEnum == InvoiceApplicationStatusEnum.REJECTED;
                case INVOICED ->
                    // 已开票 -> 审核中 (重新处理)
                        targetEnum == InvoiceApplicationStatusEnum.AUDITING;
                case REJECTED ->
                    // 已驳回 -> 待审核 (重新申请) 或 审核中 (重新处理)
                        targetEnum == InvoiceApplicationStatusEnum.PENDING
                                || targetEnum == InvoiceApplicationStatusEnum.AUDITING;
            };

            if (!isValidTransition) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, 
                    String.format("不允许从状态[%s]转换为[%s]", currentEnum.getDesc(), targetEnum.getDesc()));
            }
        });
    }

    /**
     * 更新申请状态
     */
    private Mono<HdsInvoiceApplyEntity> updateApplicationStatus(HdsInvoiceApplyEntity applicationEntity, 
                                                                       HdsInvoiceApplicationStatusUpdateReq req, 
                                                                       HeaderUtils.HeaderInfo headerInfo) {
        // 保存原始状态
        Integer originalStatus = applicationEntity.getApplicationStatus();
        
        // 更新基本状态信息
        applicationEntity.setApplicationStatus(req.getTargetStatus());
        
        // 根据目标状态设置相应字段
        InvoiceApplicationStatusEnum targetEnum = InvoiceApplicationStatusEnum.getByCode(req.getTargetStatus());
        LocalDateTime now = LocalDateTime.now();
        
        switch (targetEnum) {
            case AUDITING:
                // 开始审核或重新处理
                applicationEntity.setReviewedBy(UserUtils.getUserId(headerInfo))
                        .setReviewedByName(UserUtils.getUserName(headerInfo))
                        .setReviewedAt(now);
                break;
                
            case INVOICED:
                // 审核通过，已开票
                applicationEntity.setReviewedBy(UserUtils.getUserId(headerInfo))
                        .setReviewedByName(UserUtils.getUserName(headerInfo))
                        .setReviewedAt(now)
                        .setInvoicedAt(now);
                
                // 设置发票信息（如果提供）
                if (StringUtils.isNotBlank(req.getInvoiceNo())) {
                    applicationEntity.setInvoiceNo(req.getInvoiceNo());
                }
                if (StringUtils.isNotBlank(req.getInvoiceCode())) {
                    applicationEntity.setInvoiceCode(req.getInvoiceCode());
                }
                if (StringUtils.isNotBlank(req.getInvoiceUrl())) {
                    applicationEntity.setInvoiceUrl(req.getInvoiceUrl());
                }
                break;
                
            case REJECTED:
                // 审核驳回
                applicationEntity.setReviewedBy(UserUtils.getUserId(headerInfo))
                        .setReviewedByName(UserUtils.getUserName(headerInfo))
                        .setReviewedAt(now)
                        .setReviewRemark(req.getReviewRemark());
                break;
        }
        
        // 设置审计信息
        AuditFieldsUtils.setAuditFields(applicationEntity, headerInfo, false);
        
        return repository.save(applicationEntity)
                .doOnSuccess(entity -> log.info("成功更新发票申请状态: id={}, status={}", entity.getId(), targetEnum.getDesc()));
    }

    /**
     * 记录创建发票申请成功日志
     */
    private Mono<Void> recordCreateSuccess(HdsInvoiceApplicationCreateReq req, 
                                         HdsInvoiceApplyEntity afterObj, 
                                         HeaderUtils.HeaderInfo headerInfo, 
                                         LocalDateTime startTime) {
        try {
            LoggingContext context = LoggingContext.builder()
                    .oprParams(req)
                    .businessId(afterObj != null ? afterObj.getId() : null)
                    .oprTime(startTime)
                    .businessType(BussinessTypeEnum.INVOICE)
                    .operationType(OperationTypeEnum.ADD)
                    .methodName("create")
                    .headerInfo(headerInfo)
                    .oprResult("SUCCESS")
                    .beforeObj(null)
                    .afterObj(afterObj)
                    .oprContent("创建发票申请")
                    .executionTime(java.time.Duration.between(startTime, LocalDateTime.now()).toMillis())
                    .build();

            return operationLogUtil.recordSuccess(context)
                    .onErrorResume(e -> {
                        log.error("记录创建发票申请成功日志失败: {}", e.getMessage(), e);
                        return Mono.empty();
                    })
                    .then();
        } catch (Exception e) {
            log.error("创建操作日志上下文失败: {}", e.getMessage(), e);
            return Mono.empty();
        }
    }


    /**
     * 记录修改发票申请成功日志
     */
    private Mono<Void> recordUpdateSuccess(HdsInvoiceApplicationUpdateReq req, 
                                         HdsInvoiceApplyEntity beforeObj, 
                                         HdsInvoiceApplyEntity afterObj, 
                                         HeaderUtils.HeaderInfo headerInfo, 
                                         LocalDateTime startTime) {
        try {
            LoggingContext context = LoggingContext.builder()
                    .oprParams(req)
                    .businessId(afterObj != null ? afterObj.getId() : (beforeObj != null ? beforeObj.getId() : null))
                    .oprTime(startTime)
                    .businessType(BussinessTypeEnum.INVOICE)
                    .operationType(OperationTypeEnum.UPDATE)
                    .methodName("update")
                    .headerInfo(headerInfo)
                    .oprResult("SUCCESS")
                    .beforeObj(beforeObj)
                    .afterObj(afterObj)
                    .oprContent("修改发票申请")
                    .executionTime(java.time.Duration.between(startTime, LocalDateTime.now()).toMillis())
                    .build();

            return operationLogUtil.recordSuccess(context)
                    .onErrorResume(e -> {
                        log.error("记录修改发票申请成功日志失败: {}", e.getMessage(), e);
                        return Mono.empty();
                    })
                    .then();
        } catch (Exception e) {
            log.error("创建操作日志上下文失败: {}", e.getMessage(), e);
            return Mono.empty();
        }
    }

    /**
     * 记录取消发票申请成功日志
     */
    private Mono<Void> recordCancelSuccess(HdsInvoiceApplicationCancelReq req, 
                                         HdsInvoiceApplyEntity beforeObj, 
                                         HeaderUtils.HeaderInfo headerInfo, 
                                         LocalDateTime startTime) {
        try {
            LoggingContext context = LoggingContext.builder()
                    .oprParams(req)
                    .businessId(beforeObj != null ? beforeObj.getId() : req.getId())
                    .oprTime(startTime)
                    .businessType(BussinessTypeEnum.INVOICE)
                    .operationType(OperationTypeEnum.DELETE)
                    .methodName("cancel")
                    .headerInfo(headerInfo)
                    .oprResult("SUCCESS")
                    .beforeObj(beforeObj)
                    .afterObj(null)
                    .oprContent("取消发票申请")
                    .executionTime(java.time.Duration.between(startTime, LocalDateTime.now()).toMillis())
                    .build();

            return operationLogUtil.recordSuccess(context)
                    .onErrorResume(e -> {
                        log.error("记录取消发票申请成功日志失败: {}", e.getMessage(), e);
                        return Mono.empty();
                    })
                    .then();
        } catch (Exception e) {
            log.error("创建操作日志上下文失败: {}", e.getMessage(), e);
            return Mono.empty();
        }
    }

    /**
     * 记录状态更新发票申请成功日志
     */
    private Mono<Void> recordStatusUpdateSuccess(HdsInvoiceApplicationStatusUpdateReq req, 
                                               HdsInvoiceApplyEntity beforeObj, 
                                               HdsInvoiceApplyEntity afterObj, 
                                               HeaderUtils.HeaderInfo headerInfo, 
                                               LocalDateTime startTime) {
        try {
            LoggingContext context = LoggingContext.builder()
                    .oprParams(req)
                    .businessId(afterObj != null ? afterObj.getId() : (beforeObj != null ? beforeObj.getId() : null))
                    .oprTime(startTime)
                    .businessType(BussinessTypeEnum.INVOICE)
                    .operationType(OperationTypeEnum.UPDATE)
                    .methodName("updateStatus")
                    .headerInfo(headerInfo)
                    .oprResult("SUCCESS")
                    .beforeObj(beforeObj)
                    .afterObj(afterObj)
                    .oprContent("更新发票申请状态")
                    .executionTime(java.time.Duration.between(startTime, LocalDateTime.now()).toMillis())
                    .build();

            return operationLogUtil.recordSuccess(context)
                    .onErrorResume(e -> {
                        log.error("记录状态更新发票申请成功日志失败: {}", e.getMessage(), e);
                        return Mono.empty();
                    })
                    .then();
        } catch (Exception e) {
            log.error("创建操作日志上下文失败: {}", e.getMessage(), e);
            return Mono.empty();
        }
    }
}