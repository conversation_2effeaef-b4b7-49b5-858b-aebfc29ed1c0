package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/6/16 16:31
 * @Description：PackageRecommendReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PackageRecommendReq {

    /**
     * 套餐code
     */
    private String packageCode;

    /**
     * 是否推荐：1-是，0-否
     */
    private Integer isRecommend;
}
