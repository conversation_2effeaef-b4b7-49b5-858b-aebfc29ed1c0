package com.wormhole.hotelds.admin.controller;

import com.wormhole.hotelds.admin.model.vo.HdsPayRecordVO;
import com.wormhole.hotelds.admin.model.req.HdsPayRecordSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsPayRecordDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsPayRecordSearchReq;
import com.wormhole.hotelds.admin.service.HdsPayRecordService;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 18:00:08
 * @Description: HdsPayRecord
 */
@RestController
@RequestMapping("/hdspayrecord")
public class HdsPayRecordController {

    @Resource
    private HdsPayRecordService service;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody HdsPayRecordSaveReq hdsPayRecordSaveReq) {
        return service.create(hdsPayRecordSaveReq).flatMap(Result::success);
    }

    /**
     * 更新记录
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HdsPayRecordSaveReq hdsPayRecordSaveReq) {
        return service.update(hdsPayRecordSaveReq).flatMap(Result::success);
    }

    /**
     * 删除记录
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody HdsPayRecordDeleteReq hdsPayRecordDeleteReq) {
        return service.delete(hdsPayRecordDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<List<HdsPayRecordVO>>> search(@RequestBody HdsPayRecordSearchReq hdsPayRecordSearchReq) {
        return service.search(hdsPayRecordSearchReq).flatMap(Result::success);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<HdsPayRecordVO>> findById(@PathVariable("id") Long id) {
        return service.findById(id).flatMap(Result::success);
    }
}