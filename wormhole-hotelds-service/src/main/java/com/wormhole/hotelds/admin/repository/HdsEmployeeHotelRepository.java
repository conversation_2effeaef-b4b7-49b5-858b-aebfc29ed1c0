package com.wormhole.hotelds.admin.repository;

import aj.org.objectweb.asm.commons.Remapper;
import com.wormhole.hotelds.core.model.entity.*;
import io.reactivex.Flowable;
import org.springframework.data.repository.reactive.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface HdsEmployeeHotelRepository extends ReactiveCrudRepository<HdsEmployeeHotelEntity, Integer> {

    Mono<Long> countByHotelCode(String hotelCode);

    Flux<HdsEmployeeHotelEntity> findByEmployeeIdAndHotelCodeIn(Integer employeeId, Collection<String> hotelCodes);

    Mono<Long> countByEmployeeIdAndHotelCode(Integer employeeId, String hotelCode);

    Flux<HdsEmployeeHotelEntity> findByHotelCodeIn(Collection<String> hotelCodes);

    Flux<HdsEmployeeHotelEntity> findByHotelCode(String hotelCode);

    Mono<HdsEmployeeHotelEntity> findByEmployeeId(Integer employeeId);

    Flux<HdsEmployeeHotelEntity> findByHotelCodeAndStatus(String hotelCode, Integer status);

    Mono<HdsEmployeeHotelEntity> findByEmployeeIdAndHotelCode(Integer employeeId, String hotelCode);

    Flux<HdsEmployeeHotelEntity> findByHotelCodeAndRoleCode(String hotelCode, String roleCode);
}
