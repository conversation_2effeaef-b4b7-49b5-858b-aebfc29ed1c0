package com.wormhole.hotelds.admin.service;

import com.wormhole.hotelds.admin.model.entity.HdsThemeConfigEntity;
import com.wormhole.hotelds.admin.model.vo.HdsThemeConfigVO;
import com.wormhole.hotelds.admin.model.req.HdsThemeConfigSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsThemeConfigDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsThemeConfigSearchReq;
import com.wormhole.hotelds.admin.repository.HdsThemeConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:59:06
 * @Description: HdsThemeConfig
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HdsThemeConfigService {

    private final HdsThemeConfigRepository repository;
    private final R2dbcEntityTemplate template;

    /**
     * 创建记录
     */
    public Mono<Boolean> create(HdsThemeConfigSaveReq req) {
        return repository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 更新记录
     */
    public Mono<Boolean> update(HdsThemeConfigSaveReq req) {
        return repository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 删除记录
     */
    public Mono<Boolean> delete(HdsThemeConfigDeleteReq req) {
        return repository.deleteById(req.getId())
                .thenReturn(true);
    }

    /**
     * 搜索列表
     */
    public Mono<List<HdsThemeConfigVO>> search(HdsThemeConfigSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        return template.select(HdsThemeConfigEntity.class)
                .matching(Query.query(criteria))
                .all()
                .map(this::convertToVO)
                .collectList();
    }

    /**
     * 根据ID查询
     */
    public Mono<HdsThemeConfigVO> findById(Long id) {
        return repository.findById(id)
                .map(this::convertToVO);
    }

    private Criteria buildSearchCriteria(HdsThemeConfigSearchReq req) {
        Criteria criteria = Criteria.empty();
        // TODO: 根据实际查询条件构建Criteria
        return criteria;
    }

    private HdsThemeConfigEntity convertToEntity(HdsThemeConfigSaveReq req) {
        HdsThemeConfigEntity entity = new HdsThemeConfigEntity();
        // TODO: 实现请求对象到实体的转换
        return entity;
    }

    private HdsThemeConfigVO convertToVO(HdsThemeConfigEntity entity) {
        HdsThemeConfigVO vo = new HdsThemeConfigVO();
        // TODO: 实现实体到VO的转换
        return vo;
    }
}