package com.wormhole.hotelds.admin.service;

import com.wormhole.common.result.Result;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.TaskStatusEnum;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.req.PluginHotelReq;
import com.wormhole.hotelds.excel.TaskTypeEnum;
import com.wormhole.hotelds.util.LinkDataHotelExtractorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;

/**
 * @Author：flx
 * @Date：2025/8/19 09:23
 * @Description：解析携程链接
 */
@Slf4j
@Service
public class CtripLinkParseService {

    @Resource
    private TaskService taskService;

    @Resource
    private HotelDsApiClient hotelDsApiClient;

    /**
     * 异步执行携程链接解析任务
     *
     * @param taskId    任务ID
     * @param ctripUrl  携程链接URL
     * @param hotelCode 门店编码
     */
    @Async
    public void executeCtripLinkTaskAsync(Integer taskId, String ctripUrl, String hotelCode, String hotelName) {
        CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("开始执行携程链接解析任务: taskId={}, url={}", taskId, ctripUrl);

                        // 更新任务状态为进行中
                        taskService.updateTaskStatus(taskId, TaskStatusEnum.PROCESSING, "正在解析携程链接数据...");

                        // 调用LinkDataHotelExtractor解析携程链接
                        PluginHotelReq hotelData = LinkDataHotelExtractorUtils.extractHotelData(ctripUrl, hotelCode);

                        if (hotelData != null) {
                            if (!StringUtils.equals(hotelName, hotelData.getHotelName())) {
                                // 填的门店名称和携程门店名称不一样，不保存数据
                                taskService.updateTaskStatus(taskId, TaskStatusEnum.FAILED,
                                        String.format("携程链接解析成功，酒店数据同步失败，请求的酒店名称%s 携程的酒店名称: %s", hotelName, hotelData.getHotelName()));
                            }

                            // 解析成功，处理酒店数据
                            processHotelData(hotelCode, hotelData)
                                    .subscribe(
                                            result -> {
                                                if (result.isSuccess()) {
                                                    // 数据同步成功，更新任务状态为成功
                                                    taskService.updateTaskStatus(taskId, TaskStatusEnum.SUCCESS,
                                                            String.format("携程链接解析成功，酒店数据同步完成，酒店名称: %s", hotelData.getHotelName()));
                                                    log.info("携程链接解析任务执行成功: taskId={}, hotelName={}", taskId, hotelData.getHotelName());
                                                } else {
                                                    // 数据同步失败，但解析成功
                                                    taskService.updateTaskStatus(taskId, TaskStatusEnum.FAILED,
                                                            "携程链接解析成功，但数据同步失败");
                                                    log.error("携程数据同步失败: taskId={}, hotelName={}, message={}",
                                                            taskId, hotelData.getHotelName(), "");
                                                }
                                            },
                                            error -> {
                                                // 处理数据时发生异常
                                                taskService.updateTaskStatus(taskId, TaskStatusEnum.FAILED,
                                                        String.format("携程链接解析成功，但数据处理异常: %s", error.getMessage()));
                                                log.error("携程数据处理异常: taskId={}, hotelName={}, error={}",
                                                        taskId, hotelData.getHotelName(), error.getMessage(), error);
                                            }
                                    );
                        } else {
                            // 解析失败
                            taskService.updateTaskStatus(taskId, TaskStatusEnum.FAILED, "携程链接解析失败，无法获取酒店数据");
                            log.error("携程链接解析任务执行失败: taskId={}, url={}", taskId, ctripUrl);
                        }

                        return true;
                    } catch (Exception e) {
                        log.error("携程链接解析任务执行异常: taskId={}, error={}", taskId, e.getMessage(), e);
                        taskService.updateTaskStatus(taskId, TaskStatusEnum.FAILED, "任务执行异常: " + e.getMessage());
                        return false;
                    }
                }, CompletableFuture.delayedExecutor(1, java.util.concurrent.TimeUnit.SECONDS))
                .exceptionally(throwable -> {
                    log.error("携程链接解析任务异步执行异常: taskId={}", taskId, throwable);
                    taskService.updateTaskStatus(taskId, TaskStatusEnum.FAILED, "异步执行异常: " + throwable.getMessage());
                    return false;
                });
    }

    /**
     * 处理解析得到的酒店数据
     *
     * @param hotelCode 门店编码
     * @param hotelData 酒店数据DTO
     * @return 处理结果
     */
    private Mono<Result<Boolean>> processHotelData(String hotelCode, PluginHotelReq hotelData) {
        try {
            log.info("开始处理携程酒店数据: hotelCode={}, ctripHotelName={}", hotelCode, hotelData.getHotelName());

            return hotelDsApiClient.hotelInfoSync(hotelData, new HashMap<>())
                    .onErrorResume(e -> {
                        log.error("处理携程酒店数据异常: hotelCode={}, error={}", hotelCode, e.getMessage(), e);
                        return Result.failed("HOTEL_SYNC_FAILED", "处理携程酒店数据失败: " + e.getMessage());
                    });
        } catch (Exception e) {
            log.error("处理携程酒店数据失败: hotelCode={}, error={}", hotelCode, e.getMessage(), e);
            return Result.failed("HOTEL_SYNC_FAILED", "处理携程酒店数据失败: " + e.getMessage());
        }
    }
}