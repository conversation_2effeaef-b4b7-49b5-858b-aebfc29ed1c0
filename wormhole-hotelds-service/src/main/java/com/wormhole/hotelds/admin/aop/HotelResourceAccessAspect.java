package com.wormhole.hotelds.admin.aop;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.annotation.RequireHotelAccess;
import com.wormhole.hotelds.admin.service.ResourceAccessValidationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 酒店资源访问权限切面
 * 用于拦截需要进行酒店资源访问权限校验的方法，防止水平越权
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class HotelResourceAccessAspect {

    @Resource
    private ResourceAccessValidationService resourceAccessValidationService;

    /**
     * 定义切点 - 拦截所有标记了RequireHotelAccess注解的方法
     */
    @Pointcut("@annotation(com.wormhole.hotelds.admin.annotation.RequireHotelAccess)")
    public void resourceAccessPointcut() {
    }
    
    /**
     * 从方法参数中获取资源ID
     *
     * @param args              方法参数
     * @param method            方法对象
     * @param requireHotelAccess 注解对象
     * @return 资源ID对象
     */
    private Object getResourceIdFromArgs(Object[] args, Method method, RequireHotelAccess requireHotelAccess) {
        // 如果指定了资源ID参数名，则根据参数名获取
        String resourceIdParam = requireHotelAccess.resourceIdParam();
        if (StringUtils.isNotBlank(resourceIdParam)) {
            Parameter[] parameters = method.getParameters();
            for (int i = 0; i < parameters.length; i++) {
                if (resourceIdParam.equals(parameters[i].getName()) && i < args.length) {
                    return args[i];
                }
            }
        }
        
        // 如果没有指定资源ID参数名，则使用第一个参数作为资源ID
        return args.length > 0 ? args[0] : null;
    }

    /**
     * 环绕通知 - 在目标方法执行前后进行权限校验
     *
     * @param joinPoint 连接点
     * @return 目标方法的返回值
     * @throws Throwable 可能抛出的异常
     */
    @Around("resourceAccessPointcut() && @annotation(requireHotelAccess)")
    private Object around(ProceedingJoinPoint joinPoint, RequireHotelAccess requireHotelAccess) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String methodName = method.getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();

        log.debug("拦截到需要权限验证的方法: {}.{}", className, methodName);

        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return proceedReactive(joinPoint);
        }

        Object resourceId = getResourceIdFromArgs(args, method, requireHotelAccess);
        if (resourceId == null) {
            log.warn("无法获取资源ID，方法: {}.{}", className, methodName);
            return proceedReactive(joinPoint);
        }
        // 返回响应式流，保持与目标方法一致
        return Mono.defer(() -> validateAccessAndProceed(joinPoint, requireHotelAccess, resourceId));
    }

    private Mono<Object> validateAccessAndProceed(ProceedingJoinPoint joinPoint, RequireHotelAccess requireHotelAccess, Object resourceId) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    String userId = headerInfo.getUserId();
                    if (StringUtils.isBlank(userId)) {
                        return Mono.error(new BusinessException(ResultCode.UNAUTHORIZED, "未获取到用户信息"));
                    }

                    return validateAccess(requireHotelAccess, Integer.parseInt(userId), resourceId);
                })
                .flatMap(hasAccess -> {
                    if (Boolean.TRUE.equals(hasAccess)) {
                        return executeOriginalMethod(joinPoint);
                    } else {
                        return Mono.error(new BusinessException(ResultCode.FORBIDDEN, "无权访问该资源"));
                    }
                });
    }

    private Mono<Boolean> validateAccess(RequireHotelAccess requireHotelAccess, int userId, Object resourceId) {
        RequireHotelAccess.ResourceType resourceType = requireHotelAccess.resourceType();
        if (resourceType == RequireHotelAccess.ResourceType.DEVICE) {
            return resourceAccessValidationService.validateDeviceAccess(userId, (Long) resourceId);
        } else if (resourceType == RequireHotelAccess.ResourceType.HOTEL) {
            return resourceAccessValidationService.validateHotelAccess(userId, (Integer) resourceId);
        } else {
            return Mono.just(true); // 未知资源类型暂时放行
        }
    }

    private Mono<Object> executeOriginalMethod(ProceedingJoinPoint joinPoint) {
        return Mono.defer(() -> {
            try {
                // 执行目标方法并返回结果
                Object result = joinPoint.proceed();
                if (result instanceof Mono) {
                    return (Mono<Object>) result;
                } else {
                    return Mono.justOrEmpty(result);
                }
            } catch (Throwable e) {
                return Mono.error(e);
            }
        });
    }

    private Object proceedReactive(ProceedingJoinPoint joinPoint) {
        try {
            Object result = joinPoint.proceed();
            if (result instanceof Mono) {
                return result;
            } else {
                return Mono.justOrEmpty(result);
            }
        } catch (Throwable e) {
            return Mono.error(e);
        }
    }
}