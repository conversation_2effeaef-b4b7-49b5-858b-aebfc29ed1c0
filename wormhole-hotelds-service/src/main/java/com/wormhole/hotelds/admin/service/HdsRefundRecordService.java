package com.wormhole.hotelds.admin.service;

import com.wormhole.hotelds.admin.model.entity.HdsRefundRecordEntity;
import com.wormhole.hotelds.admin.model.vo.HdsRefundRecordVO;
import com.wormhole.hotelds.admin.model.req.HdsRefundRecordSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsRefundRecordDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsRefundRecordSearchReq;
import com.wormhole.hotelds.admin.repository.HdsRefundRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 18:00:26
 * @Description: HdsRefundRecord
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HdsRefundRecordService {

    private final HdsRefundRecordRepository repository;
    private final R2dbcEntityTemplate template;

    /**
     * 创建记录
     */
    public Mono<Boolean> create(HdsRefundRecordSaveReq req) {
        return repository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 更新记录
     */
    public Mono<Boolean> update(HdsRefundRecordSaveReq req) {
        return repository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 删除记录
     */
    public Mono<Boolean> delete(HdsRefundRecordDeleteReq req) {
        return repository.deleteById(req.getId())
                .thenReturn(true);
    }

    /**
     * 搜索列表
     */
    public Mono<List<HdsRefundRecordVO>> search(HdsRefundRecordSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        return template.select(HdsRefundRecordEntity.class)
                .matching(Query.query(criteria))
                .all()
                .map(this::convertToVO)
                .collectList();
    }

    /**
     * 根据ID查询
     */
    public Mono<HdsRefundRecordVO> findById(Long id) {
        return repository.findById(id)
                .map(this::convertToVO);
    }

    private Criteria buildSearchCriteria(HdsRefundRecordSearchReq req) {
        Criteria criteria = Criteria.empty();
        // TODO: 根据实际查询条件构建Criteria
        return criteria;
    }

    private HdsRefundRecordEntity convertToEntity(HdsRefundRecordSaveReq req) {
        HdsRefundRecordEntity entity = new HdsRefundRecordEntity();
        // TODO: 实现请求对象到实体的转换
        return entity;
    }

    private HdsRefundRecordVO convertToVO(HdsRefundRecordEntity entity) {
        HdsRefundRecordVO vo = new HdsRefundRecordVO();
        // TODO: 实现实体到VO的转换
        return vo;
    }
}