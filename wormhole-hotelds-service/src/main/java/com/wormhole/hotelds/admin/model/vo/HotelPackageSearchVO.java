package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/16 17:18
 * @Description：HotelPackageSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelPackageSearchVO {

    /**
     * 门店套餐ID
     */
    private Integer id;

    /**
     * 门店套餐编码
     */
    private String hotelPackageCode;

    /**
     * 标准套餐编码
     */
    private String packageCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 付费方式：0-按房间数量收费，1-按门店收费
     */
    private Integer paymentMode;

    /**
     * 优惠方式：0-折扣，1-一口价
     */
    private Integer discountMode;
    /**
     * 状态：1-启用，0-停用
     */
    private Integer status;

    /**
     * 房间数
     */
    private Integer totalRoom;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 产品列表
     */
    private List<HotelPackageProductVO> products;

    /**
     * 门店套餐产品VO
     */
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelPackageProductVO {

        /**
         * 产品ID
         */
        private Integer productId;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * 定价信息
         */
        private List<HotelProductPricingVO> pricing;
    }

    /**
     * 门店产品定价VO
     */
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelProductPricingVO {

        /**
         * 周期类型：1-月度，2-季度，3-年度
         */
        private Integer periodType;

        /**
         * 门市价
         */
        private String marketPrice;

        /**
         * 优惠价
         */
        private String discountPrice;

        /**
         * 折扣比例
         */
        private String discountRate;

        /**
         * 最终价格
         */
        private String finalPrice;
    }
}
