package com.wormhole.hotelds.admin.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author：flx
 * @Date：2025/4/11 11:52
 * @Description：DeviceDistributionDTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDistributionDTO {

    /**
     * 设备ID
     */
    private Long id;

    /**
     * 设备编号
     */
    private String deviceId;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 设备类型
     */
    private String deviceAppType;

    /**
     * 设备状态
     */
    private Integer deviceStatus;

    /**
     * 楼栋
     */
    private String block;

    /**
     * 楼层
     */
    private String area;

    /**
     * 位置名称
     */
    private String positionName;

    private Integer appTypeSortOrder;
    private Integer blockFloorSortOrder;
    private Integer positionSortOrder;
}
