package com.wormhole.hotelds.admin.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 需要酒店访问权限验证的注解
 * 标记在方法上，表示该方法需要进行酒店资源访问权限验证
 * 
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireHotelAccess {
    
    /**
     * 资源类型
     */
    ResourceType resourceType() default ResourceType.AUTO;
    
    /**
     * 资源ID参数名
     * 默认为空，表示使用方法的第一个参数作为资源ID
     */
    String resourceIdParam() default "";
    
    /**
     * 资源类型枚举
     */
    enum ResourceType {
        /**
         * 自动判断资源类型
         */
        AUTO,
        
        /**
         * 酒店资源
         */
        HOTEL,
        
        /**
         * 设备资源
         */
        DEVICE
    }
}