package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/5/15 13:58
 * @Description：AI 产品类型枚举类
 */
@AllArgsConstructor
@Getter
public enum AiProductTypeEnum {

    AI_CUSTOMER("AI_CUSTOMER", "AI客服"),
    OTA_AGENT("OTA_AGENT", "OTA智能体");

    private final String code;
    private final String description;

    /**
     * 根据 code 获取对应的枚举值
     *
     * @param code AI 产品类型的 code
     * @return 对应的枚举值，如果没有找到则返回 null
     */
    public static AiProductTypeEnum fromCode(String code) {
        for (AiProductTypeEnum type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 将多个 code 转换为逗号分隔的描述字符串
     *
     * @param codes 多个 code 的集合
     * @return 描述字符串，例如 "AI客服,OTA智能体"
     */
    public static String toDescriptions(String codes) {
        if (codes == null || codes.isEmpty()) {
            return "";
        }
        String[] codeArray = codes.split(",");
        StringBuilder descriptions = new StringBuilder();
        for (String code : codeArray) {
            AiProductTypeEnum type = fromCode(code.trim());
            if (type != null) {
                if (!descriptions.isEmpty()) {
                    descriptions.append(",");
                }
                descriptions.append(type.getDescription());
            }
        }
        return descriptions.toString();
    }
}

