package com.wormhole.hotelds.admin.repository;

import com.google.common.io.Files;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.model.entity.HdsWechatQrCodeEntity;
import io.reactivex.Flowable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * @Author：flx
 * @Date：2025/4/11 09:40
 * @Description：DevicePositionRepository
 */
@Repository
public interface DevicePositionRepository extends ReactiveCrudRepository<HdsDevicePositionEntity, Long> {

    /**
     * 根据位置编码查询
     *
     * @param positionCode 位置编码
     * @return 是否存在
     */
    Mono<Boolean> existsByPositionCode(@Param("positionCode") String positionCode);

    Flux<HdsDevicePositionEntity> findByHotelCodeAndPositionCodeIn(
            String hotelCode, Collection<String> positionCodes);


    Flux<HdsDevicePositionEntity> findByHotelCodeAndDeviceAppTypeIn(String hotelCode, Set<String> devicesAppTypes);

    Mono<HdsDevicePositionEntity> findByPositionCode(String positionCode);


    Flux<HdsDevicePositionEntity> findByIdIn(List<String> idList);

    Mono<Boolean> existsByHotelCodeAndBlockAndAreaAndPositionName(String hotelCode, String block, String area, String positionName);

    Flux<HdsDevicePositionEntity> findByDeviceAppTypeAndHotelCode(String deviceAppType, String hotelCode);

    Flux<HdsDevicePositionEntity> findByHotelCodeIn(Collection<String> hotelCodes);

    Flux<HdsDevicePositionEntity> findByPositionCodeIn(Collection<String> positionCodes);

    Mono<Boolean> existsByAreaCode(@Param("areaCode") String areaCode);
}
