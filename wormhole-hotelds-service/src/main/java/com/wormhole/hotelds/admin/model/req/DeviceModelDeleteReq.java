package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

import java.io.*;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:56:47
 * @Description: DeviceModel
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceModelDeleteReq implements Serializable {
    
    private static final long serialVersionUID = 1L;

    private Long id;

}