package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.*;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.StatusEnum;
import com.wormhole.hotelds.admin.model.vo.MerchantVO;
import com.wormhole.hotelds.admin.repository.MerchantRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/3/26 10:28
 * @Description：商户服务层
 */
@Slf4j
@Service
public class MerchantService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private MerchantRepository merchantRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodePoolManager codePoolManager;

    /**
     * 创建商户
     */
    public Mono<Boolean> create(MerchantSaveReq merchantSaveReq) {
        ValidatorUtils.validateMerchantSaveReq(merchantSaveReq);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionCreate(merchantSaveReq, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "merchant create error")))
                );
    }

    /**
     * 事务创建
     */
    private Mono<Boolean> executeTransferWithinTransactionCreate(MerchantSaveReq merchantSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        return codePoolManager.getCodeFromPool(BussinessTypeEnum.MERCHANT.getBusinessType())
                .flatMap(codeInfo -> transactionalOperator.transactional(
                                merchantRepository.save(buildHdsMerchantEntity(merchantSaveReq, headerInfo, codeInfo)))
                        .doOnSuccess(v -> log.info("merchant create success: id={}, username={}", merchantSaveReq.getId(), headerInfo.getUsername()))
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
                );
    }

    /**
     * 删除商户
     */
    public Mono<Boolean> delete(MerchantDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(userInfo -> executeTransferWithinTransactionDelete(userInfo, req.getId())
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "merchant delete error")))
                );
    }

    /**
     * 事务删除
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(HeaderUtils.HeaderInfo headerInfo, Integer id) {
        return transactionalOperator.transactional(
                merchantRepository.deleteById(id)
                        .doOnSuccess(v -> log.info("merchant delete success: id={}, username={}", id, headerInfo.getUsername()))
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 更新商户
     */
    public Mono<Boolean> update(MerchantSaveReq merchantSaveReq) {
        Preconditions.checkArgument(Objects.nonNull(merchantSaveReq), "merchantSaveReq must not be null");
        Preconditions.checkArgument(Objects.nonNull(merchantSaveReq.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionUpdate(merchantSaveReq, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "merchant update error")))
                );
    }

    /**
     * 事务更新
     */
    private Mono<Boolean> executeTransferWithinTransactionUpdate(MerchantSaveReq merchantSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                merchantRepository.findById(merchantSaveReq.getId())
                        .flatMap(existingMerchant -> {
                            updateHdsMerchantEntity(existingMerchant, merchantSaveReq, headerInfo);
                            return merchantRepository.save(existingMerchant);
                        })
                        .doOnSuccess(v -> log.info("merchant update success: id={}, username={}", merchantSaveReq.getId(), headerInfo.getUsername()))
                        .map(v -> true)
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 查询商户列表
     *
     * @return
     */
    public Mono<List<MerchantVO>> queryList() {
        Criteria criteria = Criteria.where(HdsMerchantFieldEnum.status.name()).is(StatusEnum.VALID.getCode());
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.select(query, HdsMerchantEntity.class)
                .collectList()
                .map(merchantEntities -> merchantEntities.stream()
                        .map(MerchantVO::toVo)
                        .toList());

    }

    /**
     * 根据ID查询商户
     */
    public Mono<MerchantVO> findById(Integer id) {
        Preconditions.checkArgument(Objects.nonNull(id), "id must not be null");
        return merchantRepository.findById(id)
                .map(MerchantVO::toVo)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "merchant not found")));
    }

    /**
     * 构建商户实体
     */
    private HdsMerchantEntity buildHdsMerchantEntity(MerchantSaveReq req, HeaderUtils.HeaderInfo headerInfo, String code) {
        HdsMerchantEntity merchantEntity = new HdsMerchantEntity();
        BeanUtils.copyProperties(req, merchantEntity);
        merchantEntity.setMerchantId(code);
        merchantEntity.setStatus(StatusEnum.VALID.getCode());

        merchantEntity.setCreatedBy(UserUtils.getUserId(headerInfo));
        merchantEntity.setCreatedByName(UserUtils.getUserName(headerInfo));
        merchantEntity.setCreatedAt(LocalDateTime.now());
        return merchantEntity;
    }

    /**
     * 构建商户实体
     *
     * @param merchantName 商户名称
     * @param code         商户编码
     * @param headerInfo   请求头信息
     * @return 返回商户实体
     */
    private HdsMerchantEntity buildHdsMerchantEntity(String merchantName, String merchantShortName, String code, HeaderUtils.HeaderInfo headerInfo) {
        HdsMerchantEntity merchantEntity = new HdsMerchantEntity();
        merchantEntity.setMerchantId(code);
        merchantEntity.setMerchantName(merchantName);
        merchantEntity.setSubjectName(merchantShortName);
        merchantEntity.setCreatedBy(UserUtils.getUserId(headerInfo));
        merchantEntity.setCreatedByName(UserUtils.getUserName(headerInfo));
        merchantEntity.setCreatedAt(LocalDateTime.now());
        merchantEntity.setRowStatus(1);
        return merchantEntity;
    }

    /**
     * 更新商户实体
     */
    private void updateHdsMerchantEntity(HdsMerchantEntity existingMerchant, MerchantSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        if (StringUtils.isNotBlank(req.getMerchantName())) {
            existingMerchant.setMerchantName(req.getMerchantName());
        }
        if (StringUtils.isNotBlank(req.getSubjectName())) {
            existingMerchant.setSubjectName(req.getSubjectName());
        }
        if (Objects.nonNull(req.getType())) {
            existingMerchant.setType(req.getType());
        }
        if (StringUtils.isNotBlank(req.getCountryCode())) {
            existingMerchant.setCountryCode(req.getCountryCode());
        }
        if (StringUtils.isNotBlank(req.getCountryName())) {
            existingMerchant.setCountryName(req.getCountryName());
        }
        if (StringUtils.isNotBlank(req.getProvinceCode())) {
            existingMerchant.setProvinceCode(req.getProvinceCode());
        }
        if (StringUtils.isNotBlank(req.getProvinceName())) {
            existingMerchant.setProvinceName(req.getProvinceName());
        }
        if (StringUtils.isNotBlank(req.getCityCode())) {
            existingMerchant.setCityCode(req.getCityCode());
        }
        if (StringUtils.isNotBlank(req.getCityName())) {
            existingMerchant.setCityName(req.getCityName());
        }
        if (StringUtils.isNotBlank(req.getDistrictCode())) {
            existingMerchant.setDistrictCode(req.getDistrictCode());
        }
        if (StringUtils.isNotBlank(req.getDistrictName())) {
            existingMerchant.setDistrictName(req.getDistrictName());
        }
        if (StringUtils.isNotBlank(req.getAddress())) {
            existingMerchant.setAddress(req.getAddress());
        }
        if (StringUtils.isNotBlank(req.getMerchantPhone())) {
            existingMerchant.setMerchantPhone(req.getMerchantPhone());
        }
        if (StringUtils.isNotBlank(req.getMerchantEmail())) {
            existingMerchant.setMerchantEmail(req.getMerchantEmail());
        }
        if (StringUtils.isNotBlank(req.getMerchantContacts())) {
            existingMerchant.setMerchantContacts(req.getMerchantContacts());
        }
        if (StringUtils.isNotBlank(req.getMerchantLicenseUrl())) {
            existingMerchant.setMerchantLicenseUrl(req.getMerchantLicenseUrl());
        }
        if (StringUtils.isNotBlank(req.getAccountName())) {
            existingMerchant.setAccountName(req.getAccountName());
        }
        if (StringUtils.isNotBlank(req.getAccountBank())) {
            existingMerchant.setAccountBank(req.getAccountBank());
        }
        if (StringUtils.isNotBlank(req.getAccountSubBank())) {
            existingMerchant.setAccountSubBank(req.getAccountSubBank());
        }
        if (StringUtils.isNotBlank(req.getAccountCardNumber())) {
            existingMerchant.setAccountCardNumber(req.getAccountCardNumber());
        }
        if (StringUtils.isNotBlank(req.getAccountReserveMobile())) {
            existingMerchant.setAccountReserveMobile(req.getAccountReserveMobile());
        }
        if (StringUtils.isNotBlank(req.getAccountCashMobile())) {
            existingMerchant.setAccountCashMobile(req.getAccountCashMobile());
        }
        existingMerchant.setUpdatedBy(headerInfo.getUserId());
        existingMerchant.setUpdatedByName(headerInfo.getUsername());
        existingMerchant.setUpdatedAt(LocalDateTime.now());
    }

    public Mono<HdsMerchantEntity> save(HdsMerchantEntity hdsMerchantEntity) {
        return merchantRepository.save(hdsMerchantEntity);
    }


    /**
     * 查询商户是否存在，如果不存在则创建一个新的商户对象（创建场景）
     * 根据参数组合判断唯一性:
     * 1. 如果merchantName不为空，则以merchantName判断
     * 2. 如果merchantShortName不为空，则以merchantShortName判断
     * 3. 如果两者都不为空，则以两个组合在一起判断
     *
     * @param merchantName      商户名称
     * @param merchantShortName 商户简称
     * @param headerInfo        请求头信息
     * @return 返回商户实体
     */
    public Mono<HdsMerchantEntity> getOrPrepareMerchantForCreate(String merchantName, String merchantShortName, HeaderUtils.HeaderInfo headerInfo) {
        if (StringUtils.isBlank(merchantName) && StringUtils.isBlank(merchantShortName)) {
            return Mono.just(new HdsMerchantEntity());
        }

        Mono<HdsMerchantEntity> existingMerchantMono;

        if (StringUtils.isNotBlank(merchantName) && StringUtils.isNotBlank(merchantShortName)) {
            existingMerchantMono = merchantRepository.findByMerchantNameAndSubjectName(merchantName, merchantShortName);
        } else if (StringUtils.isNotBlank(merchantName)) {
            existingMerchantMono = merchantRepository.findByMerchantName(merchantName);
        } else {
            existingMerchantMono = merchantRepository.findBySubjectName(merchantShortName);
        }

        return existingMerchantMono
                .switchIfEmpty(
                        // 商户不存在，创建新商户
                        codePoolManager.getCodeFromPool(BussinessTypeEnum.MERCHANT.getBusinessType())
                                .map(code -> buildHdsMerchantEntity(merchantName, merchantShortName, code, headerInfo))
                );
    }

    /**
     * 查询商户是否存在，如果存在则更新商户对象（更新场景）
     *
     * @param merchantName
     * @param merchantShortName
     * @param headerInfo
     * @param existingHotelMono
     * @return
     */
    public Mono<HdsMerchantEntity> getAndUpdateMerchant(String merchantName, String merchantShortName, HeaderUtils.HeaderInfo headerInfo, Mono<HdsHotelInfoEntity> existingHotelMono) {
        return existingHotelMono.flatMap(existingHotel -> {
            if (StringUtils.isBlank(existingHotel.getMerchantId())) {
                if (StringUtils.isNotBlank(merchantName) || StringUtils.isNotBlank(merchantShortName)) {
                    return codePoolManager.getCodeFromPool(BussinessTypeEnum.MERCHANT.getBusinessType())
                            .map(code -> buildHdsMerchantEntity(merchantName, merchantShortName, code, headerInfo));
                } else {
                    return Mono.just(new HdsMerchantEntity());
                }
            } else {
                return merchantRepository.findByMerchantId(existingHotel.getMerchantId())
                        .map(existingMerchant -> {
                            // 更新不为空的字段
                            boolean needUpdate = false;

                            if (StringUtils.isNotBlank(merchantName) &&
                                    !merchantName.equals(existingMerchant.getMerchantName())) {
                                existingMerchant.setMerchantName(merchantName);
                                needUpdate = true;
                            }

                            if (StringUtils.isNotBlank(merchantShortName) &&
                                    !merchantShortName.equals(existingMerchant.getSubjectName())) {
                                existingMerchant.setSubjectName(merchantShortName);
                                needUpdate = true;
                            }

                            // 如果有字段更新，设置更新信息
                            if (needUpdate) {
                                existingMerchant.setUpdatedBy(UserUtils.getUserId(headerInfo));
                                existingMerchant.setUpdatedByName(UserUtils.getUserName(headerInfo));
                                existingMerchant.setUpdatedAt(LocalDateTime.now());
                                return existingMerchant;
                            } else {
                                return new HdsMerchantEntity();
                            }
                        })
                        .switchIfEmpty(
                                // 商户不存在，创建新商户
                                codePoolManager.getCodeFromPool(BussinessTypeEnum.MERCHANT.getBusinessType())
                                        .map(code -> buildHdsMerchantEntity(merchantName, merchantShortName, code, headerInfo))
                        );
            }
        });
    }

    public Mono<HdsMerchantEntity> findByMerchantId(String merchantId) {
        return merchantRepository.findByMerchantId(merchantId);
    }

    /**
     * 根据酒店名称预过滤
     */
    public Mono<Criteria> preFilterByMerchantName(HotelSearchReq hotelSearchReq, Criteria baseCriteria) {
        if (StringUtils.isBlank(hotelSearchReq.getMerchantName())) {
            return Mono.just(baseCriteria);
        }
        String likeWord = "%" + hotelSearchReq.getMerchantName().trim() + "%";

        // 根据酒店名称查询符合条件的酒店编码
        Criteria merchantCriteria = Criteria.where(HdsMerchantFieldEnum.merchant_name.name()).like(likeWord)
                .or(Criteria.where(HdsMerchantFieldEnum.subject_name.name()).like(likeWord));

        return r2dbcEntityTemplate.select(HdsMerchantEntity.class)
                .matching(Query.query(merchantCriteria))
                .all()
                .map(HdsMerchantEntity::getMerchantId)
                .collectList()
                .map(merchantIds -> {
                    if (merchantIds.isEmpty()) {
                        return Criteria.where(HdsHotelInfoFieldEnum.id.name()).is(-1);
                    }
                    // 将酒店编码添加到查询条件中
                    return baseCriteria.and(HdsHotelInfoFieldEnum.merchant_id.name()).in(merchantIds);
                });
    }

    public Mono<Long> searchCount(MerchantSearchReq merchantQueryReq) {
        Criteria baseCriteria = buildBaseCriteria(merchantQueryReq);
        Query query = Query.query(baseCriteria);
        return r2dbcEntityTemplate.count(query, HdsMerchantEntity.class);
    }

    /**
     * 搜索商户列表
     */
    public Mono<List<MerchantVO>> search(MerchantSearchReq merchantQueryReq) {
        Criteria criteria = buildBaseCriteria(merchantQueryReq);

        Sort sort = Sort.by(Sort.Direction.DESC, HdsMerchantFieldEnum.created_at.name());
        Query query = Query.query(criteria)
                .sort(sort)
                .limit(merchantQueryReq.getPageSize())
                .offset((long) (merchantQueryReq.getCurrent() - 1) * merchantQueryReq.getPageSize());
        return r2dbcEntityTemplate.select(query, HdsMerchantEntity.class)
                .collectList()
                .map(merchantEntities -> merchantEntities.stream()
                        .map(MerchantVO::toVo)
                        .toList());
    }

    private Criteria buildBaseCriteria(MerchantSearchReq merchantQueryReq) {
        Criteria criteria = Criteria.empty();
        if (com.alibaba.cloud.commons.lang.StringUtils.isNotBlank(merchantQueryReq.getMerchantName())) {
            criteria = criteria.and(HdsMerchantFieldEnum.merchant_name.name()).like("%" + merchantQueryReq.getMerchantName() + "%");
        }
        if (Objects.nonNull(merchantQueryReq.getType())) {
            criteria = criteria.and(HdsMerchantFieldEnum.type.name()).is(merchantQueryReq.getType());
        }
        if (com.alibaba.cloud.commons.lang.StringUtils.isNotBlank(merchantQueryReq.getCountryCode())) {
            criteria = criteria.and(HdsMerchantFieldEnum.country_code.name()).is(merchantQueryReq.getCountryCode());
        }
        if (com.alibaba.cloud.commons.lang.StringUtils.isNotBlank(merchantQueryReq.getProvinceCode())) {
            criteria = criteria.and(HdsMerchantFieldEnum.province_code.name()).is(merchantQueryReq.getProvinceCode());
        }
        if (com.alibaba.cloud.commons.lang.StringUtils.isNotBlank(merchantQueryReq.getCityCode())) {
            criteria = criteria.and(HdsMerchantFieldEnum.city_code.name()).is(merchantQueryReq.getCityCode());
        }
        if (com.alibaba.cloud.commons.lang.StringUtils.isNotBlank(merchantQueryReq.getDistrictCode())) {
            criteria = criteria.and(HdsMerchantFieldEnum.district_code.name()).is(merchantQueryReq.getDistrictCode());
        }
        if (Objects.nonNull(merchantQueryReq.getStatus())) {
            criteria = criteria.and(HdsMerchantFieldEnum.status.name()).is(merchantQueryReq.getStatus());
        }
        if (com.alibaba.cloud.commons.lang.StringUtils.isNotBlank(merchantQueryReq.getMerchantContacts())) {
            criteria = criteria.and(HdsMerchantFieldEnum.merchant_contacts.name()).like("%" + merchantQueryReq.getMerchantContacts() + "%");
        }
        if (com.alibaba.cloud.commons.lang.StringUtils.isNotBlank(merchantQueryReq.getMerchantPhone())) {
            criteria = criteria.and(HdsMerchantFieldEnum.merchant_phone.name()).like("%" + merchantQueryReq.getMerchantPhone() + "%");
        }
        return criteria;
    }
}
