package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/11 10:38
 * @Description：DeviceDistributionVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
public class DeviceDistributionVO {

    /**
     * 设备类型分布列表
     */
//    private List<DeviceTypeCount> deviceAppTypes;

    /**
     * 设备平面分布
     */
    private List<DeviceTypeDistribution> distributions;

    /**
     * 设备类型分布
     */
    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class DeviceTypeDistribution {
        /**
         * 设备类型（全部设备、AI云电话、AI智能座机）
         */
        private String deviceAppType;

        /**
         * 设备总数
         */
        private Integer deviceCount;

        /**
         * 该类型设备的楼栋分布
         */
        private List<BuildingFloorDevices> buildingFloors;

        // 添加设备类型排序字段
        private Integer appTypeSortOrder;
    }



//    /**
//     * 设备类型统计
//     */
//    @Data
//    @Builder
//    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
//    public static class DeviceTypeCount {
//
//        /**
//         * 设备类型（全部设备、AI云电话、AI智能座机）
//         */
//        private String deviceAppType;
//
//        /**
//         * 设备数量
//         */
//        private Integer deviceCount;
//
//        /**
//         * app类型排序值
//         */
//        private Integer appTypeSortOrder;
//    }

    /**
     * 建筑楼层设备分布
     */
    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class BuildingFloorDevices {
        /**
         * 楼栋名称（A栋、B栋）
         */
        private String buildingName;

        /**
         * 楼层列表
         */
        private List<FloorDevices> floors;

        // 添加楼栋楼层排序字段
        private Integer blockFloorSortOrder;
    }

    /**
     * 楼层设备分布
     */
    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class FloorDevices {

        /**
         * 楼层名称（1层、2层等）
         */
        private String floorName;

        /**
         * 设备数量
         */
        private Integer deviceCount;

        /**
         * 房间设备列表
         */
        private List<RoomDevice> rooms;
    }

    /**
     * 房间设备信息
     */
    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class RoomDevice {

        /**
         * 房间号
         */
        private String roomNumber;

        /**
         * 设备状态（在线、离线、停用）
         */
        private Integer deviceStatus;

        /**
         * 设备ID
         */
        private Long deviceId;

        // 添加位置排序字段
        private Integer positionSortOrder;
    }
}
