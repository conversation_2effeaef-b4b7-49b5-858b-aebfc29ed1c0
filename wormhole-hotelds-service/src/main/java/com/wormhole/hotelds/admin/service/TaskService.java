package com.wormhole.hotelds.admin.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.TaskStatusEnum;
import com.wormhole.hotelds.admin.model.req.TaskRetryReq;
import com.wormhole.hotelds.admin.repository.TaskRepository;
import com.wormhole.hotelds.core.model.entity.HdsTaskEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author：flx
 * @Date：2025/8/19 09:23
 * @Description：门店服务层
 */
@Slf4j
@Service
public class TaskService {

    @Resource
    private CtripLinkParseService ctripLinkParseService;

    @Resource
    private TaskRepository taskRepository;

    /**
     * 创建任务
     *
     * @param businessType
     * @param taskType
     * @param ctripEbkUrl
     * @param hotelCode
     * @param headerInfo
     * @return
     */
    public Mono<HdsTaskEntity> createTask(String businessType,
                                          Integer taskType,
                                          String ctripEbkUrl,
                                          String hotelCode,
                                          String hotelName,
                                          HeaderUtils.HeaderInfo headerInfo) {
        HdsTaskEntity task = new HdsTaskEntity();
        task.setTaskName(generateTaskName(businessType, 3, headerInfo.getUsername()));
        task.setTaskType(taskType);
        task.setStatus(TaskStatusEnum.PENDING.getCode());
        task.setCreatedAt(LocalDateTime.now());
        task.setCreatedBy(headerInfo.getUserId());
        task.setCreatedByName(headerInfo.getUsername());
        task.setMessage("携程链接解析任务创建中");
        task.setBusinessType(businessType);

        Map<String, Object> taskParams = new HashMap<>();
        taskParams.put("ctripEbkUrl", ctripEbkUrl);
        taskParams.put("hotelCode", hotelCode);
        taskParams.put("hotelName", hotelName);
        taskParams.put("source", "web");
        taskParams.put("createdTime", LocalDateTime.now().toString());

        task.setHotelCode(hotelCode);
        task.setParams(JacksonUtils.writeValueAsString(taskParams));
        return taskRepository.save(task);
    }

    /**
     * 生成任务名称
     *
     * @param businessType 业务类型
     * @param taskType     任务类型(1-导入，2-导出 3-解析)
     * @param suffix       附加信息(可选)
     * @return 格式化的任务名称
     */
    public String generateTaskName(String businessType, Integer taskType, String suffix) {
        // 获取业务类型对应的中文名称
        String businessName = getBusinessName(businessType);

        // 获取操作类型
        String operationType = taskType == 1 ? "导入" : taskType == 2 ? "导出" : "解析";

        // 生成时间标识 (格式：yyyyMMddxxx，xxx是序号)
        String timeId = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        return suffix + operationType + businessName + "任务 - " + timeId;
    }

    /**
     * 获取业务类型的中文名称
     */
    private String getBusinessName(String businessType) {
        BussinessTypeEnum byBusinessType = BussinessTypeEnum.getByBusinessType(businessType);
        return Optional.ofNullable(byBusinessType.getBusinessTypeDesc()).orElse("");
    }

    /**
     * 更新任务状态
     *
     * @param taskId  任务ID
     * @param status  状态
     * @param message 消息
     */
    public void updateTaskStatus(Integer taskId, TaskStatusEnum status, String message) {
        try {
            taskRepository.findById(taskId)
                    .flatMap(task -> {
                        task.setStatus(status.getCode());
                        task.setMessage(message);
                        task.setUpdatedAt(LocalDateTime.now());
                        return taskRepository.save(task);
                    })
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe(
                            updatedTask -> log.debug("任务状态更新成功: taskId={}, status={}", taskId, status),
                            error -> log.error("任务状态更新失败: taskId={}, error={}", taskId, error.getMessage())
                    );
        } catch (Exception e) {
            log.error("更新任务状态异常: taskId={}, error={}", taskId, e.getMessage());
        }
    }

    /**
     * 重试携程链接解析任务
     * @param req
     * @return
     */
    public Mono<Boolean> retryCtripParseTask(TaskRetryReq req) {
        Integer originalTaskId = req.getTaskId();
        return taskRepository.findById(originalTaskId)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "原任务不存在")))
                .flatMap(originalTask -> {
                    try {
                        // 解析原任务参数
                        Map<String, Object> originalParams = JacksonUtils.readValue(
                                originalTask.getParams(),
                                new TypeReference<>() {
                                }
                        );

                        String ctripEbkUrl = (String) originalParams.get("ctripEbkUrl");
                        String hotelName = (String) originalParams.get("hotelName");
                        String hotelCode = originalTask.getHotelCode();

                        if (ctripEbkUrl == null || hotelName == null || ctripEbkUrl.trim().isEmpty()) {
                            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                                    "原任务中未找到携程链接URL"));
                        }

                        if (hotelCode == null || hotelCode.trim().isEmpty()) {
                            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                                    "原任务中未找到门店编码"));
                        }


                        log.info("开始重试携程解析任务: originalTaskId={}, ctripUrl={}, hotelCode={}",
                                originalTaskId, ctripEbkUrl, hotelCode);

                        // 获取当前用户信息
                        return HeaderUtils.getHeaderInfo()
                                .flatMap(headerInfo -> {
                                    // 创建重试任务，在任务名称中标注是重试任务
                                    HdsTaskEntity retryTask = new HdsTaskEntity();
                                    retryTask.setTaskName(generateRetryTaskName(originalTask.getTaskName(), headerInfo.getUsername()));
                                    retryTask.setTaskType(originalTask.getTaskType());
                                    retryTask.setStatus(TaskStatusEnum.PENDING.getCode());
                                    retryTask.setCreatedAt(LocalDateTime.now());
                                    retryTask.setCreatedBy(headerInfo.getUserId());
                                    retryTask.setCreatedByName(headerInfo.getUsername());
                                    retryTask.setMessage("重试携程链接解析任务创建中");
                                    retryTask.setBusinessType(originalTask.getBusinessType());
                                    retryTask.setHotelCode(hotelCode);

                                    // 创建新的任务参数，包含原始任务ID
                                    Map<String, Object> retryParams = new HashMap<>();
                                    retryParams.put("ctripEbkUrl", ctripEbkUrl);
                                    retryParams.put("hotelCode", hotelCode);
                                    retryParams.put("hotelName", hotelName);
                                    retryParams.put("source", "retry");
                                    retryParams.put("originalTaskId", originalTaskId);
                                    retryParams.put("createdTime", LocalDateTime.now().toString());

                                    retryTask.setParams(JacksonUtils.writeValueAsString(retryParams));

                                    return taskRepository.save(retryTask)
                                            .map(savedTask -> {
                                                log.info("重试任务创建成功: originalTaskId={}, retryTaskId={}, ctripUrl={}",
                                                        originalTaskId, savedTask.getId(), ctripEbkUrl);

                                                // 异步执行重试任务
                                                ctripLinkParseService.executeCtripLinkTaskAsync(
                                                        savedTask.getId(), ctripEbkUrl, hotelCode, hotelName);

                                                return true;
                                            });
                                });

                    } catch (Exception e) {
                        log.error("解析原任务参数失败: originalTaskId={}, error={}", originalTaskId, e.getMessage(), e);
                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                "解析原任务参数失败: " + e.getMessage()));
                    }
                });
    }

    /**
     * 生成重试任务名称
     *
     * @param originalTaskName 原任务名称
     * @param username         用户名
     * @return 重试任务名称
     */
    private String generateRetryTaskName(String originalTaskName, String username) {
        String timeId = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
        return String.format("[重试]%s - %s - %s", originalTaskName, username, timeId);
    }
}
