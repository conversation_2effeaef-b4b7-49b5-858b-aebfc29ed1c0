package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：yongfeng.chen
 * @Date：2025/06/20 11:26
 * @Description：BillSearchReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BillSearchReq extends QueryCondition {

    /**
     * 流水号（精确搜索）
     */
    private String transactionNo;

    /**
     * 订单编号（精确搜索）
     */
    private String orderNo;

    /**
     * 套餐名称（模糊搜索）
     */
    private String packageName;
    
    /**
     * 账单ID列表
     */
    private List<Integer> billIds;
    
    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 套餐编码（精确搜索）
     */
    private String packageCode;


}
