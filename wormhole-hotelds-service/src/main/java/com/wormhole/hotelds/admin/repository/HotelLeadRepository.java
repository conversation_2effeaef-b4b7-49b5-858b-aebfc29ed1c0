package com.wormhole.hotelds.admin.repository;

import aj.org.objectweb.asm.commons.Remapper;
import com.wormhole.hotelds.core.model.entity.HdsHotelLeadEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * @Author：flx
 * @Date：2025/5/22 13:59
 * @Description：HdsHotelLeadRepository
 */
@Repository
public interface HotelLeadRepository extends ReactiveCrudRepository<HdsHotelLeadEntity, Long> {

    Mono<HdsHotelLeadEntity> findByHotelName(String hotelName);

    Mono<HdsHotelLeadEntity> findByHotelCode(String hotelCode);

    Flux<HdsHotelLeadEntity> findByIdIn(Collection<Long> ids);

    Mono<Boolean> existsByLeadCode(String leadCode);

    Flux<HdsHotelLeadEntity> findByIdInAndStatus(Collection<Long> ids, Integer status);

    Flux<HdsHotelLeadEntity> findByCreatedBy(String createdBy);

    Flux<HdsHotelLeadEntity> findAllByIdIn(Set<Long> hotelLeadIds);
}
