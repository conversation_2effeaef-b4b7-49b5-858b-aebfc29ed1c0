package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/5/23 09:13
 * @Description：HotelLeadSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelLeadSearchReq extends QueryCondition {

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 门店线索id
     */
    private Integer userLeadId;
}
