package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：yongfeng.chen
 * @Date：2025-06-26 15:51:21
 * @Description：账单申请发票状态枚举类 是否已申请发票：0-未申请，1-已申请
 */
@Getter
@AllArgsConstructor
public enum BillInvoiceStatusEnum {

    /**
     * 0-未申请
     */
    UNAPPLIED(0, "未申请"),

    /**
     * 1-已申请
     */
    APPLIED(1, "已申请");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     */
    public static BillInvoiceStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BillInvoiceStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否是合法的状态码
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
