package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.OrderConfirmReq;
import com.wormhole.hotelds.admin.model.req.OrderPayReq;
import com.wormhole.hotelds.admin.model.req.OrderSearchReq;
import com.wormhole.hotelds.admin.model.vo.OrderConfirmVO;
import com.wormhole.hotelds.admin.model.vo.OrderInfoVO;
import com.wormhole.hotelds.admin.model.vo.OrderPayVO;
import com.wormhole.hotelds.admin.model.vo.OrderSearchVO;
import com.wormhole.hotelds.admin.service.OrderService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/6/19 09:17
 * @Description：OrderController
 */
@RestController
@RequestMapping("/order")
public class OrderController {

    @Resource
    private OrderService orderService;

    /**
     * 确认订单信息
     * @param req
     * @return
     */
    @PostMapping("/confirm")
    public Mono<Result<OrderConfirmVO>> confirm(@RequestBody OrderConfirmReq req){
        return orderService.confirmOrder(req).flatMap(Result::success);
    }

    /**
     * 订单支付
     * @param req
     * @return
     */
    @PostMapping("/pay")
    public Mono<Result<OrderPayVO>> pay(@RequestBody OrderPayReq req){
        return orderService.pay(req).flatMap(Result::success);
    }

    /**
     * 订单列表查询
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<OrderSearchVO>>> search(@RequestBody OrderSearchReq req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> Mono.zip(orderService.searchCount(req,headerInfo), orderService.search(req,headerInfo))
                        .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                        .flatMap(Result::success));

    }

    /**
     * 支付成功之后查询订单信息
     */
    @GetMapping("/query_by_order_no")
    public Mono<Result<OrderInfoVO>> queryOrderInfo(@RequestParam("order_no")String orderNo) {
        return orderService.queryOrderInfo(orderNo).flatMap(Result::success);
    }
}
