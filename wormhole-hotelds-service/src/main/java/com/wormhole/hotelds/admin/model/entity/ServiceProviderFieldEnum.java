package com.wormhole.hotelds.admin.model.entity;

/**
* @Author: flx
* @Date: 2025-03-27 17:55:12
* @Description: ServiceProvider
*/
public enum ServiceProviderFieldEnum {
    /**
     * 主键id
     */
    id,

    /**
     * 公司编码
     */
    provider_id,

    /**
     * 公司名称
     */
    provider_name,

    /**
     * 国家编码
     */
    country_code,

    /**
     * 国家
     */
    country_name,

    /**
     * 省编码
     */
    province_code,

    /**
     * 省
     */
    province_name,

    /**
     * 城市编码
     */
    city_code,

    /**
     * 城市
     */
    city_name,

    /**
     * 区县编码
     */
    district_code,

    /**
     * 区县
     */
    district_name,

    /**
     * 详细地址
     */
    address,

    /**
     * 公司电话
     */
    provider_phone,

    /**
     * 公司邮箱
     */
    provider_email,

    /**
     * 联系人
     */
    provider_contacts,

    /**
     * 公司营业执照
     */
    provider_license_url,

    /**
     * 状态 1有效 0无效
     */
    status,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}