package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/16 10:06
 * @Description：DevicePositionSortReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DevicePositionDragSortReq {

    private List<DevicePositionSortReq> positions;

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class DevicePositionSortReq{

        /**
         * 设备位置id
         */
        private Long id;

        /**
         * 设备位置id集合
         */
        private List<Long> ids;

        /**
         * 排序值
         */
        private Integer sortOrder;
    }
}
