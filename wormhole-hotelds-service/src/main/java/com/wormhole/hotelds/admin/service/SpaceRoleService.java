package com.wormhole.hotelds.admin.service;

import com.wormhole.hotelds.admin.model.enums.SpaceRoleEnum;
import com.wormhole.hotelds.admin.model.resp.SpaceRoleResp;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.stream.Stream;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
@Service
public class SpaceRoleService {
    public Mono<List<SpaceRoleResp>> list() {
        SpaceRoleEnum[] values = SpaceRoleEnum.values();
        return Mono.just(Stream.of(values)
                .map(item -> new SpaceRoleResp().setCode(item.getCode()).setName(item.getName()))
                .toList());
    }
}
