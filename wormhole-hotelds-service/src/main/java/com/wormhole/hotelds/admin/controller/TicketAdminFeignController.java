package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.req.TicketInfoReq;
import com.wormhole.hotelds.api.hotel.resp.TicketAdminListResp;
import com.wormhole.hotelds.api.hotel.resp.TicketDetailResp;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ticket_admin")
public class TicketAdminFeignController {

    @Autowired
    private HotelDsApiClient hotelDsApiClient;

    @PostMapping("/page")
    public Mono<Result<PageResult<TicketAdminListResp>>> getTicketPage(@RequestBody TicketAdminPageReq req){
        return hotelDsApiClient.getTicketPageFeign(req).flatMap(Result::success);
    }


    @PostMapping("/detail")
    public Mono<Result<TicketDetailResp>> getTicketDetail(@RequestBody TicketInfoReq req){
        return hotelDsApiClient.getTicketDetailFeign(req).flatMap(Result::success);
    }


}
