package com.wormhole.hotelds.admin.controller;

import com.wormhole.hotelds.admin.model.vo.HdsAgreementVO;
import com.wormhole.hotelds.admin.model.req.HdsAgreementSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsAgreementDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsAgreementSearchReq;
import com.wormhole.hotelds.admin.service.HdsAgreementService;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:59:28
 * @Description: HdsAgreement
 */
@RestController
@RequestMapping("/hdsagreement")
public class HdsAgreementController {

    @Resource
    private HdsAgreementService service;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody HdsAgreementSaveReq hdsAgreementSaveReq) {
        return service.create(hdsAgreementSaveReq).flatMap(Result::success);
    }

    /**
     * 更新记录
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HdsAgreementSaveReq hdsAgreementSaveReq) {
        return service.update(hdsAgreementSaveReq).flatMap(Result::success);
    }

    /**
     * 删除记录
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody HdsAgreementDeleteReq hdsAgreementDeleteReq) {
        return service.delete(hdsAgreementDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<List<HdsAgreementVO>>> search(@RequestBody HdsAgreementSearchReq hdsAgreementSearchReq) {
        return service.search(hdsAgreementSearchReq).flatMap(Result::success);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<HdsAgreementVO>> findById(@PathVariable("id") Long id) {
        return service.findById(id).flatMap(Result::success);
    }
}