package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/3/26 10:29
 * @Description：MerchantCreateReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MerchantSaveReq {

    /**
     * 主键id 如果没有就是新增，有就是更新
     */
    private Integer id;

    /**
     * 公司名称
     */
    private String merchantName;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * 商户类型(1:酒店商户 2:服务商商户)
     */
    private Integer type;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 公司电话
     */
    private String merchantPhone;

    /**
     * 公司邮箱
     */
    private String merchantEmail;

    /**
     * 联系人
     */
    private String merchantContacts;

    /**
     * 公司营业执照
     */
    private String merchantLicenseUrl;

    /**
     * 开户名称
     */
    private String accountName;

    /**
     * 开户银行
     */
    private String accountBank;

    /**
     * 开户支行
     */
    private String accountSubBank;

    /**
     * 银行卡号
     */
    private String accountCardNumber;

    /**
     * 银行预留手机号
     */
    private String accountReserveMobile;

    /**
     * 提现手机号
     */
    private String accountCashMobile;
}
