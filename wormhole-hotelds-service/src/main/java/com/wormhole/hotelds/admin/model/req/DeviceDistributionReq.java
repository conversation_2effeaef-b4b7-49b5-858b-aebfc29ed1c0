package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/4/11 10:43
 * @Description：DeviceDistributionReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceDistributionReq {

    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * 设备类型：all-全部设备，room-AI云电话，front-AI智能座机
     */
    private String deviceAppType;


    /**
     * 设备状态：
     * 0-全部设备
     * 1-在线
     * 2-离线
     * 3-停用
     */
    private Integer deviceStatus;
}
