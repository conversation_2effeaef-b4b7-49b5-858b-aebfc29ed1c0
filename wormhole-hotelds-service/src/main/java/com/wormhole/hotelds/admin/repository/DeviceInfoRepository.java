package com.wormhole.hotelds.admin.repository;

import aj.org.objectweb.asm.commons.Remapper;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceInfoEntity;
import com.wormhole.hotelds.query.DeviceModelCount;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/4/7 09:18
 * @Description：HdsDeviceInfoRepository
 */
@Repository
public interface DeviceInfoRepository extends ReactiveCrudRepository<HdsDeviceInfoEntity,Long> {

    long countByModelId(Long modelId);

    /**
     * 批量获取设备型号关联的设备数量
     * @param modelIds 模型ID列表
     * @return Flux<Tuple2<模型ID, 数量>>
     */
    @Query("SELECT model_id, COUNT(*) as count FROM hds_device_info WHERE model_id IN (:modelIds) GROUP BY model_id")
    Flux<DeviceModelCount> countByModelIds(@Param("modelIds") List<Long> modelIds);


    /**
     * 根据设备编号查询设备信息
     * @param deviceSn
     * @return
     */
    Mono<HdsDeviceInfoEntity> findFirstByDeviceSn(String deviceSn);

    Mono<HdsDeviceInfoEntity> findByDeviceSnAndModelId(String deviceSn, Long modelId);


    Flux<HdsDeviceInfoEntity> findByModelIdIn(List<Long> modelIds);

    Flux<HdsDeviceInfoEntity> findByModelId(Long id);

    Mono<HdsDeviceInfoEntity> findByDeviceSn(String deviceSn);

    Mono<HdsDeviceInfoEntity> findByDeviceSnOrImei(String deviceSn, String imei);

    Mono<HdsDeviceInfoEntity> findByImei(String imei);

    Flux<HdsDeviceInfoEntity> findByImeiIn(Collection<String> imeis);

    Flux<HdsDeviceInfoEntity> findByDeviceSnIn(Collection<String> deviceSns);
}
