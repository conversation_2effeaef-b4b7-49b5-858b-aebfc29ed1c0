package com.wormhole.hotelds.admin.service;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.OrderStatusEnum;
import com.wormhole.hotelds.admin.model.enums.PaymentMethodEnum;
import com.wormhole.hotelds.admin.model.req.OrderConfirmReq;
import com.wormhole.hotelds.admin.model.req.OrderMessageReq;
import com.wormhole.hotelds.admin.model.req.OrderPayReq;
import com.wormhole.hotelds.admin.model.req.OrderSearchReq;
import com.wormhole.hotelds.admin.model.resp.OrderStatusResp;
import com.wormhole.hotelds.admin.model.resp.YopNotifyResp;
import com.wormhole.hotelds.admin.model.vo.OrderConfirmVO;
import com.wormhole.hotelds.admin.model.vo.OrderInfoVO;
import com.wormhole.hotelds.admin.model.vo.OrderPayVO;
import com.wormhole.hotelds.admin.model.vo.OrderSearchVO;
import com.wormhole.hotelds.admin.repository.HdsOrderRepository;
import com.wormhole.hotelds.constant.SystemConstant;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.SimpleDateUtils;
import com.wormhole.hotelds.util.ValidatorUtils;
import com.wormhole.mq.producer.ReactiveMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/6/19 09:32
 * @Description：OrderService
 */
@Service
@Slf4j
public class OrderService {

    @Resource
    private OrderCalculationService orderCalculationService;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private ProductService productService;

    @Resource
    private PackageService packageService;

    @Resource
    private OrderDetailService orderDetailService;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private HdsOrderRepository hdsOrderRepository;

    @Resource
    private YopPayService yeePayService;

    @Resource
    private HotelService hotelService;

    @Resource
    private ReactiveMessageSender reactiveMessageSender;

    @Resource
    private ProductPricingService productPricingService;

    @Resource
    private HotelProductPricingService hotelProductPricingService;

    @Resource
    private HotelPackageService hotelPackageService;

    /**
     * 确认订单信息
     *
     * @param req 确认订单请求
     * @return 确认订单信息
     */
    public Mono<OrderConfirmVO> confirmOrder(OrderConfirmReq req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
//                    headerInfo.setHotelCode("HWI8H5G");
                    return Mono.just(headerInfo);
                })
                .flatMap(headerInfo -> orderCalculationService.calculateOrder(req, headerInfo.getHotelCode()))
                .onErrorResume(e -> {
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "确认订单失败"));
                });
    }

    /**
     * 创建订单
     *
     * @param req 支付订单请求
     * @return 支付订单信息
     */
    public Mono<OrderPayVO> pay(OrderPayReq req) {
        ValidatorUtils.validateOrderPayReq(req);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
//                    headerInfo.setHotelCode("HKMPJ13");
                    return Mono.just(headerInfo);
                })
                .flatMap(headerInfo -> processPayment(req, headerInfo.getHotelCode()))
                .onErrorResume(e -> {
                    log.error("支付订单失败", e);
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "支付订单失败"));
                });
    }


    private Mono<OrderPayVO> processPayment(OrderPayReq req, String hotelCode) {
        return codePoolManager.generateOrderNo()
                .flatMap(orderNo -> calculateOrderInfo(req, hotelCode)
                        .flatMap(orderConfirmVO -> transactionalOperator.transactional(
                                        // 创建订单记录
                                        createOrderRecord(orderNo, orderConfirmVO, req, hotelCode)
                                                // 调用支付接口
                                                .flatMap(this::createUnifiedOrder)
                                )
                        ));
    }

    /**
     * 计算订单信息
     *
     * @param req
     * @param hotelCode
     * @return
     */
    private Mono<OrderConfirmVO> calculateOrderInfo(OrderPayReq req, String hotelCode) {
        OrderConfirmReq confirmReq = new OrderConfirmReq()
                .setPackageCode(req.getPackageCode())
                .setHotelPackageCode(req.getHotelPackageCode())
                .setProductId(req.getProductId())
                .setPeriodType(req.getPeriodType());

        return orderCalculationService.calculateOrder(confirmReq, hotelCode)
                .doOnNext(result -> log.info("订单计算完成: packageName={}, finalPrice={}",
                        result.getPackageName(), result.getActualPayAmount()));
    }

    /**
     * 调易宝接口下单
     *
     * @param orderEntity
     * @return
     */
    private Mono<OrderPayVO> createUnifiedOrder(HdsOrderEntity orderEntity) {
        log.info("createUnifiedOrder");
        return productService.findById(orderEntity.getProductId())
                .flatMap(productEntity -> {
                    String goodsName = productEntity.getProductName();
                    return yeePayService.createUnifiedOrder(
                            orderEntity.getOrderNo(),
                            orderEntity.getOrderAmount(),
                            goodsName,
                            PaymentMethodEnum.getYeepayCodeByCode(orderEntity.getPayMethod()),
                            orderEntity.getExpireAt()
                    );
                }).map(yeeOrderVO -> {
                    OrderPayVO orderPayVO = new OrderPayVO();
                    orderPayVO.setCashierUrl(yeeOrderVO.getCashierUrl());
                    return orderPayVO;
                })
                .flatMap(orderPayVO -> {
                    // 发送mq消息
                    OrderMessageReq req = new OrderMessageReq();
                    req.setOrderNo(orderEntity.getOrderNo());
                    return reactiveMessageSender.sendDelayMessage(SystemConstant.ORDER_OVERDUE_EVENT_TOPIC, req, 15).map(sendResult -> {
                        log.info("发送订单延时消息成功 | orderNo: {} {}", orderEntity.getOrderNo(), JacksonUtils.writeValueAsString(sendResult));
                        return orderPayVO;
                    });
                });
    }

    /**
     * 创建订单
     *
     * @param orderNo
     * @param orderConfirmVO
     * @param req
     * @param hotelCode
     * @return
     */
    private Mono<HdsOrderEntity> createOrderRecord(String orderNo,
                                                   OrderConfirmVO orderConfirmVO,
                                                   OrderPayReq req, String hotelCode) {
        log.info("createOrderRecord");
        return hotelService.findByHotelCode(hotelCode)
                .flatMap(hotelEntity -> {
                    // 获取套餐信息
                    Mono<HdsPackageEntity> packageMono = packageService.findByPackageCode(req.getPackageCode());

                    // 获取产品信息
                    Mono<HdsProductEntity> productMono = productService.findById(req.getProductId());

                    // 获取价格信息
                    Mono<HdsProductPricingEntity> pricingMono = productPricingService.findByProductIdAndPeriodType(
                            req.getProductId(), req.getPeriodType());

                    // 门店套餐信息 - 可能为空
                    Mono<HdsHotelPackageEntity> hotelPackageMono = req.getHotelPackageCode() != null ?
                            hotelPackageService.findByHotelPackageCode(req.getHotelPackageCode()) :
                            Mono.empty();

                    // 门店价格信息 - 可能为空
                    Mono<HdsHotelProductPricingEntity> hotelPricingMono = req.getHotelPackageCode() != null ?
                            hotelProductPricingService.findByHotelPackageCodeAndProductIdAndPeriodType(
                                    req.getHotelPackageCode(), req.getProductId(), req.getPeriodType()) :
                            Mono.empty();

                    // 结合所有信息
                    return Mono.zip(
                            packageMono,
                            productMono,
                            pricingMono,
                            hotelPackageMono.map(Optional::of).defaultIfEmpty(Optional.empty()),
                            hotelPricingMono.map(Optional::of).defaultIfEmpty(Optional.empty())
                    ).flatMap(tuple -> {
                        HdsPackageEntity packageEntity = tuple.getT1();
                        HdsProductEntity productEntity = tuple.getT2();
                        HdsProductPricingEntity pricingEntity = tuple.getT3();

                        Optional<HdsHotelPackageEntity> hotelPackageOpt = tuple.getT4();
                        Optional<HdsHotelProductPricingEntity> hotelPricingOpt = tuple.getT5();


                        BigDecimal discountAmount = new BigDecimal(orderConfirmVO.getMarketPrice()).subtract(new BigDecimal(orderConfirmVO.getActualPayAmount()));

                        HdsOrderEntity orderEntity = new HdsOrderEntity()
                                .setOrderNo(orderNo)
                                .setHotelCode(hotelCode)
                                .setPackageCode(req.getPackageCode())
                                .setPackageName(packageEntity.getPackageName())
                                .setHotelPackageCode(req.getHotelPackageCode())
                                .setProductId(req.getProductId())
                                .setProductName(productEntity.getProductName())
                                .setRoomCount(hotelEntity.getTotalRoom())
                                .setPeriodType(req.getPeriodType())
                                .setOriginalAmount(new BigDecimal(orderConfirmVO.getMarketPrice()))
                                .setDiscountAmount(discountAmount)
                                .setOrderAmount(new BigDecimal(orderConfirmVO.getActualPayAmount()))
                                .setExpireTime(orderConfirmVO.getExpireTime())
                                .setOrderStatus(0) // 待支付
                                .setPayMethod(req.getPayMethod())
                                .setExpireAt(LocalDateTime.now().plusMinutes(20)); // 20分钟后过期
                        orderEntity.setRowStatus(1);

                        // 创建订单与订单详情
                        return hdsOrderRepository.save(orderEntity)
                                .flatMap(savedOrder -> {
                                    // 创建订单详情
                                    return orderDetailService.createOrderDetail(
                                                    savedOrder,
                                                    orderConfirmVO,
                                                    packageEntity,
                                                    productEntity,
                                                    pricingEntity,
                                                    hotelPackageOpt,
                                                    hotelPricingOpt)
                                            .thenReturn(savedOrder);
                                })
                                .doOnNext(saved -> log.info("订单记录及详情创建成功: orderNo={}", saved.getOrderNo()));
                    });
                });
    }


    /**
     * 订单列表查询总数
     */
    public Mono<Long> searchCount(OrderSearchReq req,HeaderUtils.HeaderInfo headerInfo) {
        return buildSearchCriteria(req,headerInfo)
                .flatMap(criteria -> r2dbcEntityTemplate.count(Query.query(criteria), HdsOrderEntity.class));
    }

    /**
     * 套餐列表查询
     */
    public Mono<List<OrderSearchVO>> search(OrderSearchReq req,HeaderUtils.HeaderInfo headerInfo) {
        return buildSearchCriteria(req,headerInfo)
                .flatMap(criteria -> {
                    // 添加排序，默认按创建时间降序
                    Query query = Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.DESC, HdsOrderFieldEnum.created_at.name()))
                            .limit(req.getPageSize())
                            .offset((long) (req.getCurrent() - 1) * req.getPageSize());
                    return r2dbcEntityTemplate.select(query, HdsOrderEntity.class)
                            .collectList()
                            .flatMapMany(orderEntities -> {
                                List<Mono<OrderSearchVO>> monoList = orderEntities.stream()
                                        .map(this::convertToOrderSearchVO)
                                        .collect(Collectors.toList());

                                return Flux.concat(monoList);
                            })
                            .collectList();
                });
    }

    /**
     * 构建查询条件
     */
    private Mono<Criteria> buildSearchCriteria(OrderSearchReq req,HeaderUtils.HeaderInfo headerInfo) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(req.getOrderNo())) {
            criteria = criteria.and(HdsOrderFieldEnum.order_no.name()).is(req.getOrderNo());
        }

        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(HdsOrderFieldEnum.hotel_code.name()).is(req.getHotelCode());
        }

        if (StringUtils.isNotBlank(headerInfo.getHotelCode())) {
            criteria = criteria.and(HdsOrderFieldEnum.hotel_code.name()).is(headerInfo.getHotelCode());
        }

        if (StringUtils.isNotBlank(req.getPackageCode())) {
            criteria = criteria.and(HdsOrderFieldEnum.package_code.name()).is(req.getPackageCode());
        }

        // 套餐名称
        if (StringUtils.isNotBlank(req.getPackageName())) {
            return packageService.preFilterByPackageName(req.getPackageName(), criteria);
        }

        return Mono.just(criteria);
    }

    /**
     * 转换为搜索VO（包含详细信息）
     */
    private Mono<OrderSearchVO> convertToOrderSearchVO(HdsOrderEntity entity) {
        OrderSearchVO vo = new OrderSearchVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setOrderAmount(entity.getOrderAmount().toString());
        vo.setPaymentMethod(entity.getPayMethod());

        return packageService.findByPackageCode(entity.getPackageCode())
                .map(standardPackage -> {
                    vo.setPackageName(standardPackage.getPackageName());
                    return vo;
                })
                .defaultIfEmpty(vo)
                .flatMap(searchVO -> productService.findById(entity.getProductId())
                        .map(product -> {
                            searchVO.setProductName(product.getProductName());
                            return searchVO;
                        }))
                .flatMap(searchVO -> hotelService.findByHotelCode(entity.getHotelCode())
                        .map(hotel -> {
                            searchVO.setHotelName(hotel.getHotelName());
                            return searchVO;
                        }))
                .defaultIfEmpty(vo);
    }

    public Mono<HdsOrderEntity> findByOrderNo(String orderNo) {
        return hdsOrderRepository.findByOrderNo(orderNo);
    }

    /**
     * 更新订单支付状态
     *
     * @param orderNo
     * @param orderStatus
     * @param orderStatusResp
     * @return
     */
    public Mono<Boolean> updateOrder(String orderNo, Integer orderStatus, OrderStatusResp orderStatusResp) {
        return r2dbcEntityTemplate.selectOne(
                        Query.query(Criteria.where(HdsOrderFieldEnum.order_no.name()).is(orderNo)),
                        HdsOrderEntity.class
                )
                .flatMap(existingOrder -> {
                    // 如果订单已支付，直接返回成功
                    if (OrderStatusEnum.PAID.getCode().equals(existingOrder.getOrderStatus())) {
                        log.info("订单已支付，幂等性校验通过 orderNo:{}", orderNo);
                        return Mono.just(true);
                    }

                    Criteria criteria = Criteria.where(HdsOrderFieldEnum.order_no.name()).is(orderNo)
                            .and(HdsOrderFieldEnum.order_status.name()).is(OrderStatusEnum.PENDING.getCode());

                    Update update = Update.update(HdsOrderFieldEnum.order_status.name(), orderStatus)
                            .set(HdsOrderFieldEnum.paid_at.name(), SimpleDateUtils.parseDateTime(orderStatusResp.getPaySuccessDate()))
                            .set(HdsOrderFieldEnum.updated_at.name(), LocalDateTime.now());

                    // 执行更新操作
                    return r2dbcEntityTemplate.update(
                                    Query.query(criteria),
                                    update,
                                    HdsOrderEntity.class
                            )
                            .map(updatedCount -> {
                                boolean success = updatedCount != null && updatedCount > 0;
                                if (success) {
                                    log.info("订单支付状态更新成功 orderNo:{}", orderNo);
                                } else {
                                    log.warn("订单支付状态更新失败 orderNo:{}", orderNo);
                                }
                                return success;
                            });
                })
                .defaultIfEmpty(false) // 如果订单不存在，返回false
                .onErrorResume(e -> {
                    log.error("更新订单支付状态异常 orderNo:{}", orderNo, e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新订单支付状态失败: " + e.getMessage()));
                });
    }

    /**
     * 查询最近创建和即将过期的待支付订单
     *
     * @param orderNos 指定订单号列表，为空则查询所有
     * @param minutes  查询最近多少分钟内创建的订单
     * @return 待支付订单列表
     */
    public Mono<List<HdsOrderEntity>> findRecentAndNearExpiryOrders(List<String> orderNos, Integer minutes) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime recentTime = now.minusMinutes(minutes);
        LocalDateTime nearExpiryTime = now.plusMinutes(5); // 5分钟内即将过期的订单

        // 构建查询条件：待支付状态 AND (最近创建 OR 即将过期)
        Criteria statusCriteria = Criteria.where(HdsOrderFieldEnum.order_status.name()).is(OrderStatusEnum.PENDING.getCode());

        Criteria timeCriteria = Criteria.where(HdsOrderFieldEnum.created_at.name()).greaterThanOrEquals(recentTime)
                .or(HdsOrderFieldEnum.expire_at.name()).between(now, nearExpiryTime);

        Criteria criteria = statusCriteria.and(timeCriteria);

        // 如果指定了订单号，则添加订单号条件
        if (CollectionUtils.isNotEmpty(orderNos)) {
            criteria = criteria.and(HdsOrderFieldEnum.order_no.name()).in(orderNos);
        }

        return r2dbcEntityTemplate.select(HdsOrderEntity.class)
                .matching(Query.query(criteria))
                .all()
                .collectList()
                .doOnNext(orders -> log.info("查询到{}个待检查订单（最近创建或即将过期）", orders.size()));
    }

    /**
     * 查询已过期但仍处于待支付状态的订单
     *
     * @return 已过期的待支付订单列表
     */
    public Mono<List<HdsOrderEntity>> findExpiredPendingOrders() {
        LocalDateTime now = LocalDateTime.now();

        Criteria criteria = Criteria.where(HdsOrderFieldEnum.order_status.name()).is(OrderStatusEnum.PENDING.getCode())
                .and(HdsOrderFieldEnum.expire_at.name()).lessThan(now);

        return r2dbcEntityTemplate.select(HdsOrderEntity.class)
                .matching(Query.query(criteria))
                .all()
                .collectList()
                .doOnNext(orders -> log.info("查询到{}个已过期待支付订单", orders.size()));
    }

    /**
     * 支付成功之后查询订单信息
     *
     * @param orderNo
     * @return
     */
    public Mono<OrderInfoVO> queryOrderInfo(String orderNo) {
        return hdsOrderRepository.findByOrderNo(orderNo)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "订单不存在")))
                .flatMap(orderEntity -> {
                    OrderInfoVO orderInfoVO = new OrderInfoVO();
                    BeanUtils.copyProperties(orderEntity, orderInfoVO);
                    orderInfoVO.setOriginalAmount(orderEntity.getOriginalAmount().toString());
                    orderInfoVO.setDiscountAmount(orderEntity.getDiscountAmount().toString());
                    orderInfoVO.setOrderAmount(orderEntity.getOrderAmount().toString());
                    orderInfoVO.setExpireTime(SimpleDateUtils.formatLocalDateTimeToDateHour(orderEntity.getExpireTime()));
                    return Mono.just(orderInfoVO);
                });
    }

    /**
     * @param hotelCode
     * @param status
     * @return
     */
    public Mono<Long> countByHotelCodeAndStatus(String hotelCode, Integer status) {
        return hdsOrderRepository.countByHotelCodeAndOrderStatus(hotelCode, status);
    }
}
