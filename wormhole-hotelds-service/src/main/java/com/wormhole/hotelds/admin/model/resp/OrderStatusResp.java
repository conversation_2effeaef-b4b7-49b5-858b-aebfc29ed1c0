package com.wormhole.hotelds.admin.model.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/6/24 10:32
 * @Description：OrderStatusResp
 */
@Data
public class OrderStatusResp {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 易宝收款订单号
     */
    private String uniqueOrderNo;

    /**
     * 订单状态
     * PROCESSING：订单待支付
     * SUCCESS：订单支付成功
     * TIME_OUT：订单已过期
     * FAIL:订单支付失败
     * CLOSE:订单关闭
     */
    private Integer status;

    /**
     * 订单金额.单位:元
     */
    private BigDecimal orderAmount;

    /**
     * 支付金额.单位:元
     */
    private BigDecimal payAmount;

    /**
     * 支付成功时间
     */
    private String paySuccessDate;

    /**
     * 微信交易单号/支付宝订单号,该笔订单在微信支付宝侧的唯一订单号，微信交易单号/支付宝订单号
     */
    private String channelTrxId;
}
