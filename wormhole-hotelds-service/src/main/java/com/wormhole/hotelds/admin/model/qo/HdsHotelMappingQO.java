package com.wormhole.hotelds.admin.model.qo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HdsHotelMappingQO implements Serializable {
    private String hotelCode;
    private List<String> hotelCodes;
    private String channel;
    private String platform;
    private String externalId;
    private List<String> externalIds;
    private Long id;

}