package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

import java.io.*;
import java.time.LocalDateTime;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:57:52
 * @Description: Device
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceSaveReq {

    /**
     * 设备id
     */
    private Long id;

    /**
     * 设备型号id
     */
    private Long modelId;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 保修开始时间
     */
    private String warrantyStart;

    /**
     * 保修结束时间
     */
    private String warrantyEnd;

    /**
     * 采购日期
     */
    private String purchaseTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 设备IMEI号
     */
    private String imei;
}