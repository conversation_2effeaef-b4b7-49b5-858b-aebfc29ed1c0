package com.wormhole.hotelds.admin.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:59:06
 * @Description: HdsThemeConfig
 */
@Data
@Table("hds_theme_config")
@Accessors(chain = true)
@EqualsAndHashCode
public class HdsThemeConfigEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

}