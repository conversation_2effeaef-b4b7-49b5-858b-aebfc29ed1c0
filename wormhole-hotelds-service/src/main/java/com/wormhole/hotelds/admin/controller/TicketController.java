package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.*;
import com.wormhole.hotelds.core.enums.*;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * @Description:
 * @Author: wj
 * @Date: 2025/4/15 10:51
 */
@RestController
@RequestMapping("/ticket")
public class TicketController {

    @GetMapping("/service/categories")
    public Mono<Result<List<Map<String, String>>>> categories() {
        List<Map<String, String>> mapList = Arrays.stream(ServiceCategory.values())
                .map(e -> Map.of("code", e.getCode(), "name", e.getChineseName()))
                .toList();
        return Result.success(mapList);
    }

}
