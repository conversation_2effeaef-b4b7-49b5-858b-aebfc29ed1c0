package com.wormhole.hotelds.admin.exception;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.Result;
import com.wormhole.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/2/28
 * @Description:
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public Mono<Result<Object>> handleBusinessException(
            BusinessException ex) {

        log.error("business exception: {}", ex.getMessage(), ex);
        return Result.failed(ex.getCode(), ex.getMsg());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public Mono<Result<Object>> handleBusinessException(
            IllegalArgumentException ex) {
        log.error("illegal exception: {}", ex.getMessage(), ex);

        if (StringUtils.isNotBlank(ex.getMessage())) {
            return Result.failed(ResultCode.INVALID_PARAMETER, ex.getMessage());
        }
        return Result.failed(ResultCode.INVALID_PARAMETER, "请求或参数错误");
    }


    @ExceptionHandler(Exception.class)
    public Mono<Result<Object>> handleBusinessException(
            Exception ex) {

        log.error("exception: {}", ex.getMessage(), ex);
        return Result.failed(ResultCode.INTERNAL_SERVER_ERROR, ex.getMessage());
    }
}
