package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.excel.BaseExportReq;
import com.wormhole.hotelds.storage.file.FilePartAdapter;
import com.wormhole.hotelds.admin.model.req.TaskSearchReq;
import com.wormhole.hotelds.admin.model.req.TemplateDownloadReq;
import com.wormhole.hotelds.admin.model.vo.TaskVO;
import com.wormhole.hotelds.admin.service.ExcelService;
import jakarta.annotation.Resource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/3/28 14:31
 * @Description：上传Excel文件
 */
@RestController
@RequestMapping("/excel")
public class ExcelController {

    @Resource
    private ExcelService excelService;

    /**
     * Excel导入
     *
     * @param basePath
     * @param filePart
     * @param businessType
     * @return
     */
    @PostMapping("/import")
    public Mono<Result<Integer>> importExcel(@RequestPart("base_path") String basePath,
                                             @RequestPart("file") FilePart filePart,
                                             @RequestPart("business_type") String businessType,
                                             @RequestPart(value = "business_id",required = false) String businessId) {
        return excelService.importExcel(basePath, new FilePartAdapter(filePart), businessType, businessId).flatMap(Result::success);
    }

    /**
     * 导出Excel
     */
    @PostMapping("/export")
    public Mono<ResponseEntity<InputStreamResource>> exportExcel(@RequestBody BaseExportReq req) {
        return excelService.exportExcel(req);
    }

    @PostMapping("/template/download")
    public Mono<ResponseEntity<InputStreamResource>> downloadTemplate(@RequestBody TemplateDownloadReq templateDownloadReq) {
        return excelService.downloadTemplate(templateDownloadReq);
    }

    /**
     * 查询任务列表
     *
     * @param taskSearchReq
     * @return
     */
    @PostMapping("/task/search")
    public Mono<Result<PageResult<TaskVO>>> search(@RequestBody TaskSearchReq taskSearchReq) {
        return Mono.zip(excelService.searchCount(taskSearchReq), excelService.search(taskSearchReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    /**
     * 获取任务状态
     *
     * @param id
     * @return
     */
    @GetMapping("/task/query/{id}")
    public Mono<Result<TaskVO>> queryTaskById(@PathVariable("id") Integer id) {
        return excelService.queryTaskById(id).flatMap(Result::success);
    }
}
