package com.wormhole.hotelds.admin.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:58:49
 * @Description: HdsTheme
 */
@Data
@Table("hds_theme")
@Accessors(chain = true)
@EqualsAndHashCode
public class HdsThemeEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

}