package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/3/26 14:05
 * @Description：RoomSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RoomSearchReq extends QueryCondition {

    /**
     * 房间号
     */
    private String roomNum;

    /**
     * 酒店ID
     */
    private Integer hotelId;

    /**
     * 楼层
     */
    private Integer floor;

    /**
     * 房间类型
     */
    private String roomType;

    /**
     * 状态
     */
    private Integer status;
}
