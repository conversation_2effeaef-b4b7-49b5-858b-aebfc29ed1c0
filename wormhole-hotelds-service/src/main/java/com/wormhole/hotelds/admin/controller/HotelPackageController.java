package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.HotelPackageDetailVO;
import com.wormhole.hotelds.admin.model.vo.HotelPackageSearchVO;
import com.wormhole.hotelds.admin.model.vo.PaymentPackageDisplayVO;
import com.wormhole.hotelds.admin.service.HotelPackageService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/6/16 17:15
 * @Description：HotelPaymentPackageController
 */
@RestController
@RequestMapping("/hotel/payment/package")
public class HotelPackageController {

    @Resource
    private HotelPackageService hotelPackageService;

    /**
     * 创建门店套餐
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody HotelPackageSaveReq req) {
        return hotelPackageService.create(req).flatMap(Result::success);
    }

    /**
     * 更新门店套餐
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HotelPackageUpdateReq req) {
        return hotelPackageService.update(req).flatMap(Result::success);
    }

    /**
     * 门店套餐列表查询
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<HotelPackageSearchVO>>> search(@RequestBody HotelPackageSearchReq req) {
        return Mono.zip(hotelPackageService.searchCount(req), hotelPackageService.search(req))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    /**
     * 根据ID查询门店套餐详情
     */
    @GetMapping("/query")
    public Mono<Result<HotelPackageDetailVO>> findById(@RequestParam("id") Integer id) {
        return hotelPackageService.findById(id).flatMap(Result::success);
    }

    /**
     * 启用/停用门店套餐
     */
    @PostMapping("/update/status")
    public Mono<Result<Boolean>> updateStatus(@RequestBody HotelPackageStatusReq req) {
        return hotelPackageService.updateStatus(req).flatMap(Result::success);
    }


    /**
     * 删除门店套餐
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody HotelPackageDeleteReq req) {
        return hotelPackageService.delete(req).flatMap(Result::success);
    }

    /**
     * 获取门店可用套餐（支付页面使用）
     */
    @GetMapping("/getAvailablePackages")
    public Mono<Result<PaymentPackageDisplayVO>> getAvailablePackages(
            @RequestParam("hotel_code") String hotelCode,
            @RequestParam(value = "period_type", defaultValue = "1") Integer periodType,
            @RequestParam(value = "product_id",required = false) Integer productId) {
        return hotelPackageService.getAvailablePackages(hotelCode, periodType,productId).flatMap(Result::success);
    }
}
