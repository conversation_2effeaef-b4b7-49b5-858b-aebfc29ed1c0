package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/6/19 09:20
 * @Description：ConfirmOrderVO
 */
@Builder
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderConfirmVO {

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 计费周期：1-月度，2-季度，3-年度
     */
    private Integer periodType;

    /**
     * 门市价
     */
    private String marketPrice;

    /**
     * 优惠价
     */
    private String discountPrice;

    /**
     * 实际应付金额
     */
    private String actualPayAmount;

    /**
     * 到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

}
