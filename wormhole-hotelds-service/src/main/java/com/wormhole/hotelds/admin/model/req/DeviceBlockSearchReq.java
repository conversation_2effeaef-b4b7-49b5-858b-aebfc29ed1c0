package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * @Author：flx
 * @Date：2025/4/23 13:34
 * @Description：DeviceBlockSearchReq
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceBlockSearchReq {

    private String hotelCode;

    private String deviceAppType;

    private String block;
}
