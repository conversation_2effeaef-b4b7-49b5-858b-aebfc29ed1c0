package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-27 17:16:36
 * @Description: 发票申请状态变更请求
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceApplicationStatusUpdateReq {

    /**
     * 申请ID
     */
    @NotNull(message = "申请ID不能为空")
    private Long id;

    /**
     * 目标状态：0-待审核，1-审核中，2-已开票，3-已驳回
     */
    @NotNull(message = "目标状态不能为空")
    private Integer targetStatus;

    /**
     * 审核备注（驳回时必填）
     */
    @Size(max = 200, message = "审核备注长度不能超过200个字符")
    private String reviewRemark;

    /**
     * 发票号码（审核通过时可选）
     */
    @Size(max = 64, message = "发票号码长度不能超过64个字符")
    private String invoiceNo;

    /**
     * 发票代码（审核通过时可选）
     */
    @Size(max = 32, message = "发票代码长度不能超过32个字符")
    private String invoiceCode;

    /**
     * 发票文件URL（审核通过时可选）
     */
    @Size(max = 512, message = "发票文件URL长度不能超过512个字符")
    private String invoiceUrl;
} 