package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.*;

/**
 * @Author：flx
 * @Date：2025/4/3 09:19
 * @Description：HdsEmployeeDeleteReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsEmployeeUpdateStatusReq {

    private Integer id;

    /**
     * 状态 1正常 2停用 3注销
     */
    private Integer status;

    private List<String> hotelCodes;

}
