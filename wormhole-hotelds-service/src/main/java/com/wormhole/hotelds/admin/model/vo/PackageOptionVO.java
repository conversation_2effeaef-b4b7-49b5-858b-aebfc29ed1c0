package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/16 16:46
 * @Description：PackageOptionVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PackageOptionVO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 套餐编码
     */
    private String packageCode;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 付费方式：0-按房间数量收费，1-按门店收费
     */
    private Integer paymentMode;

    /**
     * 优惠方式：0-折扣，1-一口价
     */
    private Integer discountMode;

    private List<PackageSearchVO.ProductVO> products;

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ProductVO {

        /**
         * 主键
         */
        private Integer id;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * pricing
         */
        private List<PackageSearchVO.PricingVO> pricing;
    }

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PricingVO {

        /**
         * 主键
         */
        private Integer id;

        /**
         * 周期类型：1-月度，2-季度，3-年度
         */
        private Integer periodType;

        /**
         * 门市价
         */
        private String marketPrice;

        /**
         * 优惠价
         */
        private String discountPrice;
    }
}
