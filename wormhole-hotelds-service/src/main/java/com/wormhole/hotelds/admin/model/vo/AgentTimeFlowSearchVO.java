package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.admin.model.enums.AgentFlowTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsAgentTimeFlowEntity;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/5/21 10:22
 * @Description：AgentTimeFlowSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AgentTimeFlowSearchVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 当前到期时间（修改前）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime expirationBefore;

    /**
     * 更新来源名称
     */
    private String flowTypeName;

    /**
     * 最新到期时间（修改后）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime expirationAfter;

    /**
     * 变更月数
     */
    private Integer monthsChanged;


    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 将实体转换为VO对象
     */
    public static AgentTimeFlowSearchVO toVo(HdsAgentTimeFlowEntity entity) {
        AgentTimeFlowSearchVO vo = new AgentTimeFlowSearchVO();
        BeanUtils.copyProperties(entity, vo);

        vo.setFlowTypeName(AgentFlowTypeEnum.fromCode(entity.getFlowType()));
        if (!Objects.equals(AgentFlowTypeEnum.ADMIN_ADJUSTMENT.getCode(),  entity.getFlowType())) {
            vo.setCreatedByName(null);
        }
        return vo;
    }
}
