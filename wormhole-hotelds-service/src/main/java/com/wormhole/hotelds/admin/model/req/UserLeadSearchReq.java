package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/5/20 10:25
 * @Description：MerchantLeadSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserLeadSearchReq extends QueryCondition {

    /**
     * 联系人手机号码
     */
    private String mobile;

    /**
     * 线索状态：1-有效，0-无效，null-全部
     */
    private Integer status;
}
