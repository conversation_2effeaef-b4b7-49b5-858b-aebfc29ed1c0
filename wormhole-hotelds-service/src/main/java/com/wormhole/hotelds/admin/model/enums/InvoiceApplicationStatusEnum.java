package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：yongfeng.chen
 * @Date：2025-06-26 15:43:00
 * @Description：发票申请状态枚举 '申请状态：0-待审核，1-审核中，2-已开票，3-已驳回',
 */
@Getter
@AllArgsConstructor
public enum InvoiceApplicationStatusEnum {

    /**
     * 待审核
     */
    PENDING(0, "待审核"),

    /**
     * 审核中
     */
    AUDITING(1, "审核中"),

    /**
     * 已开票
     */
    INVOICED(2, "已开票"),

    /**
     * 已驳回
     */
    REJECTED(3, "已驳回");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     */
    public static InvoiceApplicationStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (InvoiceApplicationStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否是合法的状态码
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
