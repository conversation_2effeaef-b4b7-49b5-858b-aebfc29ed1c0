package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.PackageOptionVO;
import com.wormhole.hotelds.admin.model.vo.PackageSearchVO;
import com.wormhole.hotelds.admin.model.vo.PackageVO;
import com.wormhole.hotelds.admin.repository.HdsPackageRepository;
import com.wormhole.hotelds.core.model.entity.HdsPackageEntity;
import com.wormhole.hotelds.core.model.entity.HdsPackageFieldEnum;
import com.wormhole.hotelds.core.model.entity.HdsProductEntity;
import com.wormhole.hotelds.core.model.entity.HdsProductPricingEntity;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/6/16 13:54
 * @Description：PaymentPackageService
 */
@Service
@Slf4j
public class PackageService {

    @Resource
    private HdsPackageRepository hdsPackageRepository;

    @Resource
    private ProductService productService;

    @Resource
    private ProductPricingService productPricingService;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private HotelPackageService hotelPackageService;

    /**
     * 创建套餐
     */
    public Mono<Boolean> create(PackageSaveReq req) {
        ValidatorUtils.validatePackageSaveReq(req);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 1. 校验套餐名称是否重复
                    return checkPackageNameExists(req.getPackageName(), null)
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "套餐名称已存在"));
                                }
                                return codePoolManager.getCodeFromPool(BussinessTypeEnum.PAYMENT_PACKAGE.getBusinessType())
                                        .flatMap(paymentPackageCode -> {
                                            // 2. 执行创建事务
                                            return executeCreateTransaction(paymentPackageCode, req, headerInfo);
                                        });
                            });
                })
                .onErrorResume(e -> {
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    log.error("创建套餐失败: {}", e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建套餐失败"));
                });
    }

    /**
     * 检查套餐名称是否存在
     */
    private Mono<Boolean> checkPackageNameExists(String packageName, Integer excludeId) {
        Criteria criteria = Criteria.where(HdsPackageFieldEnum.package_name.name()).is(packageName);
        if (excludeId != null) {
            criteria = criteria.and(HdsPackageFieldEnum.id.name()).not(excludeId);
        }

        return r2dbcEntityTemplate.count(Query.query(criteria), HdsPackageEntity.class)
                .map(count -> count > 0);
    }

    /**
     * 执行创建事务
     */
    private Mono<Boolean> executeCreateTransaction(String paymentPackageCode, PackageSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                Mono.defer(() -> {
                    // 构建套餐实体
                    HdsPackageEntity packageEntity = buildPackageEntity(paymentPackageCode, req, headerInfo);

                    Mono<HdsPackageEntity> savePackageMono = hdsPackageRepository.save(packageEntity);
                    Mono<Boolean> saveProductsMono = productService.createProductsWithPricing(paymentPackageCode, req, headerInfo);

                    // 等待两个操作都完成
                    return Mono.zip(savePackageMono, saveProductsMono)
                            .thenReturn(true);
                })
        );
    }

    /**
     * 构建套餐实体
     */
    private HdsPackageEntity buildPackageEntity(String paymentPackageCode, PackageSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        HdsPackageEntity entity = new HdsPackageEntity();
        entity.setPackageCode(paymentPackageCode);
        entity.setPackageName(req.getPackageName());
        entity.setPayMode(req.getPaymentMode());
        entity.setDiscountMode(req.getDiscountMode());
        entity.setStatus(1); // 默认启用
        entity.setIsRecommend(0); // 默认不推荐

        // 设置审计字段
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setRowStatus(1);
        return entity;
    }

    /**
     * 更新套餐
     */
    public Mono<Boolean> update(PackageSaveReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "套餐保存请求不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "套餐ID不能为空");
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 1. 检查套餐是否存在
                    return hdsPackageRepository.findById(req.getId())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "套餐不存在")))
                            .flatMap(existingPackage -> checkHotelAssociations(existingPackage.getPackageCode())
                                    .flatMap(hasAssociations -> {
                                        if (hasAssociations) {
                                            return checkPackageInfoChanged(existingPackage, req)
                                                    .flatMap(isChanged -> {
                                                        if (isChanged) {
                                                            return Mono.error(new BusinessException(
                                                                    ResultCode.INVALID_PARAMETER,
                                                                    "该套餐有启用的门店付费套餐关联，不支持更新"));
                                                        }
                                                        // 2. 检查名称是否重复
                                                        return checkPackageNameExists(req.getPackageName(), req.getId())
                                                                .flatMap(exists -> {
                                                                    if (exists) {
                                                                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "套餐名称已存在"));
                                                                    }
                                                                    // 3. 执行更新事务
                                                                    return executeUpdateTransaction(existingPackage, req, headerInfo);
                                                                });
                                                    });
                                        }
                                        // 2. 检查名称是否重复
                                        return checkPackageNameExists(req.getPackageName(), req.getId())
                                                .flatMap(exists -> {
                                                    if (exists) {
                                                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "套餐名称已存在"));
                                                    }
                                                    // 3. 执行更新事务
                                                    return executeUpdateTransaction(existingPackage, req, headerInfo);
                                                });
                                    }));
                }).onErrorResume(e -> {
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    log.error("更新套餐失败: {}", e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新套餐失败"));
                });
    }

    private Mono<Boolean> checkPackageInfoChanged(HdsPackageEntity existingPackage, PackageSaveReq req) {
        // 优惠方式和折扣方式
        if (!Objects.equals(existingPackage.getPayMode(), req.getPaymentMode()) ||
                !Objects.equals(existingPackage.getDiscountMode(), req.getDiscountMode())) {
            return Mono.just(true);
        }

        // 获取产品列表
        Mono<List<HdsProductEntity>> existingProductsMono = productService.findByPackageCode(existingPackage.getPackageCode())
                .collectList();

        // 产品信息
        return existingProductsMono.flatMap(existingProducts -> {
            // 1. 检查产品数量
            if (existingProducts.size() != req.getProducts().size()) {
                return Mono.just(true);
            }

            // 2. 比较产品ID集合
            Set<Integer> existingProductIds = existingProducts.stream()
                    .map(HdsProductEntity::getId)
                    .collect(Collectors.toSet());

            Set<Integer> requestProductIds = req.getProducts().stream()
                    .map(PackageSaveReq.ProductSaveReq::getId)
                    .filter(Objects::nonNull) // 过滤掉可能的null值
                    .collect(Collectors.toSet());

            if (req.getProducts().stream().anyMatch(p -> p.getId() == null) ||
                    !existingProductIds.equals(requestProductIds)) {
                return Mono.just(true);
            }

            Map<Integer, HdsProductEntity> existingProductMap = existingProducts.stream()
                    .collect(Collectors.toMap(HdsProductEntity::getId, p -> p));

            // 3. 产品定价信息
            List<Integer> productIds = existingProducts.stream()
                    .map(HdsProductEntity::getId)
                    .collect(Collectors.toList());

            return productPricingService.findAllByProductIdIn(productIds)
                    .collectList()
                    .map(existingPricings -> {
                        Map<Integer, List<HdsProductPricingEntity>> pricingMap = existingPricings.stream()
                                .collect(Collectors.groupingBy(HdsProductPricingEntity::getProductId));

                        for (PackageSaveReq.ProductSaveReq productReq : req.getProducts()) {
                            if (productReq.getId() == null) continue;

                            HdsProductEntity existingProduct = existingProductMap.get(productReq.getId());

                            if (!Objects.equals(existingProduct.getProductName(), productReq.getProductName())) {
                                return true;
                            }

                            // Todo 是否需要比较是否推荐的值

                            List<String> existingDesc = parseProductDescription(existingProduct.getProductDescription());
                            List<String> requestDesc = productReq.getProductDescription();

                            if (existingDesc.size() != requestDesc.size() ||
                                    !new HashSet<>(existingDesc).equals(new HashSet<>(requestDesc))) {
                                return true;
                            }

                            List<HdsProductPricingEntity> existingPricingList = pricingMap.getOrDefault(productReq.getId(), Collections.emptyList());
                            List<PackageSaveReq.PricingSaveReq> requestPricingList = productReq.getPricing();

                            if (existingPricingList.size() != requestPricingList.size()) {
                                return true;
                            }

                            Map<Integer, HdsProductPricingEntity> periodToPricingMap = existingPricingList.stream()
                                    .collect(Collectors.toMap(HdsProductPricingEntity::getPeriodType, p -> p));

                            for (PackageSaveReq.PricingSaveReq pricingReq : requestPricingList) {
                                HdsProductPricingEntity existingPricing = periodToPricingMap.get(pricingReq.getPeriodType());

                                if (existingPricing == null) {
                                    return true;
                                }

                                BigDecimal reqMarketPrice = new BigDecimal(pricingReq.getMarketPrice());
                                if (existingPricing.getMarketPrice().compareTo(reqMarketPrice) != 0) {
                                    return true;
                                }

                                if (existingPackage.getDiscountMode() == 0) { // 折扣模式
                                    if (pricingReq.getDiscountRate() != null) {
                                        BigDecimal reqDiscountRate = new BigDecimal(pricingReq.getDiscountRate());
                                        if (existingPricing.getDiscountRate().compareTo(reqDiscountRate) != 0) {
                                            return true;
                                        }
                                    }
                                } else { // 一口价模式
                                    if (pricingReq.getDiscountPrice() != null) {
                                        BigDecimal reqDiscountPrice = new BigDecimal(pricingReq.getDiscountPrice());
                                        if (existingPricing.getDiscountPrice().compareTo(reqDiscountPrice) != 0) {
                                            return true;
                                        }
                                    }
                                }
                            }
                        }

                        return false;
                    });
        });
    }

    private List<String> parseProductDescription(String productDescriptionJson) {
        if (StringUtils.isBlank(productDescriptionJson)) {
            return Collections.emptyList();
        }

        return JacksonUtils.readValues(productDescriptionJson, String.class);
    }


    /**
     * 执行更新事务
     */
    private Mono<Boolean> executeUpdateTransaction(HdsPackageEntity existingPackage, PackageSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 1. 先获取更新前的产品ID列表
        return productService.findByPackageCode(existingPackage.getPackageCode())
                .map(HdsProductEntity::getId)
                .collectList()
                .flatMap(oldProductIds -> transactionalOperator.transactional(
                        Mono.defer(() -> {
                            // 2. 更新套餐主表
                            existingPackage.setPackageName(req.getPackageName());
                            existingPackage.setPayMode(req.getPaymentMode());
                            existingPackage.setDiscountMode(req.getDiscountMode());
                            existingPackage.setUpdatedBy(headerInfo.getUserId());
                            existingPackage.setUpdatedByName(headerInfo.getUsername());
                            existingPackage.setUpdatedAt(LocalDateTime.now());

                            return hdsPackageRepository.save(existingPackage)
                                    .flatMap(savedPackage -> productService.updateProducts(savedPackage.getPackageCode(), req.getProducts(), headerInfo, req.getDiscountMode()))
                                    .then(hotelPackageService.updateHotelProductPricing(existingPackage.getPackageCode(), oldProductIds, headerInfo, req.getDiscountMode()))
                                    .thenReturn(true);
                        })
                ));
    }

    /**
     * 套餐列表查询总数
     */
    public Mono<Long> searchCount(PackageSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        return r2dbcEntityTemplate.count(Query.query(criteria), HdsPackageEntity.class);
    }

    /**
     * 套餐列表查询
     */
    public Mono<List<PackageSearchVO>> search(PackageSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        Query query = Query.query(criteria)
                .sort(Sort.by(Sort.Direction.DESC, HdsPackageFieldEnum.is_recommend.name()))
                .sort(Sort.by(Sort.Direction.DESC, HdsPackageFieldEnum.created_at.name()))
                .limit(req.getPageSize())
                .offset((long) (req.getCurrent() - 1) * req.getPageSize());
        return r2dbcEntityTemplate.select(query, HdsPackageEntity.class)
                .collectList()
                .flatMap(entityList -> {
                    List<Mono<PackageSearchVO>> monoList = entityList.stream()
                            .map(this::convertToSearchVOWithProducts)
                            .collect(Collectors.toList());

                    return Flux.fromIterable(monoList)
                            .flatMapSequential(mono -> mono)
                            .collectList();
                });
    }

    /**
     * 构建查询条件
     */
    private Criteria buildSearchCriteria(PackageSearchReq req) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(req.getPackageName())) {
            criteria = criteria.and(HdsPackageFieldEnum.package_name.name()).like("%" + req.getPackageName() + "%");
        }

        if (req.getPaymentMode() != null) {
            criteria = criteria.and(HdsPackageFieldEnum.pay_mode.name()).is(req.getPaymentMode());
        }

        if (req.getDiscountMode() != null) {
            criteria = criteria.and(HdsPackageFieldEnum.discount_mode.name()).is(req.getDiscountMode());
        }

        return criteria;
    }

    /**
     * 转换为搜索VO（包含产品和定价信息）
     */
    private Mono<PackageSearchVO> convertToSearchVOWithProducts(HdsPackageEntity entity) {
        PackageSearchVO vo = new PackageSearchVO();
        vo.setPaymentMode(entity.getPayMode());
        BeanUtils.copyProperties(entity, vo);

        // 1. 先获取所有产品并排序
        return productService.findByPackageCode(entity.getPackageCode())
                .collectList()
                .flatMap(productEntities -> {
                    // 2. 在内存中对完整列表进行排序
                    productEntities.sort((p1, p2) -> {
                        if (p1.getSortOrder() == null && p2.getSortOrder() == null) return 0;
                        if (p1.getSortOrder() == null) return 1;
                        if (p2.getSortOrder() == null) return -1;
                        return p1.getSortOrder().compareTo(p2.getSortOrder());
                    });

                    List<Mono<PackageSearchVO.ProductVO>> productVOMonos = productEntities.stream()
                            .map(this::convertToProductVOWithPricing)
                            .collect(Collectors.toList());

                    return Flux.concat(productVOMonos)
                            .collectList()
                            .map(products -> {
                                vo.setProducts(products);
                                return vo;
                            });
                })
                .defaultIfEmpty(vo);
    }

    /**
     * 转换为产品VO（包含定价信息）
     */
    private Mono<PackageSearchVO.ProductVO> convertToProductVOWithPricing(HdsProductEntity productEntity) {
        PackageSearchVO.ProductVO productVO = new PackageSearchVO.ProductVO();
        BeanUtils.copyProperties(productEntity, productVO);

        // 根据产品ID查询定价列表
        return productPricingService.findByProductId(productEntity.getId())
                .sort(Comparator.comparing(HdsProductPricingEntity::getPeriodType))
                .map(this::convertToPricingVO)
                .collectList()
                .map(pricingList -> {
                    productVO.setPricing(pricingList);
                    return productVO;
                })
                .defaultIfEmpty(productVO);
    }

    /**
     * 转换为定价VO
     */
    private PackageSearchVO.PricingVO convertToPricingVO(HdsProductPricingEntity pricingEntity) {
        PackageSearchVO.PricingVO pricingVO = new PackageSearchVO.PricingVO();
        pricingVO.setId(pricingEntity.getId());
        pricingVO.setPeriodType(pricingEntity.getPeriodType());

        // 处理价格字段，保留2位小数
        if (pricingEntity.getMarketPrice() != null) {
            pricingVO.setMarketPrice(pricingEntity.getMarketPrice().toString());
        }
        if (pricingEntity.getDiscountPrice() != null) {
            pricingVO.setDiscountPrice(pricingEntity.getDiscountPrice().toString());
        }
        if (pricingEntity.getDiscountRate() != null) {
            pricingVO.setDiscountRate(pricingEntity.getDiscountRate().toString());
        }

        return pricingVO;
    }

    /**
     * 根据ID查询套餐详情
     */
    public Mono<PackageVO> findById(Integer id) {
        return hdsPackageRepository.findById(id)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "套餐不存在")))
                .flatMap(packageEntity -> {
                    // 查询产品和定价信息
                    return productService.findByPackageCode(packageEntity.getPackageCode())
                            .flatMap(product -> productPricingService.findByProductId(product.getId())
                                    .collectList()
                                    .map(pricingList -> {
                                        PackageVO.ProductVO productVO = new PackageVO.ProductVO();
                                        BeanUtils.copyProperties(product, productVO);
                                        productVO.setProductDescription(JacksonUtils.readValues(product.getProductDescription(), String.class));
                                        productVO.setPricing(pricingList.stream()
                                                .map(this::convertToPricingDetailVO)
                                                .collect(Collectors.toList()));
                                        return productVO;
                                    }))
                            .collectList()
                            .map(products -> {
                                PackageVO vo = new PackageVO();
                                vo.setPaymentMode(packageEntity.getPayMode());
                                BeanUtils.copyProperties(packageEntity, vo);
                                vo.setProducts(products);
                                return vo;
                            });
                });
    }

    /**
     * 转换为定价VO
     */
    private PackageVO.PricingVO convertToPricingDetailVO(HdsProductPricingEntity entity) {
        PackageVO.PricingVO vo = new PackageVO.PricingVO();
        BeanUtils.copyProperties(entity, vo);
        if (entity.getMarketPrice() != null) {
            vo.setMarketPrice(entity.getMarketPrice().toString());
        }
        if (entity.getDiscountPrice() != null) {
            vo.setDiscountPrice(entity.getDiscountPrice().toString());
        }
        if (entity.getDiscountRate() != null) {
            vo.setDiscountRate(entity.getDiscountRate().toString());
        }
        return vo;
    }

    /**
     * 启用/停用套餐
     */
    public Mono<Boolean> updateStatus(PackageStatusReq req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> hdsPackageRepository.findByPackageCode(req.getPackageCode())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "该套餐不存在")))
                        .flatMap(packageEntity -> {
                            // 如果是停用操作，需要先检查是否是推荐套餐
                            if (Objects.equals(req.getStatus(), 0)) {
                                if (Objects.equals(packageEntity.getIsRecommend(), 1)) {
                                    return Mono.error(new BusinessException(
                                            ResultCode.INVALID_PARAMETER,
                                            "该套餐是推荐套餐，请先停用"));
                                }
                                // 如果是停用操作，检查是否有门店关联
                                return checkHotelAssociations(req.getPackageCode())
                                        .flatMap(hasAssociations -> {
                                            if (hasAssociations) {
                                                return Mono.error(new BusinessException(
                                                        ResultCode.INVALID_PARAMETER,
                                                        "该套餐关联有门店付费套餐，不支持停用，请先将门店付费套餐停用"));
                                            }
                                            packageEntity.setStatus(req.getStatus());
                                            packageEntity.setUpdatedBy(headerInfo.getUserId());
                                            packageEntity.setUpdatedByName(headerInfo.getUsername());
                                            packageEntity.setUpdatedAt(LocalDateTime.now());

                                            return hdsPackageRepository.save(packageEntity)
                                                    .thenReturn(true);
                                        });
                            } else {
                                // 启用操作，直接更新
                                packageEntity.setStatus(req.getStatus());
                                packageEntity.setUpdatedBy(headerInfo.getUserId());
                                packageEntity.setUpdatedByName(headerInfo.getUsername());
                                packageEntity.setUpdatedAt(LocalDateTime.now());
                                return hdsPackageRepository.save(packageEntity)
                                        .thenReturn(true);
                            }
                        })).onErrorResume(e -> {
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    log.error("启用/停用套餐: {}", e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "启用/停用套餐"));
                });
    }

    /**
     * 检查套餐是否有门店关联 & 是启用状态
     */
    private Mono<Boolean> checkHotelAssociations(String packageCode) {
        // 查询是否有门店套餐关联了这个套餐且状态为启用
        return hotelPackageService.existsByPackageCode(packageCode, 1);

    }

    /**
     * 删除产品
     *
     * @param req
     * @return
     */
    public Mono<Boolean> deleteProduct(PackageProductDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getPackageCode()), "packageCode must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getProductId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo ->
                        // 检查是否有启用状态的门店套餐关联了这个套餐
                        hotelPackageService.existsByPackageCode(req.getPackageCode(), 1)
                                .flatMap(exists -> {
                                    if (exists) {
                                        // 如果有门店套餐关联，不允许删除产品
                                        return Mono.error(new BusinessException("NOT_ALLOWED", "该产品和启用状态的门店套餐有关联，不能删除"));
                                    }
                                    // 没有门店套餐关联，则可以删除产品
                                    return productService.deleteProducts(req.getPackageCode(), req.getProductId());
                                })
                )
                .onErrorResume(ex -> {
                    if (ex instanceof BusinessException) {
                        return Mono.error(ex);
                    }
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                            "hotel package delete error: " + ex.getMessage()));
                });
    }

    /**
     * 添加产品
     *
     * @param req
     * @return
     */
    public Mono<Boolean> addProduct(PackageSaveReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getPackageCode()), "packageCode must not be null");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(req.getProducts()), "packageCode must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> hdsPackageRepository.findByPackageCode(req.getPackageCode())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "套餐不存在")))
                        .flatMap(packageEntity -> {
                            // 检查是否有门店关联
                            return hotelPackageService.existsByPackageCode(req.getPackageCode(), 1)
                                    .flatMap(hasHotelAssociation -> {
                                        if (hasHotelAssociation) {
                                            return Mono.error(new BusinessException("NOT_ALLOWED",
                                                    "该套餐已关联启用状态的门店套餐，不能修改产品"));
                                        }
                                        // 创建产品
                                        return productService.createProductsWithPricing(req.getPackageCode(), req, headerInfo);
                                    });
                        }))
                .onErrorResume(ex -> {
                    if (ex instanceof BusinessException) {
                        return Mono.error(ex);
                    }
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                            "添加产品失败: " + ex.getMessage()));
                });
    }

    /**
     * 设置推荐套餐
     */
    public Mono<Boolean> updateRecommend(PackageRecommendReq req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    if (Objects.equals(req.getIsRecommend(), 1)) {
                        return hdsPackageRepository.existsByIsRecommend(1)
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "已存在推荐套餐"));
                                    }
                                    return updateRecommendStatus(req.getPackageCode(), req.getIsRecommend(), headerInfo);
                                });
                    }
                    return updateRecommendStatus(req.getPackageCode(), req.getIsRecommend(), headerInfo);
                }).onErrorResume(e -> {
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    log.error("设置推荐套餐: {}", e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "设置推荐套餐"));
                });
    }

    private Mono<Boolean> updateRecommendStatus(String packageCode, Integer isRecommend, HeaderUtils.HeaderInfo headerInfo) {
        return hdsPackageRepository.findByPackageCode(packageCode)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "套餐不存在")))
                .flatMap(packageEntity -> {
                    if (packageEntity.getStatus().equals(0)) {
                        return Mono.error(new BusinessException("NOT_ALLOWED", "该套餐已停用，不能设为推荐"));
                    }
                    packageEntity.setIsRecommend(isRecommend);
                    packageEntity.setUpdatedBy(headerInfo.getUserId());
                    packageEntity.setUpdatedByName(headerInfo.getUsername());
                    packageEntity.setUpdatedAt(LocalDateTime.now());

                    return hdsPackageRepository.save(packageEntity)
                            .thenReturn(true);
                });
    }

    /**
     * 获取所有启用的套餐列表
     */
    public Mono<List<PackageSearchVO>> getEnabledPackages() {
        Criteria criteria = Criteria.where(HdsPackageFieldEnum.status.name()).is(1);
        Query query = Query.query(criteria)
                .sort(Sort.by(Sort.Direction.DESC, HdsPackageFieldEnum.is_recommend.name()))
                .sort(Sort.by(Sort.Direction.DESC, HdsPackageFieldEnum.created_at.name()));

        return r2dbcEntityTemplate.select(query, HdsPackageEntity.class)
                .flatMap(this::convertToSearchVOWithProducts)
                .collectList();
    }

    public Mono<HdsPackageEntity> findByPackageCode(String packageCode) {
        return hdsPackageRepository.findByPackageCode(packageCode);
    }

    public Flux<HdsPackageEntity> getRecommendedPackages() {
        return hdsPackageRepository.findByIsRecommend(1);
    }

    /**
     * 根据套餐名称预过滤
     */
    public Mono<Criteria> preFilterByPackageName(String packageName, Criteria baseCriteria) {
        // 先在标准套餐表中查询匹配的套餐
        Criteria packageNameCriteria = Criteria.where(HdsPackageFieldEnum.package_name.name())
                .like("%" + packageName + "%");

        return r2dbcEntityTemplate.select(Query.query(packageNameCriteria), HdsPackageEntity.class)
                .map(HdsPackageEntity::getPackageCode)
                .collectList()
                .map(packageCodes -> {
                    if (packageCodes.isEmpty()) {
                        // 没有找到匹配的套餐，返回永远不匹配的条件
                        return baseCriteria.and(HdsPackageFieldEnum.package_code.name()).is("__NOT_EXIST__");
                    } else {
                        // 找到匹配套餐，添加套餐编码过滤条件
                        return baseCriteria.and(HdsPackageFieldEnum.package_code.name()).in(packageCodes);
                    }
                });
    }
}