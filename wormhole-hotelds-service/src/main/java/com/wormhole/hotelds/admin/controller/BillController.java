package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.BillSearchReq;
import com.wormhole.hotelds.admin.model.vo.BillSearchVO;
import com.wormhole.hotelds.admin.service.BillService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

@RestController
@RequestMapping("/bill")
public class BillController {

    @Resource
    private BillService billService;

    /**
     * 账单列表查询
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<BillSearchVO>>> search(@RequestBody BillSearchReq req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> Mono.zip(billService.searchCount(req, headerInfo), billService.search(req, headerInfo))
                        .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                        .flatMap(Result::success));
    }


}