package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.*;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Set;

/**
 * @Author：flx
 * @Date：2025/3/26 10:24
 * @Description：商户Repository
 */
@Repository
public interface MerchantRepository extends ReactiveCrudRepository<HdsMerchantEntity, Integer> {

    Flux<HdsMerchantEntity> findAllByMerchantIdIn(@Param("allMerchantIds") Set<String> allMerchantIds);

    Flux<HdsMerchantEntity> findByMerchantNameLikeOrSubjectNameLike(String merchantName, String subjectName);


    /**
     * 检查商户编码是否存在
     *
     * @param merchantId 商户编码
     * @return 是否存在
     */
    Mono<Boolean> existsByMerchantId(@Param("merchantId") String merchantId);

    Mono<HdsMerchantEntity> findByMerchantId(String merchantId);

    /**
     * 根据商户名称查询商户信息
     * @param merchantName
     * @return
     */
    Mono<HdsMerchantEntity> findByMerchantName(String merchantName);

    /**
     * 根据商户简称查询商户信息
     * @param subjectName
     * @return
     */
    Mono<HdsMerchantEntity> findBySubjectName(String subjectName);

    /**
     * 根据商户名称和商户简称查询商户信息
     * @param merchantName
     * @param merchantShortName
     * @return
     */
    Mono<HdsMerchantEntity> findByMerchantNameAndSubjectName(String merchantName, String merchantShortName);
}
