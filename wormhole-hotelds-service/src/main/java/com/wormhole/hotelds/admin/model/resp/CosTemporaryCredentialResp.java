package com.wormhole.hotelds.admin.model.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/8/1 10:21
 * @Description：CosTemporaryCredentialResp
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CosTemporaryCredentialResp {

    /**
     * 临时密钥ID
     */
    private String tmpSecretId;

    /**
     * 临时密钥Key
     */
    private String tmpSecretKey;

    /**
     * 会话Token
     */
    private String sessionToken;

    /**
     * 存储桶名称
     */
    private String bucket;

    /**
     * 地域
     */
    private String region;

    /**
     * 路径
     */
    private String path;
}
