package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/5/20 10:24
 * @Description：MerchantLeadSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserLeadSaveReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 被邀请人ID
     */
    private Integer inviteeId;

    /**
     * 邀请码
     */
    private String inviteCode;
}
