package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/5/23 09:14
 * @Description：HotelLeadSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelLeadSearchVO {

    private Long id;

    /**
     * 门店线索编码
     */
    private String leadCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 携程EBK链接
     */
    private String ctripEbkUrl;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 确认绑定EBK
     */
    private boolean bindEbk;

    /**
     * 设置竞对酒店
     */
    private boolean setRivalHotel;

    /**
     * 同步全量点评
     */
    private boolean syncReview;

    /**
     * 同步酒店静态信息
     */
    private boolean syncStaticInfo;

    /**
     * 同步竞对评论
     */
    private boolean syncRivalReview;

    /**
     * 同步竞对价格
     */
    private boolean syncRivalPrice;

    /**
     * 生成初始化报告
     */
    private boolean generateReport;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 创建完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /**
     * 创建门店时长(格式化)
     * 格式: X小时X分钟
     */
    private String creationDurationFormatted;
}
