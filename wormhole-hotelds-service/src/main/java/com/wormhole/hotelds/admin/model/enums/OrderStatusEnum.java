package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/6/20 11:04
 * @Description：订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    /**
     * 待支付
     */
    PENDING(0, "待支付"),

    /**
     * 已支付
     */
    PAID(1, "已支付"),

    /**
     * 已取消
     */
    CANCELLED(2, "已取消");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     */
    public static OrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否是合法的状态码
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
