package com.wormhole.hotelds.admin.service;

import com.wormhole.hotelds.admin.model.entity.ServiceProviderEntity;
import com.wormhole.hotelds.admin.model.req.ServiceProviderDeleteReq;
import com.wormhole.hotelds.admin.model.req.ServiceProviderSaveReq;
import com.wormhole.hotelds.admin.model.req.ServiceProviderSearchReq;
import com.wormhole.hotelds.admin.model.req.ServiceProviderVO;
import com.wormhole.hotelds.admin.repository.ServiceProviderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/3/27 17:30
 * @Description：ServiceProviderService
 */
@Slf4j
@Service
public class ServiceProviderService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private ServiceProviderRepository serviceProviderRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    /**
     * 创建记录
     */
    public Mono<Boolean> create(ServiceProviderSaveReq req) {
        return serviceProviderRepository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 更新记录
     */
    public Mono<Boolean> update(ServiceProviderSaveReq req) {
        return serviceProviderRepository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 删除记录
     */
    public Mono<Boolean> delete(ServiceProviderDeleteReq req) {
        return serviceProviderRepository.deleteById(req.getId())
                .thenReturn(true);
    }

    /**
     * 搜索列表
     */
    public Mono<List<ServiceProviderVO>> search(ServiceProviderSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        Sort sort = Sort.by(Sort.Direction.DESC, "created_at");
        Query query = Query.query(criteria)
                .sort(sort)
                .limit(req.getPageSize())
                .offset((long) (req.getCurrent() - 1) * req.getPageSize());
        return r2dbcEntityTemplate.select(query,ServiceProviderEntity.class)
                .map(this::convertToVO)
                .collectList();
    }

    /**
     * 根据ID查询
     */
    public Mono<ServiceProviderVO> findById(Integer id) {
        return serviceProviderRepository.findById(id)
                .map(this::convertToVO);
    }

    private Criteria buildSearchCriteria(ServiceProviderSearchReq req) {
        Criteria criteria = Criteria.empty();
        // TODO: 根据实际查询条件构建Criteria
        return criteria;
    }

    private ServiceProviderEntity convertToEntity(ServiceProviderSaveReq req) {
        ServiceProviderEntity entity = new ServiceProviderEntity();
        // TODO: 实现请求对象到实体的转换
        return entity;
    }

    private ServiceProviderVO convertToVO(ServiceProviderEntity entity) {
        ServiceProviderVO vo = new ServiceProviderVO();
        // TODO: 实现实体到VO的转换
        return vo;
    }
}
