package com.wormhole.hotelds.admin.consumer;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.OrderStatusEnum;
import com.wormhole.hotelds.admin.model.req.OrderMessageReq;
import com.wormhole.hotelds.admin.repository.HdsOrderRepository;
import com.wormhole.hotelds.constant.SystemConstant;
import com.wormhole.hotelds.core.model.entity.HdsOrderEntity;
import com.wormhole.hotelds.core.model.entity.HdsOrderFieldEnum;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/6/20 10:55
 * @Description：OrderOverdueListener
 */
@Component
@RocketMQMessageListener(
        topic = SystemConstant.ORDER_OVERDUE_EVENT_TOPIC,
        consumerGroup = SystemConstant.ORDER_OVERDUE_CONSUMER_GROUP
)
@Slf4j
public class OrderOverdueListener extends AbstractReactiveMessageListener<OrderMessageReq> {

    @Resource
    private HdsOrderRepository hdsOrderRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Override
    protected Mono<Void> processMessage(OrderMessageReq payload) {
        OrderMessageReq req = JacksonUtils.convertValue(payload, OrderMessageReq.class);
        log.info("OrderOverdueListener monitor payload {}  ", JacksonUtils.writeValueAsString(payload));

        String orderNo = req.getOrderNo();
        return hdsOrderRepository.findByOrderNo(orderNo)
                .switchIfEmpty(Mono.defer(() -> {
                    log.warn("Order not found for orderNo: {}", orderNo);
                    return Mono.empty();
                }))
                .flatMap(order -> {
                    // 只处理待支付的订单
                    if (ObjectUtil.notEqual(order.getOrderStatus(), OrderStatusEnum.PENDING.getCode())) {
                        log.info("Order {} is not in pending payment status, current status: {}",
                                orderNo, OrderStatusEnum.getByCode(order.getOrderStatus()));
                        return Mono.empty();
                    }

                    // 更新订单状态为已取消
                    return updateOrderOverdue(orderNo, OrderStatusEnum.CANCELLED.getCode())
                            .then() // 将 Mono<Long> 转换为 Mono<Void>
                            .doOnSuccess(v -> log.info("Order {} has been cancelled due to timeout", orderNo));
                })
                .onErrorResume(e -> {
                    log.error("Failed to process order timeout for orderNo: {}", orderNo, e);
                    return Mono.empty();
                });
    }

    private Mono<Long> updateOrderOverdue(String orderNo, Integer orderStatus) {
        return r2dbcEntityTemplate.update(
                Query.query(Criteria.where(HdsOrderFieldEnum.order_no.name()).is(orderNo)),
                Update.update(HdsOrderFieldEnum.order_status.name(), orderStatus),
                HdsOrderEntity.class
        ).doOnSuccess(count -> {
            if (count > 0) {
                log.info("Successfully updated order status to {} for orderNo: {}", orderStatus, orderNo);
            } else {
                log.warn("No order was updated for orderNo: {}", orderNo);
            }
        });
    }
}
