package com.wormhole.hotelds.admin.service.strategy;

import com.wormhole.common.enums.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.model.req.GetAuthInfoReq;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.*;
import com.wormhole.hotelds.constant.RoleEnum;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.extern.slf4j.*;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.util.*;
import java.util.stream.*;

/**
 * @author: huangweijie
 * @date: 2025/5/15
 * @Description: 默认认证策略实现
 */
@Component
@Slf4j
public class DefaultAuthStrategy implements AuthSourceStrategy<AuthVO> {

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private HdsHotelMappingService hotelMappingService;

    @Resource
    private HotelWithExternalService hotelWithExternalService;

    @Resource
    private HotelService hotelService;

    @Resource
    private HotelLeadService hotelLeadService;

    @Override
    public Mono<AuthVO> processAuthInfo(GetAuthInfoReq getAuthInfoReq,AuthVO authVO, HeaderUtils.HeaderInfo headerInfo) {
        return getUserHotels(authVO);
    }

    private Mono<AuthVO> getUserHotels(AuthVO authVO) {
        // 获取用户可见酒店列表
        Mono<List<HotelVO>> hotelListMono = Objects.equals(authVO.getType(), 1)
                ? hotelService.queryList()
                : hdsEmployeeHotelService.listByEmployeeId(authVO.getId(), EmployeeStatusEnum.ACTIVE.getCode(), null)
                .map(this::filterHotelCodes)
                .flatMap(hdsEmployeeHotelEntities -> {
                    if (CollectionUtils.isEmpty(hdsEmployeeHotelEntities)) {
                        return Mono.just(Collections.emptyList());
                    }

                    List<String> hotelCodeList = hdsEmployeeHotelEntities.stream().map(HdsEmployeeHotelEntity::getHotelCode).collect(Collectors.toList());

                    return hotelService.findByHotelCodes(hotelCodeList).collectList();
                });

        return hotelListMono.flatMap(hotelList -> {
            if (CollectionUtils.isEmpty(hotelList)) {
                authVO.setHotels(Collections.emptyList());
                return Mono.just(authVO);
            }
            return processHotels(hotelList, authVO);
        });
    }

    private List<HdsEmployeeHotelEntity> filterHotelCodes(List<HdsEmployeeHotelEntity> employeeHotels) {
        String hotelAdminCode = RoleEnum.HOTEL_ADMIN.getCode();
        String otaAgentAdminCode = RoleEnum.OTA_AGENT_ADMIN.getCode();

        return employeeHotels.stream()
                .filter(employeeHotel -> {
                    // 检查单一角色字段
//                    boolean matchSingleRole = StringUtils.equals(employeeHotel.getRoleCode(), hotelAdminCode) ||
//                            StringUtils.equals(employeeHotel.getRoleCode(), otaAgentAdminCode);

                    // 检查多角色字段
                    boolean matchMultiRoles = Optional.ofNullable(employeeHotel.getRoleCode())
                            .filter(StringUtils::isNotBlank)
                            .map(roleCodes -> {
                                // 将逗号分隔的角色拆分为列表
                                List<String> roleCodeList = Arrays.stream(roleCodes.split(","))
                                        .map(String::trim)
                                        .filter(StringUtils::isNotBlank)
                                        .toList();

                                // 检查是否包含目标角色
                                return roleCodeList.contains(hotelAdminCode) ||
                                        roleCodeList.contains(otaAgentAdminCode);
                            })
                            .orElse(false);

                    // 两种条件有一个满足即可
                    return matchMultiRoles;
                })
                .collect(Collectors.toList());
    }

    private Mono<AuthVO> processHotels(List<HotelVO> hotelList, AuthVO authVO) {
//        List<String> hotelCodeList = hotelList.stream()
//                .filter(hotelVO -> !Objects.equals(0, hotelVO.getStatus()) && Objects.equals(1, hotelVO.getRowStatus()))
//                .map(HotelVO::getHotelCode)
//                .toList();

        List<HotelWithExternalVO> unmappedHotels = hotelList.stream()
                .filter(hotelVO -> !Objects.equals(0, hotelVO.getStatus()) && Objects.equals(1, hotelVO.getRowStatus()))
                .map(hotelVO -> new HotelWithExternalVO()
                        .setHotelCode(hotelVO.getHotelCode())
                        .setHotelName(hotelVO.getHotelName())
                        .setExternalHotels(Collections.emptyList()))
                .toList();

        return Flux.fromIterable(unmappedHotels)
                            .flatMap(vo -> hotelLeadService.findByHotelCode(vo.getHotelCode())
                                    .map(hotelLeadVO -> {
                                        vo.setHotelLead(hotelLeadVO);
                                        return vo;
                                    }).onErrorResume(e -> {
                                        log.warn("Hotel lead not found for hotel code: {}, continuing execution.", vo.getHotelCode());
                                        return Mono.just(vo);
                                    })
                            )
                            .collectList()
                            .map(updatedHotelWithExternalVOList -> {
                                authVO.setHotels(unmappedHotels);
                                return authVO;
                            });

//        return hotelMappingService.listHotelMappingVOByHotelCodeList(hotelCodeList)
//                .flatMap(hotelMappingVOList -> {
//                    List<HotelWithExternalVO> hotelWithExternalVOList = mergeHotelData(hotelList, hotelMappingVOList);
//
//                    // 并行处理每个酒店的 HotelLeadVO
//                    return Flux.fromIterable(hotelWithExternalVOList)
//                            .flatMap(vo -> hotelLeadService.findByHotelCode(vo.getHotelCode())
//                                    .map(hotelLeadVO -> {
//                                        vo.setHotelLead(hotelLeadVO);
//                                        return vo;
//                                    }).onErrorResume(e -> {
//                                        log.warn("Hotel lead not found for hotel code: {}, continuing execution.", vo.getHotelCode());
//                                        return Mono.just(vo);
//                                    })
//                            )
//                            .collectList()
//                            .map(updatedHotelWithExternalVOList -> {
//                                authVO.setHotels(updatedHotelWithExternalVOList);
//                                return authVO;
//                            });
//                });
    }

    private List<HotelWithExternalVO> mergeHotelData(List<HotelVO> hotelList, List<HotelMappingVO> hotelMappingVOList) {
        List<HotelWithExternalVO> hotelWithExternalVOList = new ArrayList<>(hotelWithExternalService.convertToHotelWithExternalVOList(hotelMappingVOList));

        Set<String> mappedCodes = hotelMappingVOList.stream()
                .map(HotelMappingVO::getHotelCode)
                .collect(Collectors.toSet());

        List<HotelWithExternalVO> unmappedHotels = hotelList.stream()
                .filter(hotelVO -> !mappedCodes.contains(hotelVO.getHotelCode()))
                .map(hotelVO -> new HotelWithExternalVO()
                        .setHotelCode(hotelVO.getHotelCode())
                        .setHotelName(hotelVO.getHotelName())
                        .setExternalHotels(Collections.emptyList()))
                .toList();

        hotelWithExternalVOList.addAll(unmappedHotels);
        return hotelWithExternalVOList;
    }

    @Override
    public String getSourceCode() {
        return SourcePlatform.OTHER.getCode();
    }
}