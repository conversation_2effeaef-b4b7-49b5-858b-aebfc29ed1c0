package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/6/19 10:02
 * @Description：PaymentModeEnum
 */
@AllArgsConstructor
@Getter
public enum PaymentModeEnum {

    /**
     * 按房间数量收费
     */
    ROOM_COUNT(0, "按房间数量收费"),

    /**
     * 按门店收费
     */
    HOTEL(1, "按门店收费");

    /**
     * 付费方式代码
     */
    private final Integer code;

    /**
     * 付费方式描述
     */
    private final String description;

    /**
     * 获取付费方式代码
     *
     * @return 付费方式代码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取付费方式描述
     *
     * @return 付费方式描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取付费方式枚举
     *
     * @param code 付费方式代码
     * @return 付费方式枚举，如果未找到则返回null
     */
    public static PaymentModeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PaymentModeEnum paymentMode : values()) {
            if (paymentMode.getCode().equals(code)) {
                return paymentMode;
            }
        }
        return null;
    }

    /**
     * 根据描述获取付费方式枚举
     *
     * @param description 付费方式描述
     * @return 付费方式枚举，如果未找到则返回null
     */
    public static PaymentModeEnum getByDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }
        for (PaymentModeEnum paymentMode : values()) {
            if (paymentMode.getDescription().equals(description)) {
                return paymentMode;
            }
        }
        return null;
    }

    /**
     * 判断给定代码是否有效
     *
     * @param code 付费方式代码
     * @return 如果代码有效返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
