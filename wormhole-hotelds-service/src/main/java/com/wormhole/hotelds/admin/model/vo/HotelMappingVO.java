package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

/**
 * @Description:
 * @Author: wj
 * @Date: 2025/4/9 13:49
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelMappingVO {

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 外部系统酒店id
     */
    private String externalId;

    /**
     * 外部系统酒店名称
     */
    private String externalName;

    /**
     * 外部集团或系统标识
     */
    private String externalGroup;
}
