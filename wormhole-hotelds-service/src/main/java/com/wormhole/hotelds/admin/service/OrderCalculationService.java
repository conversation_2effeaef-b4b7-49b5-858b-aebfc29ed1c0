package com.wormhole.hotelds.admin.service;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.hotelds.admin.model.enums.PaymentModeEnum;
import com.wormhole.hotelds.admin.model.req.OrderConfirmReq;
import com.wormhole.hotelds.admin.model.vo.OrderConfirmVO;
import com.wormhole.hotelds.core.model.entity.HdsPackageEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/6/19 14:38
 * @Description：OrderCalculationService
 */
@Service
@Slf4j
public class OrderCalculationService {

    @Resource
    private HotelService hotelService;

    @Resource
    private PackageService packageService;

    @Resource
    private ProductService productService;

    @Resource
    private ProductPricingService productPricingService;

    @Resource
    private HotelProductPricingService hotelProductPricingService;

    /**
     * 计算订单信息 - 公共方法
     *
     * @param req       确认订单请求
     * @param hotelCode 门店编码
     * @return 订单确认信息
     */
    public Mono<OrderConfirmVO> calculateOrder(OrderConfirmReq req, String hotelCode) {
        // 1. 获取套餐信息
        return packageService.findByPackageCode(req.getPackageCode())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "套餐不存在")))
                .flatMap(packageEntity -> {
                    // 2. 获取产品信息
                    return productService.findById(req.getProductId())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "产品不存在")))
                            .flatMap(productEntity -> {
                                // 3. 根据是否有门店个性化套餐选择定价策略
                                if (StringUtils.isNotBlank(req.getHotelPackageCode())) {
                                    // 使用门店个性化定价
                                    return calculateHotelCustomPricing(req, packageEntity, hotelCode);
                                } else {
                                    // 使用标准套餐定价
                                    return calculateStandardPricing(req, packageEntity, hotelCode);
                                }
                            });
                });
    }

    /**
     * 计算门店个性化定价
     */
    private Mono<OrderConfirmVO> calculateHotelCustomPricing(OrderConfirmReq req,
                                                             HdsPackageEntity packageEntity,
                                                             String hotelCode) {
        return hotelProductPricingService.findByHotelPackageCodeAndProductIdAndPeriodType(
                        req.getHotelPackageCode(), req.getProductId(), req.getPeriodType())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "门店个性化定价不存在")))
                .flatMap(hotelPricingEntity -> {
                    Integer paymentMode = packageEntity.getPayMode();

                    // 根据付费方式计算最终价格
                    return calculateFinalPriceWithHotelInfo(hotelPricingEntity.getCustomMarketPrice(),
                            hotelPricingEntity.getCustomDiscountPrice(),
                            paymentMode, hotelCode)
                            .map(priceAndHotelInfo -> {
                                // 计算到期时间
                                LocalDateTime expireTime = calculateExpireTime(req.getPeriodType(), priceAndHotelInfo.otaEndTime());

                                // 构建计算结果
                                return OrderConfirmVO.builder()
                                        .packageName(packageEntity.getPackageName())
                                        .marketPrice(priceAndHotelInfo.finalMarketPrice().toString())
                                        .discountPrice(priceAndHotelInfo.finalDiscountPrice().toString())
                                        .actualPayAmount(priceAndHotelInfo.finalPrice().toString())
                                        .expireTime(expireTime)
                                        .periodType(req.getPeriodType())
                                        .build();
                            });
                });
    }

    /**
     * 计算标准套餐定价
     */
    private Mono<OrderConfirmVO> calculateStandardPricing(OrderConfirmReq req,
                                                          HdsPackageEntity packageEntity,
                                                          String hotelCode) {
        return productPricingService.findByProductIdAndPeriodType(req.getProductId(), req.getPeriodType())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "标准定价不存在")))
                .flatMap(standardPricingEntity -> {
                    Integer paymentMode = packageEntity.getPayMode();

                    // 根据付费方式计算最终价格
                    return calculateFinalPriceWithHotelInfo(standardPricingEntity.getMarketPrice(),
                            standardPricingEntity.getDiscountPrice(),
                            paymentMode, hotelCode)
                            .map(priceAndHotelInfo -> {
                                // 计算到期时间
                                LocalDateTime expireTime = calculateExpireTime(req.getPeriodType(), priceAndHotelInfo.otaEndTime());

                                // 构建计算结果
                                return OrderConfirmVO.builder()
                                        .packageName(packageEntity.getPackageName())
                                        .marketPrice(priceAndHotelInfo.finalMarketPrice().toString())
                                        .discountPrice(priceAndHotelInfo.finalDiscountPrice().toString())
                                        .actualPayAmount(priceAndHotelInfo.finalPrice().toString())
                                        .expireTime(expireTime)
                                        .periodType(req.getPeriodType())
                                        .build();
                            });
                });
    }

    /**
     * 根据付费方式计算最终价格（同时获取门店信息用于计算到期时间）
     */
    private Mono<PriceAndHotelInfo> calculateFinalPriceWithHotelInfo(
            BigDecimal marketPrice,
            BigDecimal discountPrice,
            Integer paymentMode,
            String hotelCode) {
        return hotelService.findByHotelCode(hotelCode)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "门店不存在")))
                .map(hotelVO -> {
                    BigDecimal finalMarketPrice;
                    BigDecimal finalDiscountPrice;
                    BigDecimal findlActualPrice;
                    if (PaymentModeEnum.ROOM_COUNT.getCode().equals(paymentMode)) {
                        // 按房间数量收费：查询门店房间数量
                        Integer totalRoom = hotelVO.getTotalRoom();
                        if (totalRoom == null || totalRoom <= 0) {
                            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "门店房间数量配置错误");
                        }
                        finalMarketPrice = marketPrice.multiply(BigDecimal.valueOf(totalRoom));
                        findlActualPrice = discountPrice.multiply(BigDecimal.valueOf(totalRoom));
                        finalDiscountPrice = finalMarketPrice.subtract(findlActualPrice);
                    } else {
                        // 按门店收费：直接返回基础价格
                        finalMarketPrice = marketPrice;
                        finalDiscountPrice = marketPrice.subtract(discountPrice);
                        findlActualPrice = discountPrice;
                    }

                    return new PriceAndHotelInfo(finalMarketPrice, finalDiscountPrice, findlActualPrice, hotelVO.getOtaEndTime());
                });
    }

    /**
     * 计算到期时间
     */
    private LocalDateTime calculateExpireTime(Integer periodType, String otaEndTime) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime;

        // 解析门店当前到期时间
        LocalDateTime currentExpireTime = parseOtaEndTime(otaEndTime);

        // 判断服务是否过期
        if (currentExpireTime == null || currentExpireTime.isBefore(now)) {
            // 服务已过期：从当前时间开始计算
            startTime = now;
        } else {
            // 服务未过期：从到期时间开始计算
            startTime = currentExpireTime;
        }

        // 根据计费周期计算新的到期时间
        LocalDateTime newExpireTime = switch (periodType) {
            case 1 -> // 月度
                    startTime.plusMonths(1);
            case 2 -> // 季度
                    startTime.plusMonths(3);
            case 3 -> // 年度
                    startTime.plusYears(1);
            default -> startTime.plusMonths(1);
        };

        // 设置为当天的23:59:59
        return newExpireTime.withHour(23).withMinute(59).withSecond(59).withNano(0);
    }

    /**
     * 解析门店到期时间字符串
     */
    private LocalDateTime parseOtaEndTime(String otaEndTime) {
        if (StringUtils.isBlank(otaEndTime)) {
            return null;
        }
        return SimpleDateUtils.slashStringToEndDateTime(otaEndTime);
    }

    /**
     * 价格和门店信息的封装类
     */
    private record PriceAndHotelInfo(BigDecimal finalMarketPrice,
                                     BigDecimal finalDiscountPrice,
                                     BigDecimal finalPrice,
                                     String otaEndTime) {
    }
}
