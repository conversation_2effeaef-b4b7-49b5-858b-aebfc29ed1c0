package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.BrandDeleteReq;
import com.wormhole.hotelds.admin.model.req.BrandSaveReq;
import com.wormhole.hotelds.admin.model.req.BrandSearchReq;
import com.wormhole.hotelds.admin.model.vo.BrandVO;
import com.wormhole.hotelds.admin.service.BrandService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/3/26 14:37
 * @Description：BrandController
 */
@RestController
@RequestMapping("/brand")
public class BrandController {

    @Resource
    private BrandService brandService;

    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody BrandSaveReq brandSaveReq) {
        return brandService.create(brandSaveReq).flatMap(Result::success);
    }

    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody BrandSaveReq brandSaveReq) {
        return brandService.update(brandSaveReq).flatMap(Result::success);
    }

    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody BrandDeleteReq brandDeleteReq) {
        return brandService.delete(brandDeleteReq).flatMap(Result::success);
    }

    @PostMapping("/search")
    public Mono<Result<PageResult<BrandVO>>> search(@RequestBody BrandSearchReq brandSearchReq) {
        return Mono.zip(brandService.searchCount(brandSearchReq), brandService.search(brandSearchReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    @GetMapping("/query_list")
    public Mono<Result<List<BrandVO>>> queryList() {
        return brandService.queryList().flatMap(Result::success);
    }

    @GetMapping("/query/{id}")
    public Mono<Result<BrandVO>> findById(@PathVariable("id") Integer id) {
        return brandService.findById(id).flatMap(Result::success);
    }
}
