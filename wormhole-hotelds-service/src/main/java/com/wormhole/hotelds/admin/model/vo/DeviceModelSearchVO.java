package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import com.wormhole.hotelds.core.model.entity.HdsDeviceModelEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.*;
import lombok.experimental.*;
import org.springframework.beans.BeanUtils;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:56:47
 * @Description: DeviceModel
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceModelSearchVO  {

    /**
     * 设备型号id
     */
    private Long id;

    /**
     * 设备型号编码
     */
    private String modelCode;

    /**
     * 设备型号名称
     */
    private String modelName;

    /**
     * 设备类型
     */
    private String deviceAppType;

    /**
     * 厂商
     */
    private String manufacturer;

    /**
     * 入库设备数
     */
    private Integer inboundDeviceCount;

    /**
     * 使用门店数
     */
    private Integer inUsedHotelCount;

    /**
     * 使用设备数
     */
    private Integer inUsedDeviceCount;

    /**
     * 创建时间
     */
    private String createTime;
    public static DeviceModelSearchVO toVo(HdsDeviceModelEntity hdsDeviceModelEntity) {
        DeviceModelSearchVO deviceModelSearchVO = new DeviceModelSearchVO();
        BeanUtils.copyProperties(hdsDeviceModelEntity, deviceModelSearchVO);
        deviceModelSearchVO.setInboundDeviceCount(hdsDeviceModelEntity.getTotalInventory());
        deviceModelSearchVO.setCreateTime(SimpleDateUtils.formatLocalDateTimeToDateHour(hdsDeviceModelEntity.getCreatedAt()));
        return deviceModelSearchVO;
    }
}