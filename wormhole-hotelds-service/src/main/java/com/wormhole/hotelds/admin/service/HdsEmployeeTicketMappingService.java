package com.wormhole.hotelds.admin.service;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.common.exception.*;
import com.wormhole.common.result.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.repository.*;
import com.wormhole.hotelds.api.hotel.client.*;
import com.wormhole.hotelds.core.enums.AccountTypeEnum;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.*;
import org.springframework.data.r2dbc.core.*;
import org.springframework.data.relational.core.query.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.stream.*;

/**
 * @Description:
 * @Author: wj
 * @Date: 2025/4/16 17:24
 */
@Slf4j
@Service
public class HdsEmployeeTicketMappingService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsEmployeeTicketMappingRepository hdsEmployeeTicketMappingRepository;

    @Resource
    private HotelDsApiClient hotelDsApiClient;

    @Resource
    private DevicePositionRepository devicePositionRepository;

    /**
     * 根据employeeId和hotelCode查询映射数据
     *
     * @param employeeId 员工ID
     * @param hotelCode  酒店编码
     * @return 映射数据
     */
    public Mono<HdsEmployeeTicketMappingEntity> findByEmployeeIdAndHotelCode(Integer employeeId, String hotelCode) {
        Criteria criteria = Criteria.where(HdsEmployeeTicketMappingFieldEnum.employee_id.name()).is(String.valueOf(employeeId))
                .and(HdsEmployeeTicketMappingFieldEnum.hotel_code.name()).is(hotelCode);
        return r2dbcEntityTemplate.selectOne(Query.query(criteria), HdsEmployeeTicketMappingEntity.class)
                .switchIfEmpty(reactor.core.publisher.Mono.empty());
    }

    /**
     * 根据员工ID列表批量查询工单映射信息
     *
     * @param employeeIds 员工ID列表
     * @return 员工ID与工单映射信息列表的Map，一个员工可能对应多个酒店的映射
     */
    public Mono<Map<Integer, List<HdsEmployeeTicketMappingEntity>>> findByEmployeeIds(List<Integer> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return Mono.just(Collections.emptyMap());
        }

        Criteria criteria = Criteria.where(HdsEmployeeTicketMappingFieldEnum.employee_id.name()).in(employeeIds.stream()
                .map(Object::toString)
                .collect(Collectors.toList()));

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsEmployeeTicketMappingEntity.class)
                .collectList()
                .map(entities -> entities.stream()
                        .collect(Collectors.groupingBy(
                                entity -> Integer.valueOf(entity.getEmployeeId())
                        )));
    }

    /**
     * 根据员工ID和酒店代码列表删除映射关系
     *
     * @param employeeId 员工ID
     * @param hotelCodes 酒店代码列表
     * @return 删除结果
     */
    private Mono<Map<String, String>> deleteByEmployeeIdAndHotelCodes(Integer employeeId, List<String> hotelCodes) {
        if (employeeId == null || CollectionUtils.isEmpty(hotelCodes)) {
            return Mono.empty();
        }

        Criteria criteria = Criteria.where(HdsEmployeeTicketMappingFieldEnum.employee_id.name()).is(String.valueOf(employeeId))
                .and(HdsEmployeeTicketMappingFieldEnum.hotel_code.name()).in(hotelCodes);

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsEmployeeTicketMappingEntity.class)
                .collectList()
                .map(deletedEntities -> deletedEntities.stream()
                        .filter(entity -> StringUtils.isNotBlank(entity.getPositionCodes()))
                        .collect(Collectors.toMap(
                                HdsEmployeeTicketMappingEntity::getHotelCode,
                                HdsEmployeeTicketMappingEntity::getPositionCodes,
                                (existing, replacement) -> existing
                        ))).flatMap(hotelPositionMap -> {
                    // 删除已有的映射关系，然后返回之前收集的映射
                    return r2dbcEntityTemplate.delete(Query.query(criteria), HdsEmployeeTicketMappingEntity.class)
                            .thenReturn(hotelPositionMap);
                });

//        return r2dbcEntityTemplate.delete(Query.query(criteria), HdsEmployeeTicketMappingEntity.class)
//                .then();
    }

    /**
     * 保存员工工单类型映射
     *
     * @param employeeId           员工ID
     * @param employeeName         员工名称
     * @param hotelCodes           酒店代码列表
     * @param employeeType         员工类型 1总机 2分机
     * @param ticketAssignmentFlag 接单表权限
     * @return 保存结果
     */
    public Mono<Boolean> save(Integer employeeId, String employeeName, List<String> hotelCodes,
                              Integer employeeType,
                              Integer ticketAssignmentFlag, boolean isUpdate) {
        if (employeeId == null || CollectionUtils.isEmpty(hotelCodes)) {
            return Mono.just(false);
        }

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 先删除已有的映射关系
                    return deleteByEmployeeIdAndHotelCodes(employeeId, hotelCodes)
                            .flatMap(hotelPositionMap -> {
                                // 判断运营端创建还是门店端创建
                                if (StringUtils.isBlank(headerInfo.getHotelCode())) {
                                    // 运营端更新员工，位置不能修改
                                    if (isUpdate) {
                                        // 为每个酒店创建映射实体
                                        List<HdsEmployeeTicketMappingEntity> entities = hotelCodes.stream()
                                                .map(hotelCode -> {
                                                    HdsEmployeeTicketMappingEntity entity = new HdsEmployeeTicketMappingEntity();
                                                    entity.setEmployeeId(employeeId.toString());
                                                    entity.setEmployeeName(employeeName);
                                                    entity.setHotelCode(hotelCode);
                                                    entity.setEmployeeType(employeeType);
                                                    entity.setTicketAssignmentFlag(ticketAssignmentFlag);
                                                    if (ObjectUtil.equal(employeeType, AccountTypeEnum.MAIN_SWITCHBOARD.getCode())){
                                                        entity.setAreaCodes(StringUtils.EMPTY);
                                                    }
                                                    // 设置酒店对应的位置代码
//                                                    entity.setPositionCodes(hotelPositionMap.get(hotelCode));

                                                    entity.setAcceptTicketStatus(0); // 默认允许接单
                                                    entity.setCreatedAt(LocalDateTime.now());
                                                    entity.setCreatedBy(headerInfo.getUserId());
                                                    entity.setCreatedByName(headerInfo.getUsername());
                                                    return entity;
                                                })
                                                .collect(Collectors.toList());

                                        // 批量保存
                                        return hdsEmployeeTicketMappingRepository.saveAll(entities)
                                                .then(Mono.just(true))
                                                .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                                        "Failed to save employee-ticket mapping")))
                                                .defaultIfEmpty(false);
                                    }else {
                                        // 运营端创建，需要获取每个酒店的设备位置
                                        return devicePositionRepository.findByHotelCodeIn(hotelCodes)
                                                .collectList()
                                                .flatMap(devicePositions -> {
                                                    // 按酒店分组设备位置
                                                    Map<String, List<String>> positionMap = devicePositions.stream()
                                                            .collect(Collectors.groupingBy(
                                                                    HdsDevicePositionEntity::getHotelCode,
                                                                    Collectors.mapping(HdsDevicePositionEntity::getPositionCode, Collectors.toList())
                                                            ));

                                                    // 为每个酒店创建映射实体
                                                    List<HdsEmployeeTicketMappingEntity> entities = hotelCodes.stream()
                                                            .map(hotelCode -> {
                                                                HdsEmployeeTicketMappingEntity entity = new HdsEmployeeTicketMappingEntity();
                                                                entity.setEmployeeId(employeeId.toString());
                                                                entity.setEmployeeName(employeeName);
                                                                entity.setHotelCode(hotelCode);
                                                                entity.setEmployeeType(employeeType);
                                                                entity.setTicketAssignmentFlag(ticketAssignmentFlag);
//                                                                entity.setTicketCategories(ticketCategoriesStr);
                                                                if (ObjectUtil.equal(employeeType, AccountTypeEnum.MAIN_SWITCHBOARD.getCode())){
                                                                    entity.setAreaCodes(StringUtils.EMPTY);
                                                                }
                                                                // 设置酒店对应的位置代码
                                                                List<String> positionCodeList = positionMap.getOrDefault(hotelCode, Collections.emptyList());
//                                                                entity.setPositionCodes(StringUtils.join(positionCodeList, ","));

                                                                entity.setAcceptTicketStatus(0); // 默认允许接单
                                                                entity.setCreatedAt(LocalDateTime.now());
                                                                entity.setCreatedBy(headerInfo.getUserId());
                                                                entity.setCreatedByName(headerInfo.getUsername());
                                                                return entity;
                                                            })
                                                            .collect(Collectors.toList());

                                                    // 批量保存
                                                    return hdsEmployeeTicketMappingRepository.saveAll(entities)
                                                            .then(Mono.just(true))
                                                            .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                                                    "Failed to save employee-ticket mapping")))
                                                            .defaultIfEmpty(false);
                                                });
                                    }
                                } else {
                                    // 为每个酒店创建映射实体
                                    List<HdsEmployeeTicketMappingEntity> entities = hotelCodes.stream()
                                            .map(hotelCode -> {
                                                HdsEmployeeTicketMappingEntity entity = new HdsEmployeeTicketMappingEntity();
                                                entity.setEmployeeId(employeeId.toString());
                                                entity.setEmployeeName(employeeName);
                                                entity.setHotelCode(hotelCode);
                                                entity.setEmployeeType(employeeType);
                                                entity.setTicketAssignmentFlag(ticketAssignmentFlag);
//                                                entity.setTicketCategories(ticketCategoriesStr);
//                                                entity.setPositionCodes(positionCodesStr);
                                                entity.setAcceptTicketStatus(0); // 默认允许接单
                                                entity.setCreatedAt(LocalDateTime.now());
                                                entity.setCreatedBy(headerInfo.getUserId());
                                                entity.setCreatedByName(headerInfo.getUsername());
                                                if (ObjectUtil.equal(employeeType, AccountTypeEnum.MAIN_SWITCHBOARD.getCode())){
                                                    entity.setAreaCodes(StringUtils.EMPTY);
                                                }
                                                return entity;
                                            })
                                            .collect(Collectors.toList());

                                    // 批量保存
                                    return hdsEmployeeTicketMappingRepository.saveAll(entities)
                                            .then(Mono.just(true))
                                            .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                                    "Failed to save employee-ticket mapping")))
                                            .defaultIfEmpty(false);
                                }
                            })
//                            .then(Mono.defer(() -> ))
                            .flatMap(success -> {
//                                if (success) {
//                                    RefreshCommandReq req = new RefreshCommandReq();
//                                    req.setHotelCodes(hotelCodes);
//                                    return hotelDsApiClient.sendRefreshCommand(req, null)
//                                            .doOnError(error -> {
//                                                // 只记录错误日志，不抛出异常
//                                                log.error("Failed to send refresh command: {}", error.getMessage(), error);
//                                            })
//                                            // 无论成功或失败，都返回true表示更新成功
//                                            .thenReturn(true)
//                                            .onErrorReturn(true);
//                                }
                                return Mono.just(success);
                            });
                });
    }


}
