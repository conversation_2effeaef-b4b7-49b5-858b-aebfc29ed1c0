package com.wormhole.hotelds.admin.model.enums;

import lombok.*;

import java.util.*;
import java.util.stream.*;

@Getter
@AllArgsConstructor
public enum EmployeeTypeEnum {

    GROUP(1, "集团"),
    PROVIDER(2, "服务商"),
    HOTEL(3, "酒店"),
    GROUP_SERVICE(4, "集团客服");

    private final Integer code;
    private final String description;

    private static final Map<Integer, EmployeeTypeEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(EmployeeTypeEnum::getCode, e -> e));

    private static final Set<Integer> GROUP_EMPLOYEE_CODES = Set.of(GROUP.code, GROUP_SERVICE.code);

    public static EmployeeTypeEnum findByCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("代码不能为空");
        }
        EmployeeTypeEnum type = CODE_MAP.get(code);
        if (type == null) {
            throw new IllegalArgumentException("未知的员工类型代码: " + code);
        }
        return type;
    }

    public static boolean isGroupEmployee(Integer code) {
        return GROUP_EMPLOYEE_CODES.contains(code);
    }
}
