package com.wormhole.hotelds.admin.controller;

import com.wormhole.hotelds.admin.model.vo.HdsThemeVO;
import com.wormhole.hotelds.admin.model.req.HdsThemeSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsThemeDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsThemeSearchReq;
import com.wormhole.hotelds.admin.service.HdsThemeService;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:58:49
 * @Description: HdsTheme
 */
@RestController
@RequestMapping("/hdstheme")
public class HdsThemeController {

    @Resource
    private HdsThemeService service;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody HdsThemeSaveReq hdsThemeSaveReq) {
        return service.create(hdsThemeSaveReq).flatMap(Result::success);
    }

    /**
     * 更新记录
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HdsThemeSaveReq hdsThemeSaveReq) {
        return service.update(hdsThemeSaveReq).flatMap(Result::success);
    }

    /**
     * 删除记录
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody HdsThemeDeleteReq hdsThemeDeleteReq) {
        return service.delete(hdsThemeDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<List<HdsThemeVO>>> search(@RequestBody HdsThemeSearchReq hdsThemeSearchReq) {
        return service.search(hdsThemeSearchReq).flatMap(Result::success);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<HdsThemeVO>> findById(@PathVariable("id") Long id) {
        return service.findById(id).flatMap(Result::success);
    }
}