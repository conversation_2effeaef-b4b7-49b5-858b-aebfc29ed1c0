package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.relational.core.mapping.*;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/3/26 16:57
 * @Description：HotelSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelSaveReq {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户简称
     */
    private String merchantShortName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 门店ogo
     */
    private String hotelLogo;

    /**
     * 企业名称
     */
    private String subjectName;

    /**
     * 门店名称 必填
     */
    private String hotelName;

    /**
     * 门店名称拼音
     */
    private String hotelNamePinYin;

    /**
     * 门店类型
     */
    private Integer hotelType;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称 必填
     */
    private String countryName;

    /**
     * 省份编码 必填
     */
    private String provinceCode;

    /**
     * 省份名称 必填
     */
    private String provinceName;

    /**
     * 城市编码 必填
     */
    private String cityCode;

    /**
     * 城市名称 必填
     */
    private String cityName;

    /**
     * 区县编码 必填
     */
    private String districtCode;

    /**
     * 区县名称 必填
     */
    private String districtName;

    /**
     * 地址 必填
     */
    private String address;

    /**
     * 门店联系人
     */
    private String mainPerson;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 前台电话
     */
    private String frontPhone;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 总房间数
     */
    private Integer totalRoom;

    /**
     * 携程EBK链接
     */
    private String ctripEbkUrl;

    /**
     * 高德坐标经度
     */
    private String longitude;

    /**
     * 高德坐标纬度
     */
    private String latitude;

    /**
     * ai产品
     */
    private List<String> aiProductTypes;

    /**
     * ota延期月数（可为负数）
     */
    private Integer otaExtendMonths;

    /**
     * 来源 0:web 1:ota
     */
    private Integer source;

    /**
     * sos启用开关;0-否 1-是
     */
    private Integer sosSwitch;

    /**
     * AI客服通话类型：token_req（AI通话）、transfer_to_human（真人通话）
     */
    private String callCommand;

    /**
     * 文字对话启用开关;0-否 1-是
     */
    private Integer dialogSwitch;
}
