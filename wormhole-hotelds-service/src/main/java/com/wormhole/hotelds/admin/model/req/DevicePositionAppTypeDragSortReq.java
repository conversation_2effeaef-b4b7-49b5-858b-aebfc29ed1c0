package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/16 11:42
 * @Description：DevicePositionAppTypeSortReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DevicePositionAppTypeDragSortReq {

    private List<DevicePositionAppTypeSortReq> devicePositionAppTypes;

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class DevicePositionAppTypeSortReq {

        /**
         * 设备App类型
         */
        private String deviceAppType;

        /**
         * 设备排序
         */
        private Integer sortOrder;
    }
}
