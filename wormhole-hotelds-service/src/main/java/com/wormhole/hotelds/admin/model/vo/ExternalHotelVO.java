package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: h<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/5/15
 * @Description: 外部酒店信息VO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ExternalHotelVO {
    
    /**
     * 外部系统酒店id
     */
    private String code;

    /**
     * 外部系统酒店名称
     */
    private String name;

    /**
     * 外部集团或系统标识
     */
    private String group;
}