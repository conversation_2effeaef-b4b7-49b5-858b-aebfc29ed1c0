package com.wormhole.hotelds.admin.controller;

import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.AreaService;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 国家区域 Controller
 *
 * <AUTHOR>
 * @date 2025-03-27 11:56:48
 */
@RestController
@RequestMapping("/area")
public class AreaController {

    @Resource
    private AreaService areaService;

    /**
     * 查询列表
     */
    @ResponseBody
    @PostMapping("/list")
    public Mono<Result<List<AreaVo>>> list(@RequestBody AreaReq req) {
        return areaService.listRecursively(req).flatMap(Result::success);
    }
}