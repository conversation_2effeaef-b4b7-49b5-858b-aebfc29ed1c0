package com.wormhole.hotelds.admin.service;

import com.wormhole.common.constant.*;
import com.wormhole.common.exception.*;
import com.wormhole.common.result.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.hotelds.admin.repository.*;
import com.wormhole.hotelds.constant.RoleEnum;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.extern.slf4j.*;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.*;
import org.springframework.data.r2dbc.core.*;
import org.springframework.data.relational.core.query.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.time.*;
import java.util.*;
import java.util.stream.*;

/**
 * @Author：flx
 * @Date：2025/4/3 09:16
 * @Description：HdsEmployeeService
 */
@Service
@Slf4j
public class HdsEmployeeHotelService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsEmployeeHotelRepository hdsEmployeeHotelRepository;

    public Mono<List<HdsEmployeeHotelEntity>> listByEmployeeId(Integer employeeId, Integer status, List<String> hotelCodes) {
        return r2dbcEntityTemplate.select(buildQuery(employeeId, status, hotelCodes),
                HdsEmployeeHotelEntity.class).collectList();
    }

    private Query buildQuery(Integer employeeId, Integer status, List<String> hotelCodes) {
        Criteria criteria = Criteria.where(HdsEmployeeHotelFieldEnum.employee_id.name()).is(employeeId);

        if (status != null) {
            criteria = criteria.and(HdsEmployeeHotelFieldEnum.status.name()).is(status);
        }

        if (CollectionUtils.isNotEmpty(hotelCodes)) {
            criteria = criteria.and(HdsEmployeeHotelFieldEnum.hotel_code.name()).in(hotelCodes);
        }

        return Query.query(criteria);
    }

    /**
     * 更新员工酒店关联
     */
    public Mono<Boolean> saveOrUpdate(Integer employeeId, List<String> hotelCodes, String roleCode, Integer status) {
        if (employeeId == null || CollectionUtils.isEmpty(hotelCodes) || StringUtils.isBlank(roleCode)) {
            return Mono.just(false);
        }
        return HeaderUtils.getHeaderInfo().flatMap(headerInfo -> {
            // 只删除传入的酒店代码对应的关联记录，而不是删除所有关联
            Criteria criteria = Criteria.where(HdsEmployeeHotelFieldEnum.employee_id.name()).is(employeeId)
                    .and(HdsEmployeeHotelFieldEnum.hotel_code.name()).in(hotelCodes);
            return r2dbcEntityTemplate.delete(Query.query(criteria), HdsEmployeeHotelEntity.class)
                    .then(Mono.defer(() -> saveEmployeeHotel(employeeId, hotelCodes, roleCode, status, headerInfo)));
        });
    }

    /**
     * 保存员工酒店关联
     */
    public Mono<Boolean> save(Integer employeeId, List<String> hotelCodes, String roleCode) {
        return saveOrUpdate(employeeId, hotelCodes, roleCode, EmployeeStatusEnum.ACTIVE.getCode());
    }

    /**
     * 新增员工酒店关联
     */
    public Mono<Boolean> create(Integer employeeId, List<String> hotelCodes, String roleCode) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> saveEmployeeHotel(employeeId, hotelCodes, roleCode, EmployeeStatusEnum.ACTIVE.getCode(), headerInfo));
    }

    /**
     * 保存员工酒店关联
     */
    private Mono<Boolean> saveEmployeeHotel(Integer employeeId, List<String> hotelCodes, String roleCode,
                                            Integer status, HeaderUtils.HeaderInfo headerInfo) {
        if (CollectionUtils.isEmpty(hotelCodes)) {
            return Mono.just(false);
        }
        // 构建员工与酒店关联实体列表
        List<HdsEmployeeHotelEntity> entities = buildEmployeeHotelEntities(employeeId, hotelCodes, roleCode,
                status, headerInfo);
        // 批量保存
        return hdsEmployeeHotelRepository.saveAll(entities)
                .then(Mono.just(true))
                .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "Failed to save employee-hotel")))
                .defaultIfEmpty(false);
    }

    private List<HdsEmployeeHotelEntity> buildEmployeeHotelEntities(Integer employeeId, List<String> hotelCodes,
                                                                    String roleCode, Integer status,
                                                                    HeaderUtils.HeaderInfo headerInfo) {
        return hotelCodes.stream()
                .distinct()
                .map(hotelCode -> {
                    HdsEmployeeHotelEntity entity = new HdsEmployeeHotelEntity();
                    entity.setEmployeeId(employeeId);
                    entity.setHotelCode(hotelCode);
                    entity.setRoleCode(roleCode);
                    entity.setStatus(status);
                    entity.setCreatedAt(LocalDateTime.now());
                    entity.setCreatedBy(headerInfo.getUserId());
                    entity.setCreatedByName(headerInfo.getUsername());
                    return entity;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据酒店编码列表查询员工ID列表
     *
     * @param hotelCodes 酒店编码列表
     * @return 员工ID列表
     */
    public Mono<List<Integer>> findEmployeeIdsByHotelCodes(List<String> hotelCodes) {
        if (CollectionUtils.isEmpty(hotelCodes)) {
            return Mono.just(Collections.emptyList());
        }

        // 构建查询条件，使用酒店编码列表精确匹配
        Criteria criteria = Criteria.where(HdsEmployeeHotelFieldEnum.hotel_code.name()).in(hotelCodes)
                .and(HdsEmployeeHotelFieldEnum.status.name()).is(EmployeeStatusEnum.ACTIVE.getCode());

        // 查询符合条件的员工酒店关联记录
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsEmployeeHotelEntity.class)
                .map(HdsEmployeeHotelEntity::getEmployeeId)
                .distinct()
                .collectList();
    }

    /**
     * 批量更新员工酒店关联状态 或 逻辑删除
     *
     * @param employeeId 员工ID
     * @param hotelCodes 酒店代码列表
     * @param status     状态
     * @return 更新结果
     */
    public Mono<Boolean> updateEmployeeHotelStatus(Integer employeeId, List<String> hotelCodes, Integer status, boolean isDelete) {
        if (employeeId == null || CollectionUtils.isEmpty(hotelCodes)) {
            return Mono.just(false);
        }

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 查询并更新状态
                    return hdsEmployeeHotelRepository.findByEmployeeIdAndHotelCodeIn(employeeId, hotelCodes)
                            .flatMap(hotelEntity -> {
                                // 逻辑删除
                                hotelEntity.setRowStatus(isDelete ? RowStatusEnum.DELETE.getId() : RowStatusEnum.VALID.getId());

                                if (status != null) {
                                    hotelEntity.setStatus(status);
                                }
                                hotelEntity.setUpdatedBy(headerInfo.getUserId());
                                hotelEntity.setUpdatedByName(headerInfo.getUsername());
                                hotelEntity.setUpdatedAt(LocalDateTime.now());
                                return r2dbcEntityTemplate.update(hotelEntity);
                            })
                            .then(Mono.just(true))
                            .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "Failed to update employee-hotel status")))
                            .defaultIfEmpty(false);
                });
    }

    /**
     * 批量更新员工酒店关联状态 或 逻辑删除
     *
     * @param employeeId 员工ID
     * @param hotelCodes 酒店代码列表
     * @param status     状态
     * @return 更新结果
     */
    public Mono<Boolean> updateStatus(Integer employeeId, List<String> hotelCodes, Integer status, boolean isDelete) {
        if (employeeId == null || CollectionUtils.isEmpty(hotelCodes) || status == null) {
            return Mono.just(false);
        }

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 查询并更新状态
                    return r2dbcEntityTemplate.select(buildQuery(employeeId, status, hotelCodes), HdsEmployeeHotelEntity.class)
                            .flatMap(hotelEntity -> {
                                // 逻辑删除
                                hotelEntity.setRowStatus(isDelete ? RowStatusEnum.DELETE.getId() : RowStatusEnum.VALID.getId());
                                hotelEntity.setStatus(status);
                                hotelEntity.setUpdatedBy(headerInfo.getUserId());
                                hotelEntity.setUpdatedByName(headerInfo.getUsername());
                                hotelEntity.setUpdatedAt(LocalDateTime.now());
                                return r2dbcEntityTemplate.update(hotelEntity);
                            })
                            .then(Mono.just(true))
                            .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "Failed to update employee-hotel status")))
                            .defaultIfEmpty(false);
                });
    }

    /**
     * 根据员工ID和酒店编码查询关联关系是否存在
     */
    public Mono<Boolean> existsByEmployeeIdAndHotelCode(Integer employeeId, String hotelCode) {
        // 参数校验
        if (Objects.isNull(employeeId) || StringUtils.isBlank(hotelCode)) {
            return Mono.just(false);
        }
        return hdsEmployeeHotelRepository.countByEmployeeIdAndHotelCode(employeeId, hotelCode)
                .map(count -> count > 0) // 如果count大于0，则表示存在
                .defaultIfEmpty(false)
                .onErrorResume(e -> {
                    log.error("查询员工酒店关联关系失败: employeeId={}, hotelCode={}, error={}",
                            employeeId, hotelCode, e.getMessage(), e);
                    return Mono.just(false);
                });
    }

    public Mono<List<String>> getNotifyEmployees(String hotelCode, List<String> sourceList) {
        return hdsEmployeeHotelRepository.findByHotelCodeAndStatus(hotelCode, EmployeeStatusEnum.ACTIVE.getCode())
                .flatMap(entity -> Flux.fromIterable(sourceList)
                        .map(source -> source.concat("_").concat(String.valueOf(entity.getEmployeeId()))))
                .collectList()
                .defaultIfEmpty(Collections.emptyList())
                .doOnNext(ids -> {
                    if (CollectionUtils.isEmpty(ids)) {
                        log.warn("No notify employees found for hotelCode: {}", hotelCode);
                    }
                })
                .onErrorResume(e -> {
                    log.error("Error occurred while fetching employee hotel list for hotelCode {}: {}", hotelCode, e.getMessage());
                    return Mono.just(Collections.emptyList());
                });
    }

    public Flux<HdsEmployeeHotelEntity> findByHotelCode(String hotelCode) {
        return hdsEmployeeHotelRepository.findByHotelCode(hotelCode);
    }

    public Mono<List<HdsEmployeeHotelEntity>> saveAll(List<HdsEmployeeHotelEntity> updatedEmployees) {
        if (CollectionUtils.isEmpty(updatedEmployees)) {
            return Mono.just(Collections.emptyList());
        }
        return hdsEmployeeHotelRepository.saveAll(updatedEmployees).collectList();
    }

    /**
     * 根据酒店名称预过滤
     */
    public Mono<Criteria> preFilterByEmployeeId(HeaderUtils.HeaderInfo headerInfo, Criteria baseCriteria,boolean needFilterOnlyHotelStaffRole) {
        // 根据酒店名称查询符合条件的酒店编码
        Criteria employeeIdCriteria = Criteria.where(HdsEmployeeHotelFieldEnum.employee_id.name())
                .is(headerInfo.getUserId());

        return r2dbcEntityTemplate.select(Query.query(Criteria.where(HdsEmployeeFieldEnum.id.name()).is(Integer.valueOf(headerInfo.getUserId()))), HdsEmployeeEntity.class)
                .next()
                .flatMap(employee -> {
                    if (EmployeeTypeEnum.isGroupEmployee(employee.getType())) {
                        return Mono.just(baseCriteria);
                    }

                    // 否则查询员工关联的酒店编码
                    return r2dbcEntityTemplate.select(Query.query(employeeIdCriteria), HdsEmployeeHotelEntity.class)
//                            .map(HdsEmployeeHotelEntity::getHotelCode)
                            .collectList()
                            .map(hdsEmployeeHotelEntities -> {
                                if (hdsEmployeeHotelEntities.isEmpty()) {
                                    return Criteria.where(HdsHotelInfoFieldEnum.id.name()).is(-1);
                                }

                                List<String> hotelCodes;
                                // 过滤掉只有Ai客户权限
                                if (needFilterOnlyHotelStaffRole) {
                                    hotelCodes = hdsEmployeeHotelEntities.stream().filter(hdsEmployeeHotelEntity -> {
                                        if (StringUtils.isBlank(hdsEmployeeHotelEntity.getRoleCode())) {
                                            return false;
                                        }
                                        List<String> roleCodeList =  Arrays.stream(hdsEmployeeHotelEntity.getRoleCode().split(",")).toList();
                                        return !(roleCodeList.size() == 1 && roleCodeList.contains(RoleEnum.HOTEL_STAFF.getCode()));
                                    }).map(HdsEmployeeHotelEntity::getHotelCode).collect(Collectors.toList());
                                }else {
                                    hotelCodes = hdsEmployeeHotelEntities.stream().map(HdsEmployeeHotelEntity::getHotelCode).collect(Collectors.toList());
                                }

                                if (CollectionUtils.isEmpty(hotelCodes)) {
                                    return baseCriteria.and(HdsHotelInfoFieldEnum.hotel_code.name()).is(-1);
                                } else {
                                    // 将酒店编码添加到查询条件中
                                    return baseCriteria.and(HdsHotelInfoFieldEnum.hotel_code.name()).in(hotelCodes);
                                }
                            });
                })
                // 处理没有找到员工的情况
                .switchIfEmpty(Mono.just(Criteria.where(HdsHotelInfoFieldEnum.id.name()).is(-1)));
    }
}
