package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Author：flx
 * @Date：2025/5/8 13:39
 * @Description：ExportTypeEnum
 */
@Getter
@AllArgsConstructor
public enum ExportTypeEnum {

    /**
     * 全部导出 - 不使用过滤条件
     */
    ALL("all", "全部导出"),

    /**
     * 筛选导出 - 使用请求中的过滤条件
     */
    FILTERED("filtered", "筛选导出"),

    /**
     * 当前页导出 - 根据IDs列表
     */
    CURRENT("current", "当前页导出");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举实例
     */
    public static ExportTypeEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(code))
                .findFirst()
                .orElse(FILTERED); // 默认为筛选导出
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return Arrays.stream(values())
                .anyMatch(type -> type.getCode().equals(code));
    }

    /**
     * 是否为全部导出
     */
    public static boolean isAll(String code) {
        return ALL.getCode().equals(code);
    }

    /**
     * 是否为当前页导出
     */
    public static boolean isCurrent(String code) {
        return CURRENT.getCode().equals(code);
    }

    /**
     * 是否为筛选导出
     */
    public static boolean isFiltered(String code) {
        return FILTERED.getCode().equals(code);
    }
}
