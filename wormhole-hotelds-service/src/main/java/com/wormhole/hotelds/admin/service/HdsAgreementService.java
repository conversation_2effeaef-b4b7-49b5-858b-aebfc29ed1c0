package com.wormhole.hotelds.admin.service;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.util.CodeGeneratorUtils;
import com.wormhole.hotelds.util.CodeInfo;
import com.wormhole.hotelds.util.UserUtils;
import com.wormhole.hotelds.util.ValidatorUtils;
import com.wormhole.hotelds.admin.model.entity.HdsAgreementEntity;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.vo.HdsAgreementVO;
import com.wormhole.hotelds.admin.model.req.HdsAgreementSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsAgreementDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsAgreementSearchReq;
import com.wormhole.hotelds.admin.repository.HdsAgreementRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:59:28
 * @Description: HdsAgreement
 */
@Service
@Slf4j
public class HdsAgreementService {

    @Resource
    private HdsAgreementRepository repository;

    @Resource
    private R2dbcEntityTemplate template;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodeGeneratorUtils codeGeneratorUtils;

    /**
     * 创建记录
     */
    public Mono<Boolean> create(HdsAgreementSaveReq req) {
        ValidatorUtils.validateHdsAgreementSaveReq(req);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionCreate(req, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "hdsagreement create error")))
                );
    }

    /**
     * 事务创建
     */
    private Mono<Boolean> executeTransferWithinTransactionCreate(HdsAgreementSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        return codeGeneratorUtils.generateCodes(BussinessTypeEnum.HDS_AGREEMENT.getBusinessType())
                .flatMap(codeInfo -> transactionalOperator.transactional(
                                repository.save(buildHdsAgreementEntity(req, headerInfo,codeInfo)))
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
                );
    }

    /**
     * 构建商户实体
     */
    private HdsAgreementEntity buildHdsAgreementEntity(HdsAgreementSaveReq req, HeaderUtils.HeaderInfo headerInfo, CodeInfo codeInfo) {
        HdsAgreementEntity hdsAgreementEntity = new HdsAgreementEntity();
        BeanUtils.copyProperties(req, hdsAgreementEntity);
        hdsAgreementEntity.setCreatedBy(UserUtils.getUserId(headerInfo));
        hdsAgreementEntity.setCreatedByName(UserUtils.getUserName(headerInfo));
        hdsAgreementEntity.setCreatedAt(LocalDateTime.now());
        return hdsAgreementEntity;
    }

    /**
     * 更新记录
     */
    public Mono<Boolean> update(HdsAgreementSaveReq req) {
        return repository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 删除记录
     */
    public Mono<Boolean> delete(HdsAgreementDeleteReq req) {
        return repository.deleteById(req.getId())
                .thenReturn(true);
    }

    /**
     * 搜索列表
     */
    public Mono<List<HdsAgreementVO>> search(HdsAgreementSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        return template.select(HdsAgreementEntity.class)
                .matching(Query.query(criteria))
                .all()
                .map(this::convertToVO)
                .collectList();
    }

    /**
     * 根据ID查询
     */
    public Mono<HdsAgreementVO> findById(Long id) {
        return repository.findById(id)
                .map(this::convertToVO);
    }

    private Criteria buildSearchCriteria(HdsAgreementSearchReq req) {
        Criteria criteria = Criteria.empty();
        // TODO: 根据实际查询条件构建Criteria
        return criteria;
    }

    private HdsAgreementEntity convertToEntity(HdsAgreementSaveReq req) {
        HdsAgreementEntity entity = new HdsAgreementEntity();
        // TODO: 实现请求对象到实体的转换
        return entity;
    }

    private HdsAgreementVO convertToVO(HdsAgreementEntity entity) {
        HdsAgreementVO vo = new HdsAgreementVO();
        // TODO: 实现实体到VO的转换
        return vo;
    }
}