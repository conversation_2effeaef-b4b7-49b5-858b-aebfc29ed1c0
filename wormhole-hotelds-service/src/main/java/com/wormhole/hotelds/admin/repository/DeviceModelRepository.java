package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.*;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:56:47
 * @Description: DeviceModel
 */
@Repository
public interface DeviceModelRepository extends ReactiveCrudRepository<HdsDeviceModelEntity, Long> {
    Flux<HdsDeviceModelEntity> findByModelCodeIn(Set<String> strings);

    Flux<HdsDeviceModelEntity> findByModelCode(String modelCode);

    /**
     * 检查型号编码是否存在
     * @param modelCode 型号编码
     * @return 是否存在
     */
    Mono<Boolean> existsByModelCode(String modelCode);
}