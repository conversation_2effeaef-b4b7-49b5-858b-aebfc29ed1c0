package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.HotelLeadSaveReq;
import com.wormhole.hotelds.admin.model.req.HotelLeadSearchReq;
import com.wormhole.hotelds.admin.model.req.UserLeadSearchReq;
import com.wormhole.hotelds.admin.model.vo.HotelLeadSearchVO;
import com.wormhole.hotelds.admin.model.vo.HotelLeadVO;
import com.wormhole.hotelds.admin.model.vo.UserLeadSearchVO;
import com.wormhole.hotelds.admin.service.HotelLeadService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/5/22 14:26
 * @Description：HotelLeadController
 */
@RestController
@RequestMapping("/hotel_lead")
public class HotelLeadController {

    @Resource
    private HotelLeadService hotelLeadService;

    /**
     * 创建用户邀请线索
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HotelLeadSaveReq hotelLeadSaveReq) {
        return hotelLeadService.update(hotelLeadSaveReq).flatMap(Result::success);
    }

    @GetMapping("/queryByCode")
    public Mono<Result<HotelLeadVO>> findByHotelCode(@RequestParam("hotel_code") String hotelCode) {
        return hotelLeadService.findByHotelCode(hotelCode).flatMap(Result::success);
    }

    /**
     * 搜索用户线索列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<HotelLeadSearchVO>>> search(@RequestBody HotelLeadSearchReq searchReq) {
        return Mono.zip(
                        hotelLeadService.searchCount(searchReq),
                        hotelLeadService.search(searchReq)
                )
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }
}
