package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsOperationLogEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/5/6 15:57
 * @Description：LogSearchVO
 */
@Slf4j
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LogSearchVO {

    private Long id;

    private Long businessId;

    private String moduleName;

    private String oprDesc;

    private String employeeName;

    private String oprBefore;

    private String oprTime;

    private String oprResult;


    public static LogSearchVO toVo(HdsOperationLogEntity hdsOperationLogEntity) {
        LogSearchVO logSearchVO = new LogSearchVO();
        BeanUtils.copyProperties(hdsOperationLogEntity, logSearchVO);
        logSearchVO.setOprTime(SimpleDateUtils.formatLocalDateTimeToDateHour(hdsOperationLogEntity.getOprTime()));
        logSearchVO.setOprDesc(hdsOperationLogEntity.getOprContent());
        logSearchVO.setBusinessId(hdsOperationLogEntity.getObjectId());
        return logSearchVO;
    }
}
