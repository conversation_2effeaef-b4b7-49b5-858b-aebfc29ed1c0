package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/16 14:58
 * @Description：PackageSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PackageSearchVO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 套餐编码
     */
    private String packageCode;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 付费方式：0-按房间数量收费，1-按门店收费
     */
    private Integer paymentMode;

    /**
     * 优惠方式：0-折扣，1-一口价
     */
    private Integer discountMode;

    /**
     * 状态：1-启用，0-停用
     */
    private Integer status;

    /**
     * 是否推荐：1-是，0-否
     */
    private Integer isRecommend;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    private List<ProductVO> products;

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ProductVO {

        /**
         * 主键
         */
        private Integer id;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * pricing
         */
        private List<PricingVO> pricing;
    }

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PricingVO {

        /**
         * 主键
         */
        private Integer id;

        /**
         * 周期类型：1-月度，2-季度，3-年度
         */
        private Integer periodType;

        /**
         * 门市价
         */
        private String marketPrice;

        /**
         * 优惠价
         */
        private String discountPrice;

        /**
         * 折扣比例（折扣模式使用，0.01-1.00）
         */
        private String discountRate;
    }
}
