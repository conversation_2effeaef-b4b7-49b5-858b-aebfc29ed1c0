package com.wormhole.hotelds.admin.constant;

import java.util.Set;

/**
 * @Author：flx
 * @Date：2025/4/1 10:39
 * @Description：Excel字段常量管理器
 */
public interface ExcelConstant {

    /**
     * excel文件最大限制
     */
    long MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 允许的文件扩展名
     */
    Set<String> ALLOWED_EXTENSIONS = Set.of(".xlsx", ".xls");


    /*门店相关常量*/
    String FIELD_HOTEL_ID = "门店ID";
    String FIELD_HOTEL_CODE = "门店Code";
    String FIELD_HOTEL_LOGO = "门店Logo";
    String FIELD_HOTEL_NAME = "门店名称";
    String FIELD_COUNTRY_NAME = "国家名称";
    String FIELD_PROVINCE_NAME = "省份名称";
    String FIELD_CITY_NAME = "城市名称";
    String FIELD_DISTRICT_NAME = "区县名称";
    String FIELD_ADDRESS = "详细地址";
    String FIELD_LONGITUDE = "经度";
    String FIELD_LATITUDE = "纬度";
    String FIELD_MERCHANT_NAME = "商户名称";
    String FIELD_MERCHANT_SHORT_NAME = "商户简称";
    String FIELD_BRAND_NAME = "品牌名称";
    String FIELD_SUBJECT_NAME = "企业名称";
    String FIELD_MAIN_PERSON = "门店联系人";
    String FIELD_PHONE = "联系电话";
    String FIELD_EMAIL = "联系邮箱";
    String FIELD_TOTAL_ROOM = "总房间数";
    String FIELD_CTRIP_EBK_URL = "携程EBK链接";


    /*设备相关常量*/
    String FIELD_DEVICE_ID = "设备id";
    String FIELD_DEVICE_TYPE_CODE = "设备类型编码";
    String FIELD_DEVICE_SN = "设备SN码";
    String FIELD_DEVICE_IMEI = "设备IMEI号";
    String FIELD_PURCHASE_TIME = "采购时间";
    String FIELD_WARRANTY_START_TIME = "保修开始时间";
    String FIELD_WARRANTY_END_TIME = "保修结束时间";
    String FIELD_REMARK = "备注";


    /* 设备位置 */
    String FIELD_DEVICE_APP_TYPE = "设备App类型";
    String FIELD_BLOCK = "楼栋";
    String FIELD_AREA = "区域";
    String FIELD_POSITION_NAME = "位置名称";


    /*员工相关字段常量*/
    String FIELD_EMPLOYEE_ID = "员工ID";
    String FIELD_RELATION_HOTEL = "关联门店";
    String FIELD_EMPLOYEE_NAME = "姓名";
    String FIELD_EMPLOYEE_USERNAME = "自定义账号";
    String FIELD_EMPLOYEE_MOBILE = "手机号码";
    String FIELD_EMPLOYEE_EMAIL = "邮箱";
    String FIELD_EMPLOYEE_ROLE_TYPE = "角色类型";
    String FIELD_EMPLOYEE_ACCOUNT_TYPE = "员工类型";
//    String FIELD_EMPLOYEE_TICKET_TYPE = "工单类型";
    String FIELD_EMPLOYEE_TICKET_ASSIGNMENT_FLAG = "接单表";
    String FIELD_EMPLOYEE_POSITION_DETAILS = "位置";
    String FIELD_EMPLOYEE_CREATE_BY = "创建人";


    /*用户线索相关字段常量*/
    String FIELD_USER_LEAD_ID = "ID";
    String FIELD_USER_LEAD_CODE = "用户线索编码";
    String FIELD_USER_LEAD_MOBILE = "手机号码";
    String FIELD_USER_LEAD_STATUS = "线索状态";
    String FIELD_USER_LEAD_EBK_COUNT = "获取EBK数";
    String FIELD_USER_LEAD_HOTEL_COUNT = "创建门店数";
    String FIELD_USER_LEAD_INVITED_BY = "邀请人";
    String FIELD_USER_LEAD_INVITE_CODE = "被邀请码";
    String FIELD_USER_LEAD_REGISTERED_AT = "注册时间";

    // 门店线索导出字段常量
    String FIELD_HOTEL_LEAD_ID = "ID";
    String FIELD_HOTEL_LEAD_CODE = "门店线索编码";
    String FIELD_HOTEL_LEAD_HOTEL_NAME = "门店名称";
    String FIELD_HOTEL_LEAD_EBK_URL = "携程EBK链接";
    String FIELD_HOTEL_LEAD_CREATE_BY = "创建人";
    String FIELD_HOTEL_LEAD_BIND_EBK = "确认绑定EBK";
    String FIELD_HOTEL_LEAD_SET_RIVAL_HOTEL = "设置竞对酒店";
    String FIELD_HOTEL_LEAD_SYNC_REVIEW = "同步全量点评";
    String FIELD_HOTEL_LEAD_SYNC_STATIC_INFO = "同步酒店静态信息";
    String FIELD_HOTEL_LEAD_SYNC_RIVAL_REVIEW = "同步竞对评论";
    String FIELD_HOTEL_LEAD_SYNC_RIVAL_PRICE = "同步竞对价格";
    String FIELD_HOTEL_LEAD_GENERATE_REPORT = "生成初始化报告";
    String FIELD_HOTEL_LEAD_CREATED_AT = "创建时间";
    String FIELD_HOTEL_LEAD_COMPLETE_TIME = "完成时间";
    String FIELD_HOTEL_LEAD_DURATION = "创建门店时长";

    /*日报数据导出相关字段常量*/
    String FIELD_DAILY_AI_DATE = "日期";
    String FIELD_DAILY_AI_HOTEL = "酒店";
    String FIELD_DAILY_AI_ROOM_USE_COUNT = "使用房间数";
    String FIELD_DAILY_AI_CALL_COUNT = "AI语音通话数";
    String FIELD_DAILY_AI_AVG_CALL_DURATION = "平均通话时长（秒）";
    String FIELD_DAILY_AI_TEXT_DIALOGUE_COUNT = "文字对话数";
    String FIELD_DAILY_AI_COMPLETED_TICKET_COUNT = "完成工单数";
    String FIELD_DAILY_AI_SOLVE_COUNT = "AI解决数";
    String FIELD_DAILY_AI_RETURN_CALL_TICKET_COUNT = "人工回拨数";
    String FIELD_DAILY_AI_AVG_COMPLETE_DURATION = "处理时长（秒）";
    String FIELD_DAILY_AI_OVERDUE_COUNT = "超时工单数";
    String FIELD_DAILY_AI_COMPLAINT_COUNT = "投诉工单数";
}
