package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：yongfeng.chen
 * @Date：2025-06-30 11:47:06
 * @Description：发票默认状态枚举
 */
@Getter
@AllArgsConstructor
public enum InvoiceInfoDefaultEnum {

    /**
     * 0 非默认
     */
    NON_DEFAULT(0, "非默认"),

    /**
     * 1 默认
     */
    DEFAULT(1, "默认");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     */
    public static InvoiceInfoDefaultEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (InvoiceInfoDefaultEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否是合法的状态码
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
