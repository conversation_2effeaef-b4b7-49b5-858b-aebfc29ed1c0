package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.StatusEnum;
import com.wormhole.hotelds.admin.model.req.DeviceInfoDeleteReq;
import com.wormhole.hotelds.admin.model.req.DeviceInfoSaveReq;
import com.wormhole.hotelds.admin.model.req.DeviceInfoSearchReq;
import com.wormhole.hotelds.admin.model.vo.DeviceInfoVO;
import com.wormhole.hotelds.admin.repository.DeviceInfoRepository;
import com.wormhole.hotelds.core.model.entity.HdsDeviceInfoEntity;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/4/7 09:26
 * @Description：DeviceInfoService
 */
@Slf4j
@Service
public class DeviceInfoService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private DeviceInfoRepository deviceInfoRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodePoolManager codePoolManager;

    /**
     * 创建设备信息
     */
    public Mono<Boolean> create(DeviceInfoSaveReq deviceInfoSaveReq) {
//        ValidatorUtils.validateDeviceInfoSaveReq(deviceInfoSaveReq);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionCreate(deviceInfoSaveReq, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device info create error")))
                );
    }

    /**
     * 事务创建
     */
    private Mono<Boolean> executeTransferWithinTransactionCreate(DeviceInfoSaveReq deviceInfoSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        return codePoolManager.getCodeFromPool(BussinessTypeEnum.DEVICE.getBusinessType())
                .flatMap(codeInfo -> transactionalOperator.transactional(
                                deviceInfoRepository.save(buildHdsDeviceInfoEntity(deviceInfoSaveReq, headerInfo, codeInfo)))
                        .doOnSuccess(v -> log.info("device info create success: id={}, username={}", deviceInfoSaveReq.getId(), headerInfo.getUsername()))
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
                );
    }

    /**
     * 删除设备信息
     */
    public Mono<Boolean> delete(DeviceInfoDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(userInfo -> executeTransferWithinTransactionDelete(userInfo, req.getId())
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device info delete error")))
                );
    }

    /**
     * 事务删除
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(HeaderUtils.HeaderInfo headerInfo, Long id) {
        return transactionalOperator.transactional(
                deviceInfoRepository.deleteById(id)
                        .doOnSuccess(v -> log.info("device info delete success: id={}, username={}", id, headerInfo.getUsername()))
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 更新设备信息
     */
    public Mono<Boolean> update(DeviceInfoSaveReq deviceInfoSaveReq) {
        Preconditions.checkArgument(Objects.nonNull(deviceInfoSaveReq), "deviceInfoSaveReq must not be null");
        Preconditions.checkArgument(Objects.nonNull(deviceInfoSaveReq.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionUpdate(deviceInfoSaveReq, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device info update error")))
                );
    }

    /**
     * 事务更新
     */
    private Mono<Boolean> executeTransferWithinTransactionUpdate(DeviceInfoSaveReq deviceInfoSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                deviceInfoRepository.findById(deviceInfoSaveReq.getId())
                        .flatMap(existingDeviceInfo -> {
                            updateHdsDeviceInfoEntity(existingDeviceInfo, deviceInfoSaveReq, headerInfo);
                            return deviceInfoRepository.save(existingDeviceInfo);
                        })
                        .doOnSuccess(v -> log.info("device info update success: id={}, username={}", deviceInfoSaveReq.getId(), headerInfo.getUsername()))
                        .map(v -> true)
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 搜索设备信息列表
     */
    public Mono<List<DeviceInfoVO>> search(DeviceInfoSearchReq deviceInfoSearchReq) {
        Query query = buildQuery(deviceInfoSearchReq);
        return r2dbcEntityTemplate.select(query, HdsDeviceInfoEntity.class)
                .collectList()
                .map(deviceInfoEntities -> deviceInfoEntities.stream()
                        .map(DeviceInfoVO::toVo)
                        .toList());
    }

    /**
     * 搜索设备信息总数
     */
    public Mono<Long> searchCount(DeviceInfoSearchReq deviceInfoSearchReq) {
        Query query = buildQuery(deviceInfoSearchReq);
        return r2dbcEntityTemplate.count(query, HdsDeviceInfoEntity.class);
    }

    /**
     * 构建查询条件
     */
    private Query buildQuery(DeviceInfoSearchReq deviceInfoSearchReq) {
        Criteria criteria = Criteria.empty();

//        if (StringUtils.isNotBlank(deviceInfoSearchReq.getDeviceSn())) {
//            criteria = criteria.and("device_sn").like("%" + deviceInfoSearchReq.getDeviceSn() + "%");
//        }
//        if (Objects.nonNull(deviceInfoSearchReq.getModelId())) {
//            criteria = criteria.and("model_id").is(deviceInfoSearchReq.getModelId());
//        }
//        if (StringUtils.isNotBlank(deviceInfoSearchReq.getMacAddress())) {
//            criteria = criteria.and("mac_address").like("%" + deviceInfoSearchReq.getMacAddress() + "%");
//        }
//        if (Objects.nonNull(deviceInfoSearchReq.getWarrantyMonths())) {
//            criteria = criteria.and("warranty_months").is(deviceInfoSearchReq.getWarrantyMonths());
//        }
//        if (StringUtils.isNotBlank(deviceInfoSearchReq.getAppVersion())) {
//            criteria = criteria.and("app_version").like("%" + deviceInfoSearchReq.getAppVersion() + "%");
//        }
//        if (StringUtils.isNotBlank(deviceInfoSearchReq.getDeviceType())) {
//            criteria = criteria.and("device_type").is(deviceInfoSearchReq.getDeviceType());
//        }
//        if (Objects.nonNull(deviceInfoSearchReq.getStatus())) {
//            criteria = criteria.and("status").is(deviceInfoSearchReq.getStatus());
//        }

        Sort sort = Sort.by(Sort.Direction.DESC, "created_at");
        return Query.query(criteria)
                .sort(sort)
                .limit(deviceInfoSearchReq.getPageSize())
                .offset((long) (deviceInfoSearchReq.getCurrent() - 1) * deviceInfoSearchReq.getPageSize());
    }

    /**
     * 查询设备信息列表
     */
    public Mono<List<DeviceInfoVO>> queryList() {
        Criteria criteria = Criteria.where("status").is(StatusEnum.VALID.getCode());
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.select(query, HdsDeviceInfoEntity.class)
                .collectList()
                .map(deviceInfoEntities -> deviceInfoEntities.stream()
                        .map(DeviceInfoVO::toVo)
                        .toList());
    }

    /**
     * 根据ID查询设备信息
     */
    public Mono<DeviceInfoVO> findById(Long id) {
        Preconditions.checkArgument(Objects.nonNull(id), "id must not be null");
        return deviceInfoRepository.findById(id)
                .map(DeviceInfoVO::toVo)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "device info not found")));
    }

    /**
     * 构建设备信息实体
     */
    private HdsDeviceInfoEntity buildHdsDeviceInfoEntity(DeviceInfoSaveReq req, HeaderUtils.HeaderInfo headerInfo, String codeInfo) {
        HdsDeviceInfoEntity deviceInfoEntity = new HdsDeviceInfoEntity();
        BeanUtils.copyProperties(req, deviceInfoEntity);
//        deviceInfoEntity.setStatus(StatusEnum.VALID.getCode());
//        deviceInfoEntity.setDeviceSn(codeInfo.getCode());
        deviceInfoEntity.setCreatedBy(UserUtils.getUserId(headerInfo));
        deviceInfoEntity.setCreatedByName(UserUtils.getUserName(headerInfo));
        deviceInfoEntity.setCreatedAt(LocalDateTime.now());
        return deviceInfoEntity;
    }

    /**
     * 更新设备信息实体
     */
    private void updateHdsDeviceInfoEntity(HdsDeviceInfoEntity existingDeviceInfo, DeviceInfoSaveReq req, HeaderUtils.HeaderInfo headerInfo) {

        if (StringUtils.isNotBlank(req.getMacAddress())) {
            existingDeviceInfo.setMacAddress(req.getMacAddress());
        }
        if (Objects.nonNull(req.getStorageTime())) {
            existingDeviceInfo.setStorageTime(req.getStorageTime());
        }
        if (StringUtils.isNotBlank(req.getAppVersion())) {
            existingDeviceInfo.setAppVersion(req.getAppVersion());
        }
        if (StringUtils.isNotBlank(req.getDeviceAppType())) {
            existingDeviceInfo.setDeviceAppType(req.getDeviceAppType());
        }
        if (Objects.nonNull(req.getPrice())) {
            existingDeviceInfo.setPrice(req.getPrice());
        }
        if (StringUtils.isNotBlank(req.getSpecDesc())) {
            existingDeviceInfo.setSpecDesc(req.getSpecDesc());
        }
        if (StringUtils.isNotBlank(req.getRemark())) {
            existingDeviceInfo.setRemark(req.getRemark());
        }

        existingDeviceInfo.setUpdatedBy(UserUtils.getUserId(headerInfo));
        existingDeviceInfo.setUpdatedByName(UserUtils.getUserName(headerInfo));
        existingDeviceInfo.setUpdatedAt(LocalDateTime.now());
    }
}
