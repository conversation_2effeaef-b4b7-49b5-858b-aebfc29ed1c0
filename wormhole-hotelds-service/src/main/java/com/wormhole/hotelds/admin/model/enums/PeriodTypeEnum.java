package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/6/17 09:59
 * @Description：PeriodTypeEnum
 */
@AllArgsConstructor
@Getter
public enum PeriodTypeEnum {

    /**
     * 月度
     */
    MONTHLY(1, "月度"),

    /**
     * 季度
     */
    QUARTERLY(2, "季度"),

    /**
     * 年度
     */
    YEARLY(3, "年度");

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     */
    public static PeriodTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PeriodTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     */
    public static String getNameByCode(Integer code) {
        PeriodTypeEnum typeEnum = getByCode(code);
        return typeEnum != null ? typeEnum.getName() : null;
    }

    /**
     * 验证编码是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
