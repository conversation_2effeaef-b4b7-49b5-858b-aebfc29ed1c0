package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/5/19 18:49
 * @Description：InvitationCodeLogSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InvitationCodeLogSearchVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 操作事项
     */
    private String oprDesc;

    /**
     * 操作时间
     */
    private String oprTime;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 创建人
     */
    private String createdBy;
}
