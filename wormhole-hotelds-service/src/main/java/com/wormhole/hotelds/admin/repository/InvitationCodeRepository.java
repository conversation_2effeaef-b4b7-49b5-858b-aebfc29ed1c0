package com.wormhole.hotelds.admin.repository;

import aj.org.objectweb.asm.commons.Remapper;
import com.wormhole.hotelds.core.model.entity.HdsAppVersionsEntity;
import com.wormhole.hotelds.core.model.entity.HdsInvitationCodeEntity;
import io.reactivex.Flowable;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/16 15:19
 * @Description：InvitationCodeRepository
 */
@Repository
public interface InvitationCodeRepository extends ReactiveCrudRepository<HdsInvitationCodeEntity, Integer> {

    /**
     * 根据邀请码查询
     * @param invitationCode
     * @return
     */
    Mono<Boolean> existsByInvitationCode(String invitationCode);

    Mono<Boolean> existsByName(String name);

    Mono<Long> countByHotelCode(String hotelCode);

    Mono<HdsInvitationCodeEntity> findByHotelCode(String hotelCode);

    Flux<HdsInvitationCodeEntity> findByIdIn(Collection<Integer> ids);

    Mono<HdsInvitationCodeEntity> findByInvitationCode(String invitationCode);
}
