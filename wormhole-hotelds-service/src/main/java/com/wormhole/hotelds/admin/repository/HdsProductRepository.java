package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsProductEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Author：flx
 * @Date：2025/1/16 15:30
 * @Description：HdsPaymentProductRepository
 */
@Repository
public interface HdsProductRepository extends ReactiveCrudRepository<HdsProductEntity, Integer> {

    Flux<HdsProductEntity> findByPackageCode(String packageCode);

    Mono<Void> deleteAllByPackageCode(String packageCode);
}