package com.wormhole.hotelds.admin.model.dto;

import com.wormhole.hotelds.core.model.entity.HdsBillEntity;
import com.wormhole.hotelds.core.model.entity.HdsOrderEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-25 18:35:00
 * @Description: 账单详情 DTO，包含账单、订单、套餐信息
 */
@Data
@Accessors(chain = true)
public class BillDetailDTO {
    
    /**
     * 账单信息
     */
    private HdsBillEntity billEntity;
    
    /**
     * 订单信息
     */
    private HdsOrderEntity orderEntity;
    
    /**
     * 套餐名称
     */
    private String packageName;
} 