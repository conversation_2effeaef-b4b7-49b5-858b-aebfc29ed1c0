package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/6/17 09:59
 * @Description：PeriodTypeEnum
 */
@AllArgsConstructor
@Getter
public enum DiscountModelEnum {

    /**
     * 折扣
     */
    DISCOUNT(0, "折扣"),

    /**
     * 一口价
     */
    FIXED_PRICE(1, "一口价");

    /**
     * 优惠方式代码
     */
    private final Integer code;

    /**
     * 优惠方式描述
     */
    private final String description;

    /**
     * 获取优惠方式代码
     *
     * @return 优惠方式代码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取优惠方式描述
     *
     * @return 优惠方式描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取优惠方式枚举
     *
     * @param code 优惠方式代码
     * @return 优惠方式枚举，如果未找到则返回null
     */
    public static DiscountModelEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DiscountModelEnum discountModel : values()) {
            if (discountModel.getCode().equals(code)) {
                return discountModel;
            }
        }
        return null;
    }

    /**
     * 根据描述获取优惠方式枚举
     *
     * @param description 优惠方式描述
     * @return 优惠方式枚举，如果未找到则返回null
     */
    public static DiscountModelEnum getByDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }
        for (DiscountModelEnum discountModel : values()) {
            if (discountModel.getDescription().equals(description)) {
                return discountModel;
            }
        }
        return null;
    }

    /**
     * 判断给定代码是否有效
     *
     * @param code 优惠方式代码
     * @return 如果代码有效返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
