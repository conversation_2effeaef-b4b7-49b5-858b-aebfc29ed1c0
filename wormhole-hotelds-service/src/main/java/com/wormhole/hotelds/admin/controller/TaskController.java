package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.TaskRetryReq;
import com.wormhole.hotelds.admin.service.TaskService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

@RestController
@RequestMapping("/task")
public class TaskController {

    @Resource
    private TaskService taskService;

    /**
     * 重试携程链接解析任务
     * @param req
     * @return
     */
    @PostMapping("/retry-ctrip-parse")
    public Mono<Result<Boolean>> retryCtripParseTask(@RequestBody TaskRetryReq req) {
        return taskService.retryCtripParseTask(req).flatMap(Result::success);
    }
}
