package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.DeviceModelDeleteReq;
import com.wormhole.hotelds.admin.model.req.DeviceModelSaveReq;
import com.wormhole.hotelds.admin.model.req.DeviceModelSearchReq;
import com.wormhole.hotelds.admin.model.vo.DeviceModelSearchVO;
import com.wormhole.hotelds.admin.model.vo.DeviceModelVO;
import com.wormhole.hotelds.admin.service.DeviceModelService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:56:47
 * @Description: DeviceModel
 */
@RestController
@RequestMapping("/device_model")
public class DeviceModelController {

    @Resource
    private DeviceModelService deviceModelService;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody DeviceModelSaveReq deviceModelSaveReq) {
        return deviceModelService.create(deviceModelSaveReq).flatMap(Result::success);
    }

    /**
     * 更新记录
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody DeviceModelSaveReq deviceModelSaveReq) {
        return deviceModelService.update(deviceModelSaveReq).flatMap(Result::success);
    }

    /**
     * 删除记录
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody DeviceModelDeleteReq deviceModelDeleteReq) {
        return deviceModelService.delete(deviceModelDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<DeviceModelSearchVO>>> search(@RequestBody DeviceModelSearchReq deviceModelSearchReq) {
        return Mono.zip(deviceModelService.searchCount(deviceModelSearchReq), deviceModelService.search(deviceModelSearchReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<DeviceModelVO>> findById(@PathVariable("id") Long id) {
        return deviceModelService.findById(id).flatMap(Result::success);
    }
}