package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

import java.io.*;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:59:28
 * @Description: HdsAgreement
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsAgreementVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

}