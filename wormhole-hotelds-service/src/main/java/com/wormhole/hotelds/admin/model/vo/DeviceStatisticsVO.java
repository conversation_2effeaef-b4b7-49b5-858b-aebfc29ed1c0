package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/4/11 10:15
 * @Description：设备统计信息
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
public class DeviceStatisticsVO {

    /**
     * 设备总数
     */
    private Long totalCount;

    /**
     * 在线数量
     */
    private Long onlineCount;

    /**
     * 离线数量
     */
    private Long offlineCount;

    /**
     * 停用数量
     */
    private Long disabledCount;

    /**
     * 活跃设备数
     */
    private Long activeCount;
}
