package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-26 19:40:00
 * @Description: 发票申请详情响应
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceApplicationDetailVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申请编号
     */
    private String applicationNo;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 关联账单数量
     */
    private Integer billCount;

    /**
     * 申请状态：0-待审核，1-审核中，2-已开票，3-已驳回
     */
    private Integer applicationStatus;

    /**
     * 发票抬头
     */
    private String invoiceHeader;

    /**
     * 纳税人识别号
     */
    private String taxNumber;

    /**
     * 接收邮箱
     */
    private String receiveEmail;

    /**
     * 发票内容
     */
    private String invoiceContent;

    /**
     * 更多信息/备注
     */
    private String moreInfo;

    /**
     * 审核备注
     */
    private String reviewRemark;

    /**
     * 审核人
     */
    private String reviewedBy;

    /**
     * 审核人名称
     */
    private String reviewedByName;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewedAt;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票文件URL
     */
    private String invoiceUrl;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invoicedAt;

    /**
     * 申请人
     */
    private String createdByName;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 关联账单明细列表
     */
    private List<InvoiceDetailItemVO> billDetails;

    /**
     * 发票明细项
     */
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class InvoiceDetailItemVO {

        /**
         * 明细ID
         */
        private Integer id;

        /**
         * 账单ID
         */
        private Integer billId;

        /**
         * 订单编号
         */
        private String orderNo;

        /**
         * 套餐名称
         */
        private String packageName;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * 账单金额
         */
        private BigDecimal billAmount;

        /**
         * 周期类型：1-月度，2-季度，3-年度
         */
        private Integer periodType;

        /**
         * 房间数量
         */
        private Integer roomCount;

        /**
         * 产品到期时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime expireTime;

        /**
         * 支付方式：1-微信，2-支付宝
         */
        private Integer payMethod;

        /**
         * 交易完成时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime transactionAt;
    }
} 