package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/4/16 10:38
 * @Description：DevicePositionRoomVO
 */
@Builder
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DevicePositionNameVO {

    /**
     * id
     */
    private Long id;

    /**
     * 位置名称
     */
    private String positionName;
}
