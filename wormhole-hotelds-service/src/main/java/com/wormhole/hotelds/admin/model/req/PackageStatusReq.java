package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/6/16 16:13
 * @Description：PackageStatusReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PackageStatusReq {

    /**
     * 套餐ID
     */
    private String packageCode;

    /**
     * 状态：1-启用，0-停用
     */
    private Integer status;
}
