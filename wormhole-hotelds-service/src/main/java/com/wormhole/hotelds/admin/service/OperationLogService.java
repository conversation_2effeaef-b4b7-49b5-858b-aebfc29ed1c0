package com.wormhole.hotelds.admin.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.admin.model.req.OperationLogSearchReq;
import com.wormhole.hotelds.admin.model.vo.DeviceModelVO;
import com.wormhole.hotelds.admin.model.vo.LogSearchVO;
import com.wormhole.hotelds.admin.model.vo.OperationLogVO;
import com.wormhole.hotelds.admin.repository.OperationLogRepository;
import com.wormhole.hotelds.core.model.entity.HdsDeviceFieldEnum;
import com.wormhole.hotelds.core.model.entity.HdsOperationLogEntity;
import com.wormhole.hotelds.core.model.entity.HdsOperationLogFieldEnum;
import com.wormhole.hotelds.util.JsonCompareUtils;
import com.wormhole.hotelds.util.SimpleDateUtils;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.asm.Advice;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;

import static com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum.*;

/**
 * @Author：flx
 * @Date：2025/5/6 15:54
 * @Description：LogService
 */
@Slf4j
@Service
public class OperationLogService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private OperationLogRepository operationLogRepository;

    /**
     * 搜索商户列表
     */
    public Mono<List<LogSearchVO>> search(HeaderUtils.HeaderInfo headerInfo, OperationLogSearchReq operationLogSearchReq) {
        Criteria criteria = buildBaseCriteria(headerInfo, operationLogSearchReq);

        Sort sort = Sort.by(Sort.Direction.DESC, HdsOperationLogFieldEnum.create_time.name());
        Query query = Query.query(criteria)
                .sort(sort)
                .limit(operationLogSearchReq.getPageSize())
                .offset((long) (operationLogSearchReq.getCurrent() - 1) * operationLogSearchReq.getPageSize());
        return r2dbcEntityTemplate.select(query, HdsOperationLogEntity.class)
                .collectList()
                .map(hdsOperationLogEntities -> hdsOperationLogEntities.stream()
                        .map(LogSearchVO::toVo)
                        .toList());
    }

    public Mono<Long> searchCount(HeaderUtils.HeaderInfo headerInfo, OperationLogSearchReq operationLogSearchReq) {
        Criteria criteria = buildBaseCriteria(headerInfo, operationLogSearchReq);
        return r2dbcEntityTemplate.count(Query.query(criteria), HdsOperationLogEntity.class);
    }

    private Criteria buildBaseCriteria(HeaderUtils.HeaderInfo headerInfo, OperationLogSearchReq operationLogSearchReq) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(headerInfo.getHotelCode())) {
            criteria = criteria.and(HdsOperationLogFieldEnum.hotel_code.name()).is(headerInfo.getHotelCode());
        }
        if (StringUtils.isNotBlank(operationLogSearchReq.getBusinessType())) {
            criteria = criteria.and(HdsOperationLogFieldEnum.object_type.name()).is(operationLogSearchReq.getBusinessType());
        }

        if (Objects.nonNull(operationLogSearchReq.getBusinessId())) {
            criteria = criteria.and(HdsOperationLogFieldEnum.object_id.name()).is(operationLogSearchReq.getBusinessId());
        }
        if (Objects.nonNull(operationLogSearchReq.getEmployeeId())) {
            criteria = criteria.and(HdsOperationLogFieldEnum.employee_id.name()).is(operationLogSearchReq.getEmployeeId());
        }
        if (Objects.nonNull(operationLogSearchReq.getEmployeeName())) {
            criteria = criteria.and(HdsOperationLogFieldEnum.employee_name.name()).like("%" + operationLogSearchReq.getEmployeeName() + "%");
        }
        // 时间范围
        if (StringUtils.isNotBlank(operationLogSearchReq.getStartTime())) {
            criteria = criteria.and(HdsDeviceFieldEnum.created_at.name())
                    .greaterThanOrEquals(SimpleDateUtils.slashStringToStartDateTime(operationLogSearchReq.getStartTime()));
        }
        if (StringUtils.isNotBlank(operationLogSearchReq.getEndTime())) {
            criteria = criteria.and(HdsDeviceFieldEnum.created_at.name())
                    .lessThanOrEquals(SimpleDateUtils.slashStringToEndDateTime(operationLogSearchReq.getEndTime()));
        }
        return criteria;
    }

    /**
     * 根据ID查询设备型号
     */
    public Mono<OperationLogVO> findById(Long id) {
        Preconditions.checkArgument(Objects.nonNull(id), "id must not be null");
        return operationLogRepository.findById(id)
                .map(this::toVo)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "operation log not found")));
    }

    private OperationLogVO toVo(HdsOperationLogEntity hdsOperationLogEntity) {
        OperationLogVO operationLogVO = new OperationLogVO();
        BeanUtils.copyProperties(hdsOperationLogEntity, operationLogVO);
        operationLogVO.setOprTime(SimpleDateUtils.formatLocalDateTimeToDateHour(hdsOperationLogEntity.getOprTime()));
        operationLogVO.setCreateTime(SimpleDateUtils.formatLocalDateTimeToDate(hdsOperationLogEntity.getCreateTime()));

        // 获取操作类型
        String oprType = hdsOperationLogEntity.getOprType();
        OperationTypeEnum operationType = OperationTypeEnum.getByCode(oprType);

        // 根据操作类型生成变更描述
        List<String> changes = new ArrayList<>();

        switch (operationType) {
            case ADD:
                // 新增操作
                if (StringUtils.isNotBlank(hdsOperationLogEntity.getOprAfter())) {
                    changes = generateAddOperationDetails(hdsOperationLogEntity.getObjectType());
                }
                break;
            case UPDATE:
                // 更新操作 - 比较beforeObj和afterObj
                if (StringUtils.isNotBlank(hdsOperationLogEntity.getOprBefore())
                        && StringUtils.isNotBlank(hdsOperationLogEntity.getOprAfter())) {
                    changes = JsonCompareUtils.compareJsonObjects(
                            hdsOperationLogEntity.getOprBefore(),
                            hdsOperationLogEntity.getOprAfter(),
                            hdsOperationLogEntity.getObjectType(),
                            hdsOperationLogEntity.getOprMethod());
                }
                break;
            case DELETE:
                // 删除操作
                if (StringUtils.isNotBlank(hdsOperationLogEntity.getOprBefore())) {
                    changes = generateDeleteOperationDetails(
                            hdsOperationLogEntity.getObjectType());
                }
                break;
            default:
                break;
        }
        operationLogVO.setChangeDetails(changes);
        return operationLogVO;
    }

    /**
     * 生成新增操作的描述
     *
     * @param objectType 业务类型
     * @return 变更描述列表
     */
    private List<String> generateAddOperationDetails(String objectType) {
        List<String> changes = new ArrayList<>();

        // 获取业务对象名称
        String objectName = getObjectTypeName(objectType);

        changes.add(OperationTypeEnum.ADD.getDesc() + objectName);

        return changes;
    }

    /**
     * 生成删除操作的描述
     *
     * @param objectType 业务类型
     * @return 变更描述列表
     */
    private List<String> generateDeleteOperationDetails(String objectType) {
        List<String> changes = new ArrayList<>();

        // 获取业务对象名称
        String objectName = getObjectTypeName(objectType);
        changes.add(OperationTypeEnum.DELETE.getDesc() + objectName);

        return changes;
    }

    /**
     * 获取业务类型对应的名称
     *
     * @param objectType 业务类型
     * @return 业务类型名称
     */
    private String getObjectTypeName(String objectType) {
        // 根据业务类型返回对应的名称
        BussinessTypeEnum bussinessTypeEnum = BussinessTypeEnum.getByBusinessType(objectType);
        return switch (bussinessTypeEnum) {
            case DEVICE_MODEL -> "设备类型";
            case HOTEL -> "门店";
            case EMPLOYEE -> "员工";
            case DEVICE -> "设备";
            case DEVICE_POSITION -> "设备位置";
            default -> "记录";
        };
    }
}
