package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/3/27 18:05
 * @Description：AppVersionsSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppVersionsSearchReq extends QueryCondition {

    /**
     * 渠道：ios/android
     */
    private String platform;

    /**
     * 版本上线开始日期
     */
    private String startTime;

    /**
     * 版本上线结束日期
     */
    private String endTime;

    /**
     * 版本状态：1-正常，2-失效
     */
    private Integer status;

    /**
     * 应用（如hotel_admin / hotel_guest等等）
     */
    private String appCode;
}
