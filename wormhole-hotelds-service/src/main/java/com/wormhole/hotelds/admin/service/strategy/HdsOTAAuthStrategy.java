package com.wormhole.hotelds.admin.service.strategy;

import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.AiProductTypeEnum;
import com.wormhole.hotelds.admin.model.enums.StatusEnum;
import com.wormhole.hotelds.admin.model.req.GetAuthInfoReq;
import com.wormhole.hotelds.admin.model.vo.AuthVO;
import com.wormhole.hotelds.admin.model.vo.HotelMappingVO;
import com.wormhole.hotelds.admin.model.vo.HotelVO;
import com.wormhole.hotelds.admin.model.vo.HotelWithExternalVO;
import com.wormhole.hotelds.admin.service.*;
import com.wormhole.hotelds.constant.RoleEnum;
import com.wormhole.hotelds.core.enums.EmployeeStatusEnum;
import com.wormhole.hotelds.core.model.entity.HdsEmployeeHotelEntity;
import com.wormhole.hotelds.core.model.entity.HdsHotelLeadEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/5/27 15:39
 * @Description：HdsOTAAuthStrategy
 */
@Component
@Slf4j
public class HdsOTAAuthStrategy implements AuthSourceStrategy<AuthVO> {

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private HotelService hotelService;

    @Resource
    private HotelLeadService hotelLeadService;

    @Override
    public Mono<AuthVO> processAuthInfo(GetAuthInfoReq getAuthInfoReq, AuthVO authVO, HeaderUtils.HeaderInfo headerInfo) {
        return getUserHotels(authVO);
    }

    private Mono<AuthVO> getUserHotels(AuthVO authVO) {
        // 获取用户可见酒店列表
        Mono<List<HotelVO>> hotelListMono = Objects.equals(authVO.getType(), 1)
                ? this.filterOtaHotel()
                : mergeHotelData(authVO)
                .flatMap(hotelCodeList -> {
                    if (CollectionUtils.isEmpty(hotelCodeList)) {
                        return Mono.just(Collections.emptyList());
                    }
                    return hotelService.findByHotelCodes(hotelCodeList).collectList();
                });

        return hotelListMono.flatMap(hotelList -> {
            if (CollectionUtils.isEmpty(hotelList)) {
                authVO.setHotels(Collections.emptyList());
                return Mono.just(authVO);
            }
            return processHotels(hotelList, authVO);
        });
    }

    private Mono<List<HotelVO>> filterOtaHotel() {
        return hotelService.queryList()
                .map(hotelList -> hotelList.stream()
                        .filter(vo -> CollectionUtils.isNotEmpty(vo.getAiProductTypes())
                                && vo.getAiProductTypes().contains(AiProductTypeEnum.OTA_AGENT.getCode()))
                        .collect(Collectors.toList()));
    }

    private Mono<List<String>> mergeHotelData(AuthVO authVO) {
        // 获取员工关联的酒店代码列表
        Mono<List<String>> employeeHotelCodesMono = getHotelCodesByEmployeeId(authVO.getId(), EmployeeStatusEnum.ACTIVE.getCode(), null);

        // 获取由该员工创建的酒店线索的酒店代码列表
        Mono<List<String>> hotelLeadCodesMono = hotelLeadService.findByCreatedBy(String.valueOf(authVO.getId()))
                .map(hdsHotelLeadEntities -> {
                    if (CollectionUtils.isEmpty(hdsHotelLeadEntities)) {
                        return Collections.<String>emptyList();
                    }
                    return hdsHotelLeadEntities.stream()
                            .map(HdsHotelLeadEntity::getHotelCode)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                })
                .defaultIfEmpty(Collections.emptyList());

        Mono<List<String>> hotelCodesMono = hotelLeadCodesMono.flatMap(hotelLeadCodes -> {
            if (CollectionUtils.isEmpty(hotelLeadCodes)) {
                return Mono.just(Collections.emptyList());
            }
            return getHotelCodesByEmployeeId(authVO.getId(), EmployeeStatusEnum.ACTIVE.getCode(), hotelLeadCodes);
        });

        return Mono.zip(employeeHotelCodesMono, hotelCodesMono)
                .map(tuple -> {
                    List<String> employeeHotelCodes = tuple.getT1();
                    List<String> hotelLeadCodes = tuple.getT2();

                    Set<String> mergedSet = new HashSet<>(employeeHotelCodes);
                    mergedSet.addAll(hotelLeadCodes);

                    return new ArrayList<>(mergedSet);
                });
    }

    private Mono<List<String>> getHotelCodesByEmployeeId(Integer employeeId,Integer status,List<String> hotelCodes) {
        return hdsEmployeeHotelService.listByEmployeeId(
                        employeeId,
                        status,
                        CollectionUtils.isNotEmpty(hotelCodes) ? hotelCodes : null
                )
                .map(hdsEmployeeHotelEntities -> {
                    if (CollectionUtils.isEmpty(hdsEmployeeHotelEntities)) {
                        return Collections.<String>emptyList();
                    }
                    return hdsEmployeeHotelEntities.stream()
                            .map(HdsEmployeeHotelEntity::getHotelCode)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                })
                .defaultIfEmpty(Collections.emptyList());
    }

    private Mono<AuthVO> processHotels(List<HotelVO> hotelList, AuthVO authVO) {
        List<HotelWithExternalVO> unmappedHotels = hotelList.stream()
                .filter(this::isOtaServiceNotExpired)
                .map(hotelVO -> new HotelWithExternalVO()
                        .setHotelCode(hotelVO.getHotelCode())
                        .setHotelName(hotelVO.getHotelName())
                        .setExternalHotels(Collections.emptyList()))
                .toList();

        return Flux.fromIterable(unmappedHotels)
                .flatMap(vo -> hotelLeadService.findByHotelCode(vo.getHotelCode())
                        .map(hotelLeadVO -> {
                            vo.setHotelLead(hotelLeadVO);
                            return vo;
                        }).onErrorResume(e -> {
                            log.warn("Hotel lead not found for hotel code: {}, continuing execution.", vo.getHotelCode());
                            return Mono.just(vo);
                        })
                )
                .collectList()
                .map(updatedHotelWithExternalVOList -> {
                    authVO.setHotels(unmappedHotels);
                    return authVO;
                });
    }

    /**
     * 判断酒店的OTA服务是否已到期
     *
     * @param hotel 酒店信息
     * @return 是否到期
     */
    private boolean isOtaServiceNotExpired(HotelVO hotel) {
        // 删除门店/停用门店
        if (StatusEnum.INVALID.getCode().equals(hotel.getRowStatus()) ||
                StatusEnum.INVALID.getCode().equals(hotel.getStatus())) {
            return false;
        }
        // 未完成初始化的门店
        return true;
//        return StringUtils.isNotEmpty(hotel.getOtaEndTime())
//                && LocalDate.parse(hotel.getOtaEndTime()).isAfter(LocalDate.now());
    }

    @Override
    public String getSourceCode() {
        return SourcePlatform.CHROME_PLUGIN.getCode();
    }
}
