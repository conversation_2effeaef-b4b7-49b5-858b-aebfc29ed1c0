package com.wormhole.hotelds.admin.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/4/7 16:14
 * @Description：OperationTypeEnum
 */
@Getter
public enum OperationTypeEnum {

    /**
     * 新增操作
     */
    ADD("add", "新增"),

    /**
     * 修改操作
     */
    UPDATE("update", "修改"),

    /**
     * 删除操作
     */
    DELETE("delete", "删除"),

    /**
     * 查询操作
     */
    QUERY("query", "查询"),

    /**
     * 导出操作
     */
    EXPORT("export", "导出"),

    /**
     * 导入操作
     */
    IMPORT("import", "导入"),

    /**
     * 登录操作
     */
    LOGIN("login", "登录"),

    /**
     * 登出操作
     */
    LOGOUT("logout", "登出"),

    /**
     * 审核操作
     */
    AUDIT("audit", "审核"),

    /**
     * 上传操作
     */
    UPLOAD("upload", "上传"),

    /**
     * 下载操作
     */
    DOWNLOAD("download", "下载"),

    /**
     * 分配操作
     */
    ASSIGN("assign", "分配"),

    /**
     * 启用操作
     */
    ENABLE("enable", "启用"),

    /**
     * 禁用操作
     */
    DISABLE("disable", "禁用"),

    /**
     * 重置操作（如重置密码）
     */
    RESET("reset", "重置"),

    /**
     * 解绑操作
     */
    UN_BIND("unbind", "解绑"),

    /**
     * 其他操作
     */
    OTHER("other", "其他");

    /**
     * 操作编码
     */
    private final String code;

    /**
     * 操作描述
     */
    private final String desc;

    /**
     * 枚举映射
     */
    private static final Map<String, OperationTypeEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(OperationTypeEnum::getCode, Function.identity()));

    /**
     * 构造函数
     *
     * @param code 操作编码
     * @param desc 操作描述
     */
    OperationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举实例
     *
     * @param code 操作编码
     * @return 对应的枚举实例，如果不存在则返回OTHER
     */
    public static OperationTypeEnum getByCode(String code) {
        return CODE_MAP.getOrDefault(code, OTHER);
    }

    /**
     * 检查编码是否存在
     *
     * @param code 操作编码
     * @return 是否存在
     */
    public static boolean contains(String code) {
        return CODE_MAP.containsKey(code);
    }
}
