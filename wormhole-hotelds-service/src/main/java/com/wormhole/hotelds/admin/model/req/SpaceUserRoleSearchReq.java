package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SpaceUserRoleSearchReq implements Serializable {

    @NotNull(message = "spaceCode不能为空")
    private String spaceCode;

    private String searchKey;

    private String roleCode;

}
