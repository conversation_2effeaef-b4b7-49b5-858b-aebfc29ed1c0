package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @Date 2025/02/10 09:43
 **/
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SpaceSaveReq {

    /**
     * 空间ID
     */
    private Long id;

    /**
     * 所属门店ID
     */
    private Long hotelId;

    /**
     * 楼层
     */
    private Integer floor;

    /**
     * 空间编号（房号或自定义编码）
     */
    private String spaceCode;

    /**
     * 空间类型，如：客房/前台/餐厅/健身房等
     */
    private String spaceType;

    /**
     * 空间名称（如101/前台/西餐厅/）
     */
    private String spaceName;

    /**
     * WiFi SSID
     */
    private String wifiSsid;

    /**
     * WiFi 密码
     */
    private String wifiPassword;

}
