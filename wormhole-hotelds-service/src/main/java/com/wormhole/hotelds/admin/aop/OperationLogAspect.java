package com.wormhole.hotelds.admin.aop;

import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.annotation.OperationLog;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.admin.repository.*;
import com.wormhole.hotelds.admin.service.HdsEmployeeHotelService;
import com.wormhole.hotelds.core.model.entity.HdsEmployeeHotelEntity;
import com.wormhole.hotelds.util.OperationLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/5/6 10:54
 * @Description：操作日志记录切面
 */
@Aspect
@Component
@Slf4j
public class OperationLogAspect {
    private static final String SUCCESS = "SUCCESS";
    private static final String FAIL = "FAIL";

    @Resource
    private OperationLogUtil operationLogUtil;
    @Resource
    private DeviceRepository deviceRepository;
    @Resource
    private DeviceModelRepository deviceModelRepository;
    @Resource
    private HotelRepository hotelRepository;
    @Resource
    private DevicePositionRepository devicePositionRepository;
    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private InvitationCodeRepository invitationCodeRepository;

    @Around("@annotation(com.wormhole.hotelds.admin.annotation.OperationLog)")
    public Object logOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名和注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationLog operationLog = method.getAnnotation(OperationLog.class);

        // 如果未获取到注解，直接执行原方法
        if (operationLog == null) {
            return joinPoint.proceed();
        }

        // 准备日志上下文并执行方法
        LoggingContext context = createLoggingContext(joinPoint, operationLog);
        return executeWithLogging(joinPoint, context);
    }

    private LoggingContext createLoggingContext(ProceedingJoinPoint joinPoint, OperationLog operationLog) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();
        Object requestParam = args.length > operationLog.requestParamIndex() ?
                args[operationLog.requestParamIndex()] : null;

        return LoggingContext.builder()
                .oprParams(requestParam)
                .businessId(extractId(requestParam))
                .oprTime(LocalDateTime.now())
                .businessType(operationLog.businessType())
                .operationType(operationLog.operationType())
                .methodName(signature.getMethod().getName())
                .build();
    }

    private Object executeWithLogging(ProceedingJoinPoint joinPoint, LoggingContext context) {
        // 获取请求头和操作前对象
        Mono<LoggingContext> contextWithHeader = HeaderUtils.getHeaderInfo()
                .map(headerInfo -> {
                    context.setHeaderInfo(headerInfo);
                    return context;
                })
                .defaultIfEmpty(context);

        Mono<LoggingContext> contextWithBefore = needsBeforeObject(context)
                ? contextWithHeader.flatMap(ctx ->
                getObjectById(ctx)
                        .map(beforeObj -> {
                            ctx.setBeforeObj(beforeObj);
                            return ctx;
                        })
                        .defaultIfEmpty(ctx))
                : contextWithHeader;

        return contextWithBefore
                .flatMap(ctx -> {
                    try {
                        // 获取开始时间
                        LocalDateTime startTime = ctx.getOprTime();

                        // 执行原方法，获取 Mono 结果
                        Mono<?> resultMono = (Mono<?>) joinPoint.proceed();

                        // 处理执行结果
                        return resultMono
                                .flatMap(value -> {
                                    // 计算执行时间
                                    long executionTime = Duration.between(
                                            startTime.atZone(ZoneId.systemDefault()).toInstant(),
                                            Instant.now()
                                    ).toMillis();
                                    ctx.setExecutionTime(executionTime);
                                    ctx.setOprResult(SUCCESS);

                                    if (Objects.isNull(ctx.getBusinessId())) {
                                        ctx.setBusinessId(extractId(ctx.getOprParams()));
                                    }

                                    // 获取操作后对象（如果需要）
                                    return (needsAfterObject(ctx)
                                            ? getObjectById(ctx)
                                            .map(afterObj -> {
                                                ctx.setAfterObj(afterObj);
                                                return ctx;
                                            })
                                            .defaultIfEmpty(ctx)
                                            : Mono.just(ctx))
                                            .flatMap(completeCtx -> {
                                                completeCtx.setOprContent(generateOperationContent(completeCtx));
                                                return operationLogUtil.recordSuccess(completeCtx)
                                                        .thenReturn(value);
                                            });
                                });
//                                .onErrorResume(error -> {
//                                    ctx.setOprResult(FAIL);
//                                    ctx.setErrorMsg(error.getMessage());
//                                    return operationLogUtil.recordFail(ctx)
//                                            .then(Mono.error(error));
//                                });
                    } catch (Throwable e) {
//                        ctx.setOprResult(FAIL);
//                        ctx.setErrorMsg(e.getMessage());
//                        return operationLogUtil.recordFail(ctx)
//                                .then(Mono.error(e));
                        return Mono.error(e);
                    }
                });
    }

    private boolean needsBeforeObject(LoggingContext context) {
        return context.getOperationType() != null &&
                (context.getOperationType() == OperationTypeEnum.UPDATE ||
                        context.getOperationType() == OperationTypeEnum.DELETE) &&
                context.getBusinessId() != null;
    }

    private boolean needsAfterObject(LoggingContext context) {
        return context.getOperationType() != null &&
                (context.getOperationType() == OperationTypeEnum.ADD ||
                        context.getOperationType() == OperationTypeEnum.UPDATE) &&
                context.getBusinessId() != null;
    }

    private Mono<Object> getObjectById(LoggingContext ctx) {
        BussinessTypeEnum businessType = ctx.getBusinessType();
        Long id = ctx.getBusinessId();
        String methodName = ctx.getMethodName();
        if (id == null) {
            return Mono.empty();
        }

        try {
            return switch (businessType) {
                case DEVICE -> deviceRepository.findById(id).cast(Object.class);
                case DEVICE_MODEL -> deviceModelRepository.findById(id).cast(Object.class);
                case HOTEL -> hotelRepository.findById(Math.toIntExact(id)).cast(Object.class);
                case DEVICE_POSITION -> devicePositionRepository.findById(id).cast(Object.class);
                case EMPLOYEE -> {
                    // 特殊逻辑：如果方法名为updateHotelList，返回员工-门店列表
                    if ("updateHotelList".equals(methodName)) {
                        yield hdsEmployeeHotelService.listByEmployeeId(id.intValue(), null, null)
                                .map(hdsEmployeeHotelEntities ->
                                        hdsEmployeeHotelEntities.stream().map(HdsEmployeeHotelEntity::getHotelCode)
                                                .collect(Collectors.toSet()));
                    } else {
                        yield hdsEmployeeRepository.findById(Math.toIntExact(id)).cast(Object.class);
                    }
                }
                case INVITATION_CODE -> invitationCodeRepository.findById(Math.toIntExact(id)).cast(Object.class);
                default -> {
                    log.warn("未支持的业务类型: {}", businessType);
                    yield Mono.empty();
                }
            };
        } catch (Exception e) {
            log.warn("获取对象时发生异常: {}", e.getMessage());
            return Mono.empty();
        }
    }

    private String generateOperationContent(LoggingContext context) {
        // 处理员工状态更新的特殊情况
        if (BussinessTypeEnum.EMPLOYEE == context.getBusinessType() &&
                context.getMethodName() != null) {

            if (context.getMethodName().contains("updateStatus")) {
                Integer status = extractStatus(context.getOprParams());
                if (status != null) {
                    return "更新状态为" + convertStatusToDesc(status);
                }
            }

            if (context.getMethodName().contains("resetPassword")) {
                return "重置密码";
            }

            if (context.getMethodName().contains("updateHotelList")) {
                return "关联门店";
            }
        }

        if (BussinessTypeEnum.INVITATION_CODE == context.getBusinessType() &&
                context.getMethodName() != null) {

            if (context.getMethodName().contains("updateStatus")) {
                Integer status = extractStatus(context.getOprParams());
                if (status != null) {
                    return convertInvitationCodeStatusToDesc(status);
                }
            }
        }

        // 生成默认操作内容
        return Optional.ofNullable(context.getOperationType())
                .map(type -> type.getDesc() + getBusinessTypeDesc(context.getBusinessType()))
                .orElse("未知操作" + getBusinessTypeDesc(context.getBusinessType()));
    }

    private String getBusinessTypeDesc(BussinessTypeEnum businessType) {
        if (businessType == null) {
            return "未知业务";
        }

        return switch (businessType) {
            case HOTEL -> "门店";
            case DEVICE -> "设备";
            case DEVICE_MODEL -> "设备类型";
            case DEVICE_POSITION -> "设备位置";
            case EMPLOYEE -> "员工";
            default -> businessType.getBusinessType();
        };
    }

    private Long extractId(Object requestParam) {
        if (requestParam == null) {
            return null;
        }

        try {
            // 如果入参本身就是Number类型
            if (requestParam instanceof Number) {
                return ((Number) requestParam).longValue();
            }
            // 如果入参本身就是String类型
            if (requestParam instanceof String) {
                return Long.parseLong((String) requestParam);
            }
            Method idGetter = findIdGetter(requestParam.getClass());
            if (idGetter != null) {
                Object value = idGetter.invoke(requestParam);
                if (value instanceof Number) {
                    return ((Number) value).longValue();
                }
                if (value instanceof String) {
                    return Long.parseLong((String) value);
                }
            }
        } catch (Exception e) {
            log.debug("提取ID失败: {}", e.getMessage());
        }
        return null;
    }

    private Method findIdGetter(Class<?> clazz) {
        // 处理基本类型及其包装类
        if (clazz == Integer.class || clazz == int.class) {
            try {
                return Integer.class.getMethod("intValue");
            } catch (NoSuchMethodException ignored) {
            }
        }
        if (clazz == Long.class || clazz == long.class) {
            try {
                return Long.class.getMethod("longValue");
            } catch (NoSuchMethodException ignored) {
            }
        }
        if (clazz == String.class) {
            try {
                return String.class.getMethod("toString");
            } catch (NoSuchMethodException ignored) {
            }
        }
        for (String fieldName : new String[]{"id", "Id", "ID"}) {
            try {
                return clazz.getMethod("get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1));
            } catch (NoSuchMethodException ignored) {
            }
        }
        return null;
    }

    private Integer extractStatus(Object requestParam) {
        if (requestParam == null) {
            return null;
        }

        try {
            Method statusGetter = findStatusGetter(requestParam.getClass());
            if (statusGetter != null) {
                Object value = statusGetter.invoke(requestParam);
                if (value instanceof Number) {
                    return ((Number) value).intValue();
                }
                if (value instanceof String) {
                    return Integer.parseInt((String) value);
                }
            }
        } catch (Exception e) {
            log.debug("提取状态值失败: {}", e.getMessage());
        }
        return null;
    }

    private Method findStatusGetter(Class<?> clazz) {
        try {
            return clazz.getMethod("getStatus");
        } catch (NoSuchMethodException e) {
            return null;
        }
    }

    private String convertStatusToDesc(Integer status) {
        return switch (status) {
            case 1 -> "正常";
            case 2 -> "停用";
            case 3 -> "注销";
            default -> "未知(" + status + ")";
        };
    }

    private String convertInvitationCodeStatusToDesc(Integer status) {
        return switch (status) {
            case 1 -> "启用";
            case 0 -> "停用";
            default -> "未知(" + status + ")";
        };
    }
}