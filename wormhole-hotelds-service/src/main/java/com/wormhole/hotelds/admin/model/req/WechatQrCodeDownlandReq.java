package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/21 18:11
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WechatQrCodeDownlandReq implements Serializable {

    private List<String> idList;

    /**
     * 类型: "wechat" 表示微信二维码, "normal" 表示普通二维码
     */
    private String type;
}
