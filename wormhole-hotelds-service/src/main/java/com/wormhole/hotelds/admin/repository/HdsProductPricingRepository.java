package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsProductPricingEntity;
import dev.langchain4j.service.V;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/1/16 15:30
 * @Description：HdsProductPricingRepository
 */
@Repository
public interface HdsProductPricingRepository extends ReactiveCrudRepository<HdsProductPricingEntity, Integer> {

    Flux<HdsProductPricingEntity> findByProductId(Integer productId);

    Flux<Boolean> deleteAllByProductIdIn(Collection<Integer> productIds);

    Flux<HdsProductPricingEntity> findByIdIn(Collection<Integer> ids);

    Mono<HdsProductPricingEntity> findByProductIdAndPeriodType(Integer productId, Integer periodType);

    Flux<HdsProductPricingEntity> findAllByProductIdIn(Collection<Integer> productIds);
}