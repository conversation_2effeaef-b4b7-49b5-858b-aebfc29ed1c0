package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsHotelProductPricingEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/1/16 15:30
 * @Description：HdsHotelProductPricingRepository
 */
@Repository
public interface HdsHotelProductPricingRepository extends ReactiveCrudRepository<HdsHotelProductPricingEntity, Long> {

    Flux<HdsHotelProductPricingEntity> deleteAllByHotelPackageCode(String hotelPackageCode);

    Flux<HdsHotelProductPricingEntity> findByHotelPackageCode(String hotelPackageCode);

    Flux<HdsHotelProductPricingEntity> findByHotelPackageCodeAndProductId(String hotelPackageCode, Integer productId);

    Flux<HdsHotelProductPricingEntity> findByIdIn(Collection<Integer> ids);

    Mono<HdsHotelProductPricingEntity> findByHotelPackageCodeAndProductIdAndPeriodType(String hotelPackageCode, Integer productId, Integer periodType);

    Mono<Void> deleteAllByHotelPackageCodeAndProductIdIn(String hotelPackageCode, List<Integer> productIds);
}