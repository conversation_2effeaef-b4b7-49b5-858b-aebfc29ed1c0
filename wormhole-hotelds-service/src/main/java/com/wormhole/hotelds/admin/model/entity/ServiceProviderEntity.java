package com.wormhole.hotelds.admin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:55:12
 * @Description: ServiceProvider
 */
@Data
@Table("hds_service_provider")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ServiceProviderEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    private Integer id;


    /**
     * 公司编码
     */
    @Column("provider_id")
    private String providerId;


    /**
     * 公司名称
     */
    @Column("provider_name")
    private String providerName;


    /**
     * 国家编码
     */
    @Column("country_code")
    private String countryCode;


    /**
     * 国家
     */
    @Column("country_name")
    private String countryName;


    /**
     * 省编码
     */
    @Column("province_code")
    private String provinceCode;


    /**
     * 省
     */
    @Column("province_name")
    private String provinceName;


    /**
     * 城市编码
     */
    @Column("city_code")
    private String cityCode;


    /**
     * 城市
     */
    @Column("city_name")
    private String cityName;


    /**
     * 区县编码
     */
    @Column("district_code")
    private String districtCode;


    /**
     * 区县
     */
    @Column("district_name")
    private String districtName;


    /**
     * 详细地址
     */
    @Column("address")
    private String address;


    /**
     * 公司电话
     */
    @Column("provider_phone")
    private String providerPhone;


    /**
     * 公司邮箱
     */
    @Column("provider_email")
    private String providerEmail;


    /**
     * 联系人
     */
    @Column("provider_contacts")
    private String providerContacts;


    /**
     * 公司营业执照
     */
    @Column("provider_license_url")
    private String providerLicenseUrl;


    /**
     * 状态 1有效 0无效
     */
    @Column("status")
    private Integer status;


}