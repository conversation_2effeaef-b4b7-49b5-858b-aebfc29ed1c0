package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/6/16 17:20
 * @Description：HotelPackageSearchReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelPackageSearchReq extends QueryCondition {

    /**
     * 门店编码（支持模糊搜索）
     */
    private String hotelCode;

    /**
     * 套餐名称（支持模糊搜索）
     */
    private String packageName;

    /**
     * 状态：1-启用，0-停用
     */
    private Integer status;
}
