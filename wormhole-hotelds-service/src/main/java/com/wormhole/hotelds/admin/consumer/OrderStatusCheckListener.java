package com.wormhole.hotelds.admin.consumer;

import com.wormhole.hotelds.admin.model.enums.OrderStatusEnum;
import com.wormhole.hotelds.admin.model.req.OrderMessageReq;
import com.wormhole.hotelds.admin.service.OrderPaymentHandler;
import com.wormhole.hotelds.admin.service.OrderService;
import com.wormhole.hotelds.admin.service.YopPayService;
import com.wormhole.hotelds.constant.SystemConstant;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import com.wormhole.mq.producer.ReactiveMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/6/20 11:30
 * @Description：支付状态检查监听器
 */
@Component
@RocketMQMessageListener(
        topic = SystemConstant.ORDER_PAYMENT_CHECK_TOPIC,
        consumerGroup = SystemConstant.ORDER_PAYMENT_CHECK_CONSUMER_GROUP
)
@Slf4j
public class OrderStatusCheckListener extends AbstractReactiveMessageListener<OrderMessageReq> {

    // 检查间隔（秒）：30s, 60s, 180s, 300s, 600s
    private static final int[] CHECK_INTERVALS = {30, 60, 180, 300, 600};
    private static final int MAX_CHECK_TIMES = CHECK_INTERVALS.length;

    @Resource
    private OrderService orderService;

    @Resource
    private ReactiveMessageSender reactiveMessageSender;

    @Resource
    private OrderOverdueListener orderOverdueListener;

    @Resource
    private YopPayService yopPayService;

    @Resource
    private OrderPaymentHandler orderPaymentHandler;

    @Override
    protected Mono<Void> processMessage(OrderMessageReq messageReq) {
        String orderNo = messageReq.getOrderNo();
        int checkCount = messageReq.getCheckCount();

        log.info("PaymentStatusCheckListener checking payment status for orderNo: {}, checkCount: {}",
                orderNo, checkCount);

        return orderPaymentHandler.executeWithLock(orderNo, lockedOrderNo ->
                orderService.findByOrderNo(lockedOrderNo)
                        .switchIfEmpty(Mono.defer(() -> {
                            log.warn("Order not found for orderNo: {}", lockedOrderNo);
                            return Mono.empty();
                        }))
                        .flatMap(order -> {
                            // 如果订单已支付或已取消，不需要继续检查
                            if (!OrderStatusEnum.PENDING.getCode().equals(order.getOrderStatus())) {
                                log.info("Order {} is already in final status: {}",
                                        lockedOrderNo, OrderStatusEnum.getByCode(order.getOrderStatus()));
                                return Mono.empty();
                            }

                            OrderMessageReq req = new OrderMessageReq();
                            req.setOrderNo(lockedOrderNo);
                            // 检查订单是否已过期
                            if (order.getExpireAt() != null && order.getExpireAt().isBefore(LocalDateTime.now())) {
                                log.info("Order {} has expired, cancelling...", lockedOrderNo);
                                return orderOverdueListener.processMessage(req);
                            }

                            // 检查是否达到最大检查次数
                            if (checkCount >= MAX_CHECK_TIMES) {
                                log.info("Order {} has reached max check times: {}", lockedOrderNo, MAX_CHECK_TIMES);
                                return Mono.empty();
                            }

                            // 查询易宝支付结果
                            return yopPayService.queryOrderStatus(lockedOrderNo)
                                    .flatMap(orderStatusResp -> {
                                        // 处理查询结果
                                        if (OrderStatusEnum.PAID.getCode().equals(orderStatusResp.getStatus())) {
                                            log.info("Order {} is paid according to Yeepay query", lockedOrderNo);
                                            // 调用支付成功处理逻辑
                                            return orderPaymentHandler.handlePaymentSuccess(orderStatusResp);
                                        } else if (OrderStatusEnum.CANCELLED.getCode().equals(orderStatusResp.getStatus())) {
                                            log.info("Order {} is closed or expired according to Yeepay query", lockedOrderNo);
                                            return orderOverdueListener.processMessage(req);
                                        } else {
                                            return sendNextCheckMessage(lockedOrderNo, checkCount);
                                        }
                                    }).onErrorResume(e ->{
                                        log.error("Failed to query payment status for orderNo: {}", lockedOrderNo, e);
                                        return sendNextCheckMessage(lockedOrderNo, checkCount);
                                    });
                        })
        ).then();
    }

    private Mono<Void> sendNextCheckMessage(String orderNo, int currentCheckCount) {
        OrderMessageReq req = new OrderMessageReq();
        req.setOrderNo(orderNo);
        req.setCheckCount(currentCheckCount + 1);

        // 获取下一次检查的延迟级别
        int delaySeconds = CHECK_INTERVALS[currentCheckCount];
        int delayLevel = convertToRocketMQDelayLevel(delaySeconds);

        log.info("Scheduling next check for order {} in {} seconds (delay level {}), check count: {}",
                orderNo, delaySeconds, delayLevel, currentCheckCount + 1);

        return reactiveMessageSender.sendDelayMessage(
                SystemConstant.ORDER_PAYMENT_CHECK_TOPIC,
                req,
                delayLevel
        ).then();
    }

    /**
     * 将秒数转换为RocketMQ的延迟级别
     * RocketMQ的延迟级别：
     * 1s, 5s, 10s, 30s, 1m, 2m, 3m, 4m, 5m, 6m, 7m, 8m, 9m, 10m, 20m, 30m, 1h, 2h
     */
    private int convertToRocketMQDelayLevel(int seconds) {
        if (seconds <= 1) return 1;      // 1s
        if (seconds <= 5) return 2;      // 5s
        if (seconds <= 10) return 3;     // 10s
        if (seconds <= 30) return 4;     // 30s
        if (seconds <= 60) return 5;     // 1m
        if (seconds <= 120) return 6;    // 2m
        if (seconds <= 180) return 7;    // 3m
        if (seconds <= 240) return 8;    // 4m
        if (seconds <= 300) return 9;    // 5m
        if (seconds <= 360) return 10;   // 6m
        if (seconds <= 420) return 11;   // 7m
        if (seconds <= 480) return 12;   // 8m
        if (seconds <= 540) return 13;   // 9m
        if (seconds <= 600) return 14;   // 10m
        if (seconds <= 1200) return 15;  // 20m
        if (seconds <= 1800) return 16;  // 30m
        return 17;                       // 1h
    }
}