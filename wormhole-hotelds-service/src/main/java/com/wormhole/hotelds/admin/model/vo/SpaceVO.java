package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.beans.*;

/**
 * @Description:
 * @Author: wj
 * @Date: 2025/3/31 16:38
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SpaceVO {

    /**
     * 所属门店ID
     */
    private Long hotelId;

    /**
     * 楼层
     */
    private Integer floor;

    /**
     * 空间编号（房号或自定义编码）
     */
    private String spaceCode;

    /**
     * 空间类型，如：客房/前台/餐厅/健身房等
     */
    private String spaceType;

    /**
     * 空间名称（如101/前台/西餐厅/）
     */
    private String spaceName;

    /**
     * WiFi SSID
     */
    private String wifiSsid;

    /**
     * WiFi 密码
     */
    private String wifiPassword;

}
