package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @author: huangweijie
 * @date: 2025/5/15
 * @Description: 酒店来源用户认证信息VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelAuthVO extends AuthVO {
    
    /**
     * 账号类型，1总机 2分机
     */
    private Integer accountType;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色代码
     */
    private String roleCode;

    /**
     * 角色
     */
    private List<String> roleCodes;

    /**
     * 设备位置
     */
    private String positionCode;

    /**
     * 设备名称
     */
    private String positionName;

    /**
     * 暂停接单 0允许接单 1暂停接单
     */
    private Integer acceptTicketStatus;

    private Integer ticketAssignmentFlag;
}