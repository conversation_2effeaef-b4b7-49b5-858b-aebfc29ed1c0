package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-26 15:33:40
 * @Description: 发票申请搜索请求
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceApplicationSearchReq extends QueryCondition {

    /**
     * 申请编号（精确搜索）
     */
    private String applicationNo;

    /**
     * 酒店编码/门店编码（模糊搜索）
     */
    private String hotelCode;

    /**
     * 申请状态：0-待审核，1-审核中，2-已开票，3-已驳回
     */
    private Integer applicationStatus;

} 