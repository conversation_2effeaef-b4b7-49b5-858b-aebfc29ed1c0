package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.OperationLogSearchReq;
import com.wormhole.hotelds.admin.model.vo.DeviceModelVO;
import com.wormhole.hotelds.admin.model.vo.LogSearchVO;
import com.wormhole.hotelds.admin.model.vo.OperationLogVO;
import com.wormhole.hotelds.admin.service.OperationLogService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/5/6 15:51
 * @Description：LogController
 */
@RestController
@RequestMapping("/log")
public class OperationLogController {

    @Resource
    private OperationLogService operationLogService;

    /**
     * 日志列表查询
     * @param operationLogSearchReq
     * @return
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<LogSearchVO>>> search(@RequestBody OperationLogSearchReq operationLogSearchReq) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> Mono.zip(operationLogService.searchCount(headerInfo, operationLogSearchReq), operationLogService.search(headerInfo, operationLogSearchReq))
                        .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                        .flatMap(Result::success));
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<OperationLogVO>> findById(@PathVariable("id") Long id) {
        return operationLogService.findById(id).flatMap(Result::success);
    }
}
