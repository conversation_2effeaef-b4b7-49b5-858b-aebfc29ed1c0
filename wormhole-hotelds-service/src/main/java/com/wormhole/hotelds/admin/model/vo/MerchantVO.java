package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/3/26 10:35
 * @Description：MerchantVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MerchantVO {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * 商户类型 1酒店商户 2服务商商户
     */
    private Integer type;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 商户联系电话
     */
    private String merchantPhone;

    /**
     * 商户邮箱
     */
    private String merchantEmail;

    /**
     * 商户联系人
     */
    private String merchantContacts;

    /**
     * 商户营业执照URL
     */
    private String merchantLicenseUrl;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 开户银行
     */
    private String accountBank;

    /**
     * 开户支行
     */
    private String accountSubBank;

    /**
     * 银行卡号
     */
    private String accountCardNumber;

    /**
     * 账户预留手机号
     */
    private String accountReserveMobile;

    /**
     * 账户收款手机号
     */
    private String accountCashMobile;

    /**
     * 状态 1有效 0无效
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 转换为视图对象
     */
    public static MerchantVO toVo(HdsMerchantEntity entity) {
        MerchantVO merchantVO = new MerchantVO();
        BeanUtils.copyProperties(entity, merchantVO);
        return merchantVO;
    }
}
