package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

import java.util.*;

/**
 * @Author：flx
 * @Date：2025/4/3 09:19
 * @Description：HdsEmployeeDeleteReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsEmployeeUpdateHotelReq {

    private Integer id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 角色code
     */
    private String roleCode;

    private String roleName;

    /**
     * 账号类型，1总机 2分机
     */
    private Integer accountType;

//    /**
//     * 工单类型
//     */
//    private List<String> tickets;

    /**
     * 关联的酒店编码列表
     */
    private List<String> hotelCodes;

//    /**
//     * 关联的位置列表
//     */
//    private List<String> positionCodes;

    /**
     * 员工类型 1-集团 2-服务商 3-酒店
     */
    private Integer type;

    private Integer ticketAssignmentFlag;

}
