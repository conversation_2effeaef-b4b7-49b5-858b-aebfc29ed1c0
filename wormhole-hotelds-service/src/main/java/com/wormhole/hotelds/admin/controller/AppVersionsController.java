package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.AppVersionsDeleteReq;
import com.wormhole.hotelds.admin.model.req.AppVersionsSaveReq;
import com.wormhole.hotelds.admin.model.req.AppVersionsSearchReq;
import com.wormhole.hotelds.admin.model.vo.AppVersionSearchVO;
import com.wormhole.hotelds.admin.model.vo.AppVersionsVO;
import com.wormhole.hotelds.admin.service.AppVersionsService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:58:35
 * @Description: AppVersions
 */
@RestController
@RequestMapping("/appversion")
public class AppVersionsController {

    @Resource
    private AppVersionsService appVersionsService;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody AppVersionsSaveReq appVersionSaveReq) {
        return appVersionsService.create(appVersionSaveReq).flatMap(Result::success);
    }

    /**
     * 更新记录
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody AppVersionsSaveReq appVersionSaveReq) {
        return appVersionsService.update(appVersionSaveReq).flatMap(Result::success);
    }

    /**
     * 删除记录
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody AppVersionsDeleteReq appVersionDeleteReq) {
        return appVersionsService.delete(appVersionDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<AppVersionSearchVO>>> search(@RequestBody AppVersionsSearchReq appVersionSearchReq) {
        return Mono.zip(appVersionsService.searchCount(appVersionSearchReq), appVersionsService.search(appVersionSearchReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<AppVersionsVO>> findById(@PathVariable("id") Integer id) {
        return appVersionsService.findById(id).flatMap(Result::success);
    }

    /**
     * 最新版本查询
     * @return
     */
    @PostMapping("/latest")
    public Mono<Result<AppVersionsVO>> getLatest(@RequestBody AppVersionsSearchReq req) {
        return appVersionsService.findLatestByPlatform(req).flatMap(Result::success);
    }

    /**
     * 通过当前版本号校验是否需要更新
     * @return
     */
    @PostMapping("/checkUpdate")
    public Mono<Result<AppVersionsVO>> checkUpdate(@RequestBody AppVersionsSearchReq req,
                                                @RequestHeader("AppVersion") String currentVersion) {
        return appVersionsService.checkUpdate(req, currentVersion)
                .flatMap(Result::success);
    }
}