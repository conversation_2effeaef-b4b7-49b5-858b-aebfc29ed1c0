package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.relational.core.mapping.Column;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:56:47
 * @Description: 设备型号
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceModelSaveReq {

    private Long id;

    /**
     * 设备类型名称
     */
    private String modelName;

    /**
     * 设备型号
     */
    private String modelCode;

    /**
     * 厂商名称
     */
    private String manufacturer;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 联系人
     */
    private String manufacturerContactPerson;

    /**
     * 联系方式
     */
    private String manufacturerContact;

    /**
     * 保修期（年）
     */
    private Integer warrantyMonths;

    /**
     * app类型
     */
    private String deviceAppType;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 厂商服务提供商
     */
    private String manufacturerServiceProvider;

    /**
     * 保修情况
     */
    private String warrantyInfo;

    /**
     * 备注
     */
    private String remark;
}