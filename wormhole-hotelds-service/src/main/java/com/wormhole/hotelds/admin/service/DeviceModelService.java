package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.annotation.OperationLog;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.admin.model.enums.StatusEnum;
import com.wormhole.hotelds.admin.model.req.DeviceModelDeleteReq;
import com.wormhole.hotelds.admin.model.req.DeviceModelSaveReq;
import com.wormhole.hotelds.admin.model.req.DeviceModelSearchReq;
import com.wormhole.hotelds.admin.model.req.DeviceUnbindReq;
import com.wormhole.hotelds.admin.model.vo.DeviceModelSearchVO;
import com.wormhole.hotelds.admin.model.vo.DeviceModelVO;
import com.wormhole.hotelds.admin.repository.DeviceInfoRepository;
import com.wormhole.hotelds.admin.repository.DeviceModelRepository;
import com.wormhole.hotelds.admin.repository.DeviceRepository;
import com.wormhole.hotelds.admin.repository.OperationLogRepository;
import com.wormhole.hotelds.core.enums.DeviceStatusEnum;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.query.DeviceModelCount;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.OperationLogUtil;
import com.wormhole.hotelds.util.UserUtils;
import com.wormhole.hotelds.util.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/4/7 09:30
 * @Description：DeviceModelService
 */
@Slf4j
@Service
public class DeviceModelService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private DeviceModelRepository deviceModelRepository;

    @Resource
    private DeviceInfoRepository deviceInfoRepository;

    @Resource
    private DeviceRepository deviceRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    /**
     * 创建设备类型
     *
     * @param deviceModelSaveReq 请求参数
     * @return Mono<Boolean>
     */
    @OperationLog(businessType = BussinessTypeEnum.DEVICE_MODEL,
            operationType = OperationTypeEnum.ADD)
    public Mono<Boolean> create(DeviceModelSaveReq deviceModelSaveReq) {
        ValidatorUtils.validateDeviceModelSaveReq(deviceModelSaveReq, false);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionCreate(deviceModelSaveReq, headerInfo)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device model create error"));
                        })
                );
    }

    /**
     * 事务创建
     *
     * @param deviceModelSaveReq 请求参数
     * @param headerInfo         请求头信息
     * @return Mono<Boolean>
     */
    private Mono<Boolean> executeTransferWithinTransactionCreate(DeviceModelSaveReq deviceModelSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        return deviceModelRepository.existsByModelCode(deviceModelSaveReq.getModelCode().trim())
                .flatMap(exists -> {
                    if (exists) {
                        // 如果已存在，返回错误
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                                "设备型号编码[" + deviceModelSaveReq.getModelCode().trim() + "]已存在"));
                    } else {
                        // 2. 创建并保存设备型号
                        return transactionalOperator.transactional(
                                        deviceModelRepository.save(buildHdsDeviceModelEntity(deviceModelSaveReq, headerInfo)))
                                .doOnSuccess(hdsDeviceModelEntity ->
                                        deviceModelSaveReq.setId(hdsDeviceModelEntity.getId())
//                                        handleSuccess(OperationTypeEnum.ADD,
//                                                deviceModelSaveReq,
//                                                null,
//                                                hdsDeviceModelEntity,
//                                                null,
//                                                headerInfo)
                                )
                                .doOnError(error ->
                                        handleError(OperationTypeEnum.ADD,
                                                error,
                                                deviceModelSaveReq,
                                                null, null, headerInfo))
                                .then(Mono.just(true))
                                .defaultIfEmpty(false);
                    }
                });
    }

    /**
     * 构建设备型号实体
     */
    private HdsDeviceModelEntity buildHdsDeviceModelEntity(DeviceModelSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        HdsDeviceModelEntity deviceModelEntity = new HdsDeviceModelEntity();
        BeanUtils.copyProperties(req, deviceModelEntity);
        deviceModelEntity.setModelCode(req.getModelCode().trim());
        deviceModelEntity.setStatus(StatusEnum.VALID.getCode());
        deviceModelEntity.setCreatedBy(headerInfo.getUserId());
        deviceModelEntity.setCreatedByName(headerInfo.getUsername());
        deviceModelEntity.setCreatedAt(LocalDateTime.now());
        deviceModelEntity.setRowStatus(1);
        return deviceModelEntity;
    }

    /**
     * 记录成功日志
     *
     * @param operationTypeEnum
     * @param req
     * @param beforeEntity
     * @param afterEntity
     * @param executionTime
     * @param headerInfo
     */
    private void handleSuccess(OperationTypeEnum operationTypeEnum,
                               Object req,
                               Object beforeEntity,
                               Object afterEntity,
                               Long executionTime,
                               HeaderUtils.HeaderInfo headerInfo) {
//        operationLogUtil.recordSuccess(
//                BussinessTypeEnum.DEVICE_MODEL,
//                operationTypeEnum,
//                req,
//                beforeEntity,
//                afterEntity,
//                executionTime,
//                headerInfo
//        ).subscribe();
    }

    /**
     * 记录错误日志
     *
     * @param operationTypeEnum
     * @param error
     * @param req
     * @param beforeEntity
     * @param headerInfo
     */
    private void handleError(OperationTypeEnum operationTypeEnum,
                             Throwable error,
                             Object req,
                             Object beforeEntity,
                             Long executionTime,
                             HeaderUtils.HeaderInfo headerInfo) {
//        operationLogUtil.recordFail(
//                BussinessTypeEnum.DEVICE_MODEL,
//                operationTypeEnum,
//                req,
//                beforeEntity,
//                executionTime,
//                error.getMessage(),
//                headerInfo
//        ).subscribe();
    }

    /**
     * 删除设备型号
     */
    @OperationLog(businessType = BussinessTypeEnum.DEVICE_MODEL,
            operationType = OperationTypeEnum.DELETE)
    public Mono<Boolean> delete(DeviceModelDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(userInfo -> executeTransferWithinTransactionDelete(userInfo, req.getId())
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device model delete error"));
                        })
                );
    }

    /**
     * 事务删除
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(HeaderUtils.HeaderInfo headerInfo, Long id) {
        return deviceModelRepository.findById(id)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "设备类型不存在")))
                .flatMap(existDeviceModel -> {
                    // 查询是否有关联的设备
                    return deviceInfoRepository.findByModelId(id)
                            .collectList()
                            .flatMap(deviceInfos -> {
                                // 如果没有关联设备，直接删除设备型号
                                if (deviceInfos.isEmpty()) {
                                    return deviceModelRepository.deleteById(id)
                                            .then(Mono.just(true));
                                }

                                // 收集所有设备的SN和IMEI
                                Set<String> deviceSns = deviceInfos.stream()
                                        .map(HdsDeviceInfoEntity::getDeviceSn)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toSet());

                                Set<String> imeis = deviceInfos.stream()
                                        .map(HdsDeviceInfoEntity::getImei)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toSet());

                                // 如果设备SN和IMEI都为空，可以删除设备型号
                                if (deviceSns.isEmpty() && imeis.isEmpty()) {
                                    return deviceModelRepository.deleteById(id)
                                            .then(Mono.just(true));
                                }

                                return findDevices(deviceSns, imeis)
                                        .count()
                                        .flatMap(count -> {
                                            if (count > 0) {
                                                return Mono.error(new BusinessException(ResultCode.NOT_ACCEPTABLE,
                                                        "该设备型号下有" + count + "个设备已激活或使用中，无法删除"));
                                            } else {
                                                return deviceModelRepository.deleteById(id)
                                                        .doOnSuccess(v -> log.info("设备型号删除成功: id={}, username={}",
                                                                id, headerInfo.getUsername()))
                                                        .then(Mono.just(true));
                                            }
                                        });
                            });
                })
                .defaultIfEmpty(false);
    }

    private Flux<HdsDeviceEntity> findDevices(Collection<String> deviceSns, Collection<String> imeis) {
        boolean hasSns = CollectionUtils.isNotEmpty(deviceSns);
        boolean hasImeis = CollectionUtils.isNotEmpty(imeis);

        if (hasSns && hasImeis) {
            return deviceRepository.findByDeviceSnInOrImeiIn(deviceSns, imeis);
        } else if (hasSns) {
            return deviceRepository.findByDeviceSnIn(deviceSns);
        } else if (hasImeis) {
            return deviceRepository.findByImeiIn(imeis);
        } else {
            return Flux.empty();
        }
    }

    /**
     * 更新设备型号
     */
    @OperationLog(businessType = BussinessTypeEnum.DEVICE_MODEL,
            operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> update(DeviceModelSaveReq deviceModelSaveReq) {
        ValidatorUtils.validateDeviceModelSaveReq(deviceModelSaveReq, true);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionUpdate(deviceModelSaveReq, headerInfo)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            log.error("设备型号更新事务执行失败: {}", ex.getMessage(), ex);
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device model update error"));
                        })
                );
    }

    /**
     * 事务更新
     */
    private Mono<Boolean> executeTransferWithinTransactionUpdate(DeviceModelSaveReq deviceModelSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        return deviceModelRepository.findById(deviceModelSaveReq.getId())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "device model not found")))
                .flatMap(existingDeviceModel ->{
                    updateHdsDeviceModelEntity(existingDeviceModel, deviceModelSaveReq, headerInfo);
                    return transactionalOperator.transactional(deviceModelRepository.save(existingDeviceModel));
                })
                .doOnSuccess(v -> log.info("device model update success: id={}, username={}", deviceModelSaveReq.getId(), headerInfo.getUsername()))
                .map(v -> true)
                .defaultIfEmpty(false);
    }

    /**
     * 更新设备型号实体
     */
    private void updateHdsDeviceModelEntity(HdsDeviceModelEntity existingDeviceModel, DeviceModelSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        existingDeviceModel.setModelName(req.getModelName());
        existingDeviceModel.setModelCode(req.getModelCode());
        existingDeviceModel.setManufacturer(req.getManufacturer());
        existingDeviceModel.setCompanyName(req.getCompanyName());
        existingDeviceModel.setManufacturerContactPerson(req.getManufacturerContactPerson());
        existingDeviceModel.setManufacturerContact(req.getManufacturerContact());
        existingDeviceModel.setWarrantyMonths(req.getWarrantyMonths());
        existingDeviceModel.setBrandName(req.getBrandName());
        existingDeviceModel.setManufacturerServiceProvider(req.getManufacturerServiceProvider());
        existingDeviceModel.setWarrantyInfo(req.getWarrantyInfo());
        existingDeviceModel.setRemark(req.getRemark());

        existingDeviceModel.setUpdatedBy(UserUtils.getUserId(headerInfo));
        existingDeviceModel.setUpdatedByName(UserUtils.getUserName(headerInfo));
        existingDeviceModel.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * 统计设备类型数量
     *
     * @param deviceModelSearchReq
     * @return
     */
    public Mono<Long> searchCount(DeviceModelSearchReq deviceModelSearchReq) {
        Criteria baseCriteria = buildBaseCriteria(deviceModelSearchReq);
        Query query = Query.query(baseCriteria);
        return r2dbcEntityTemplate.count(query, HdsDeviceModelEntity.class);
    }

    public Mono<List<DeviceModelSearchVO>> search(DeviceModelSearchReq deviceModelSearchReq) {
        Criteria criteria = buildBaseCriteria(deviceModelSearchReq);
        Sort sort = Sort.by(Sort.Direction.DESC, HdsDeviceModelFieldEnum.created_at.name());
        Query query = Query.query(criteria)
                .sort(sort)
                .limit(deviceModelSearchReq.getPageSize())
                .offset((long) (deviceModelSearchReq.getCurrent() - 1) * deviceModelSearchReq.getPageSize());

        return r2dbcEntityTemplate.select(query, HdsDeviceModelEntity.class)
                .collectList()
                .flatMap(deviceModelEntities -> {
                    if (deviceModelEntities.isEmpty()) {
                        return Mono.just(Collections.emptyList());
                    }

                    List<Long> modelIds = deviceModelEntities.stream()
                            .map(HdsDeviceModelEntity::getId)
                            .toList();

                    // 获取所有相关设备信息
                    Criteria deviceCriteria = Criteria.where(HdsDeviceFieldEnum.model_id.name()).in(modelIds);
                    return r2dbcEntityTemplate.select(Query.query(deviceCriteria), HdsDeviceEntity.class)
                            .collectList()
                            .map(devices -> {
                                // 根据modelId对设备进行分组
                                Map<Long, List<HdsDeviceEntity>> deviceMap = devices.stream()
                                        .collect(Collectors.groupingBy(HdsDeviceEntity::getModelId));

                                // 将设备模型转换为VO对象并设置统计信息
                                return deviceModelEntities.stream()
                                        .map(model -> {
                                            DeviceModelSearchVO vo = DeviceModelSearchVO.toVo(model);

                                            // 获取该模型下的所有设备
                                            List<HdsDeviceEntity> modelDevices = deviceMap.getOrDefault(model.getId(), List.of());

                                            // 计算使用中的设备数量
                                            long inUsedDeviceCount = modelDevices.stream()
                                                    .filter(device -> Objects.nonNull(device.getDeviceStatus())
                                                            && DeviceStatusEnum.PENDING_ACTIVATION.getCode() != device.getDeviceStatus())
                                                    .count();

                                            // 计算使用中的酒店数量
                                            long inUsedHotelCount = modelDevices.stream()
                                                    .map(HdsDeviceEntity::getHotelCode)
                                                    .filter(StringUtils::isNotBlank)
                                                    .distinct()
                                                    .count();

                                            // 设置统计值
                                            vo.setInUsedDeviceCount((int) inUsedDeviceCount);
                                            vo.setInUsedHotelCount((int) inUsedHotelCount);
                                            return vo;
                                        })
                                        .collect(Collectors.toList());
                            });
                });
    }

    private Criteria buildBaseCriteria(DeviceModelSearchReq deviceModelSearchReq) {
        Criteria criteria = Criteria.where(HdsDeviceModelFieldEnum.status.name()).is(StatusEnum.VALID.getCode());
        if (Objects.nonNull(deviceModelSearchReq.getId())) {
            criteria = criteria.and(HdsDeviceModelFieldEnum.id.name()).is(deviceModelSearchReq.getId());
        }
        if (StringUtils.isNotBlank(deviceModelSearchReq.getModelCode())) {
            criteria = criteria.and(HdsDeviceModelFieldEnum.model_code.name()).like("%" + deviceModelSearchReq.getModelCode() + "%");
        }
        if (StringUtils.isNotBlank(deviceModelSearchReq.getModelName())) {
            criteria = criteria.and(HdsDeviceModelFieldEnum.model_name.name()).like("%" + deviceModelSearchReq.getModelName() + "%");
        }
        return criteria;
    }

    /**
     * 查询设备型号列表
     */
    public Mono<List<DeviceModelSearchVO>> queryList() {
        Criteria criteria = Criteria.where(HdsDeviceModelFieldEnum.status.name()).is(StatusEnum.VALID.getCode());
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.select(query, HdsDeviceModelEntity.class)
                .collectList()
                .map(deviceModelEntities -> deviceModelEntities.stream()
                        .map(DeviceModelSearchVO::toVo)
                        .toList());
    }

    /**
     * 根据ID查询设备型号
     */
    public Mono<DeviceModelVO> findById(Long id) {
        Preconditions.checkArgument(Objects.nonNull(id), "id must not be null");
        return deviceModelRepository.findById(id)
                .map(DeviceModelVO::toVo)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "device model not found")));
    }
}