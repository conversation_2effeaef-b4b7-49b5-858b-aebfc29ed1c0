package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/4/8 11:23
 * @Description：DeviceEventTypeEnum
 */
@Getter
@AllArgsConstructor
public enum DeviceEventTypeEnum {

    /**
     * 设备入库事件
     */
    INBOUND("inbound", "入库"),

    /**
     * 设备激活事件
     */
    ACTIVATION("activation", "激活"),

    /**
     * 设备解绑事件
     */
    UNBIND("unbind", "解绑"),

    /**
     * 设备换绑事件
     */
    REBINDING("rebinding", "换绑"),

    /**
     * 设备停用事件
     */
    DEACTIVATION("deactivation", "停用"),

    /**
     * 设备启用事件
     */
    ENABLING("enabling", "启用"),

    /**
     * 设备维修事件
     */
    MAINTENANCE("maintenance", "维修"),

    /**
     * 设备升级事件
     */
    UPGRADE("upgrade", "升级"),

    /**
     * 设备报废事件
     */
    SCRAPPING("scrapping", "报废"),

    /**
     * 设备转移事件
     */
    TRANSFER("transfer", "转移"),

    /**
     * 设备借用事件
     */
    BORROWING("borrowing", "借用"),

    /**
     * 设备归还事件
     */
    RETURNING("returning", "归还");

    /**
     * 事件类型编码
     */
    private final String code;

    /**
     * 事件类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举实例
     *
     * @param code 事件类型编码
     * @return 对应的枚举实例，若未找到则返回null
     */
    public static DeviceEventTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }

        for (DeviceEventTypeEnum type : DeviceEventTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    /**
     * 判断是否为状态变更类事件
     *
     * @return 如果是激活、停用、启用等状态变更事件返回true，否则返回false
     */
    public boolean isStatusChangeEvent() {
        return this == ACTIVATION || this == DEACTIVATION || this == ENABLING;
    }

    /**
     * 判断是否为资产变动类事件
     *
     * @return 如果是入库、报废、转移等资产变动事件返回true，否则返回false
     */
    public boolean isAssetChangeEvent() {
        return this == INBOUND || this == SCRAPPING || this == TRANSFER;
    }
}
