package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/16 16:53
 * @Description：DevicePositionTreeVO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DevicePositionTreeVO {

    private List<BlockNode> blocks;

    /**
     * 楼栋节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BlockNode {

        /**
         * key
         */
        private String key;

        /**
         * 名称
         */
        private String name;

        /**
         * 区域子集
         */
        private List<AreaNode> children;
    }

    /**
     * 区域/楼层节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AreaNode {
        /**
         * id
         */
        private String key;

        /**
         * 名称
         */
        private String name;

        /**
         * 位置子集
         */
        private List<PositionNode> children;
    }

    /**
     * 位置节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PositionNode {

        /**
         * id
         */
        private String key;

        /**
         * 位置
         */
        private String name;

        /**
         * 是否为叶子节点
         */
        private boolean isLeaf;
    }
}
