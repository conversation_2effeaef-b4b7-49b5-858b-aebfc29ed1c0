package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.HdsInvoiceInfoEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-24 11:53:30
 * @Description: 发票信息搜索响应
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceInfoSearchVO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 门店编码
     */
    private String hotelCode;

    /**
     * 发票抬头
     */
    private String invoiceHeader;

    /**
     * 是否默认：1-是，0-否
     */
    private Integer isDefault;

    /**
     * 纳税人识别号
     */
    private String taxNumber;

    /**
     * 接收邮箱
     */
    private String receiveEmail;

    /**
     * 发票内容
     */
    private String invoiceContent;

    /**
     * 更多信息/备注
     */
    private String moreInfo;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 将实体转换为VO
     * @param entity 实体
     * @return VO
     */
    public static HdsInvoiceInfoSearchVO toVo(HdsInvoiceInfoEntity entity) {
        HdsInvoiceInfoSearchVO vo = new HdsInvoiceInfoSearchVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setCreateTime(SimpleDateUtils.formatLocalDateTimeToDateHour(entity.getCreatedAt()));
        vo.setUpdateTime(SimpleDateUtils.formatLocalDateTimeToDateHour(entity.getUpdatedAt()));
        return vo;
    }
} 