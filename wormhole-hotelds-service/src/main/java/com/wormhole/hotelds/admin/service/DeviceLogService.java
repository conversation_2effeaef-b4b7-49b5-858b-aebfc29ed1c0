package com.wormhole.hotelds.admin.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.DeviceEventTypeEnum;
import com.wormhole.hotelds.admin.model.req.DeviceLogQueryReq;
import com.wormhole.hotelds.admin.model.vo.DeviceLogVO;
import com.wormhole.hotelds.admin.model.vo.HotelVO;
import com.wormhole.hotelds.admin.repository.DeviceLogRepository;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceLogEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceLogFieldEnum;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoEntity;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/4/10 11:31
 * @Description：DeviceLogService
 */
@Service
public class DeviceLogService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private DeviceLogRepository deviceLogRepository;

    /**
     * 获取门店列表
     *
     * @return
     */
    public Mono<List<DeviceLogVO>> queryList(DeviceLogQueryReq deviceLogQueryReq) {
        return r2dbcEntityTemplate.select(HdsDeviceLogEntity.class)
                .matching(buildQuery(deviceLogQueryReq))
                .all()
                .map(DeviceLogVO::toVo)
                .collectList();
    }

    private Query buildQuery(DeviceLogQueryReq deviceLogQueryReq) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(deviceLogQueryReq.getDeviceCode())) {
            criteria = criteria.and(HdsDeviceLogFieldEnum.device_id.name()).is(deviceLogQueryReq.getDeviceCode());
        }
        Sort sort = Sort.by(Sort.Direction.DESC, HdsDeviceLogFieldEnum.created_at.name());
        return Query.query(criteria)
                .sort(sort)
                .limit(5);
    }

    Mono<List<HdsDeviceLogEntity>> saveAll(List<HdsDeviceLogEntity> deviceLogs){
        return deviceLogRepository.saveAll(deviceLogs)
                .collectList()
                .defaultIfEmpty(Collections.emptyList());
    }

    public HdsDeviceLogEntity createDeviceLog(
            HdsDeviceEntity device,
            Map<String, HdsHotelInfoEntity> hotelMap,
            DeviceEventTypeEnum eventType,
            String operationDesc,
            HeaderUtils.HeaderInfo headerInfo) {

        HdsHotelInfoEntity hotel = hotelMap.get(device.getHotelCode());
        HdsDeviceLogEntity log = new HdsDeviceLogEntity();
        log.setDeviceId(device.getDeviceId());
        log.setDeviceSn(device.getDeviceSn());
        log.setEventType(eventType.getCode());
        log.setEventTime(LocalDateTime.now());
        log.setCreatedAt(LocalDateTime.now());
        log.setCreatedBy(headerInfo.getUserId());
        log.setCreatedByName(headerInfo.getUsername());
        log.setRemark(String.format("设备[%s]状态变更为%s，所属酒店[%s]",
                device.getDeviceSn(),
                operationDesc,
                hotel != null ? hotel.getHotelName() : "未知"));
        return log;
    }
}
