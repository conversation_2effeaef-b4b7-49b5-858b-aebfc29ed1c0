package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.HdsInvitationCodeEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/5/19 10:19
 * @Description：InvitationCodeSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InvitationCodeSearchVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 邀请码名称
     */
    private String name;

    /**
     * 状态: 1-启用, 0-关闭
     */
    private Integer status;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 邀请门店数
     */
    private Integer invitationCount;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 创建时间
     */
    private String createTime;
}
