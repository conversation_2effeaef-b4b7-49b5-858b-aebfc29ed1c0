package com.wormhole.hotelds.admin.service.strategy;

import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.GetAuthInfoReq;
import com.wormhole.hotelds.admin.model.vo.AuthVO;
import reactor.core.publisher.Mono;

/**
 * @author: huang<PERSON><PERSON><PERSON>
 * @date: 2025/5/15
 * @Description: 认证来源策略接口
 */
public interface AuthSourceStrategy<T extends AuthVO> {
    /**
     * 处理认证信息
     * @param authVO 基础认证信息
     * @param headerInfo 请求头信息
     * @return 处理后的认证信息
     */
    Mono<T> processAuthInfo(GetAuthInfoReq getAuthInfoReq,AuthVO authVO, HeaderUtils.HeaderInfo headerInfo);

    /**
     * 获取策略支持的来源
     * @return 来源代码
     */
    String getSourceCode();
}