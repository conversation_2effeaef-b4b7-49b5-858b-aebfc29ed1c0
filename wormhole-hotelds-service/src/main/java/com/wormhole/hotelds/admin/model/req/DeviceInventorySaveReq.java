package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

import java.io.*;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:57:41
 * @Description: DeviceInventory
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceInventorySaveReq implements Serializable {
    
    private static final long serialVersionUID = 1L;

}