package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/16 17:13
 * @Description：HotelPackageSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelPackageSaveReq {

    private Integer id;

    /**
     * 门店编码
     */
    private String hotelCode;

    /**
     * 标准套餐编码
     */
    private String packageCode;

    /**
     * 个性化定价列表
     */
    private List<HotelProductCustomPricingReq> customPricing;

    /**
     * 调价原因
     */
    private String adjustPriceReason;

    /**
     * 门店产品个性化定价请求
     */
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelProductCustomPricingReq {

        /**
         * 价格id
         */
        private Integer id;

        /**
         * 产品ID
         */
        private Integer productId;

        /**
         * 周期类型：1-月度，2-季度，3-年度
         */
        private Integer periodType;

        /**
         * 自定义门市价
         */
        private String customMarketPrice;

        /**
         * 自定义优惠价（一口价模式）
         */
        private String customDiscountPrice;

        /**
         * 自定义折扣比例（折扣模式，0.01-1.00）
         */
        private String customDiscountRate;
    }
}
