package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

/**
 * @Description:
 * @Author: wj
 * @Date: 2025/3/27 14:19
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AreaVo {

    /**
     * 区域类型(1 省份/洲 2 城市 3 区县级市)
     */
    private Integer areaType;


    /**
     * 地区唯一编号
     */
    private String areaCode;


    /**
     * 父节点
     */
    private String parentCode;


    /**
     * 地区名称中文全拼
     */
    private String areaPinyin;


    /**
     * 拼音首字母
     */
    private String firstLetter;


    /**
     * 邮政编码
     */
    private String postCode;


    /**
     * 区域中文名称
     */
    private String areaName;


    /**
     * 区域英文名称
     */
    private String areaNameEn;
}
