package com.wormhole.hotelds.admin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.admin.model.req.HdsHotelMappingSaveReq;
import com.wormhole.hotelds.core.model.entity.HdsHotelMappingEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsHotelMappingDTO implements Serializable {
    private Long id;
    private String hotelCode;
    private String hotelName;
    private String externalId;
    private String externalName;
    private String channel;
    private String platform;

    public static HdsHotelMappingDTO toDTO(HdsHotelMappingEntity hotelMappingEntity) {
        HdsHotelMappingDTO dto = new HdsHotelMappingDTO();
        dto.setHotelCode(hotelMappingEntity.getHotelCode());
        dto.setHotelName(hotelMappingEntity.getHotelName());
        dto.setExternalId(hotelMappingEntity.getExternalId());
        dto.setExternalName(hotelMappingEntity.getExternalName());
        dto.setChannel(hotelMappingEntity.getChannel());
        dto.setPlatform(hotelMappingEntity.getPlatform());
        dto.setId(hotelMappingEntity.getId());
        return dto;
    }
}
