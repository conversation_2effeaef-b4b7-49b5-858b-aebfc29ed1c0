package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/17 11:42
 * @Description：PaymentPackageDisplayVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PaymentPackageDisplayVO {

    private Integer id;

    private List<PeriodOptionVO> periodOptions;

    private List<PackageDisplayVO> packages;

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PeriodOptionVO{

        /**
         * 周期类型：1-月度，2-季度，3-年度
         */
        private Integer periodType;

        /**
         * 节省百分比
         */
        private String savingsPercentage;

        /**
         * 节省金额
         */
        private String savingsAmount;
    }

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PackageDisplayVO{

        /**
         * 主套餐id
         */
        private String packageCode;

        /**
         * 门店套餐code
         */
        private String hotelPackageCode;

        /**
         * 产品id
         */
        private Integer productId;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * 是否推荐
         */
        private Integer isRecommend;

        /**
         * 月度价格
         */
        private String monthPrice;

        /**
         * 门市价
         */
        private String marketPrice;

        /**
         * 优惠价
         */
        private String discountPrice;

        /**
         * 产品能力
         */
        private List<String> productCapabilities;
    }

}
