package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/5/20 11:48
 * @Description：HdsEmployeeRegisterReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsEmployeeRegisterReq {

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邀请码
     */
    private String invitationCode;
}
