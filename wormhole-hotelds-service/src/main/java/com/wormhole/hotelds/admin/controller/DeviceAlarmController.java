package com.wormhole.hotelds.admin.controller;

import com.wormhole.hotelds.admin.model.vo.DeviceAlarmVO;
import com.wormhole.hotelds.admin.model.req.DeviceAlarmSaveReq;
import com.wormhole.hotelds.admin.model.req.DeviceAlarmDeleteReq;
import com.wormhole.hotelds.admin.model.req.DeviceAlarmSearchReq;
import com.wormhole.hotelds.admin.service.DeviceAlarmService;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:58:23
 * @Description: DeviceAlarm
 */
@RestController
@RequestMapping("/devicealarm")
public class DeviceAlarmController {

    @Resource
    private DeviceAlarmService service;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody DeviceAlarmSaveReq deviceAlarmSaveReq) {
        return service.create(deviceAlarmSaveReq).flatMap(Result::success);
    }

    /**
     * 更新记录
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody DeviceAlarmSaveReq deviceAlarmSaveReq) {
        return service.update(deviceAlarmSaveReq).flatMap(Result::success);
    }

    /**
     * 删除记录
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody DeviceAlarmDeleteReq deviceAlarmDeleteReq) {
        return service.delete(deviceAlarmDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<List<DeviceAlarmVO>>> search(@RequestBody DeviceAlarmSearchReq deviceAlarmSearchReq) {
        return service.search(deviceAlarmSearchReq).flatMap(Result::success);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<DeviceAlarmVO>> findById(@PathVariable("id") Long id) {
        return service.findById(id).flatMap(Result::success);
    }
}