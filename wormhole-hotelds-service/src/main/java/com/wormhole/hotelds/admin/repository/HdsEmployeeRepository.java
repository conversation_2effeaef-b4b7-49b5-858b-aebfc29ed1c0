package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.core.model.entity.*;
import io.reactivex.Flowable;
import org.springframework.data.r2dbc.repository.*;
import org.springframework.data.repository.query.*;
import org.springframework.data.repository.reactive.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/3 09:13
 * @Description：员工接口
 */
@Repository
public interface HdsEmployeeRepository extends ReactiveCrudRepository<HdsEmployeeEntity, Integer> {

    Mono<Boolean> existsByUsername(@Param("username") String username);

    Mono<Boolean> existsByMobile(@Param("mobile") String mobile);

    Mono<Boolean> existsByEmail(@Param("email") String email);

    @Query("""
                SELECT count(1)
                FROM hds_employee e
                    LEFT JOIN hds_employee_hotel eh ON e.id = eh.employee_id
                    LEFT JOIN hds_hotel_info hi ON eh.hotel_code = hi.hotel_code
                WHERE ((:hotelCode IS NULL OR :hotelCode = '') OR eh.hotel_code = :hotelCode)
                  AND hi.row_status = 1
                  AND eh.row_status = 1
                  AND (:status IS NULL OR eh.status = :status)
                  AND (:roleCode IS NULL OR
                     (FIND_IN_SET(:roleCode, COALESCE(eh.role_code, '')) > 0))
                  AND (
                        :userKeyword IS NULL
                        OR e.name LIKE CONCAT('%', :userKeyword, '%')
                        OR e.mobile LIKE CONCAT('%', :userKeyword, '%')
                    )
                  AND (
                        :groupKeyword IS NULL
                        OR hi.hotel_name LIKE CONCAT('%', :groupKeyword, '%')
                        OR hi.hotel_code LIKE CONCAT('%', :groupKeyword, '%')
                        OR hi.merchant_name LIKE CONCAT('%', :groupKeyword, '%')
                        OR hi.brand_name LIKE CONCAT('%', :groupKeyword, '%')
                    )
                  AND (e.type = :type)
                  AND (
                          :isOtaAdminOnly = 0
                          OR (
                              FIND_IN_SET('OTA_AGENT_ADMIN', COALESCE(eh.role_code, '')) > 0
                              OR FIND_IN_SET('OTA_AGENT_STAFF', COALESCE(eh.role_code, '')) > 0
                          )
                      )
                ;
            """)
    Mono<Long> count(@Param("hotelCode") String hotelCode,
                     @Param("status") Integer status,
                     @Param("roleCode") String roleCode,
                     @Param("userKeyword") String userKeyword,
                     @Param("groupKeyword") String groupKeyword,
                     @Param("type") Integer type,
                     @Param("isOtaAdminOnly") int isOtaAdminOnly
    );

    @Query("""
                SELECT e.id,
                       e.username,
                       e.name,
                       e.mobile,
                       e.email,
                       e.gender,
                       CASE WHEN :type = 1 THEN e.status
                           WHEN :type = 3 THEN eh.status
                           ELSE e.status
                           END AS status,
                       e.type,
                       e.created_at,
                       e.created_by,
                       eh.hotel_code,
                       eh.status,
                       eh.role_code,
                       hi.hotel_name,
                       hi.merchant_name
                FROM hds_employee e
                    INNER JOIN hds_employee_hotel eh ON e.id = eh.employee_id
                    LEFT JOIN hds_hotel_info hi ON eh.hotel_code = hi.hotel_code
                WHERE ((:hotelCode IS NULL OR :hotelCode = '') OR eh.hotel_code = :hotelCode) 
                  AND hi.row_status = 1
                  AND eh.row_status = 1
                  AND (
                      :status IS NULL OR
                      (:type = 1 AND e.status = :status) OR
                      (:type = 3 AND eh.status = :status) OR
                      (:type NOT IN (1, 3) AND e.status = :status) -- 默认使用e.status
                  )
                  AND (:roleCode IS NULL OR
                     (FIND_IN_SET(:roleCode, COALESCE(eh.role_code, '')) > 0))
                  AND (
                        :userKeyword IS NULL
                        OR e.name LIKE CONCAT('%', :userKeyword, '%')
                        OR e.mobile LIKE CONCAT('%', :userKeyword, '%')
                    )
                  AND (
                        :groupKeyword IS NULL
                        OR hi.hotel_name LIKE CONCAT('%', :groupKeyword, '%')
                        OR hi.hotel_code LIKE CONCAT('%', :groupKeyword, '%')
                        OR hi.merchant_name LIKE CONCAT('%', :groupKeyword, '%')
                        OR hi.brand_name LIKE CONCAT('%', :groupKeyword, '%')
                    )
                  AND (e.type = :type)
                AND (
                      :isOtaAdminOnly = 0
                      OR (
                          FIND_IN_SET('OTA_AGENT_ADMIN', COALESCE(eh.role_code, '')) > 0
                          OR FIND_IN_SET('OTA_AGENT_STAFF', COALESCE(eh.role_code, '')) > 0
                      )
                  )
                ORDER BY e.created_at DESC
                LIMIT :limit OFFSET :offset;
            """)
    Flux<HdsEmployeeVO> list(@Param("hotelCode") String hotelCode,
                             @Param("status") Integer status,
                             @Param("roleCode") String roleCode,
                             @Param("userKeyword") String userKeyword,
                             @Param("groupKeyword") String groupKeyword,
                             @Param("type") Integer type,
                             @Param("isOtaAdminOnly") int isOtaAdminOnly,
                             @Param("limit") int limit,
                             @Param("offset") long offset);

    @Query("""
                SELECT e.id,
                       e.username,
                       e.name,
                       e.mobile,
                       e.email,
                       e.gender,
                       CASE WHEN :type = 1 THEN e.status
                           WHEN :type = 3 THEN eh.status
                           ELSE e.status
                           END AS status,
                       e.type,
                       e.created_at,
                       e.created_by,
                       eh.hotel_code,
                       eh.status,
                       eh.role_code,
                       hi.hotel_name,
                       hi.merchant_name
                FROM hds_employee e
                    INNER JOIN hds_employee_hotel eh ON e.id = eh.employee_id
                    LEFT JOIN hds_hotel_info hi ON eh.hotel_code = hi.hotel_code
                WHERE ((:hotelCode IS NULL OR :hotelCode = '') OR eh.hotel_code = :hotelCode) 
                  AND hi.row_status = 1
                  AND eh.row_status = 1
                  AND (
                      :status IS NULL OR
                      (:type = 1 AND e.status = :status) OR
                      (:type = 3 AND eh.status = :status) OR
                      (:type NOT IN (1, 3) AND e.status = :status) -- 默认使用e.status
                  )
                  AND (:roleCode IS NULL OR
                     (FIND_IN_SET(:roleCode, COALESCE(eh.role_code, '')) > 0))
                  AND (
                        :userKeyword IS NULL
                        OR e.name LIKE CONCAT('%', :userKeyword, '%')
                        OR e.mobile LIKE CONCAT('%', :userKeyword, '%')
                    )
                  AND (
                        :groupKeyword IS NULL
                        OR hi.hotel_name LIKE CONCAT('%', :groupKeyword, '%')
                        OR hi.hotel_code LIKE CONCAT('%', :groupKeyword, '%')
                        OR hi.merchant_name LIKE CONCAT('%', :groupKeyword, '%')
                        OR hi.brand_name LIKE CONCAT('%', :groupKeyword, '%')
                    )
                  AND (e.type = :type)
                AND (
                      :isOtaAdminOnly = 0
                      OR (
                          FIND_IN_SET('OTA_AGENT_ADMIN', COALESCE(eh.role_code, '')) > 0
                          OR FIND_IN_SET('OTA_AGENT_STAFF', COALESCE(eh.role_code, '')) > 0
                      )
                  )
                ORDER BY e.created_at DESC
            """)
    Flux<HdsEmployeeVO> exportList(@Param("hotelCode") String hotelCode,
                                   @Param("status") Integer status,
                                   @Param("roleCode") String roleCode,
                                   @Param("userKeyword") String userKeyword,
                                   @Param("groupKeyword") String groupKeyword,
                                   @Param("type") Integer type,
                                   @Param("isOtaAdminOnly") int isOtaAdminOnly);


    @Modifying
    @Query("""
                UPDATE hds_employee 
                SET status = :status,
                    updated_by = :updatedBy,
                    updated_by_name = :updatedByName,
                    updated_at = NOW()
                WHERE id IN (:employeeIds)
            """)
    Mono<Integer> updateEmployeeStatus(@Param("employeeIds") List<Integer> employeeIds,
                                       @Param("status") Integer status,
                                       @Param("updatedBy") String updatedBy,
                                       @Param("updatedByName") String updatedByName);

    @Modifying
    @Query("""
                UPDATE hds_employee 
                SET row_status = :rowStatus,
                    updated_by = :updatedBy,
                    updated_by_name = :updatedByName,
                    updated_at = NOW()
                WHERE id = employeeId
            """)
    Mono<Integer> updateEmployeeRowStatus(@Param("employeeIds") List<Integer> employeeIds,
                                       @Param("rowStatus") Integer rowStatus,
                                       @Param("updatedBy") String updatedBy,
                                       @Param("updatedByName") String updatedByName);

    Flux<HdsEmployeeEntity> findByIdIn(Collection<Integer> ids);

    Mono<HdsEmployeeEntity> findByMobile(String mobile);

    Mono<HdsEmployeeEntity> findByEmail(String email);
}
