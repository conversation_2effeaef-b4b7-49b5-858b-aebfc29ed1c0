package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/3/26 11:52
 * @Description：BrandBaseVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BrandVO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 品牌编号
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌logo图片url
     */
    private String brandLogo;

    /**
     * 品牌描述简介
     */
    private String brandDesc;

    /**
     * 公司编码
     */
    private String merchantId;

    /**
     * 公司名称
     */
    private String merchantName;

    /**
     * 星级类型：0经济型 1舒适型 2品质型 3高档型 4豪华型
     */
    private Integer starType;

    /**
     * 状态 1有效 0无效
     */
    private Integer status;

    /**
     * 创建人id
     */
    private String createdBy;

    /**
     * 创建人名称
     */
    private String createdByName;

    /**
     * 修改人id
     */
    private String updatedBy;

    /**
     * 修改人名称
     */
    private String updatedByName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 转换为视图对象
     */
    public static BrandVO toVo(HdsBrandEntity entity) {
        BrandVO brandVO = new BrandVO();
        BeanUtils.copyProperties(entity, brandVO);
        return brandVO;
    }
}
