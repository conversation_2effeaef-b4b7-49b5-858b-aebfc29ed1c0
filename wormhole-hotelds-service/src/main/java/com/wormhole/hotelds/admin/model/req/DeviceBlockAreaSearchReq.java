package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/4/23 11:54
 * @Description：DeviceBlockAreaSearchReq
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceBlockAreaSearchReq {

    private String hotelCode;

    private String deviceAppType;

    private String block;

    private String area;
}
