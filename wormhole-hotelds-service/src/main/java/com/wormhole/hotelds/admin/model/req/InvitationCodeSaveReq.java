package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/5/19 09:38
 * @Description：InvitationCodeSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InvitationCodeSaveReq {

    /**
     * 主键ID（更新时必填）
     */
    private Integer id;

    /**
     * 邀请码名称
     */
    private String name;
}
