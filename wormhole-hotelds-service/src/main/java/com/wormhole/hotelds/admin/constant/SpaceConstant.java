package com.wormhole.hotelds.admin.constant;

import com.wormhole.hotelds.constant.SpaceTypeEnum;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
public interface SpaceConstant {

    String PRIVATE_SPACE_CODE = SpaceTypeEnum.PERSONAL.name();

    String PERSIONAL_SPACE_NAME = "个人空间";

    String DEFAULT_IMAGE_STR = "{\"md5\": \"A4D1088D4CE981EC8542B8771A597D6F\", \"uid\": \"20250210171257124dbdcbd6b7\", \"url\": \"https://bdw-test.oss-cn-shenzhen.aliyuncs.com/wormhole/img/20250210171257d6fca281e8a7.jpeg?Expires=2054538777&OSSAccessKeyId=LTAI5tLjDNipNDcoyrHzVTkq&Signature=XVRZYKlSIp34vor4JlZq2tAdIy4%3D\", \"name\": \"delonix.jpeg\", \"size\": 152289, \"status\": null, \"object_key\": \"wormhole/img/20250210171257d6fca281e8a7.jpeg\", \"intranet_url\": \"https://bdw-test.oss-cn-shenzhen.aliyuncs.com/wormhole/img/20250210171257d6fca281e8a7.jpeg?Expires=2054538777&OSSAccessKeyId=LTAI5tLjDNipNDcoyrHzVTkq&Signature=XVRZYKlSIp34vor4JlZq2tAdIy4%3D\"}";

    String SPACE_DEFAULT_DESC = "用户个人空间";



}
