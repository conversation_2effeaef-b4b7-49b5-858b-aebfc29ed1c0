package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Author：flx
 * @Date：2025/5/7 18:01
 * @Description：PlatformEnum
 */
@Getter
@AllArgsConstructor
public enum PlatformEnum {

    ANDROID("android", "安卓"),
    IOS("ios", "iOS");

    private final String code;
    private final String desc;

    public static boolean isValid(String platform) {
        return Arrays.stream(values())
                .anyMatch(e -> e.getCode().equalsIgnoreCase(platform));
    }
}
