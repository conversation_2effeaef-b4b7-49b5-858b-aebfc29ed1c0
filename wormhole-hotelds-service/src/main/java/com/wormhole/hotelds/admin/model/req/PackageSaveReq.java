package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/16 13:47
 * @Description：PackageCreateReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PackageSaveReq {

    /**
     * 套餐编号id
     */
    private Integer id;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 套餐code
     */
    private String packageCode;

    /**
     * 付费方式：0-按房间数量收费，1-按门店收费
     */
    private Integer paymentMode;

    /**
     * 优惠方式：0-折扣，1-一口价
     */
    private Integer discountMode;

    /**
     * 产品列表（最多5个产品）
     */
    private List<ProductSaveReq> products;

    /**
     * 产品创建请求
     */
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ProductSaveReq {

        private Integer id;

        /**
         * 产品名称（不超过20字符）
         */
        private String productName;

        /**
         * 产品描述（最多10条，每条不超过50字符）
         */
        private List<String> productDescription;

        /**
         * 是否推荐 1是 0否
         */
        private Integer isRecommend;

        /**
         * 定价信息
         */
        private List<PricingSaveReq> pricing;
    }

    /**
     * 定价创建请求
     */
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PricingSaveReq {

        private Integer id;

        /**
         * 周期类型：1-月度，2-季度，3-年度
         */
        private Integer periodType;

        /**
         * 门市价
         */
        private String marketPrice;

        /**
         * 优惠价（一口价模式使用）
         */
        private String discountPrice;

        /**
         * 折扣比例（折扣模式使用，0.01-1.00）
         */
        private String discountRate;
    }
}
