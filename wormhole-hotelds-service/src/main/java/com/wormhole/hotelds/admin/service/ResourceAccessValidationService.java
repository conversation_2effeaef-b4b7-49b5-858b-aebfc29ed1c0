package com.wormhole.hotelds.admin.service;

import com.wormhole.hotelds.admin.model.enums.EmployeeTypeEnum;
import com.wormhole.hotelds.admin.repository.*;
import com.wormhole.hotelds.api.hotel.enums.*;
import lombok.extern.slf4j.*;
import org.apache.commons.lang3.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.util.*;

/**
 * 资源访问权限验证服务
 * 提供各种资源的访问权限验证功能
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ResourceAccessValidationService {

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;
    
    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;
    
    @Resource
    private DeviceRepository deviceRepository;
    
    @Resource
    private HotelRepository hotelRepository;

    /**
     * 验证用户是否有权限访问指定设备
     *
     * @param employeeId 员工ID
     * @param deviceId   设备ID
     * @return 是否有权限访问
     */
    public Mono<Boolean> validateDeviceAccess(Integer employeeId, Long deviceId) {
        if (employeeId == null || deviceId == null) {
            return Mono.just(false);
        }

        // 先查询设备信息，获取设备所属的酒店code
        return deviceRepository.findById(deviceId)
                .flatMap(device -> {
                    String hotelCode = device.getHotelCode();
                    if (hotelCode == null) {
                        return Mono.just(true); // 如果设备没有关联酒店，暂时默认允许访问
                    }
                    
                    // 验证用户是否有权限访问该酒店
                    return validateHotelCodeAccess(employeeId, hotelCode);
                })
                .defaultIfEmpty(false); // 如果设备不存在，拒绝访问
    }

    /**
     * 验证用户是否有权限访问指定酒店ID
     *
     * @param employeeId 员工ID
     * @param hotelId    酒店ID
     * @return 是否有权限访问
     */
    public Mono<Boolean> validateHotelAccess(Integer employeeId, Integer hotelId) {
        if (employeeId == null || hotelId == null) {
            return Mono.just(false);
        }
        // 先查询酒店信息，获取酒店编码
        return hotelRepository.findById(hotelId)
                .flatMap(hotel -> {
                    String hotelCode = hotel.getHotelCode();
                    if (StringUtils.isBlank(hotelCode)) {
                        return Mono.just(false);
                    }
                    // 验证用户是否有权限访问该酒店
                    return validateHotelCodeAccess(employeeId, hotelCode);
                })
                .defaultIfEmpty(false);
    }

    /**
     * 验证用户是否有权限访问指定酒店编码
     *
     * @param employeeId 员工ID
     * @param hotelCode  酒店编码
     * @return 是否有权限访问
     */
    public Mono<Boolean> validateHotelCodeAccess(Integer employeeId, String hotelCode) {
        return hdsEmployeeRepository.findById(employeeId)
                .flatMap(employee -> {
                    if (employee == null) {
                        return Mono.just(false);
                    }
                    // 集团员工放开
                    if (Objects.equals(employee.getType(), EmployeeTypeEnum.GROUP.getCode())) {
                        return Mono.just(true);
                    }
                    return hdsEmployeeHotelService.existsByEmployeeIdAndHotelCode(employeeId, hotelCode);
                })
                .defaultIfEmpty(false)
                .doOnError(e -> log.error("验证酒店编码访问权限时发生错误: employeeId = {}, hotelCode = {}", employeeId, hotelCode, e));
    }
}