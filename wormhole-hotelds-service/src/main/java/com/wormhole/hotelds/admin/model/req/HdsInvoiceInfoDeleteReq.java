package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

import java.io.*;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:59:44
 * @Description: HdsInvoiceInfo
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceInfoDeleteReq implements Serializable {
    
    private static final long serialVersionUID = 1L;

    private Long id;

}