package com.wormhole.hotelds.admin.service;

import cn.hutool.core.date.*;
import com.wormhole.channel.consts.*;
import com.wormhole.channel.consts.enums.*;
import com.wormhole.channel.consts.message.*;
import com.wormhole.common.enums.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.mq.producer.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.*;
import org.springframework.cloud.context.config.annotation.*;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.util.List;

@Service
@Slf4j
@RefreshScope
public class HotelServiceExpiryHandler {

    @Value("${rocketmq.topic.channelSseEvent: channel_sse_event}")
    private String channelSseEventTopic;

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private ReactiveMessageSender reactiveMessageSender;

    /**
     * 发送酒店服务到期消息给酒店管理员
     *
     * @param hotelCode 酒店代码
     * @return 完成信号
     */
    public Mono<Void> sendHotelServiceExpiryMessage(String hotelCode, ChannelEventEnum eventEnum) {
        List<String> sourceList = List.of(SourcePlatform.HDS_MC.getCode(), SourcePlatform.CHROME_PLUGIN.getCode());
        return hdsEmployeeHotelService.getNotifyEmployees(hotelCode, sourceList)
                .flatMap(userIds -> {
                    // 构建消息体
                    HotelDisableMessage message = new HotelDisableMessage();
                    message.setEvent(eventEnum.name());
                    message.setUserIds(userIds);
                    message.setHotelCode(hotelCode);
                    message.setMessage(String.format("酒店服务已到期,酒店代码为:%s", hotelCode));

                    MessageBody messageBody = new MessageBody(SseActionEnum.HOTEL_UNAVAILABLE.name(), String.valueOf(SystemClock.now()), message);
                    log.info("发送酒店服务到期消息: userIds={}, message={}", userIds, JacksonUtils.writeValueAsString(messageBody));
                    return reactiveMessageSender.sendMessage(channelSseEventTopic, messageBody);
                })
                .then();
    }

}