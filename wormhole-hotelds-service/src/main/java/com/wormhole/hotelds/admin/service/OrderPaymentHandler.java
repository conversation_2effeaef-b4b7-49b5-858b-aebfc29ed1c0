package com.wormhole.hotelds.admin.service;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.hotelds.admin.model.enums.OrderStatusEnum;
import com.wormhole.hotelds.admin.model.resp.OrderStatusResp;
import com.wormhole.hotelds.admin.model.resp.YopNotifyResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.function.Function;

/**
 * @Author：flx
 * @Date：2025/6/24 10:39
 * @Description：订单支付处理器
 */
@Component
@Slf4j
public class OrderPaymentHandler {

    @Resource
    private OrderService orderService;

    @Resource
    HotelService hotelService;

    @Resource
    private BillService billService;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;

    private static final String LOCK_KEY_PREFIX = "payment:check:lock:";

    private static final Duration LOCK_EXPIRE_DURATION = Duration.ofSeconds(30);


    /**
     * 使用分布式锁执行操作
     *
     * @param orderNo   订单号
     * @param operation 要执行的操作
     * @return 操作结果
     */
    public <T> Mono<T> executeWithLock(String orderNo, Function<String, Mono<T>> operation) {
        String lockKey = LOCK_KEY_PREFIX + orderNo;

        return reactiveRedisTemplate.opsForValue()
                .setIfAbsent(lockKey, "1", LOCK_EXPIRE_DURATION)
                .flatMap(locked -> {
                    if (!locked) {
                        log.info("Order {} is being processed by another thread", orderNo);
                        return Mono.empty();
                    }

                    return operation.apply(orderNo)
                            .onErrorResume(e -> {
                                log.error("Error processing order {}: {}", orderNo, e.getMessage(), e);
                                return Mono.empty();
                            });
                })
                .doFinally(signalType ->
                        // 释放分布式锁
                        reactiveRedisTemplate.delete(lockKey)
                                .subscribe(deleted ->
                                        log.debug("Released lock for order: {}, deleted: {}", orderNo, deleted)
                                )
                );
    }

    /**
     * 异步处理支付结果
     */
    public void processPaymentResult(String orderNo, YopNotifyResp yopNotifyResp) {
        executeWithLock(orderNo, lockedOrderNo ->
                orderService.findByOrderNo(lockedOrderNo)
                        .switchIfEmpty(Mono.defer(() -> {
                            log.warn("Order not found for orderNo: {}", lockedOrderNo);
                            throw new RuntimeException("订单不存在");
                        }))
                        .flatMap(order -> {
                            // 如果订单已支付或已取消，不需要继续处理
                            if (!OrderStatusEnum.PENDING.getCode().equals(order.getOrderStatus())) {
                                log.info("Order {} is already in final status: {}",
                                        lockedOrderNo, OrderStatusEnum.getByCode(order.getOrderStatus()));
                                throw new RuntimeException("订单已支付或已取消，不需要继续处理");
                            }

                            OrderStatusResp orderStatusResp = new OrderStatusResp();
                            BeanUtils.copyProperties(yopNotifyResp, orderStatusResp);
                            // 处理支付成功逻辑
                            return handlePaymentSuccess(orderStatusResp);
                        })
        ).subscribe(
                result -> log.info("Payment notification processed successfully for order: {}", orderNo),
                error -> log.error("Error processing payment notification for order: {}", orderNo, error)
        );
    }

    /**
     * 处理支付成功的逻辑
     *
     * @param orderStatusResp 订单状态响应
     * @return 处理结果
     */
    public Mono<Void> handlePaymentSuccess(OrderStatusResp orderStatusResp) {
        if (orderStatusResp == null || orderStatusResp.getOrderId() == null) {
            log.error("Invalid order status response: {}", orderStatusResp);
            return Mono.error(new IllegalArgumentException("订单状态响应不能为空"));
        }

        String orderNo = orderStatusResp.getOrderId();
        log.info("Processing payment success for order: {}", orderNo);

        return orderService.findByOrderNo(orderNo)
                .switchIfEmpty(Mono.error(new RuntimeException("订单不存在: " + orderNo)))
                .flatMap(orderEntity -> {
                    // 使用事务包装所有数据库操作
                    return transactionalOperator.transactional(
                            Mono.defer(() -> {
                                // 1. 更新订单状态
                                Mono<Boolean> updateOrderMono = orderService.updateOrder(
                                        orderNo,
                                        OrderStatusEnum.PAID.getCode(),
                                        orderStatusResp
                                );

                                // 2. 修改OTA到期时间
                                Mono<Boolean> updateOtaExpireTimeMono = hotelService.findByHotelCode(orderEntity.getHotelCode())
                                        .flatMap(hotelEntity ->
                                                hotelService.updateOtaExpireTime(
                                                        hotelEntity.getHotelCode(),
                                                        orderEntity.getExpireTime(),
                                                        orderEntity
                                                )
                                        )
                                        .defaultIfEmpty(false);

                                // 3. 创建账单记录
                                Mono<Boolean> createBillMono = billService.create(
                                        orderStatusResp,
                                        orderEntity
                                );

                                // 组合所有操作
                                return Mono.zip(
                                        updateOrderMono,
                                        updateOtaExpireTimeMono,
                                        createBillMono
                                ).flatMap(results -> {
                                    boolean orderUpdated = results.getT1();
                                    boolean otaTimeUpdated = results.getT2();
                                    boolean billCreated = results.getT3();

                                    if (!orderUpdated || !otaTimeUpdated || !billCreated) {
                                        String errorMsg = String.format(
                                                "Failed to complete payment process: orderUpdated=%s, otaTimeUpdated=%s, billCreated=%s",
                                                orderUpdated, otaTimeUpdated, billCreated
                                        );
                                        log.error(errorMsg);
                                        return Mono.error(new RuntimeException(errorMsg));
                                    }

                                    log.info("Payment success process completed for order: {}", orderNo);
                                    return Mono.empty();
                                });
                            })
                    );
                })
                .doOnSuccess(v -> log.info("Successfully processed payment for order: {}", orderNo))
                .onErrorResume(e -> {
                    log.error("Failed to process payment success for order: {}", orderNo, e);
                    return Mono.error(e);
                }).then();
    }
}
