package com.wormhole.hotelds.admin.annotation;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;

import java.lang.annotation.*;
import java.time.LocalDateTime;


/**
* @Author：flx
* @Date：2025/5/6  10:53
* @Description：操作日志注解，用于标记需要记录操作日志的方法
*/
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 业务类型
     */
    BussinessTypeEnum businessType();

    /**
     * 操作子类型，用于区分相同操作类型下的不同操作
     */
    String subType() default "";

    /**
     * 操作类型
     */
    OperationTypeEnum operationType();

    /**
     * 请求参数的方法参数索引（从0开始）
     */
    int requestParamIndex() default 0;
}
