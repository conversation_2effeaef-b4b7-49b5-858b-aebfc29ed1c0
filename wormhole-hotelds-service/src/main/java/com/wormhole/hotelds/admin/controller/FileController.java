package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.CosTemporaryCredentialReq;
import com.wormhole.hotelds.admin.model.resp.CosTemporaryCredentialResp;
import com.wormhole.hotelds.storage.FileService;
import com.wormhole.hotelds.storage.file.FilePartAdapter;
import com.wormhole.hotelds.storage.model.*;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * FileController
 *
 * <AUTHOR>
 * @version 2024/11/4
 */
@RestController
@RequestMapping(value = {"/file"})
public class FileController {

    @Resource
    private FileService fileService;

    /**
     * 文件上传
     */
    @PostMapping(value = "/uploadFile")
    public Mono<Result<FileUploadResultDTO>> uploadFile(@RequestPart("base_path") String basePath, @RequestPart("file") FilePart filePart) {
        return fileService.upload(basePath, new FilePartAdapter(filePart)).flatMap(Result::success);
    }

    /**
     * 查看文件内容
     */
    @RequestMapping(value = "/getObjectContent")
    public Mono<Result<String>> getObjectContent(@RequestBody ObjectContentParams objectContentParams) {
        String bucketName = objectContentParams.getBucketName();
        String ossKey = objectContentParams.getObjectKey();
        String encoding = objectContentParams.getEncoding();
        String charset = StringUtils.defaultIfBlank(encoding, StandardCharsets.UTF_8.name());
        return fileService.getObjectContent(bucketName, ossKey, charset).flatMap(Result::success);
    }

    /**
     * 查看文件内容
     */
    @RequestMapping(value = "/getObjectUrl")
    public Mono<Result<String>> getObjectUrl(@RequestBody ObjectContentParams objectContentParams) {
        String bucketName = objectContentParams.getBucketName();
        String ossKey = objectContentParams.getObjectKey();
        String encoding = objectContentParams.getEncoding();
        String charset = StringUtils.defaultIfBlank(encoding, StandardCharsets.UTF_8.name());
        return fileService.getObjectUrl(bucketName, ossKey, charset).flatMap(Result::success);
    }

    /**
     * 获取腾讯云COS上传临时密钥
     */
    @PostMapping(value = "/getCosTemporaryCredential")
    public Mono<Result<CosTemporaryCredentialResp>> getCosTemporaryCredential(@RequestBody CosTemporaryCredentialReq req) {
        return fileService.getCosTemporaryCredential(req)
                .flatMap(Result::success);
    }

}
