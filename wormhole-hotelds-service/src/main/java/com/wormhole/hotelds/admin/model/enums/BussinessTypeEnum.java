package com.wormhole.hotelds.admin.model.enums;

import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/3/27 09:48
 * @Description：BusinessTypeEnum
 */
@Getter
public enum BussinessTypeEnum {

    HOTEL(1000, "hotel", "酒店管理"),
    BRAND(1001, "brand", "品牌管理"),
    MERCHANT(1002, "merchant", "商家管理"),
    ROOM(1003, "room", "房间管理"),
    HDS_AGREEMENT(1004, "hds_agreement", "协议管理"),
    DEVICE_MODEL(1005, "device_model", "设备类型管理"),
    DEVICE(1005, "device", "设备管理"),
    DEVICE_V2(1005, "device_v2", "设备管理"),
    DEVICE_POSITION(1006, "device_positions", "设备位置管理"),
    EMPLOYEE(1007, "employee", "员工管理"),
    INVITATION_CODE(1008, "invitation_code", "邀请码管理"),
    USER_LEAD(1009, "user_lead", "用户线索管理"),
    HOTEL_LEAD(1009, "hotel_lead", "门店线索管理"),
    DEVICE_AREA_CODE(1010, "device_area_code", "设备位置管理-区域"),
    PAYMENT_PACKAGE(1011, "payment_package", "付费套餐管理"),
    HOTEL_PAYMENT_PACKAGE(1012, "hotel_payment_package", "门店付费套餐管理"),
    INVOICE(1013, "invoice", "发票管理"),
    DAILY_AI_STATISTICS(1014, "dailyAiStatistics", "门店日报数据"),
    DAILY_AI_STATISTICS_GROUP(1015, "dailyAiStatisticsGroup", "集团日报数据"),
    PARSE_CTRIP_LINK(1016, "parse_ctrip_link", "解析携程链接"),
    ;

    private final Integer code;
    private final String businessType;
    private final String businessTypeDesc;

    BussinessTypeEnum(Integer code, String businessType, String businessTypeDesc) {
        this.code = code;
        this.businessType = businessType;
        this.businessTypeDesc = businessTypeDesc;
    }

    /**
     * 根据业务类型获取枚举
     *
     * @param businessType 业务类型
     * @return 业务类型枚举
     */
    public static BussinessTypeEnum getByBusinessType(String businessType) {
        if (businessType == null) {
            return null;
        }
        for (BussinessTypeEnum value : values()) {
            if (value.getBusinessType().equals(businessType)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断业务类型是否有效
     *
     * @param businessType 业务类型
     * @return 是否有效
     */
    public static boolean isValid(String businessType) {
        return getByBusinessType(businessType) != null;
    }
}
