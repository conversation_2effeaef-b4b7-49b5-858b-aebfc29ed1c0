package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/4/11 15:06
 * @Description：DevicePositionSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DevicePositionSearchVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 位置
     */
    private String positionCode;

    /**
     * 门店位置
     */
    private String hotelCode;

    /**
     * 楼栋
     */
    private String block;

    /**
     * 区域
     */
    private String area;

    /**
     * 位置名称
     */
    private String positionName;

    /**
     * 设备类型
     */
    private String deviceAppType;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 转换为视图对象
     */
    public static DevicePositionSearchVO toVo(HdsDevicePositionEntity entity) {
        DevicePositionSearchVO devicePositionSearchVO = new DevicePositionSearchVO();
        BeanUtils.copyProperties(entity, devicePositionSearchVO);
        devicePositionSearchVO.setCreateTime(SimpleDateUtils.formatLocalDateTimeToDateHour(entity.getCreatedAt()));
        return devicePositionSearchVO;
    }
}
