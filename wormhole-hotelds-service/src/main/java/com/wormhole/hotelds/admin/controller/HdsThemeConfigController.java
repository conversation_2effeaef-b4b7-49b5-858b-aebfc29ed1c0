package com.wormhole.hotelds.admin.controller;

import com.wormhole.hotelds.admin.model.vo.HdsThemeConfigVO;
import com.wormhole.hotelds.admin.model.req.HdsThemeConfigSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsThemeConfigDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsThemeConfigSearchReq;
import com.wormhole.hotelds.admin.service.HdsThemeConfigService;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:59:06
 * @Description: HdsThemeConfig
 */
@RestController
@RequestMapping("/hdsthemeconfig")
public class HdsThemeConfigController {

    @Resource
    private HdsThemeConfigService service;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody HdsThemeConfigSaveReq hdsThemeConfigSaveReq) {
        return service.create(hdsThemeConfigSaveReq).flatMap(Result::success);
    }

    /**
     * 更新记录
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HdsThemeConfigSaveReq hdsThemeConfigSaveReq) {
        return service.update(hdsThemeConfigSaveReq).flatMap(Result::success);
    }

    /**
     * 删除记录
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody HdsThemeConfigDeleteReq hdsThemeConfigDeleteReq) {
        return service.delete(hdsThemeConfigDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<List<HdsThemeConfigVO>>> search(@RequestBody HdsThemeConfigSearchReq hdsThemeConfigSearchReq) {
        return service.search(hdsThemeConfigSearchReq).flatMap(Result::success);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<HdsThemeConfigVO>> findById(@PathVariable("id") Long id) {
        return service.findById(id).flatMap(Result::success);
    }
}