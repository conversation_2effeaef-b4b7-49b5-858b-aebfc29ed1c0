package com.wormhole.hotelds.admin.service;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.req.PackageSaveReq;
import com.wormhole.hotelds.admin.repository.HdsProductRepository;
import com.wormhole.hotelds.core.model.entity.HdsProductEntity;
import com.wormhole.hotelds.core.model.entity.HdsProductFieldEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Author：flx
 * @Date：2025/6/16 14:12
 * @Description：PaymentProductService
 */
@Service
@Slf4j
public class ProductService {

    @Resource
    private HdsProductRepository hdsProductRepository;

    @Resource
    private ProductPricingService productPricingService;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    /**
     * 批量创建产品和定价
     */
    public Mono<Boolean> createProductsWithPricing(String paymentPackageCode, PackageSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        List<PackageSaveReq.ProductSaveReq> products = req.getProducts();
        return Flux.fromIterable(products)
                .index()
                .flatMap(indexedProduct -> {
                    Long index = indexedProduct.getT1();
                    PackageSaveReq.ProductSaveReq productReq = indexedProduct.getT2();

                    // 1. 先创建产品，sortOrder从1开始
                    HdsProductEntity productEntity = buildProductEntity(paymentPackageCode, productReq, headerInfo, index.intValue() + 1);

                    return hdsProductRepository.save(productEntity)
                            .flatMap(savedProduct -> {
                                // 2. 再创建该产品的定价
                                return productPricingService.createPricingForProduct(savedProduct.getId(), productReq.getPricing(), headerInfo, req.getDiscountMode(), Collections.emptyList());
                            });
                })
                .then(Mono.just(true))
                .onErrorResume(e -> {
                    log.error("创建产品和定价失败: {}", e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建产品和定价失败"));
                });
    }

    /**
     * 构建产品实体
     */
    private HdsProductEntity buildProductEntity(String paymentPackageCode, PackageSaveReq.ProductSaveReq productReq, HeaderUtils.HeaderInfo headerInfo, Integer sortOrder) {
        HdsProductEntity entity = new HdsProductEntity();
        entity.setPackageCode(paymentPackageCode);
        entity.setProductName(productReq.getProductName());
        entity.setProductDescription(JacksonUtils.writeValueAsString(productReq.getProductDescription()));
        entity.setSortOrder(sortOrder); // 设置排序字段
        entity.setIsRecommend(productReq.getIsRecommend());

        // 设置审计字段
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setRowStatus(1);
        return entity;
    }

    public Flux<HdsProductEntity> findByPackageCode(String packageCode) {
        return hdsProductRepository.findByPackageCode(packageCode);
    }

    public Mono<Boolean> deleteProducts(String packageCode, Integer productId) {
        Criteria criteria = Criteria.empty();
        criteria.and(Criteria.where(HdsProductFieldEnum.package_code.name()).is(packageCode));

        if (Objects.nonNull(productId)) {
            criteria = criteria.and(Criteria.where(HdsProductFieldEnum.id.name()).is(productId));
        }

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsProductEntity.class)
                .collectList()
                .flatMap(productEntities -> {
                    if (CollectionUtils.isEmpty(productEntities)) {
                        return Mono.just(true);
                    }

                    List<Integer> productIds = productEntities.stream()
                            .map(HdsProductEntity::getId)
                            .collect(Collectors.toList());

                    // 先批量删除定价，再批量删除产品
                    return productPricingService.deletePricingByProductIds(productIds)
                            .then(hdsProductRepository.deleteAllByPackageCode(packageCode))
                            .thenReturn(true);
                }).onErrorResume(e -> {
                    log.error("删除套餐产品和定价失败, packageCode: {}, error: {}", packageCode, e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除套餐产品和定价失败"));
                });
    }

    /**
     * 更新产品
     *
     * @param packageCode  套餐编码
     * @param products     产品列表
     * @param headerInfo   请求头信息
     * @param discountMode 折扣模式
     * @return 处理结果
     */
    public Mono<Boolean> updateProducts(String packageCode, List<PackageSaveReq.ProductSaveReq> products, HeaderUtils.HeaderInfo headerInfo, Integer discountMode) {
        // 如果产品列表为空，删除所有产品
        if (CollectionUtils.isEmpty(products)) {
            return deleteAllProducts(packageCode);
        }

        // 获取入参产品ID集合
        Set<Integer> inputProductIds = products.stream()
                .map(PackageSaveReq.ProductSaveReq::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 构建产品请求Map和排序Map
        Map<Integer, PackageSaveReq.ProductSaveReq> productRequestMap = products.stream()
                .filter(p -> p.getId() != null)
                .collect(Collectors.toMap(
                        PackageSaveReq.ProductSaveReq::getId,
                        Function.identity(),
                        (a, b) -> a
                ));

        Map<Integer, Integer> productOrderMap = IntStream.range(0, products.size())
                .boxed()
                .filter(i -> products.get(i).getId() != null)
                .collect(Collectors.toMap(
                        i -> products.get(i).getId(),
                        i -> i + 1,
                        (a, b) -> a
                ));

        // 查询数据库现有产品
        return r2dbcEntityTemplate.select(
                        Query.query(Criteria.where(HdsProductFieldEnum.package_code.name()).is(packageCode)),
                        HdsProductEntity.class
                ).collectList()
                .flatMap(existingProducts -> {
                    Set<Integer> existingProductIds = existingProducts.stream()
                            .map(HdsProductEntity::getId)
                            .collect(Collectors.toSet());

                    // 需要删除的产品ID
                    Set<Integer> toDeleteIds = existingProductIds.stream()
                            .filter(id -> !inputProductIds.contains(id))
                            .collect(Collectors.toSet());

                    // 需要更新的产品
                    List<Mono<Boolean>> updateOperations = existingProducts.stream()
                            .filter(product -> inputProductIds.contains(product.getId()))
                            .map(productEntity -> {
                                PackageSaveReq.ProductSaveReq req = productRequestMap.get(productEntity.getId());
                                Integer sortOrder = productOrderMap.get(productEntity.getId());
                                return updateExistingProduct(productEntity, req, sortOrder, headerInfo, discountMode);
                            })
                            .toList();

                    // 需要新增的产品
                    List<Mono<Boolean>> createOperations = products.stream()
                            .filter(p -> p.getId() == null)
                            .map(req -> createNewProduct(packageCode, req, products.indexOf(req) + 1, headerInfo, discountMode))
                            .toList();

                    // 删除操作
                    Mono<Boolean> deleteOperation = toDeleteIds.isEmpty() ?
                            Mono.just(true) :
                            deleteProducts(toDeleteIds);

                    // 合并所有操作
                    List<Mono<Boolean>> allOperations = new ArrayList<>();
                    allOperations.add(deleteOperation);
                    allOperations.addAll(updateOperations);
                    allOperations.addAll(createOperations);

                    return Mono.when(allOperations).thenReturn(true);
                });
    }

    /**
     * 更新现有产品
     */
    private Mono<Boolean> updateExistingProduct(HdsProductEntity productEntity,
                                                PackageSaveReq.ProductSaveReq req,
                                                Integer sortOrder,
                                                HeaderUtils.HeaderInfo headerInfo,
                                                Integer discountMode) {
        if (!checkProductNeedUpdate(productEntity, req, sortOrder)) {
            return productPricingService.updatePricingForProduct(productEntity.getId(), req.getPricing(), headerInfo, discountMode);
        }

        productEntity.setProductName(req.getProductName());
        productEntity.setProductDescription(JacksonUtils.writeValueAsString(req.getProductDescription()));
        productEntity.setSortOrder(sortOrder);
        productEntity.setIsRecommend(req.getIsRecommend());
        productEntity.setUpdatedBy(headerInfo.getUserId());
        productEntity.setUpdatedByName(headerInfo.getUsername());
        productEntity.setUpdatedAt(LocalDateTime.now());

        return r2dbcEntityTemplate.update(productEntity)
                .then(productPricingService.updatePricingForProduct(productEntity.getId(), req.getPricing(), headerInfo, discountMode));
    }

    /**
     * 创建新产品
     */
    private Mono<Boolean> createNewProduct(String packageCode,
                                           PackageSaveReq.ProductSaveReq req,
                                           Integer sortOrder,
                                           HeaderUtils.HeaderInfo headerInfo,
                                           Integer discountMode) {

        HdsProductEntity hdsProductEntity = buildProductEntity(packageCode, req, headerInfo, sortOrder);
        return hdsProductRepository.save(hdsProductEntity)
                .flatMap(inserted -> productPricingService.createPricingForProduct(inserted.getId(), req.getPricing(), headerInfo, discountMode,Collections.emptyList()));
    }

    /**
     * 删除指定产品
     */
    private Mono<Boolean> deleteProducts(Set<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Mono.just(true);
        }
        return r2dbcEntityTemplate.delete(
                Query.query(Criteria.where(HdsProductFieldEnum.id.name()).in(productIds)),
                HdsProductEntity.class
        ).thenReturn(true);
    }

    /**
     * 删除套餐下所有产品
     */
    private Mono<Boolean> deleteAllProducts(String packageCode) {
        return r2dbcEntityTemplate.delete(
                Query.query(Criteria.where(HdsProductFieldEnum.package_code.name()).is(packageCode)),
                HdsProductEntity.class
        ).then(Mono.just(true));
    }

    private boolean checkProductNeedUpdate(HdsProductEntity entity, PackageSaveReq.ProductSaveReq req, Integer sortOrder) {
        if (!StringUtils.equals(entity.getProductName(), req.getProductName())) {
            return true;
        }
        String reqProductDescription = CollectionUtils.isNotEmpty(req.getProductDescription()) ?
                JacksonUtils.writeValueAsString(req.getProductDescription()) : "";
        if (!StringUtils.equals(entity.getProductDescription(), reqProductDescription)) {
            return true;
        }
        return !Objects.equals(entity.getSortOrder(), sortOrder);
    }

    public Mono<HdsProductEntity> findById(Integer productId) {
        return hdsProductRepository.findById(productId);
    }
}