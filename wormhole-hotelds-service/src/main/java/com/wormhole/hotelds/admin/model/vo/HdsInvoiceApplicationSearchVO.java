package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-26 15:33:33
 * @Description: 发票申请搜索响应
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceApplicationSearchVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申请编号
     */
    private String applicationNo;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 发票抬头
     */
    private String invoiceHeader;

    /**
     * 纳税人识别号
     */
    private String taxNumber;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 关联账单数量
     */
    private Integer billCount;

    /**
     * 申请状态：0-待审核，1-审核中，2-已开票，3-已驳回
     */
    private Integer applicationStatus;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invoicedAt;

    /**
     * 审核人
     */
    private String reviewedByName;

    /**
     * 审核备注
     */
    private String reviewRemark;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票文件URL
     */
    private String invoiceUrl;

    /**
     * 申请人
     */
    private String createdByName;
} 