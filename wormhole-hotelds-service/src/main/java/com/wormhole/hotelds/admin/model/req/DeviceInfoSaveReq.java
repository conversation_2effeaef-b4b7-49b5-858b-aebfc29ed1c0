package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/4/7 09:28
 * @Description：DeviceInfoSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceInfoSaveReq {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 设备序列号
     */
    private String deviceSn;


    /**
     * MAC地址
     */
    private String macAddress;

    /**
     * 保修期(月)
     */
    private Integer warrantyMonths;

    /**
     * 入库时间
     */
    private LocalDateTime storageTime;

    /**
     * 当前APP版本
     */
    private String appVersion;

    /**
     * 设备归属类型
     */
    private String deviceAppType;

    /**
     * 采购单价(元)
     */
    private BigDecimal price;

    /**
     * 规格描述
     */
    private String specDesc;

    /**
     * 备注
     */
    private String remark;
}
