package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @author: huang<PERSON><PERSON>e
 * @date: 2025/5/15
 * @Description: 酒店及其关联的外部酒店信息VO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelWithExternalVO {
    
    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 外部酒店列表
     */
    private List<ExternalHotelVO> externalHotels;

    /**
     * 门店线索进度
     */
    private HotelLeadVO hotelLead;
}