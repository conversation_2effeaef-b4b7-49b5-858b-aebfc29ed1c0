package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:59:28
 * @Description: 协议
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsAgreementSaveReq {

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 营业执照图片地址
     */
    private String licenseUrl;


    /**
     * 统一信用代码
     */
    private String creditCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业地址
     */
    private String companyAddress;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 法人身份证号码
     */
    private String legalIdNumber;

    /**
     * 法人身份证正面
     */
    private String legalIdFront;

    /**
     * 法人身份证反面
     */
    private String legalIdBack;

}