package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsInvoiceInfoEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-24 11:50:55
 * @Description: 发票信息Repository
 */
@Repository
public interface HdsInvoiceInfoRepository extends ReactiveCrudRepository<HdsInvoiceInfoEntity, Integer> {
    
    /**
     * 根据酒店编码查询发票信息
     * @param hotelCode 酒店编码
     * @return 发票信息
     */
    Flux<HdsInvoiceInfoEntity> findByHotelCode(String hotelCode);
    
    /**
     * 根据酒店编码和是否默认查询发票信息
     * @param hotelCode 酒店编码
     * @param isDefault 是否默认
     * @return 发票信息
     */
    Flux<HdsInvoiceInfoEntity> findByHotelCodeAndIsDefault(String hotelCode, Integer isDefault);
    
    /**
     * 根据纳税人识别号查询发票信息
     * @param taxNumber 纳税人识别号
     * @return 发票信息
     */
    Mono<HdsInvoiceInfoEntity> findByTaxNumber(String taxNumber);
    
    /**
     * 根据酒店编码和纳税人识别号查询发票信息
     * @param hotelCode 酒店编码
     * @param taxNumber 纳税人识别号
     * @return 发票信息
     */
    Mono<HdsInvoiceInfoEntity> findByHotelCodeAndTaxNumber(String hotelCode, String taxNumber);
} 