package com.wormhole.hotelds.admin.service;

import com.wormhole.hotelds.admin.model.vo.ExternalHotelVO;
import com.wormhole.hotelds.admin.model.vo.HotelMappingVO;
import com.wormhole.hotelds.admin.model.vo.HotelWithExternalVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: h<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/5/15
 * @Description: 酒店及外部酒店信息服务类
 */
@Service
public class HotelWithExternalService {

    /**
     * 将HotelMappingVO列表转换为HotelWithExternalVO列表
     * @param hotelMappingVOList 酒店映射VO列表
     * @return 酒店及外部酒店信息VO列表
     */
    public List<HotelWithExternalVO> convertToHotelWithExternalVOList(List<HotelMappingVO> hotelMappingVOList) {
        if (hotelMappingVOList == null || hotelMappingVOList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 按照酒店编码分组
        Map<String, List<HotelMappingVO>> hotelMappingMap = hotelMappingVOList.stream()
                .collect(Collectors.groupingBy(HotelMappingVO::getHotelCode));
        
        // 转换为HotelWithExternalVO列表
        return hotelMappingMap.entrySet().stream()
                .map(entry -> {
                    String hotelCode = entry.getKey();
                    List<HotelMappingVO> mappings = entry.getValue();
                    
                    // 创建HotelWithExternalVO对象
                    HotelWithExternalVO hotelWithExternalVO = new HotelWithExternalVO();
                    hotelWithExternalVO.setHotelCode(hotelCode);
                    
                    // 设置酒店名称（取第一个映射的酒店名称）
                    if (!mappings.isEmpty()) {
                        hotelWithExternalVO.setHotelName(mappings.get(0).getHotelName());
                    }
                    
                    // 转换外部酒店信息
                    List<ExternalHotelVO> externalHotels = mappings.stream()
                            .map(this::convertToExternalHotelVO)
                            .collect(Collectors.toList());
                    hotelWithExternalVO.setExternalHotels(externalHotels);
                    
                    return hotelWithExternalVO;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 将HotelMappingVO转换为ExternalHotelVO
     * @param hotelMappingVO 酒店映射VO
     * @return 外部酒店信息VO
     */
    private ExternalHotelVO convertToExternalHotelVO(HotelMappingVO hotelMappingVO) {
        ExternalHotelVO externalHotelVO = new ExternalHotelVO();
        externalHotelVO.setCode(hotelMappingVO.getExternalId())
                .setName(hotelMappingVO.getExternalName())
                .setGroup(hotelMappingVO.getExternalGroup());
        return externalHotelVO;
    }
}