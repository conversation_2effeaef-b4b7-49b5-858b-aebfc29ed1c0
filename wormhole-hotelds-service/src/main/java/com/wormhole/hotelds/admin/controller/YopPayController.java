package com.wormhole.hotelds.admin.controller;

import com.alibaba.fastjson.JSON;
import com.wormhole.hotelds.admin.service.YopPayService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/6/20 11:40
 * @Description：YopPayController
 */
@RestController
@RequestMapping("/yop")
public class YopPayController {

    @Resource
    private YopPayService yopPayService;

    @PostMapping("/notify/pay")
    public String payNotify(@RequestBody String data, @RequestHeader Map<String, String> headers) {
        return yopPayService.payNotify(data,headers);
    }
}
