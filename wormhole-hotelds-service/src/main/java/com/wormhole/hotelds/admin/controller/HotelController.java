package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.HotelDeleteReq;
import com.wormhole.hotelds.admin.model.req.HotelSaveReq;
import com.wormhole.hotelds.admin.model.req.HotelSearchReq;
import com.wormhole.hotelds.admin.model.req.UpdateDeviceStatusReq;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.HotelService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/3/26 16:55
 * @Description：门店控制器
 */
@RestController
@RequestMapping("/hotel")
public class HotelController {

    @Resource
    private HotelService hotelService;

    @PostMapping("/create")
    public Mono<Result<HotelCreateVO>> create(@RequestBody HotelSaveReq hotelSaveReq) {
        return hotelService.create(hotelSaveReq).flatMap(Result::success);
    }

    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HotelSaveReq hotelSaveReq) {
        return hotelService.update(hotelSaveReq).flatMap(Result::success);
    }

    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody HotelDeleteReq hotelDeleteReq) {
        return hotelService.delete(hotelDeleteReq).flatMap(Result::success);
    }

    @PostMapping("/search")
    public Mono<Result<PageResult<HotelSearchVO>>> search(@RequestBody HotelSearchReq hotelSearchReq) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo ->
                        Mono.zip(
                                        hotelService.searchCount(hotelSearchReq, headerInfo),
                                        hotelService.search(hotelSearchReq, headerInfo)
                                )
                                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                                .flatMap(Result::success)
                );
    }

    @PostMapping("/search/active")
    public Mono<Result<PageResult<HotelSearchVO>>> searchActive(@RequestBody HotelSearchReq hotelSearchReq) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo ->
                        Mono.zip(
                                        hotelService.searchActiveCount(hotelSearchReq,headerInfo),
                                        hotelService.searchActive(hotelSearchReq,headerInfo)
                                )
                                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                                .flatMap(Result::success)
                );
    }

    @PostMapping("/getEmployeeHotel")
    public Mono<Result<PageResult<EmployeeHotelSearchVO>>> getEmployeeHotel(@RequestBody HotelSearchReq hotelSearchReq) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo ->
                        Mono.zip(
                                        hotelService.getEmployeeHotelCount(hotelSearchReq, headerInfo),
                                        hotelService.getEmployeeHotelSearch(hotelSearchReq, headerInfo),
                                hotelService.checkIsNewUser(headerInfo)
                                )
                                .map(tuple -> {
                                    Boolean isNewUser = tuple.getT3();
                                    Map<String, Object> extraInfo = new HashMap<>();
                                    extraInfo.put("isNewUser", isNewUser);
                                    return PageResult.create(tuple.getT1(), tuple.getT2(),null,extraInfo);
                                })
                                .flatMap(Result::success)
                );
    }

    /**
     * 获取门店列表
     *
     * @return
     */
    @GetMapping("/query/list")
    public Mono<Result<List<HotelVO>>> queryList() {
        return hotelService.queryList().flatMap(Result::success);
    }

    /**
     * 获取门店统计信息
     *
     * @param hotelSearchReq
     * @return
     */
    @PostMapping("/statistics")
    public Mono<Result<HotelStatisticsVO>> getStatistics(@RequestBody HotelSearchReq hotelSearchReq) {
        return hotelService.getStatistics(hotelSearchReq).flatMap(Result::success);
    }

    /**
     * 根据id查询门店信息
     * @param id
     * @return
     */
    @GetMapping("/query/{id}")
    public Mono<Result<HotelVO>> findById(@PathVariable("id") Integer id) {
        return hotelService.findById(id).flatMap(Result::success);
    }

    /**
     * 门店启用停用，设备启用、停用，员工启用停用
     * @param updateDeviceStatusReq
     * @return
     */
    @PostMapping("/update/device/status")
    public Mono<Result<Boolean>> updateDeviceStatus(@RequestBody UpdateDeviceStatusReq updateDeviceStatusReq) {
        return hotelService.updateDeviceStatus(updateDeviceStatusReq).flatMap(Result::success);
    }

    @GetMapping("/queryByCode")
    public Mono<Result<HotelVO>> findById(@RequestParam("hotel_code") String hotelCode) {
        return hotelService.findByHotelCode(hotelCode).flatMap(Result::success);
    }
}
