package com.wormhole.hotelds.admin.service;

import com.alibaba.nacos.shaded.com.google.common.base.Preconditions;
import com.wormhole.common.constant.*;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.hotelds.admin.model.req.AppVersionsDeleteReq;
import com.wormhole.hotelds.admin.model.req.AppVersionsSaveReq;
import com.wormhole.hotelds.admin.model.req.AppVersionsSearchReq;
import com.wormhole.hotelds.admin.model.vo.AppVersionSearchVO;
import com.wormhole.hotelds.admin.model.vo.AppVersionsVO;
import com.wormhole.hotelds.admin.repository.AppVersionsRepository;
import com.wormhole.hotelds.core.model.entity.HdsAppVersionsEntity;
import com.wormhole.hotelds.core.model.entity.HdsAppVersionsFieldEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceFieldEnum;
import com.wormhole.hotelds.util.SimpleDateUtils;
import com.wormhole.hotelds.util.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:58:35
 * @Description: AppVersions
 */
@Slf4j
@Service
public class AppVersionsService {

    @Resource
    private AppVersionsRepository appVersionsRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private TransactionalOperator transactionalOperator;

    /**
     * 创建版本
     */
//    @OperationLog(businessType = BussinessTypeEnum.APP_VERSION, operationType = OperationTypeEnum.ADD)
    public Mono<Boolean> create(AppVersionsSaveReq req) {
        ValidatorUtils.validateAppVersionsSaveReq(req, false);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionCreate(req, headerInfo)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "版本创建失败"));
                        })
                );
    }

    private Mono<Boolean> executeTransferWithinTransactionCreate(AppVersionsSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        return appVersionsRepository.existsByAppVersionAndAppCodeAndPlatform(req.getAppVersion(), req.getAppCode(), req.getPlatform())
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                                "版本号[" + req.getAppVersion() + "]已存在"));
                    }
                    return transactionalOperator.transactional(
                                    appVersionsRepository.save(buildAppVersionEntity(req, headerInfo))
                            )
                            .thenReturn(true)
                            .defaultIfEmpty(false);
                });
    }

    private HdsAppVersionsEntity buildAppVersionEntity(AppVersionsSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        HdsAppVersionsEntity entity = new HdsAppVersionsEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setMd5Hash(DigestUtils.md5Hex(req.getDownloadUrl()));
        entity.setOnLineTime(SimpleDateUtils.slashStringToStartDateTime(req.getOnlineTime()));
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setRowStatus(RowStatusEnum.VALID.getId());
        return entity;
    }

    /**
     * 更新版本
     */
//    @OperationLog(businessType = BussinessTypeEnum.APP_VERSION, operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> update(AppVersionsSaveReq req) {
        ValidatorUtils.validateAppVersionsSaveReq(req, true);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionUpdate(req, headerInfo)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            log.error("版本更新失败: {}", ex.getMessage(), ex);
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "版本更新失败"));
                        })
                );
    }

    private Mono<Boolean> executeTransferWithinTransactionUpdate(AppVersionsSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        return appVersionsRepository.findById(req.getId())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "版本不存在")))
                .flatMap(existingVersion -> {
                    return appVersionsRepository.existsByAppVersionAndAppCodeAndPlatformAndIdNotIn(req.getAppVersion(), req.getAppCode(), req.getPlatform(), List.of(existingVersion.getId()))
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                                            "版本号[" + req.getAppVersion() + "]已存在"));
                                }
                                updateAppVersionEntity(existingVersion, req, headerInfo);
                                return transactionalOperator.transactional(appVersionsRepository.save(existingVersion));
                            });
                })
                .map(v -> true)
                .defaultIfEmpty(false);
    }

    private void updateAppVersionEntity(HdsAppVersionsEntity entity, AppVersionsSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        entity.setPlatform(req.getPlatform());
        entity.setAppVersion(req.getAppVersion());
        entity.setAppCode(req.getAppCode());
        entity.setUpdateContent(req.getUpdateContent());
        entity.setDownloadUrl(req.getDownloadUrl());
        entity.setIsForce(req.getIsForce());
        entity.setStatus(req.getStatus());
        entity.setUpdatedBy(headerInfo.getUserId());
        entity.setUpdatedByName(headerInfo.getUsername());
        entity.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * 删除商户
     */
    public Mono<Boolean> delete(AppVersionsDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(userInfo -> executeTransferWithinTransactionDelete(userInfo, req.getId())
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "app version delete error")))
                );
    }

    /**
     * 事务删除
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(HeaderUtils.HeaderInfo headerInfo, Integer id) {
        return transactionalOperator.transactional(
                appVersionsRepository.deleteById(id)
                        .doOnSuccess(v -> log.info("app version delete success: id={}, username={}", id, headerInfo.getUsername()))
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
        );
    }

//    /**
//     * 更新版本状态
//     */
////    @OperationLog(businessType = BussinessTypeEnum.APP_VERSION, operationType = OperationTypeEnum.UPDATE)
//    public Mono<Boolean> updateStatus(AppVersionStatusReq req) {
//        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id must not be null");
//        Preconditions.checkArgument(Objects.nonNull(req.getStatus()), "status must not be null");
//
//        return HeaderUtils.getHeaderInfo()
//                .flatMap(headerInfo -> appVersionsRepository.findById(req.getId())
//                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "版本不存在")))
//                        .flatMap(version -> {
//                            version.setStatus(req.getStatus());
//                            version.setUpdatedBy(headerInfo.getUserId());
//                            version.setUpdatedByName(headerInfo.getUsername());
//                            version.setUpdatedAt(LocalDateTime.now());
//                            return appVersionsRepository.save(version);
//                        })
//                        .thenReturn(true)
//                );
//    }

    /**
     * 查询版本列表
     */
    public Mono<List<AppVersionSearchVO>> search(AppVersionsSearchReq req) {
        Criteria criteria = buildBaseCriteria(req);
        Sort sort = Sort.by(Sort.Direction.DESC, HdsAppVersionsFieldEnum.created_at.name());
        Query query = Query.query(criteria)
                .sort(sort)
                .limit(req.getPageSize())
                .offset((long) (req.getCurrent() - 1) * req.getPageSize());

        return r2dbcEntityTemplate.select(query, HdsAppVersionsEntity.class)
                .map(AppVersionSearchVO::toVo)
                .collectList();
    }

    /**
     * 统计版本数量
     */
    public Mono<Long> searchCount(AppVersionsSearchReq req) {
        Criteria criteria = buildBaseCriteria(req);
        return r2dbcEntityTemplate.count(Query.query(criteria), HdsAppVersionsEntity.class);
    }

    private Criteria buildBaseCriteria(AppVersionsSearchReq req) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(req.getPlatform())) {
            criteria = criteria.and(HdsAppVersionsFieldEnum.platform.name()).is(req.getPlatform());
        }

        if (StringUtils.isNotBlank(req.getStartTime())) {
            criteria = criteria.and(HdsDeviceFieldEnum.created_at.name())
                    .greaterThanOrEquals(SimpleDateUtils.slashStringToStartDateTime(req.getStartTime()));
        }

        if (StringUtils.isNotBlank(req.getEndTime())) {
            criteria = criteria.and(HdsDeviceFieldEnum.created_at.name())
                    .lessThanOrEquals(SimpleDateUtils.slashStringToEndDateTime(req.getEndTime()));
        }

        if (req.getStatus() != null) {
            criteria = criteria.and(HdsAppVersionsFieldEnum.status.name()).is(req.getStatus());
        }

        return criteria;
    }

    /**
     * 根据ID查询版本
     */
    public Mono<AppVersionsVO> findById(Integer id) {
        Preconditions.checkArgument(Objects.nonNull(id), "id must not be null");
        return appVersionsRepository.findById(id)
                .map(AppVersionsVO::toVO)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "版本不存在")));
    }

    /**
     * 查询最新版本
     */
    public Mono<AppVersionsVO> findLatestByPlatform(AppVersionsSearchReq req) {
        Preconditions.checkArgument(StringUtils.isNoneEmpty(req.getAppCode()), "app_code不能为空");
        Preconditions.checkArgument(PlatformEnum.isValid(req.getPlatform()), "无效的平台类型: " + req.getPlatform());

        Criteria criteria =
                Criteria.where(HdsAppVersionsFieldEnum.platform.name()).is(req.getPlatform())
                        .and(HdsAppVersionsFieldEnum.app_code.name()).is(req.getAppCode())
                        .and(HdsAppVersionsFieldEnum.status.name()).is(StatusEnum.VALID.getCode());
        Sort sort = Sort.by(Sort.Direction.DESC, HdsAppVersionsFieldEnum.app_version.name());
        Query query = Query.query(criteria).sort(sort).limit(1);

        return r2dbcEntityTemplate.select(query, HdsAppVersionsEntity.class).next()
                .map(AppVersionsVO::toVO)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "版本不存在")));
    }

    /**
     * 检查是否需要更新
     */
    public Mono<AppVersionsVO> checkUpdate(AppVersionsSearchReq req, String currentVersion) {
        Preconditions.checkArgument(StringUtils.isNoneEmpty(req.getAppCode()), "app_code不能为空");
        Preconditions.checkArgument(PlatformEnum.isValid(req.getPlatform()), "无效的平台类型: " + req.getPlatform());
        Preconditions.checkArgument(StringUtils.isNotBlank(currentVersion), "header中必须带当前版本号");

        Criteria criteria = Criteria.where(HdsAppVersionsFieldEnum.platform.name()).is(req.getPlatform())
                .and(HdsAppVersionsFieldEnum.app_code.name()).is(req.getAppCode())
                .and(HdsAppVersionsFieldEnum.status.name()).is(StatusEnum.VALID.getCode())
                .and(HdsAppVersionsFieldEnum.app_version.name()).greaterThan(currentVersion);
        Sort sort = Sort.by(Sort.Direction.ASC, HdsAppVersionsFieldEnum.app_version.name());
        Query query = Query.query(criteria).sort(sort);

        return r2dbcEntityTemplate.select(query, HdsAppVersionsEntity.class)
                .collectList()
                .flatMap(list -> {
                    if (list.isEmpty()) {
                        // 不需要更新
                        AppVersionsVO vo = new AppVersionsVO();
                        vo.setNeedUpdate(false);
                        return Mono.just(vo);
                    }
                    // 有新版本，取最新
                    HdsAppVersionsEntity latest = list.get(list.size() - 1);
                    boolean forceUpdate = list.stream().anyMatch(v -> v.getIsForce() != null && v.getIsForce() == 1);
                    AppVersionsVO vo = AppVersionsVO.toVO(latest);
                    vo.setNeedUpdate(true);
                    vo.setIsForce(forceUpdate ? 1 : 0);
                    return Mono.just(vo);
                });
    }
}