package com.wormhole.hotelds.admin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.nacos.shaded.com.google.common.base.Preconditions;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.dao.HdsHotelMappingDao;
import com.wormhole.hotelds.admin.model.dto.HdsHotelMappingDTO;
import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.hotelds.admin.model.qo.HdsHotelMappingQO;
import com.wormhole.hotelds.admin.model.req.HdsHotelMappingDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsHotelMappingQueryReq;
import com.wormhole.hotelds.admin.model.req.HdsHotelMappingSaveReq;
import com.wormhole.hotelds.admin.model.req.HotelInfoListReq;
import com.wormhole.hotelds.admin.model.resp.HotelInfoResp;
import com.wormhole.hotelds.admin.model.resp.HotelSimpleInfoResp;
import com.wormhole.hotelds.admin.model.vo.HotelMappingVO;
import com.wormhole.hotelds.config.CommonProperties;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.WebClientUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.*;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Date：2025/4/3 09:16
 * @Description：HdsEmployeeService
 */
@Service
@Slf4j
public class HdsHotelMappingService {


    @Autowired
    private HdsHotelMappingDao hdsHotelMappingDao;

    @Autowired
    private WebClientUtils webClientUtils;
    @Autowired
    private CommonProperties commonProperties;

    public Mono<List<HdsHotelMappingEntity>> listByHotelCodeList(List<String> hotelCodeList) {
        HdsHotelMappingQO hdsHotelMappingQO = HdsHotelMappingQO.builder().hotelCodes(hotelCodeList).build();
        return hdsHotelMappingDao.findList(hdsHotelMappingQO);
    }

    /**
     * 根据酒店编码列表查询酒店映射VO列表
     *
     * @param hotelCodeList 酒店编码列表
     * @return 酒店映射VO列表
     */
    public Mono<List<HotelMappingVO>> listHotelMappingVOByHotelCodeList(List<String> hotelCodeList) {
        return listByHotelCodeList(hotelCodeList)
                .defaultIfEmpty(Collections.emptyList())
                .map(mappings -> mappings.stream()
                        .map(this::convertToHotelMappingVO)
                        .collect(Collectors.toList()));
    }

    /**
     * 将酒店映射实体转换为VO
     *
     * @param entity 酒店映射实体
     * @return 酒店映射VO
     */
    private HotelMappingVO convertToHotelMappingVO(HdsHotelMappingEntity entity) {
        HotelMappingVO vo = new HotelMappingVO();
        vo.setHotelCode(entity.getHotelCode())
                .setHotelName(entity.getHotelName())
                .setExternalId(entity.getExternalId())
                .setExternalName(entity.getExternalName())
                .setExternalGroup(entity.getPlatform());
        return vo;
    }


    public Mono<Boolean> save(HdsHotelMappingSaveReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()), "hotelCode 不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelName()), "hotelName 不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getExternalId()), "externalId 不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getExternalName()), "externalName 不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getChannel()), "channel 不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getPlatform()), "platform 不能为空");

        HdsHotelMappingQO query = HdsHotelMappingQO.builder()
                .hotelCode(req.getHotelCode())
                .platform(req.getPlatform())
                .channel(req.getChannel())
                .build();

        return hdsHotelMappingDao.findOne(query)
                .switchIfEmpty(Mono.defer(() -> {
                    HdsHotelMappingEntity entity = new HdsHotelMappingEntity();
                    return Mono.just(entity);
                }))
                .flatMap(entity -> {
                    entity.setHotelCode(req.getHotelCode())
                            .setHotelName(req.getHotelName())
                            .setExternalId(req.getExternalId())
                            .setExternalName(req.getExternalName())
                            .setPlatform(req.getPlatform())
                            .setChannel(req.getChannel());

                    return hdsHotelMappingDao.save(entity).thenReturn(true);
                })
                .onErrorResume(ex -> {
                    // 可以记录日志或进行其他异常处理
                    return Mono.just(false);
                });
    }

    public Mono<HdsHotelMappingDTO> query(HdsHotelMappingQueryReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()), "hotelCode 不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getChannel()), "channel 不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getPlatform()), "platform 不能为空");

        HdsHotelMappingQO query = HdsHotelMappingQO.builder()
                .hotelCode(req.getHotelCode())
                .platform(req.getPlatform())
                .channel(req.getChannel())
                .build();

        return hdsHotelMappingDao.findOne(query).map(HdsHotelMappingDTO::toDTO).switchIfEmpty(Mono.empty());
    }


    public Mono<Boolean> delete(HdsHotelMappingDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id 不能为空");

        HdsHotelMappingQO query = HdsHotelMappingQO.builder()
                .id(req.getId())
                .build();
        return hdsHotelMappingDao.findOne(query).flatMap(entity -> {
            entity.setStatus(StatusEnum.INVALID.getCode());
            return hdsHotelMappingDao.save(entity).thenReturn(true);
        }).switchIfEmpty(Mono.just(true));

    }


    public Mono<List<HotelSimpleInfoResp>> queryHotelInfo(HotelInfoListReq hotelInfoListReq){
        String merchantHost = commonProperties.getMerchantHost();
        String url = merchantHost + "/merchant_web/v1/hotel/query_hotels_info";
        return webClientUtils.sendPost(url, null, JSONUtil.toJsonStr(hotelInfoListReq), null, new TypeReference<MerchantHotelInfoResp>() {
        }).map(resp -> {
            if (Objects.isNull(resp) || CollUtil.isEmpty(resp.getData())) {
                return Collections.<HotelSimpleInfoResp>emptyList();
            }
            return  resp.getData().stream().map(HotelSimpleInfoResp::toSimpleInfoResp).collect(Collectors.toList());
        }).onErrorResume(e -> Mono.error( new RuntimeException("查询酒店信息失败", e)));
    }

    @Data
    public static class MerchantHotelInfoResp{
        private List<HotelInfoResp> data;
    }

}
