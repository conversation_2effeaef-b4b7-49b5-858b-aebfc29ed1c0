package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsOrderEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * @Author：flx
 * @Date：2025/6/19 16:24
 * @Description：HdsPaymentorderRepository
 */
@Repository
public interface HdsOrderRepository extends ReactiveCrudRepository<HdsOrderEntity, Long> {

    Mono<HdsOrderEntity> findByOrderNo(String orderNo);

    Mono<Long> countByHotelCodeAndOrderStatus(String hotelCode,Integer status);
}
