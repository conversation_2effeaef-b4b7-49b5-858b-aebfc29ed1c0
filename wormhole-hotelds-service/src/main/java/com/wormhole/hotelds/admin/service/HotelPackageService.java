package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.DiscountModelEnum;
import com.wormhole.hotelds.admin.model.enums.PaymentModeEnum;
import com.wormhole.hotelds.admin.model.enums.PeriodTypeEnum;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.repository.HdsHotelPackageRepository;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.ValidatorUtils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/6/16 17:17
 * @Description：HotelpackageService
 */
@Service
@Slf4j
public class HotelPackageService {

    @Resource
    private HdsHotelPackageRepository hdsHotelPackageRepository;

    @Resource
    private PackageService packageService;

    @Resource
    private ProductService productService;

    @Resource
    private ProductPricingService productPricingService;

    @Resource
    private HotelProductPricingService hotelProductPricingService;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HotelService hotelService;

    /**
     * 创建门店套餐
     */
    public Mono<Boolean> create(HotelPackageSaveReq req) {
        ValidatorUtils.validateHotelPackageSaveReq(req);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 1. 校验标准套餐是否存在且启用
                    return packageService.findByPackageCode(req.getPackageCode())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "标准套餐不存在")))
                            .flatMap(standardPackage -> {
                                if (!Objects.equals(standardPackage.getStatus(), 1)) {
                                    return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "关联套餐已停用，无法创建门店套餐"));
                                }
                                // 2. 生成门店套餐编码并执行创建事务
                                return codePoolManager.getCodeFromPool(BussinessTypeEnum.HOTEL_PAYMENT_PACKAGE.getBusinessType())
                                        .flatMap(hotelPackageCode -> executeCreateTransaction(standardPackage, hotelPackageCode, req, headerInfo));
                            });
                })
                .onErrorResume(e -> {
                    if (e instanceof BusinessException) {
                        return Mono.error(e);
                    }
                    log.error("创建门店套餐失败: {}", e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建门店套餐失败"));
                });
    }

    /**
     * 执行创建事务
     */
    private Mono<Boolean> executeCreateTransaction(HdsPackageEntity standardPackage, String hotelPackageCode, HotelPackageSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                Mono.defer(() -> {
                    // 1. 创建门店套餐主记录
                    HdsHotelPackageEntity packageEntity = buildHotelPackageEntity(standardPackage, hotelPackageCode, req, headerInfo);
                    return hdsHotelPackageRepository.save(packageEntity)
                            .flatMap(savedPackage -> {
                                // 2. 创建门店定价（混合自定义和标准定价）
                                return createOrUpdatePricingForHotel(
                                        standardPackage.getDiscountMode(),
                                        savedPackage.getHotelPackageCode(),
                                        req.getPackageCode(),
                                        req.getCustomPricing(),
                                        headerInfo,
                                        Collections.emptyList())
                                        .thenReturn(true);
                            });
                })
        );
    }

    /**
     * 为门店创建混合定价（自定义+标准）
     */
    private Mono<Void> createOrUpdatePricingForHotel(Integer discountMode,
                                                     String hotelPackageCode,
                                                     String packageCode,
                                                     List<HotelPackageSaveReq.HotelProductCustomPricingReq> customPricingList,
                                                     HeaderUtils.HeaderInfo headerInfo,
                                                     List<HdsHotelProductPricingEntity> entities) {
        // 1. 查询主套餐的所有产品
        return productService.findByPackageCode(packageCode)
                .collectList()
                .flatMap(products -> {
                    // 2. 为每个产品创建智能定价
                    return products.stream()
                            .map(product ->
                                    createSmartPricingForSingleProduct(discountMode,
                                            hotelPackageCode,
                                            product.getId(),
                                            customPricingList,
                                            headerInfo,
                                            entities))
                            .reduce(Mono.empty(), Mono::then)
                            .then();
                })
                .then();
    }

    /**
     * 为单个产品创建智能定价
     */
    private Mono<Void> createSmartPricingForSingleProduct(Integer discountMode,
                                                          String hotelPackageCode,
                                                          Integer productId,
                                                          List<HotelPackageSaveReq.HotelProductCustomPricingReq> customPricingList,
                                                          HeaderUtils.HeaderInfo headerInfo,
                                                          List<HdsHotelProductPricingEntity> entities) {
        // 1. 查询该产品的所有标准定价
        return productPricingService.findByProductId(productId)
                .collectList()
                .flatMap(standardPricingList -> {
                    if (standardPricingList.isEmpty()) {
                        log.warn("产品 {} 没有标准定价", productId);
                        return Mono.empty();
                    }

                    // 2. 过滤出该产品的自定义定价
                    List<HotelPackageSaveReq.HotelProductCustomPricingReq> productCustomPricing = customPricingList.stream()
                            .filter(pricing -> pricing.getProductId().equals(productId))
                            .collect(Collectors.toList());

                    // 3. 使用智能定价服务创建完整的定价体系
                    return hotelProductPricingService.createSmartPricingForProduct(
                            discountMode,
                            hotelPackageCode,
                            productId,
                            productCustomPricing,
                            standardPricingList,
                            headerInfo,
                            entities);
                });
    }


    /**
     * 构建门店套餐实体
     */
    private HdsHotelPackageEntity buildHotelPackageEntity(HdsPackageEntity standardPackage,
                                                          String hotelPackageCode,
                                                          HotelPackageSaveReq req,
                                                          HeaderUtils.HeaderInfo headerInfo) {
        HdsHotelPackageEntity entity = new HdsHotelPackageEntity();
        entity.setHotelPackageCode(hotelPackageCode);
        entity.setHotelCode(req.getHotelCode());
        entity.setPackageCode(req.getPackageCode());
        entity.setDiscountMode(standardPackage.getDiscountMode());
        entity.setPayMode(standardPackage.getPayMode());
        entity.setStatus(0); // 默认停用

        // 设置审计字段
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setRowStatus(1);
        entity.setAdjustPriceReason(req.getAdjustPriceReason());
        return entity;
    }


    /**
     * 更新门店套餐
     */
    public Mono<Boolean> update(HotelPackageUpdateReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "门店套餐更新请求不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "门店套餐ID不能为空");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 1. 检查门店套餐是否存在
                    return hdsHotelPackageRepository.findById(req.getId())
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "门店套餐不存在")))
                            .flatMap(existingPackage -> {
                                if (Objects.equals(existingPackage.getStatus(), 1)) {
                                    return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "请先停用门店套餐，再修改"));
                                }
                                return packageService.findByPackageCode(existingPackage.getPackageCode())
                                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "套餐不存在")))
                                        .flatMap(packageEntity -> {
                                            // 2. 执行更新事务
                                            return executeUpdateTransaction(packageEntity, existingPackage, req, headerInfo);
                                        });
                            });
                }).onErrorResume(e -> {
                    log.error("更新门店套餐失败", e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新门店套餐失败"));
                });
    }

    /**
     * 执行更新事务
     */
    private Mono<Boolean> executeUpdateTransaction(HdsPackageEntity packageEntity, HdsHotelPackageEntity existingPackage, HotelPackageUpdateReq req, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                Mono.defer(() -> hdsHotelPackageRepository.save(buildUpdateHotelPackageEntity(packageEntity, existingPackage, headerInfo, req))
                        .flatMap(entity -> {
                            List<HotelPackageSaveReq.HotelProductCustomPricingReq> customPricing = req.getCustomPricing();
                            // 拿到所有产品ids
                            Set<Integer> priceIds = customPricing.stream().map(HotelPackageSaveReq.HotelProductCustomPricingReq::getId).collect(Collectors.toSet());
                            return hotelProductPricingService.findByIds(priceIds)
                                    .flatMap(entities ->
                                            createOrUpdatePricingForHotel(
                                                    packageEntity.getDiscountMode(),
                                                    existingPackage.getHotelPackageCode(),
                                                    req.getPackageCode(),
                                                    req.getCustomPricing(),
                                                    headerInfo,
                                                    entities)).thenReturn(true);
                        })));
    }

    /**
     * 构建门店套餐实体
     */
    private HdsHotelPackageEntity buildUpdateHotelPackageEntity(HdsPackageEntity standardPackage,
                                                                HdsHotelPackageEntity existingPackage,
                                                                HeaderUtils.HeaderInfo headerInfo,
                                                                HotelPackageUpdateReq req) {
        existingPackage.setDiscountMode(standardPackage.getDiscountMode());
        existingPackage.setPayMode(standardPackage.getPayMode());

        // 设置审计字段
        existingPackage.setUpdatedBy(headerInfo.getUserId());
        existingPackage.setUpdatedByName(headerInfo.getUsername());
        existingPackage.setUpdatedAt(LocalDateTime.now());
        existingPackage.setAdjustPriceReason(req.getAdjustPriceReason());
        return existingPackage;
    }

    /**
     * 门店套餐列表查询总数
     */
    public Mono<Long> searchCount(HotelPackageSearchReq req) {
        return buildSearchCriteria(req).flatMap(criteria ->
                r2dbcEntityTemplate.count(Query.query(criteria), HdsHotelPackageEntity.class));
    }

    /**
     * 门店套餐列表查询
     */
    public Mono<List<HotelPackageSearchVO>> search(HotelPackageSearchReq req) {
        return buildSearchCriteria(req)
                .flatMap(criteria -> {
                    Query query = Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.DESC, HdsHotelPackageFieldEnum.created_at.name()))
                            .limit(req.getPageSize())
                            .offset((long) (req.getCurrent() - 1) * req.getPageSize());

                    return r2dbcEntityTemplate.select(query, HdsHotelPackageEntity.class)
                            .collectList()
                            .flatMap(entityList -> {
                                List<Mono<HotelPackageSearchVO>> monoList = entityList.stream()
                                        .map(this::convertToSearchVOWithDetails)
                                        .collect(Collectors.toList());
                                return Flux.fromIterable(monoList)
                                        .flatMapSequential(mono -> mono)
                                        .collectList();
                            });
                });
    }

    /**
     * 构建查询条件
     */
    private Mono<Criteria> buildSearchCriteria(HotelPackageSearchReq req) {
        Criteria criteria = Criteria.empty();

        criteria = criteria.and(HdsHotelPackageFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId());

        // 门店编码
        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(HdsHotelPackageFieldEnum.hotel_code.name()).like("%" + req.getHotelCode() + "%");
        }

        // 状态
        if (req.getStatus() != null) {
            criteria = criteria.and(HdsHotelPackageFieldEnum.status.name()).is(req.getStatus());
        }

        // 套餐名称
        if (StringUtils.isNotBlank(req.getPackageName())) {
            return packageService.preFilterByPackageName(req.getPackageName(), criteria);
        }

        return Mono.just(criteria);
    }

    /**
     * 转换为搜索VO（包含详细信息）
     */
    private Mono<HotelPackageSearchVO> convertToSearchVOWithDetails(HdsHotelPackageEntity entity) {
        HotelPackageSearchVO vo = new HotelPackageSearchVO();
        BeanUtils.copyProperties(entity, vo);

        // 查询标准套餐信息
        return packageService.findByPackageCode(entity.getPackageCode())
                .map(standardPackage -> {
                    vo.setPackageCode(standardPackage.getPackageCode());
                    vo.setPackageName(standardPackage.getPackageName());
                    vo.setPaymentMode(standardPackage.getPayMode());
                    vo.setDiscountMode(standardPackage.getDiscountMode());
                    return vo;
                })
                .flatMap(searchVO -> hotelService.findByHotelCode(entity.getHotelCode())
                        .map(hotel -> {
                            searchVO.setHotelName(hotel.getHotelName());
                            searchVO.setTotalRoom(hotel.getTotalRoom());
                            return searchVO;
                        })
                        .onErrorResume(throwable -> {
                            // 如果找不到酒店，记录日志并返回原始VO（酒店名称等字段保持为空）
                            log.warn("Hotel not found for hotelCode: {}, error: {}", entity.getHotelCode(), throwable.getMessage());
                            return Mono.just(searchVO);
                        }))
                .flatMap(searchVO -> getProductsWithPricing(entity, searchVO))
                .defaultIfEmpty(vo);
    }

    /**
     * 加载产品和定价信息
     */
    private Mono<HotelPackageSearchVO> getProductsWithPricing(HdsHotelPackageEntity entity, HotelPackageSearchVO vo) {
        // 根据标准套餐编码查询产品列表
        return productService.findByPackageCode(entity.getPackageCode())
                .sort((p1, p2) -> {
                    if (p1.getSortOrder() == null && p2.getSortOrder() == null) return 0;
                    if (p1.getSortOrder() == null) return 1;
                    if (p2.getSortOrder() == null) return -1;
                    return p1.getSortOrder().compareTo(p2.getSortOrder());
                })
                .flatMap(product -> convertToProductVOWithPricing(entity.getHotelPackageCode(), product, vo))
                .collectList()
                .map(products -> {
                    vo.setProducts(products);
                    return vo;
                });
    }

    /**
     * 转换为产品VO（包含定价信息）
     */
    private Mono<HotelPackageSearchVO.HotelPackageProductVO> convertToProductVOWithPricing(String hotelPackageCode,
                                                                                           HdsProductEntity productEntity,
                                                                                           HotelPackageSearchVO vo) {
        HotelPackageSearchVO.HotelPackageProductVO productVO = new HotelPackageSearchVO.HotelPackageProductVO();
        productVO.setProductId(productEntity.getId());
        productVO.setProductName(productEntity.getProductName());

        // 查询门店个性化定价，如果没有则使用标准定价
        return hotelProductPricingService.findByHotelPackageCode(hotelPackageCode)
                .filter(pricing -> pricing.getProductId().equals(productEntity.getId()))
                .sort(Comparator.comparing(HdsHotelProductPricingEntity::getPeriodType))
                .map(pricing -> convertToHotelPricingVO(pricing, vo))
                .collectList()
                .flatMap(hotelPricingList -> {
                    productVO.setPricing(hotelPricingList);
                    return Mono.just(productVO);
                });
    }

    /**
     * 转换为门店定价VO
     */
    private HotelPackageSearchVO.HotelProductPricingVO convertToHotelPricingVO(HdsHotelProductPricingEntity pricingEntity, HotelPackageSearchVO vo) {
        HotelPackageSearchVO.HotelProductPricingVO pricingVO = new HotelPackageSearchVO.HotelProductPricingVO();
        pricingVO.setPeriodType(pricingEntity.getPeriodType());

        // 房间数，用于计算总价（当按房间数收费时）
        int roomCount = vo.getTotalRoom() != null ? vo.getTotalRoom() : 1;

        // 按支付模式处理价格
        if (Objects.equals(vo.getPaymentMode(), PaymentModeEnum.ROOM_COUNT.getCode())) {
            // 按房间数量收费 - 价格需要乘以房间数
            if (pricingEntity.getCustomMarketPrice() != null) {
                BigDecimal totalMarketPrice = pricingEntity.getCustomMarketPrice().multiply(new BigDecimal(roomCount));
                pricingVO.setMarketPrice(totalMarketPrice.setScale(2, RoundingMode.HALF_UP).toString());
            }

            if (pricingEntity.getCustomDiscountPrice() != null) {
                BigDecimal totalDiscountPrice = pricingEntity.getCustomDiscountPrice().multiply(new BigDecimal(roomCount));
                pricingVO.setDiscountPrice(totalDiscountPrice.setScale(2, RoundingMode.HALF_UP).toString());
            }

            // 折扣率不需要乘以房间数
            if (pricingEntity.getCustomDiscountRate() != null) {
                pricingVO.setDiscountRate(pricingEntity.getCustomDiscountRate().toString());
            }

            if (pricingEntity.getFinalPrice() != null) {
                BigDecimal totalFinalPrice = pricingEntity.getFinalPrice().multiply(new BigDecimal(roomCount));
                pricingVO.setFinalPrice(totalFinalPrice.setScale(2, RoundingMode.HALF_UP).toString());
            }
        } else {
            // 按门店收费 - 直接使用原价格
            if (pricingEntity.getCustomMarketPrice() != null) {
                pricingVO.setMarketPrice(pricingEntity.getCustomMarketPrice().setScale(2, RoundingMode.HALF_UP).toString());
            }

            if (pricingEntity.getCustomDiscountPrice() != null) {
                pricingVO.setDiscountPrice(pricingEntity.getCustomDiscountPrice().setScale(2, RoundingMode.HALF_UP).toString());
            }

            if (pricingEntity.getCustomDiscountRate() != null) {
                pricingVO.setDiscountRate(pricingEntity.getCustomDiscountRate().toString());
            }

            if (pricingEntity.getFinalPrice() != null) {
                pricingVO.setFinalPrice(pricingEntity.getFinalPrice().setScale(2, RoundingMode.HALF_UP).toString());
            }
        }

        return pricingVO;
    }

    /**
     * 根据ID查询门店套餐详情
     */
    public Mono<HotelPackageDetailVO> findById(Integer id) {
        return hdsHotelPackageRepository.findById(id)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "门店套餐不存在")))
                .flatMap(this::convertToDetailVO);
    }

    /**
     * 转换为详情VO
     */
    private Mono<HotelPackageDetailVO> convertToDetailVO(HdsHotelPackageEntity entity) {
        HotelPackageDetailVO vo = new HotelPackageDetailVO();
        BeanUtils.copyProperties(entity, vo);

        // 查询标准套餐信息
        return packageService.findByPackageCode(entity.getPackageCode())
                .map(standardPackage -> {
                    vo.setPackageId(standardPackage.getId());
                    vo.setPackageName(standardPackage.getPackageName());
                    vo.setPaymentMode(standardPackage.getPayMode());
                    vo.setDiscountMode(standardPackage.getDiscountMode());
                    return vo;
                })
                .flatMap(detailVO -> {
                    // 设置状态名称
                    return hotelService.findByHotelCode(entity.getHotelCode())
                            .map(hotel -> {
                                HotelPackageDetailVO.HotelVO hotelVO = new HotelPackageDetailVO.HotelVO();
                                hotelVO.setHotelName(hotel.getHotelName());
                                hotelVO.setHotelCode(hotel.getHotelCode());
                                hotelVO.setRoomCount(hotel.getTotalRoom());
                                detailVO.setHotelVo(hotelVO);
                                return detailVO;
                            });
                })
                .flatMap(detailVO -> {
                    // 查询产品详情
                    return getProductDetails(entity, detailVO);
                });
    }

    /**
     * 加载产品详情
     */
    private Mono<HotelPackageDetailVO> getProductDetails(HdsHotelPackageEntity entity, HotelPackageDetailVO vo) {
        return productService.findByPackageCode(entity.getPackageCode())
                .flatMap(product -> {
                    HotelPackageDetailVO.HotelPackageProductDetailVO productVO = new HotelPackageDetailVO.HotelPackageProductDetailVO();
                    productVO.setId(product.getId());
                    productVO.setProductName(product.getProductName());
                    productVO.setSortOrder(product.getSortOrder());
                    productVO.setProductDescription(JacksonUtils.readValues(product.getProductDescription(), String.class));

                    // 查询定价详情
                    return hotelProductPricingService.findByHotelPackageCode(entity.getHotelPackageCode())
                            .filter(pricing -> pricing.getProductId().equals(product.getId()))
                            .map(this::convertToPricingDetailVO)
                            .collectList()
                            .flatMap(hotelPricingList -> {
                                productVO.setPricing(hotelPricingList);
                                return Mono.just(productVO);
                            });
                })
                .collectList()
                .map(products -> {
                    products.sort(Comparator.comparing(HotelPackageDetailVO.HotelPackageProductDetailVO::getSortOrder,
                            Comparator.nullsLast(Comparator.naturalOrder())));

                    vo.setProducts(products);
                    return vo;
                });
    }

    /**
     * 转换为定价详情VO
     */
    private HotelPackageDetailVO.HotelProductPricingDetailVO convertToPricingDetailVO(HdsHotelProductPricingEntity entity) {
        HotelPackageDetailVO.HotelProductPricingDetailVO pricingDetailVO = new HotelPackageDetailVO.HotelProductPricingDetailVO();
        pricingDetailVO.setId(entity.getId());
        pricingDetailVO.setPeriodType(entity.getPeriodType());

        if (entity.getCustomMarketPrice() != null) {
            pricingDetailVO.setMarketPrice(entity.getCustomMarketPrice().toString());
        }
        if (entity.getCustomDiscountPrice() != null) {
            pricingDetailVO.setDiscountPrice(entity.getCustomDiscountPrice().toString());
        }
        if (entity.getCustomDiscountRate() != null) {
            pricingDetailVO.setDiscountRate(entity.getCustomDiscountRate().toString());
        }
        if (entity.getFinalPrice() != null) {
            pricingDetailVO.setFinalPrice(entity.getFinalPrice().toString());
        }

        return pricingDetailVO;
    }

    /**
     * 启用/停用门店套餐
     */
    public Mono<Boolean> updateStatus(HotelPackageStatusReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getHotelPackageCode()), "hotelPackageCode must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getStatus()), "status must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> processPackageStatus(req, headerInfo));
    }

    /**
     * 处理套餐状态更新
     */
    private Mono<Boolean> processPackageStatus(HotelPackageStatusReq req, HeaderUtils.HeaderInfo headerInfo) {
        return hdsHotelPackageRepository.findByHotelPackageCode(req.getHotelPackageCode())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "门店套餐不存在: " + req.getHotelPackageCode())))
                .flatMap(packageEntity -> {
                    // 如果是启用操作，需要检查主套餐状态
                    if (Objects.equals(req.getStatus(), 1)) {
                        return hdsHotelPackageRepository.existsByHotelCodeAndStatus(packageEntity.getHotelCode(), 1)
                                .flatMap(isExsit -> {
                                    if (isExsit) {
                                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "当前酒店已有启用的套餐，不能同时启用多个套餐"));
                                    }
                                    return packageService.findByPackageCode(packageEntity.getPackageCode())
                                            .flatMap(standardPackage -> {
                                                if (!Objects.equals(standardPackage.getStatus(), 1)) {
                                                    return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "关联付费套餐已停用，无法操作门店套餐启用"));
                                                }
                                                // 判断集团端套餐信息是否发生变化（付费方式/优惠方式/产品列表）
                                                return checkPackageChanges(packageEntity, standardPackage)
                                                        .flatMap(message -> Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, message)))
                                                        .switchIfEmpty(updatePackageStatus(packageEntity, req.getStatus(), headerInfo));
                                            });
                                });
                    } else {
                        return updatePackageStatus(packageEntity, req.getStatus(), headerInfo);
                    }
                }).thenReturn(true)
                .onErrorResume(e -> {
                    if (e instanceof BusinessException) {
                        return Mono.error((BusinessException) e);
                    }
                    log.error("更新门店套餐状态失败", e);
                    return Mono.error(e);
                });
    }

    /**
     * 检查集团端套餐信息是否发生变化
     *
     * @param hotelPackage    门店套餐
     * @param standardPackage 标准套餐
     * @return 如果发生变化返回错误信息，否则返回empty
     */
    private Mono<String> checkPackageChanges(HdsHotelPackageEntity hotelPackage, HdsPackageEntity standardPackage) {
        log.info("检查套餐变化 hotelPackageCode:{}, standardPackageCode:{}",
                hotelPackage.getHotelPackageCode(), standardPackage.getPackageCode());

        String message = "集团套餐已更新，需要重新设置门店付费套餐才可启用";

        // 检查付费方式
        if (!Objects.equals(hotelPackage.getPayMode(), standardPackage.getPayMode())) {
            log.info("付费方式不一致 hotel:{}, standard:{}",
                    hotelPackage.getPayMode(), standardPackage.getPayMode());
            return Mono.just(message);
        }

        // 检查优惠方式
        if (!Objects.equals(hotelPackage.getDiscountMode(), standardPackage.getDiscountMode())) {
            log.info("优惠方式不一致 hotel:{}, standard:{}",
                    hotelPackage.getDiscountMode(), standardPackage.getDiscountMode());
            return Mono.just(message);
        }

        // 检查产品列表
        return r2dbcEntityTemplate.select(
                        Query.query(Criteria.where(HdsHotelProductPricingFieldEnum.hotel_package_code.name())
                                .is(hotelPackage.getHotelPackageCode())),
                        HdsHotelProductPricingEntity.class
                ).collectList()
                .doOnNext(prices -> log.info("门店产品定价数量: {}", prices.size()))
                .zipWith(r2dbcEntityTemplate.select(
                        Query.query(Criteria.where(HdsProductFieldEnum.package_code.name())
                                .is(standardPackage.getPackageCode())),
                        HdsProductEntity.class
                ).collectList().doOnNext(products -> log.info("标准产品数量: {}", products.size())))
                .flatMap(tuple -> {
                    List<HdsHotelProductPricingEntity> hotelProductPrices = tuple.getT1();
                    List<HdsProductEntity> standardProducts = tuple.getT2();

                    // 检查产品ID是否一致
                    Set<Integer> hotelProductIds = hotelProductPrices.stream()
                            .map(HdsHotelProductPricingEntity::getProductId)
                            .collect(Collectors.toSet());
                    Set<Integer> standardProductIds = standardProducts.stream()
                            .map(HdsProductEntity::getId)
                            .collect(Collectors.toSet());

                    log.info("对比产品ID集合 hotel:{}, standard:{}", hotelProductIds, standardProductIds);

                    if (!hotelProductIds.equals(standardProductIds)) {
                        return Mono.just(message);
                    }

                    return Mono.empty();
                });
    }

    /**
     * 更新套餐状态
     */
    private Mono<HdsHotelPackageEntity> updatePackageStatus(HdsHotelPackageEntity packageEntity, Integer status, HeaderUtils.HeaderInfo headerInfo) {
        packageEntity.setStatus(status);
        packageEntity.setUpdatedBy(headerInfo.getUserId());
        packageEntity.setUpdatedByName(headerInfo.getUsername());
        packageEntity.setUpdatedAt(LocalDateTime.now());

        return hdsHotelPackageRepository.save(packageEntity);
    }

    /**
     * 删除门店套餐
     *
     * @param req
     * @return
     */
    public Mono<Boolean> delete(HotelPackageDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionDelete(headerInfo, req)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                    "hotel package delete error: " + ex.getMessage()));
                        })
                );
    }

    /**
     * 事务删除
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(HeaderUtils.HeaderInfo headerInfo, HotelPackageDeleteReq req) {
        // 1. 查询酒店信息
        return hdsHotelPackageRepository.findById(req.getId())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "门店套餐不存在")))
                .flatMap(packageEntity -> updatePackageRowStatus(packageEntity, headerInfo));
    }

    /**
     * 更新套餐row status
     */
    private Mono<Boolean> updatePackageRowStatus(HdsHotelPackageEntity packageEntity, HeaderUtils.HeaderInfo headerInfo) {
        packageEntity.setRowStatus(RowStatusEnum.DELETE.getId());
        packageEntity.setUpdatedBy(headerInfo.getUserId());
        packageEntity.setUpdatedByName(headerInfo.getUsername());
        packageEntity.setUpdatedAt(LocalDateTime.now());

        return hdsHotelPackageRepository.save(packageEntity)
                .flatMap(entity -> Mono.just(true));
    }

    /**
     * 获取门店可用套餐（支付页面使用）
     */
    public Mono<PaymentPackageDisplayVO> getAvailablePackages(String hotelCode, Integer periodType, Integer productId) {

        return getHotelPackages(hotelCode)
                .flatMap(hotelPackages -> {
                    if (CollectionUtils.isNotEmpty(hotelPackages)) {
                        // 门店有套餐，使用门店套餐
                        HdsHotelPackageEntity hotelPackage = hotelPackages.get(0);
                        return packageService.findByPackageCode(hotelPackage.getPackageCode())
                                .flatMap(standardPackage -> buildHotelPackageDisplayVO(hotelPackage, periodType, productId, standardPackage));
                    } else {
                        // 门店没有套餐，获取推荐的标准套餐
                        return getRecommendedStandardPackages()
                                .flatMap(standardPackages -> {
                                    if (CollectionUtils.isNotEmpty(standardPackages)) {
                                        HdsPackageEntity standardPackage = standardPackages.get(0);
                                        return buildStandardPackageDisplayVO(standardPackage, periodType, productId, hotelCode);
                                    } else {
                                        return Mono.just(new PaymentPackageDisplayVO());
                                    }
                                });
                    }
                });
    }

    /**
     * 查询门店启用的套餐
     */
    private Mono<List<HdsHotelPackageEntity>> getHotelPackages(String hotelCode) {
        return hdsHotelPackageRepository.findByHotelCodeAndStatus(hotelCode, 1)
                .collectList()
                .defaultIfEmpty(Collections.emptyList());
    }

    /**
     * 查询推荐的标准套餐（排除门店已关联的）
     */
    private Mono<List<HdsPackageEntity>> getRecommendedStandardPackages() {
        return packageService.getRecommendedPackages()
                .collectList()
                .defaultIfEmpty(Collections.emptyList());
    }

    /**
     * 构建门店套餐展示VO
     */
    private Mono<PaymentPackageDisplayVO> buildHotelPackageDisplayVO(
            HdsHotelPackageEntity hotelPackage,
            Integer periodType,
            Integer productId,
            HdsPackageEntity standardPackage) {

        return buildPackageDisplayVO(standardPackage, hotelPackage, periodType, productId, hotelPackage.getHotelCode());
    }

    /**
     * 构建标准套餐展示VO
     */
    private Mono<PaymentPackageDisplayVO> buildStandardPackageDisplayVO(
            HdsPackageEntity standardPackage,
            Integer periodType,
            Integer productId,
            String hotelCode) {

        return buildPackageDisplayVO(standardPackage, null, periodType, productId, hotelCode);
    }

    /**
     * 构建套餐展示VO（统一处理门店和标准套餐）
     */
    private Mono<PaymentPackageDisplayVO> buildPackageDisplayVO(
            HdsPackageEntity standardPackage,
            HdsHotelPackageEntity hotelPackage,
            Integer periodType,
            Integer productId,
            String hotelCode) {

        // 获取套餐代码
        String packageCode = hotelPackage != null ?
                hotelPackage.getPackageCode() :
                standardPackage != null ? standardPackage.getPackageCode() : null;

        if (packageCode == null) {
            return Mono.just(new PaymentPackageDisplayVO());
        }

        return productService.findByPackageCode(packageCode)
                .collectList()
                .flatMap(products -> {
                    if (CollectionUtils.isEmpty(products)) {
                        return Mono.just(new PaymentPackageDisplayVO());
                    }

                    // 获取产品ID列表
                    List<Integer> productIds = products.stream()
                            .map(HdsProductEntity::getId)
                            .collect(Collectors.toList());

                    // 获取酒店信息
                    Mono<HotelVO> hotelVOMono = hotelService.findByHotelCode(hotelCode);

                    // 获取定价信息（根据套餐类型不同获取不同的定价）
                    Mono<List<?>> pricingsMono;
                    if (hotelPackage != null) {
                        pricingsMono = hotelProductPricingService.findByHotelPackageCode(
                                hotelPackage.getHotelPackageCode()).collectList().map(list -> list);
                    } else {
                        pricingsMono = productPricingService.findAllByProductIdIn(productIds)
                                .collectList().map(list -> list);
                    }

                    // 并行获取定价信息和酒店信息
                    return Mono.zip(pricingsMono, hotelVOMono)
                            .flatMap(tuple -> {
                                List<?> pricings = tuple.getT1();
                                HotelVO hotelVO = tuple.getT2();

                                if (hotelVO.getTotalRoom() == null) {
                                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "门店房间数量配置错误"));
                                }

                                PaymentPackageDisplayVO displayVO = new PaymentPackageDisplayVO();

                                // 获取目标产品（用于计算节省金额）
                                HdsProductEntity targetProduct = getTargetProduct(products, productId);

                                // 构建产品列表
                                List<PaymentPackageDisplayVO.PackageDisplayVO> packages = buildProductPackages(
                                        standardPackage, hotelPackage, products, pricings, periodType, hotelVO.getTotalRoom());
                                displayVO.setPackages(packages);

                                // 构建周期选项
                                List<PaymentPackageDisplayVO.PeriodOptionVO> periodOptions =
                                        buildPeriodOptionsWithSavings(pricings, targetProduct.getId(),
                                                getPayMode(standardPackage, hotelPackage), getDiscountMode(standardPackage, hotelPackage), hotelVO.getTotalRoom());
                                displayVO.setPeriodOptions(periodOptions);

                                return Mono.just(displayVO);
                            });
                });
    }

    /**
     * 获取目标产品
     */
    private HdsProductEntity getTargetProduct(List<HdsProductEntity> products, Integer productId) {
        if (CollectionUtils.isEmpty(products)) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "产品列表为空");
        }

        if (productId != null) {
            return products.stream()
                    .filter(p -> p.getId().equals(productId))
                    .findFirst()
                    .orElse(products.get(0));
        }

        return products.get(0);
    }

    /**
     * 获取付费模式
     */
    private Integer getPayMode(HdsPackageEntity standardPackage, HdsHotelPackageEntity hotelPackage) {
        if (standardPackage != null && standardPackage.getPayMode() != null) {
            return standardPackage.getPayMode();
        }

        if (hotelPackage != null && hotelPackage.getPayMode() != null) {
            return hotelPackage.getPayMode();
        }

        return null;
    }

    /**
     * 获取付费模式
     */
    private Integer getDiscountMode(HdsPackageEntity standardPackage, HdsHotelPackageEntity hotelPackage) {
        if (standardPackage != null && standardPackage.getDiscountMode() != null) {
            return standardPackage.getDiscountMode();
        }

        if (hotelPackage != null && hotelPackage.getDiscountMode() != null) {
            return hotelPackage.getDiscountMode();
        }

        return null;
    }

    /**
     * 构建产品列表
     */
    private List<PaymentPackageDisplayVO.PackageDisplayVO> buildProductPackages(
            HdsPackageEntity standardPackage,
            HdsHotelPackageEntity hotelPackage,
            List<HdsProductEntity> products,
            List<?> pricings,
            Integer periodType,
            Integer totalRooms) {

        // 获取付费模式
        Integer payMode = getPayMode(standardPackage, hotelPackage);
        boolean calculateByRoom = PaymentModeEnum.ROOM_COUNT.getCode().equals(payMode); // 0-按房间数量收费

        return products.stream()
                .map(product -> {
                    // 查找对应周期的价格
                    Object pricing = findPricingForProduct(pricings, product.getId(), periodType);
                    if (pricing == null) {
                        return null;
                    }

                    // 构建套餐显示对象
                    PaymentPackageDisplayVO.PackageDisplayVO packageVO = createPackageDisplayVO(
                            standardPackage, hotelPackage, product, pricing, calculateByRoom, totalRooms);

                    // 添加月价格
                    Object monthlyPricing = findPricingForProduct(pricings, product.getId(), 1);
                    if (monthlyPricing != null) {
                        packageVO.setMonthPrice(calculatePrice(monthlyPricing, calculateByRoom, totalRooms));
                    }

                    return packageVO;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(p -> {
                    try {
                        return Double.parseDouble(p.getDiscountPrice());
                    } catch (NumberFormatException e) {
                        return 0.0;
                    }
                }))
                .collect(Collectors.toList());
    }

    /**
     * 创建套餐显示对象
     */
    private PaymentPackageDisplayVO.PackageDisplayVO createPackageDisplayVO(
            HdsPackageEntity standardPackage,
            HdsHotelPackageEntity hotelPackage,
            HdsProductEntity product,
            Object pricing,
            boolean calculateByRoom,
            Integer totalRooms) {

        PaymentPackageDisplayVO.PackageDisplayVO vo = new PaymentPackageDisplayVO.PackageDisplayVO();
        vo.setProductId(product.getId());
        vo.setProductName(product.getProductName());

        // 设置产品能力
        vo.setProductCapabilities(JacksonUtils.readValues(product.getProductDescription(), String.class));

        // 提取价格信息
        PriceInfo priceInfo = extractPriceInfo(pricing);
        if (priceInfo == null) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "定价信息不完整");
        }

        // 设置套餐代码
        if (hotelPackage != null) {
            vo.setHotelPackageCode(hotelPackage.getHotelPackageCode());
            vo.setPackageCode(hotelPackage.getPackageCode());
        } else if (standardPackage != null) {
            vo.setHotelPackageCode(null);
            vo.setPackageCode(standardPackage.getPackageCode());
        }
        vo.setIsRecommend(product.getIsRecommend());
        // 计算最终价格
        BigDecimal marketPrice = priceInfo.getMarketPrice();
        BigDecimal discountPrice = priceInfo.getDiscountPrice();

        if (calculateByRoom && totalRooms != null && totalRooms > 0) {
            BigDecimal roomCount = new BigDecimal(totalRooms);
            marketPrice = marketPrice.multiply(roomCount);
            discountPrice = discountPrice.multiply(roomCount);
        }

        vo.setMarketPrice(marketPrice.toString());
        vo.setDiscountPrice(discountPrice.toString());

        return vo;
    }

    /**
     * 提取价格信息
     */
    private PriceInfo extractPriceInfo(Object pricing) {
        if (pricing == null) {
            return null;
        }

        PriceInfo info = new PriceInfo();

        if (pricing instanceof HdsHotelProductPricingEntity hotelPricing) {
            info.setMarketPrice(hotelPricing.getCustomMarketPrice());
            // 一口价模式-折扣价
            if (Objects.nonNull(hotelPricing.getCustomDiscountPrice())) {
                info.setDiscountPrice(hotelPricing.getCustomDiscountPrice());
            } else {
                // 折扣模式-计算的价格
                info.setDiscountPrice(hotelPricing.getFinalPrice());
            }
            info.setDiscountRate(hotelPricing.getCustomDiscountRate());
        } else if (pricing instanceof HdsProductPricingEntity standardPricing) {
            info.setMarketPrice(standardPricing.getMarketPrice());
            info.setDiscountPrice(standardPricing.getDiscountPrice());
            info.setDiscountRate(standardPricing.getDiscountRate());
        }

        // 验证必要字段
        if (info.getMarketPrice() == null || info.getDiscountPrice() == null) {
            return null;
        }

        return info;
    }

    /**
     * 计算折扣价格（考虑房间数）
     */
    private String calculatePrice(Object pricing, boolean calculateByRoom, Integer totalRooms) {
        PriceInfo priceInfo = extractPriceInfo(pricing);
        if (priceInfo == null || priceInfo.getDiscountPrice() == null) {
            return null;
        }

        BigDecimal discountPrice = priceInfo.getDiscountPrice();

        if (calculateByRoom && totalRooms != null && totalRooms > 0) {
            discountPrice = discountPrice.multiply(new BigDecimal(totalRooms));
        }

        return discountPrice.toString();
    }

    /**
     * 构建带节省金额的周期选项
     */
    private List<PaymentPackageDisplayVO.PeriodOptionVO> buildPeriodOptionsWithSavings(
            List<?> pricings, Integer productId, Integer payMode, Integer discountMode, Integer totalRooms) {

        boolean calculateByRoom = PaymentModeEnum.ROOM_COUNT.getCode().equals(payMode);
        List<Integer> periodTypes = Arrays.asList(PeriodTypeEnum.MONTHLY.getCode(),
                PeriodTypeEnum.QUARTERLY.getCode(), PeriodTypeEnum.YEARLY.getCode()); // 月度、季度、年度

        Object monthlyPricing = findPricingForProduct(pricings, productId, PeriodTypeEnum.MONTHLY.getCode());
        PriceInfo monthlyPriceInfo;
        if (monthlyPricing != null) {
            monthlyPriceInfo = extractPriceInfo(monthlyPricing);
        } else {
            monthlyPriceInfo = null;
        }


        return periodTypes.stream()
                .map(periodType -> {
                    Object pricing = findPricingForProduct(pricings, productId, periodType);
                    if (pricing == null) {
                        return null;
                    }
                    int multiplier = getMultiplierForPeriod(periodType);

                    PricingInfo savingsInfo = calculateSavings(pricing, monthlyPriceInfo, multiplier, calculateByRoom, totalRooms);
                    PaymentPackageDisplayVO.PeriodOptionVO periodOptionVO = new PaymentPackageDisplayVO.PeriodOptionVO()
                            .setPeriodType(periodType);
                    if (DiscountModelEnum.DISCOUNT.getCode().equals(discountMode)) {
                        periodOptionVO.setSavingsPercentage(savingsInfo.getSavingsPercentage());
                    }
                    if (DiscountModelEnum.FIXED_PRICE.getCode().equals(discountMode)) {
                        periodOptionVO.setSavingsAmount(savingsInfo.getSavingsAmount());
                    }
                    return periodOptionVO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 根据周期类型获取倍数
     */
    private int getMultiplierForPeriod(Integer periodType) {
        return switch (periodType) {
            case 1 -> 1;  // 月度
            case 2 -> 3;  // 季度
            case 3 -> 12; // 年度
            default -> 1;
        };
    }


    /**
     * 计算节省金额和比例
     */
    private PricingInfo calculateSavings(Object pricing,
                                         PriceInfo monthlyPriceInfo,
                                         int multiplier,
                                         boolean calculateByRoom, Integer totalRooms) {
        PriceInfo priceInfo = extractPriceInfo(pricing);
        if (priceInfo == null) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "定价信息不完整");
        }

        BigDecimal marketPrice = priceInfo.getMarketPrice();
        BigDecimal discountPrice = priceInfo.getDiscountPrice();
        BigDecimal discountRate = priceInfo.getDiscountRate();

        // 应用房间数量
        if (calculateByRoom && totalRooms != null && totalRooms > 0) {
            BigDecimal roomCount = new BigDecimal(totalRooms);
            marketPrice = marketPrice.multiply(roomCount);
            discountPrice = discountPrice.multiply(roomCount);
        }

        PricingInfo result = new PricingInfo();

        // 优先使用折扣率
        if (discountRate != null) {
            result.setSavingsPercentage(discountRate.toString());
        } else {
            BigDecimal savingsAmount;

            // 对于季度和年度，计算与月度价格的比较节省
            if (multiplier > 1 && monthlyPriceInfo != null) {
                // 获取月度折扣价并应用倍数
                BigDecimal monthlyDiscountPrice = monthlyPriceInfo.getDiscountPrice();
                if (calculateByRoom && totalRooms != null && totalRooms > 0) {
                    monthlyDiscountPrice = monthlyDiscountPrice.multiply(new BigDecimal(totalRooms));
                }

                // 月度价格 * 倍数 - 当前周期折扣价
                BigDecimal equivalentMonthlyPrice = monthlyDiscountPrice.multiply(new BigDecimal(multiplier));
                savingsAmount = equivalentMonthlyPrice.subtract(discountPrice);
            } else {
                // 月度或无月度价格信息时，使用市场价 - 折扣价
                savingsAmount = marketPrice.subtract(discountPrice);
            }
            result.setSavingsAmount(savingsAmount.toString());
        }

        return result;
    }

    /**
     * 根据产品ID和周期类型查找对应的定价
     */
    private Object findPricingForProduct(List<?> pricings, Integer productId, Integer periodType) {
        if (pricings == null || productId == null || periodType == null) {
            return null;
        }

        return pricings.stream()
                .filter(pricing -> {
                    if (pricing instanceof HdsHotelProductPricingEntity hotelPricing) {
                        return hotelPricing.getProductId().equals(productId) &&
                                hotelPricing.getPeriodType().equals(periodType);
                    } else if (pricing instanceof HdsProductPricingEntity standardPricing) {
                        return standardPricing.getProductId().equals(productId) &&
                                standardPricing.getPeriodType().equals(periodType);
                    }
                    return false;
                })
                .findFirst()
                .orElse(null);
    }

    /**
     * 价格信息类
     */
    @Setter
    @Getter
    private static class PriceInfo {
        private BigDecimal marketPrice;
        private BigDecimal discountPrice;
        private BigDecimal discountRate;

    }

    public Mono<Boolean> existsByPackageCode(String packageCode, Integer status) {
        return hdsHotelPackageRepository.existsByPackageCodeAndStatus(packageCode, status);
    }

    public Mono<HdsHotelPackageEntity> findByHotelPackageCode(String hotelPackageCode) {
        return hdsHotelPackageRepository.findByHotelPackageCode(hotelPackageCode);
    }

    /**
     * 更新门店产品价格
     * 先删除旧产品对应的门店价格，再为新产品创建价格
     */
    public Mono<Void> updateHotelProductPricing(String packageCode, List<Integer> oldProductIds, HeaderUtils.HeaderInfo headerInfo, Integer discountMode) {
        // 1. 查询关联的门店套餐
        return hdsHotelPackageRepository.findAllByPackageCode(packageCode)
                .collectList()
                .flatMap(hotelPackages -> {
                    if (CollectionUtils.isEmpty(hotelPackages) || CollectionUtils.isEmpty(oldProductIds)) {
                        return Mono.empty(); // 没有关联门店套餐或旧产品，直接返回
                    }

                    // 2. 查询更新后的产品列表
                    return productService.findByPackageCode(packageCode)
                            .collectList()
                            .flatMap(newProducts -> {
                                // 3. 为每个门店套餐处理产品价格
                                List<Mono<Void>> operations = new ArrayList<>();

                                for (var hotelPackage : hotelPackages) {
                                    String hotelPackageCode = hotelPackage.getHotelPackageCode();

                                    // 3.1 删除旧产品价格
                                    Mono<Void> deleteOp = hotelProductPricingService.deleteByHotelPackageCodeAndProductIds(
                                            hotelPackageCode, oldProductIds);

                                    if (CollectionUtils.isEmpty(newProducts)) {
                                        // 如果更新后没有产品，只需删除旧产品价格
                                        operations.add(deleteOp);
                                        continue;
                                    }

                                    // 3.2 获取新产品ID列表
                                    List<Integer> newProductIds = newProducts.stream()
                                            .map(HdsProductEntity::getId)
                                            .collect(Collectors.toList());

                                    // 3.3 查询新产品的标准定价
                                    Mono<Void> createOp = productPricingService.findAllByProductIdIn(newProductIds)
                                            .collectList()
                                            .flatMap(pricings -> {
                                                // 按产品ID分组
                                                Map<Integer, List<HdsProductPricingEntity>> pricingMap = pricings.stream()
                                                        .collect(Collectors.groupingBy(HdsProductPricingEntity::getProductId));

                                                // 3.4 为每个新产品创建智能定价
                                                List<Mono<Void>> productOps = newProducts.stream()
                                                        .map(product -> {
                                                            List<HdsProductPricingEntity> productPricings =
                                                                    pricingMap.getOrDefault(product.getId(), Collections.emptyList());

                                                            return hotelProductPricingService.createSmartPricingForProduct(
                                                                    discountMode,
                                                                    hotelPackageCode,
                                                                    product.getId(),
                                                                    Collections.emptyList(),
                                                                    productPricings,
                                                                    headerInfo,
                                                                    Collections.emptyList()
                                                            );
                                                        })
                                                        .collect(Collectors.toList());

                                                return Flux.concat(productOps).then();
                                            });

                                    // 组合删除和创建操作
                                    operations.add(deleteOp.then(createOp));
                                }

                                return Flux.concat(operations).then();
                            });
                });
    }

    /**
     * 定价信息内部类
     */
    @Data
    private static class PricingInfo {
        String savingsAmount;
        String savingsPercentage;

    }
}
