package com.wormhole.hotelds.admin.service;

import com.wormhole.hotelds.admin.model.entity.HdsPayRecordEntity;
import com.wormhole.hotelds.admin.model.vo.HdsPayRecordVO;
import com.wormhole.hotelds.admin.model.req.HdsPayRecordSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsPayRecordDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsPayRecordSearchReq;
import com.wormhole.hotelds.admin.repository.HdsPayRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 18:00:08
 * @Description: HdsPayRecord
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HdsPayRecordService {

    private final HdsPayRecordRepository repository;
    private final R2dbcEntityTemplate template;

    /**
     * 创建记录
     */
    public Mono<Boolean> create(HdsPayRecordSaveReq req) {
        return repository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 更新记录
     */
    public Mono<Boolean> update(HdsPayRecordSaveReq req) {
        return repository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 删除记录
     */
    public Mono<Boolean> delete(HdsPayRecordDeleteReq req) {
        return repository.deleteById(req.getId())
                .thenReturn(true);
    }

    /**
     * 搜索列表
     */
    public Mono<List<HdsPayRecordVO>> search(HdsPayRecordSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        return template.select(HdsPayRecordEntity.class)
                .matching(Query.query(criteria))
                .all()
                .map(this::convertToVO)
                .collectList();
    }

    /**
     * 根据ID查询
     */
    public Mono<HdsPayRecordVO> findById(Long id) {
        return repository.findById(id)
                .map(this::convertToVO);
    }

    private Criteria buildSearchCriteria(HdsPayRecordSearchReq req) {
        Criteria criteria = Criteria.empty();
        // TODO: 根据实际查询条件构建Criteria
        return criteria;
    }

    private HdsPayRecordEntity convertToEntity(HdsPayRecordSaveReq req) {
        HdsPayRecordEntity entity = new HdsPayRecordEntity();
        // TODO: 实现请求对象到实体的转换
        return entity;
    }

    private HdsPayRecordVO convertToVO(HdsPayRecordEntity entity) {
        HdsPayRecordVO vo = new HdsPayRecordVO();
        // TODO: 实现实体到VO的转换
        return vo;
    }
}