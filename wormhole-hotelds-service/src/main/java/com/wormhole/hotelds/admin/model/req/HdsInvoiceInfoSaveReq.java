package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-24 18:00:00
 * @Description: 发票信息保存请求
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceInfoSaveReq {

    /**
     * ID，更新时使用
     */
    private Integer id;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 发票抬头
     */
    private String invoiceHeader;

    /**
     * 是否默认：1-是，0-否
     */
    private Integer isDefault = 0;

    /**
     * 纳税人识别号
     */
    private String taxNumber;

    /**
     * 接收邮箱
     */
    private String receiveEmail;

    /**
     * 发票内容
     */
    private String invoiceContent = "服务费";

    /**
     * 更多信息/备注
     */
    private String moreInfo;
}