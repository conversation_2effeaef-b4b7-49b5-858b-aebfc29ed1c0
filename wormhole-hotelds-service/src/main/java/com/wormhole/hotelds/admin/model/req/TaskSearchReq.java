package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/4/2 15:56
 * @Description：TaskSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TaskSearchReq extends QueryCondition {

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 门店code
     */
    private String hotelCode;
}
