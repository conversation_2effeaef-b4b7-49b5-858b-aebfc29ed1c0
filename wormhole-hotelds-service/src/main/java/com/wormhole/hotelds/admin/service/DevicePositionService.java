package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.model.entity.BaseEntity;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.annotation.OperationLog;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.repository.DevicePositionRepository;
import com.wormhole.hotelds.admin.repository.DeviceRepository;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionFieldEnum;
import com.wormhole.hotelds.core.utils.HoteldsApiUtils;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.OperationLogUtil;
import com.wormhole.hotelds.util.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.wormhole.hotelds.core.model.entity.HdsDevicePositionFieldEnum.area;
import static com.wormhole.hotelds.core.model.entity.HdsDevicePositionFieldEnum.hotel_code;

/**
 * @Author：flx
 * @Date：2025/4/11 09:15
 * @Description：DevicePositionService
 */
@Slf4j
@Service
public class DevicePositionService {

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private OperationLogUtil operationLogUtil;

    @Resource
    private DevicePositionRepository devicePositionRepository;

    @Resource
    private DeviceRepository deviceRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsWechatQrCodeService hdsWechatQrCodeService;

    /**
     * 创建位置
     *
     * @param devicePositionSaveReq 请求参数
     * @return Mono<Boolean>
     */
    @OperationLog(businessType = BussinessTypeEnum.DEVICE_POSITION,
            operationType = OperationTypeEnum.ADD)
    public Mono<Boolean> create(DevicePositionSaveReq devicePositionSaveReq) {
        ValidatorUtils.validateDevicePositionSaveReq(devicePositionSaveReq);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo ->
                        executeTransferWithinTransactionCreate(devicePositionSaveReq, headerInfo)
                )
                .flatMap(savedEntity -> {
                    if (savedEntity == null) {
                        return Mono.just(false);
                    }
                    hdsWechatQrCodeService.tryAsyncGenerate(savedEntity);
                    return Mono.just(true);
                })
                .onErrorResume(ex -> {
                    log.error("create devicePosition error, ex={}", ex.getMessage());
                    if (ex instanceof BusinessException) {
                        return Mono.error(ex);
                    }
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device model create error"));
                });

    }

    /**
     * 事务创建
     *
     * @param devicePositionSaveReq 请求参数
     * @param headerInfo            请求头信息
     * @return Mono<Boolean>
     */
    private Mono<HdsDevicePositionEntity> executeTransferWithinTransactionCreate(DevicePositionSaveReq devicePositionSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        String hotelCode = headerInfo.getHotelCode();
        return devicePositionRepository.existsByHotelCodeAndBlockAndAreaAndPositionName(
                        hotelCode,
                        devicePositionSaveReq.getBlock(),
                        devicePositionSaveReq.getArea(),
                        devicePositionSaveReq.getPositionName())
                .flatMap(exist -> {
                    if (exist) {
                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "位置已存在"));
                    }
                    return codePoolManager.getCodeFromPool(BussinessTypeEnum.DEVICE_POSITION.getBusinessType())
                            .flatMap(code -> transactionalOperator.transactional(
                                                    devicePositionRepository.save(buildHdsDevicePositionEntity(devicePositionSaveReq, headerInfo, code)))
                                            .doOnSuccess(hdsDeviceModelEntity ->
                                                            devicePositionSaveReq.setId(hdsDeviceModelEntity.getId())
//                                            handleSuccess(hdsDeviceModelEntity, devicePositionSaveReq, OperationTypeEnum.ADD, headerInfo)
                                            )
                                            .doOnError(error -> handleError(error, devicePositionSaveReq, OperationTypeEnum.ADD, headerInfo))
                            );
                });
    }

    /**
     * 构建商户实体
     */
    private HdsDevicePositionEntity buildHdsDevicePositionEntity(DevicePositionSaveReq devicePositionSaveReq, HeaderUtils.HeaderInfo headerInfo, String code) {
        HdsDevicePositionEntity hdsDevicePositionEntity = new HdsDevicePositionEntity();
        BeanUtils.copyProperties(devicePositionSaveReq, hdsDevicePositionEntity);
        hdsDevicePositionEntity.setPositionCode(code);
        hdsDevicePositionEntity.setAreaCode(HoteldsApiUtils.generateAreaCode(headerInfo.getHotelCode(), devicePositionSaveReq.getBlock(), devicePositionSaveReq.getArea()));
        hdsDevicePositionEntity.setHotelCode(headerInfo.getHotelCode());
        setAuditFields(hdsDevicePositionEntity, headerInfo);
        hdsDevicePositionEntity.setRowStatus(1);
        return hdsDevicePositionEntity;
    }

    /**
     * 设置审计字段
     *
     * @param entity     实体对象
     * @param headerInfo 请求头信息
     */
    private void setAuditFields(BaseEntity entity, HeaderUtils.HeaderInfo headerInfo) {
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
    }


    /**
     * 更新商户
     */
    @OperationLog(businessType = BussinessTypeEnum.DEVICE_POSITION,
            operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> update(DevicePositionSaveReq devicePositionSaveReq) {
//        ValidatorUtils.validateDeviceSaveReq();
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionUpdate(devicePositionSaveReq, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "brand update error")))
                );
    }

    /**
     * 事务更新
     */
    private Mono<Boolean> executeTransferWithinTransactionUpdate(DevicePositionSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                devicePositionRepository.findById(req.getId())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "device position not found")))
                        .flatMap(existingPosition -> {
                            String oldPositionName = existingPosition.getPositionName();
                            updateDevicePositionEntity(existingPosition, req, headerInfo);
                            String newPositionName = req.getPositionName();
                            // 先保存
                            return devicePositionRepository.save(existingPosition)
                                .flatMap(saved -> {
                                    // positionName 变更才触发
                                    if (!Objects.equals(oldPositionName, newPositionName)) {
                                        // 先查出二维码 entity，再传递
                                        return hdsWechatQrCodeService.getOrCreateEntity(saved.getHotelCode(), saved.getPositionCode())
                                            .flatMap(qrEntity -> hdsWechatQrCodeService.generateNormalQrCodeAndPoster(
                                                saved.getHotelCode(), saved.getPositionCode(), saved.getPositionName(), qrEntity
                                            )).thenReturn(saved);
                                    }
                                    return Mono.just(saved);
                                });
                        })
                        .doOnSuccess(entity -> handleSuccess(entity, req, OperationTypeEnum.UPDATE, headerInfo))
                        .doOnError(error -> handleError(error, req, OperationTypeEnum.UPDATE, headerInfo))
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 更新设备位置实体
     */
    private void updateDevicePositionEntity(HdsDevicePositionEntity entity, DevicePositionSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 只更新允许修改的字段
        if (StringUtils.isNotBlank(req.getPositionName())) {
            entity.setPositionName(req.getPositionName());
        }
        if (StringUtils.isNotBlank(req.getBlock())) {
            entity.setBlock(req.getBlock());
        }
        if (StringUtils.isNotBlank(req.getArea())) {
            entity.setArea(req.getArea());
        }
        if (StringUtils.isNotBlank(req.getBlock()) || StringUtils.isNotBlank(req.getArea())){
            entity.setAreaCode(HoteldsApiUtils.generateAreaCode(entity.getHotelCode(),entity.getBlock(),entity.getArea()));
        }

        // 设置更新信息
        entity.setUpdatedBy(headerInfo.getUserId());
        entity.setUpdatedByName(headerInfo.getUsername());
        entity.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * 删除设备位置
     */
    @OperationLog(businessType = BussinessTypeEnum.DEVICE_POSITION,
            operationType = OperationTypeEnum.DELETE)
    public Mono<Boolean> delete(DevicePositionDeleteReq devicePositionDeleteReq) {
        Preconditions.checkArgument(Objects.nonNull(devicePositionDeleteReq.getId()), "id must not be null");
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionDelete(devicePositionDeleteReq, headerInfo)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device position delete error"));
                        }));
    }

    /**
     * 事务删除
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(DevicePositionDeleteReq devicePositionDeleteReq, HeaderUtils.HeaderInfo headerInfo) {
        Long id = devicePositionDeleteReq.getId();
        return transactionalOperator.transactional(
                // 1. 查询设备位置是否存在
                devicePositionRepository.findById(id)
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "device position not found")))
                        .flatMap(position -> {
                            // 2. 检查是否有在线设备
                            return deviceRepository.findByPositionCode(position.getPositionCode())
                                    .collectList()
                                    .flatMap(devices -> {
                                        if (CollectionUtils.isNotEmpty(devices)) {
                                            return Mono.error(new BusinessException(ResultCode.NOT_ACCEPTABLE,
                                                    "不能删除设备位置，存在已绑定的在线设备"));
                                        }

                                        // 3. 更新关联设备的position_code为null
                                        return Flux.fromIterable(devices)
                                                .flatMap(device -> {
                                                    device.setPositionCode(null);
                                                    device.setUpdatedBy(headerInfo.getUserId());
                                                    device.setUpdatedByName(headerInfo.getUsername());
                                                    device.setUpdatedAt(LocalDateTime.now());
                                                    return deviceRepository.save(device);
                                                })
                                                .then(Mono.just(position));
                                    });
                        })
                        .flatMap(position -> {
                            // 4. 删除设备位置
                            return devicePositionRepository.delete(position)
                                    .doOnSuccess(v -> handleSuccess(position, devicePositionDeleteReq, OperationTypeEnum.DELETE, headerInfo))
                                    .doOnError(error -> handleError(error, devicePositionDeleteReq, OperationTypeEnum.DELETE, headerInfo));
                        })
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
        );
    }

    private void handleSuccess(Object entity, Object req, OperationTypeEnum operationType, HeaderUtils.HeaderInfo headerInfo) {
//        String businessName = "";
//        String targetId = null;
//        String targetName = "";
//
//        if (entity instanceof HdsDevicePositionEntity position) {
//            targetId = String.valueOf(position.getId());
//            businessName = String.format("%s-%s-%s", position.getBlock(), position.getArea(), position.getPositionName());
//            targetName = position.getPositionName();
//        } else if (entity instanceof Long) {
//            targetId = String.valueOf(entity);
//            if (req instanceof DevicePositionDeleteReq deleteReq) {
//                businessName = String.format("删除设备位置: ID=%s", deleteReq.getId());
//                targetName = String.valueOf(deleteReq.getId());
//            }
//        }
//
//        String operationResult = switch (operationType) {
//            case ADD -> String.format("创建设备位置成功: %s", targetName);
//            case UPDATE -> String.format("更新设备位置成功: %s", targetName);
//            case DELETE -> String.format("删除设备位置成功: %s", targetName);
//            default -> String.format("设备位置操作成功: %s", targetName);
//        };
//
//        operationLogUtil.recordSuccess(
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                targetId != null ? Long.valueOf(targetId) : null,
//                businessName,
//                operationType.getCode(),
//                operationResult,
//                req,
//                headerInfo
//        ).subscribe();
//
//        log.info("设备位置{}成功: {}", operationType.getDesc(), businessName);
    }

    /**
     * 统一处理失败日志记录
     */
    private void handleError(Throwable error, Object req, OperationTypeEnum operationType, HeaderUtils.HeaderInfo headerInfo) {
//        String businessName = "";
//        String targetName = "";
//
//        if (req instanceof DevicePositionSaveReq saveReq) {
//            businessName = String.format("%s-%s-%s", saveReq.getBlock(), saveReq.getArea(), saveReq.getPositionName());
//            targetName = saveReq.getPositionName();
//        } else if (req instanceof DevicePositionDeleteReq deleteReq) {
//            businessName = String.format("ID=%s", deleteReq.getId());
//            targetName = String.valueOf(deleteReq.getId());
//        }
//
//        String operationResult = switch (operationType) {
//            case ADD -> String.format("创建设备位置失败: %s", targetName);
//            case UPDATE -> String.format("更新设备位置失败: %s", targetName);
//            case DELETE -> String.format("删除设备位置失败: %s", targetName);
//            default -> String.format("设备位置操作失败: %s", targetName);
//        };
//
//        operationLogUtil.recordFail(
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                null,
//                businessName,
//                operationType.getCode(),
//                operationResult,
//                req,
//                error.getMessage(),
//                headerInfo
//        ).subscribe();
//
//        log.error("设备位置{}失败: {}, error={}", operationType.getDesc(), businessName, error.getMessage());
    }

    /**
     * 搜索设备型号总数
     */
    public Mono<Long> searchCount(DevicePositionSearchReq devicePositionSearchReq) {
        Criteria baseCriteria = buildBaseCriteria(devicePositionSearchReq);
        Query query = Query.query(baseCriteria);
        return r2dbcEntityTemplate.count(query, HdsDevicePositionEntity.class);
    }

    /**
     * 搜索设备型号
     *
     * @param devicePositionSearchReq
     * @return
     */
    public Mono<List<DevicePositionSearchVO>> search(DevicePositionSearchReq devicePositionSearchReq) {
        Criteria criteria = buildBaseCriteria(devicePositionSearchReq);
        Sort sort = Sort.by(Sort.Direction.DESC, HdsDevicePositionFieldEnum.created_at.name())
                .and(Sort.by(Sort.Direction.DESC, HdsDevicePositionFieldEnum.id.name()));
        Query query = Query
                .query(criteria)
                .sort(sort)
                .limit(devicePositionSearchReq.getPageSize())
                .offset((long) (devicePositionSearchReq.getCurrent() - 1) * devicePositionSearchReq.getPageSize());
        return r2dbcEntityTemplate.select(query, HdsDevicePositionEntity.class)
                .collectList()
                .map(hdsDevicePositionEntities -> hdsDevicePositionEntities.stream()
                        .map(DevicePositionSearchVO::toVo)
                        .toList());
    }

    /**
     * 构建基础查询条件
     *
     * @param devicePositionSearchReq
     * @return
     */
    private Criteria buildBaseCriteria(DevicePositionSearchReq devicePositionSearchReq) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(devicePositionSearchReq.getHotelCode())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.hotel_code.name()).is(devicePositionSearchReq.getHotelCode());
        }

        if (StringUtils.isNotBlank(devicePositionSearchReq.getBlock())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.block.name()).like("%" + devicePositionSearchReq.getBlock() + "%");
        }

        if (StringUtils.isNotBlank(devicePositionSearchReq.getArea())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.area.name()).like("%" + devicePositionSearchReq.getArea() + "%");
        }

        if (StringUtils.isNotBlank(devicePositionSearchReq.getPositionName())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.position_name.name()).like("%" + devicePositionSearchReq.getPositionName() + "%");
        }

        if (StringUtils.isNotBlank(devicePositionSearchReq.getDeviceAppType())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.device_app_type.name()).is(devicePositionSearchReq.getDeviceAppType());
        }
        return criteria;
    }


    /**
     * 根据ID查询设备位置
     */
    public Mono<DevicePositionVO> findById(Long id) {
        Preconditions.checkArgument(Objects.nonNull(id), "id must not be null");
        return devicePositionRepository.findById(id)
                .map(DevicePositionVO::toVo)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "position not found")));
    }

    /**
     * 获取楼层区域排序
     */
    public Mono<DevicePositionBlockAndFloorVO> getBlockAndFloor(String hotelCode, String deviceAppType) {
        Criteria criteria = Criteria
                .where(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsDevicePositionFieldEnum.device_app_type.name()).is(deviceAppType);
        Sort sort = Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.block_area_sort_order.name());
        Query query = Query.query(criteria)
                .sort(sort);

        return r2dbcEntityTemplate.select(query, HdsDevicePositionEntity.class)
                .collectList()
                .map(entities -> {
                    // 首先按 groupName 分组
                    Map<String, List<HdsDevicePositionEntity>> groupByName = entities.stream()
                            .filter(entity -> StringUtils.isNotBlank(entity.getBlock()) && Objects.nonNull(entity.getArea()))
                            .collect(Collectors.groupingBy(entity -> entity.getBlock() + entity.getArea()));

                    // 然后为每个组创建一个 PositionGroup 对象
                    List<DevicePositionBlockAndFloorVO.PositionGroup> groups = new ArrayList<>();

                    for (Map.Entry<String, List<HdsDevicePositionEntity>> entry : groupByName.entrySet()) {
                        String groupName = entry.getKey();
                        List<HdsDevicePositionEntity> groupEntities = entry.getValue();

                        // 收集组内所有实体的ID
                        List<Long> ids = groupEntities.stream()
                                .map(HdsDevicePositionEntity::getId)
                                .collect(Collectors.toList());

                        // 使用组内第一个实体的排序顺序
                        Integer sortOrder = groupEntities.get(0).getBlockAreaSortOrder();

                        // 创建 PositionGroup
                        DevicePositionBlockAndFloorVO.PositionGroup group = DevicePositionBlockAndFloorVO.PositionGroup.builder()
                                .id(groupEntities.get(0).getId())
                                .groupName(groupName)
                                .sortOrder(sortOrder)
                                .ids(ids)
                                .build();
                        groups.add(group);
                    }

                    // 按排序顺序排序
                    groups.sort(Comparator.comparing(DevicePositionBlockAndFloorVO.PositionGroup::getSortOrder));

                    // 构建最终返回对象
                    return DevicePositionBlockAndFloorVO.builder()
                            .deviceAppType(deviceAppType)
                            .positionGroups(groups)
                            .build();
                });

    }

    /**
     * 获取设备类型列表
     *
     * @param hotelCode
     * @return
     */
    public Mono<List<DeviceAppTypeVO>> getDeviceAppTypes(String hotelCode) {
        if (StringUtils.isBlank(hotelCode)) {
            return Mono.just(Arrays.stream(DeviceTypeEnum.values())
                    .filter(deviceTypeEnum -> DeviceTypeEnum.FRONT.getCode().equals(deviceTypeEnum.getCode()) ||
                            DeviceTypeEnum.ROOM.getCode().equals(deviceTypeEnum.getCode()))
                    .map(deviceTypeEnum -> DeviceAppTypeVO.builder()
                            .code(deviceTypeEnum.getCode())
                            .label(deviceTypeEnum.getDescription())
                            .build())
                    .collect(Collectors.toList()));
        }


        Criteria criteria = Criteria
                .where(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode);
        Sort sort = Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.app_type_sort_order.name());
        Query query = Query.query(criteria)
                .sort(sort);

        return r2dbcEntityTemplate.select(query, HdsDevicePositionEntity.class)
                .map(entity -> DeviceAppTypeVO.builder()
                        .code(entity.getDeviceAppType())
                        .label(DeviceTypeEnum.getDescriptionByCode(entity.getDeviceAppType()))
                        .build())
                .distinct()
                .collectList();

    }

    /**
     * 根据酒店编码、设备应用类型查询所有楼栋
     *
     * @param hotelCode
     * @param deviceAppType
     * @return
     */
    public Mono<List<String>> getBlocks(String hotelCode, String deviceAppType) {
        Criteria criteria = Criteria
                .where(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsDevicePositionFieldEnum.device_app_type.name()).is(deviceAppType);
        Sort sort = Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.block.name());
        Query query = Query.query(criteria)
                .sort(sort);

        return r2dbcEntityTemplate.select(query, HdsDevicePositionEntity.class)
                .collectList()
                .map(entities -> entities.stream()
                        .map(HdsDevicePositionEntity::getBlock)
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .collect(Collectors.toList()));

    }

    /**
     * 根据酒店编码、设备应用类型和楼栋查询所有楼层
     *
     * @param deviceBlockSearchReq
     * @return
     */
    public Mono<List<String>> getFloors(DeviceBlockSearchReq deviceBlockSearchReq) {
        String hotelCode = deviceBlockSearchReq.getHotelCode();
        String deviceAppType = deviceBlockSearchReq.getDeviceAppType();
        String block = deviceBlockSearchReq.getBlock();
        Criteria criteria = Criteria.where(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsDevicePositionFieldEnum.device_app_type.name()).is(deviceAppType)
                .and(HdsDevicePositionFieldEnum.block.name()).is(block);

        Sort sort = Sort.by(Sort.Direction.ASC, area.name());
        Query query = Query.query(criteria)
                .sort(sort);

        return r2dbcEntityTemplate.select(query, HdsDevicePositionEntity.class)
                .collectList()
                .map(entities -> entities.stream()
                        .map(HdsDevicePositionEntity::getArea)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()));
    }


    /**
     * 根据酒店编码、设备应用类型、楼栋和楼层查询所有位置
     *
     * @param deviceBlockAreaSearchReq
     * @return
     */
    public Mono<List<DevicePositionNameVO>> getPositions(DeviceBlockAreaSearchReq deviceBlockAreaSearchReq) {
        String hotelCode = deviceBlockAreaSearchReq.getHotelCode();
        String deviceAppType = deviceBlockAreaSearchReq.getDeviceAppType();
        String block = deviceBlockAreaSearchReq.getBlock();
        String area = deviceBlockAreaSearchReq.getArea();
        Criteria criteria = Criteria.where(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsDevicePositionFieldEnum.device_app_type.name()).is(deviceAppType)
                .and(HdsDevicePositionFieldEnum.block.name()).is(block)
                .and(HdsDevicePositionFieldEnum.area.name()).is(area);

        log.info("hotelCode:{} deviceAppType:{} block:{} area:{}", hotelCode, deviceAppType, block, area);

        Sort sort = Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.position_sort_order.name());

        return r2dbcEntityTemplate.select(HdsDevicePositionEntity.class)
                .matching(Query.query(criteria).sort(sort))
                .all()
                .map(entity -> DevicePositionNameVO.builder()
                        .id(entity.getId())
                        .positionName(entity.getPositionName())
                        .build())
                .collectList();
    }

    /**
     * 更新位置排序
     *
     * @param req
     * @return
     */
    public Mono<Boolean> updatePositionNameSort(DevicePositionDragSortReq req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionSort(req, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "position sort update error")))
                );
    }

    /**
     * 事务更新排序
     */
    private Mono<Boolean> executeTransferWithinTransactionSort(DevicePositionDragSortReq req, HeaderUtils.HeaderInfo headerInfo) {
        if (CollectionUtils.isEmpty(req.getPositions())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "位置排序列表不能为空"));
        }
        return transactionalOperator.transactional(
                Flux.fromIterable(req.getPositions())
                        .flatMap(position -> devicePositionRepository.findById(position.getId())
                                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND,
                                        String.format("device position not found: %d", position.getId()))))
                                .flatMap(existingPosition -> {
                                    updatePositionSort(existingPosition, position.getSortOrder(), headerInfo);
                                    return devicePositionRepository.save(existingPosition);
                                })
                        )
                        .collectList()
                        .doOnSuccess(entities -> handleSortSuccess(entities, req, headerInfo))
                        .doOnError(error -> handleSortError(error, req, headerInfo))
                        .then(Mono.just(true))
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 更新排序值
     */
    private void updatePositionSort(HdsDevicePositionEntity entity, Integer sortOrder, HeaderUtils.HeaderInfo headerInfo) {
        entity.setPositionSortOrder(sortOrder);
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setUpdatedBy(headerInfo.getUserId());
        entity.setUpdatedByName(headerInfo.getUsername());
    }

    /**
     * 处理排序更新成功
     */
    private void handleSortSuccess(List<HdsDevicePositionEntity> entities, DevicePositionDragSortReq req,
                                   HeaderUtils.HeaderInfo headerInfo) {
//        entities.forEach(entity -> {
//            // 记录操作日志
//            operationLogUtil.recordSuccess(
//                    BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                    BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                    entity.getId(),
//                    entity.getPositionName(),
//                    OperationTypeEnum.UPDATE.getCode(),
//                    String.format("更新设备位置排序成功: %s-%s", entity.getId(), entity.getPositionName()),
//                    req,
//                    headerInfo
//            ).subscribe();
//
//            log.info("设备位置排序{}成功: {}", OperationTypeEnum.UPDATE.getDesc(), BussinessTypeEnum.DEVICE_POSITION.getBusinessType());
//        });

    }

    /**
     * 处理排序更新错误
     */
    private void handleSortError(Throwable error, DevicePositionDragSortReq req,
                                 HeaderUtils.HeaderInfo headerInfo) {
//        log.error("更新设备位置排序失败: {}", error.getMessage());
//
//        operationLogUtil.recordFail(
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                null,
//                req.getPositions().stream().map(position -> position.getId().toString()).collect(Collectors.joining(",")),
//                OperationTypeEnum.UPDATE.getCode(),
//                String.format("更新设备位置排序失败: %s", req.getPositions()),
//                req,
//                error.getMessage(),
//                headerInfo
//        ).subscribe();
    }

    /**
     * 更新位置排序
     *
     * @param req
     * @return
     */
    public Mono<Boolean> updatePositionBlockAndFloorSort(DevicePositionDragSortReq req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferPositionNameSort(req, headerInfo)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "position sort update error"));
                        })
                );
    }

    /**
     * 事务更新排序
     */
    private Mono<Boolean> executeTransferPositionNameSort(DevicePositionDragSortReq req, HeaderUtils.HeaderInfo headerInfo) {
        if (CollectionUtils.isEmpty(req.getPositions())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "位置排序列表不能为空"));
        }
        // 使用事务执行批量更新
        return transactionalOperator.transactional(
                Flux.fromIterable(req.getPositions())
                        .flatMap(position -> {
                            if (CollectionUtils.isEmpty(position.getIds())) {
                                return Mono.empty();
                            }

                            // 批量查询所有ID对应的位置实体
                            return devicePositionRepository.findAllById(position.getIds())
                                    .map(existingPosition -> {
                                        // 更新排序值
                                        updatePositionBlockAndFloorSort(existingPosition, position.getSortOrder(), headerInfo);
                                        return existingPosition;
                                    })
                                    .collectList()
                                    .flatMapMany(updatedPositions -> {
                                        if (updatedPositions.isEmpty()) {
                                            return Mono.empty();
                                        }
                                        // 批量保存更新后的实体
                                        return devicePositionRepository.saveAll(updatedPositions);
                                    });
                        })
                        .collectList()
                        .doOnSuccess(entities -> handleSortSuccess(entities, req, headerInfo))
                        .doOnError(error -> handleSortError(error, req, headerInfo))
                        .then(Mono.just(true))
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 更新排序值
     */
    private void updatePositionBlockAndFloorSort(HdsDevicePositionEntity entity, Integer sortOrder, HeaderUtils.HeaderInfo headerInfo) {
        entity.setBlockAreaSortOrder(sortOrder);
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setUpdatedBy(headerInfo.getUserId());
        entity.setUpdatedByName(headerInfo.getUsername());
    }


    /**
     * 更新位置排序
     *
     * @param req
     * @return
     */
    public Mono<Boolean> updatePositionAppTypeSort(DevicePositionAppTypeDragSortReq
                                                           req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferAppTypeSort(req, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "position sort update error")))
                );
    }

    /**
     * 事务更新排序
     */
    private Mono<Boolean> executeTransferAppTypeSort(DevicePositionAppTypeDragSortReq req, HeaderUtils.HeaderInfo headerInfo) {
        if (CollectionUtils.isEmpty(req.getDevicePositionAppTypes())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "设备位置类型排序列表不能为空"));
        }
        return transactionalOperator.transactional(
                Flux.fromIterable(req.getDevicePositionAppTypes())
                        .flatMap(positionType -> devicePositionRepository
                                .findByDeviceAppTypeAndHotelCode(positionType.getDeviceAppType(), headerInfo.getHotelCode())
                                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND,
                                        String.format("device app type not found: %s", positionType.getDeviceAppType()))))
                                .map(existingPosition -> {
                                    updatePositionAppType(existingPosition, positionType.getSortOrder(), headerInfo);
                                    return existingPosition;
                                })
                                .collectList()
                                .flatMapMany(positions -> devicePositionRepository.saveAll(positions))
                        )
                        .collectList()
                        .doOnSuccess(entities -> handleSortSuccess(entities, req, headerInfo))
                        .doOnError(error -> handleSortError(error, req, headerInfo))
                        .thenReturn(true)
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 更新设备类型
     */
    private void updatePositionAppType(HdsDevicePositionEntity entity, Integer sortOrder, HeaderUtils.HeaderInfo headerInfo) {
        entity.setAppTypeSortOrder(sortOrder);
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setUpdatedBy(headerInfo.getUserId());
        entity.setUpdatedByName(headerInfo.getUsername());
    }

    /**
     * 处理排序更新成功
     */
    private void handleSortSuccess(List<HdsDevicePositionEntity> entities, DevicePositionAppTypeDragSortReq req,
                                   HeaderUtils.HeaderInfo headerInfo) {
//        entities.forEach(entity -> {
//            // 记录操作日志
//            operationLogUtil.recordSuccess(
//                    BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                    BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                    entity.getId(),
//                    entity.getDeviceAppType(),
//                    OperationTypeEnum.UPDATE.getCode(),
//                    String.format("更新设备类型排序成功: %s-%s", entity.getId(), entity.getDeviceAppType()),
//                    req,
//                    headerInfo
//            ).subscribe();
//
//            log.info("设备类型排序{}成功: {}", OperationTypeEnum.UPDATE.getDesc(), BussinessTypeEnum.DEVICE_POSITION.getBusinessType());
//        });
    }

    /**
     * 处理排序更新错误
     */
    private void handleSortError(Throwable error, DevicePositionAppTypeDragSortReq req,
                                 HeaderUtils.HeaderInfo headerInfo) {
//        log.error("更新设备类型排序失败: {}", error.getMessage());
//
//        operationLogUtil.recordFail(
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                BussinessTypeEnum.DEVICE_POSITION.getBusinessType(),
//                null,
//                req.getDevicePositionAppTypes().stream()
//                        .map(DevicePositionAppTypeDragSortReq.DevicePositionAppTypeSortReq::getDeviceAppType)
//                        .collect(Collectors.joining(",")),
//                OperationTypeEnum.UPDATE.getCode(),
//                String.format("更新设备类型排序失败: %s", req.getDevicePositionAppTypes()),
//                req,
//                error.getMessage(),
//                headerInfo
//        ).subscribe();
    }

    public Mono<HdsDevicePositionEntity> findByPositionCode(String positionCode) {
        return devicePositionRepository.findByPositionCode(positionCode)
                .defaultIfEmpty(new HdsDevicePositionEntity());
    }

    /**
     * 获取设备位置树形结构
     * 返回楼栋->楼层->位置的三级树形结构
     *
     * @return Mono<DevicePositionTreeVO> 楼栋树形结构
     */
    public Mono<DevicePositionTreeVO> getPositionTreeStructure() {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 设置查询条件，只查询当前酒店的设备位置
                    Criteria criteria = Criteria.where(hotel_code.name()).is(headerInfo.getHotelCode());
                    Query query = Query.query(criteria);

                    return r2dbcEntityTemplate.select(query, HdsDevicePositionEntity.class)
                            .collectList()
                            .map(entities -> {
                                DevicePositionTreeVO result = new DevicePositionTreeVO();

                                // 按楼栋分组并构建树形结构
                                Map<String, List<HdsDevicePositionEntity>> blockMap = entities.stream()
                                        .collect(Collectors.groupingBy(
                                                HdsDevicePositionEntity::getBlock,
                                                Collectors.toList()
                                        ));

                                List<DevicePositionTreeVO.BlockNode> blockNodes = new ArrayList<>();

                                for (Map.Entry<String, List<HdsDevicePositionEntity>> blockEntry : blockMap.entrySet()) {
                                    String block = blockEntry.getKey();
                                    List<HdsDevicePositionEntity> blockEntities = blockEntry.getValue();

                                    // 创建楼栋节点
                                    DevicePositionTreeVO.BlockNode blockNode = new DevicePositionTreeVO.BlockNode();

                                    blockNode.setKey(generateShortUuid());
                                    blockNode.setName(block);

                                    // 构建区域节点列表
                                    // 按区域/楼层分组
                                    Map<String, List<HdsDevicePositionEntity>> areaMap = blockEntities.stream()
                                            .collect(Collectors.groupingBy(
                                                    HdsDevicePositionEntity::getArea,
                                                    Collectors.toList()
                                            ));

                                    List<DevicePositionTreeVO.AreaNode> areaNodes = new ArrayList<>();

                                    for (Map.Entry<String, List<HdsDevicePositionEntity>> areaEntry : areaMap.entrySet()) {
                                        String area = areaEntry.getKey();
                                        List<HdsDevicePositionEntity> areaEntities = areaEntry.getValue();

                                        // 创建区域节点
                                        DevicePositionTreeVO.AreaNode areaNode = new DevicePositionTreeVO.AreaNode();

                                        areaNode.setKey(generateShortUuid());
                                        areaNode.setName(block + "-" + area);

                                        // 构建位置节点列表
                                        // 创建位置列表
                                        List<DevicePositionTreeVO.PositionNode> positionNodes = areaEntities.stream()
                                                .map(entity -> {
                                                    DevicePositionTreeVO.PositionNode node = new DevicePositionTreeVO.PositionNode();
                                                    node.setKey(entity.getPositionCode());
                                                    node.setName(block + "-" + area + "-" + entity.getPositionName());
                                                    node.setLeaf(true);
                                                    return node;
                                                })
                                                .sorted(Comparator.comparing(
                                                        DevicePositionTreeVO.PositionNode::getName,
                                                        Comparator.nullsLast(Comparator.naturalOrder())
                                                ))
                                                .collect(Collectors.toList());

                                        areaNode.setChildren(positionNodes);
                                        areaNodes.add(areaNode);
                                    }

                                    // 区域排序逻辑 - 使用提取的通用排序方法
                                    areaNodes.sort(this::compareByNameWithNumberPriority);

                                    blockNode.setChildren(areaNodes);
                                    blockNodes.add(blockNode);
                                }

                                // 楼栋排序逻辑 - 使用相同的通用排序方法
                                blockNodes.sort(this::compareByNameWithNumberPriority);

                                result.setBlocks(blockNodes);
                                return result;
                            });
                });
    }

    /**
     * 通用的比较方法，优先按数字排序，再按字符串排序
     * 适用于包含name属性的任何对象
     *
     * @param o1 第一个对象
     * @param o2 第二个对象
     * @return 比较结果
     */
    private <T> int compareByNameWithNumberPriority(T o1, T o2) {
        String name1 = null;
        String name2 = null;

        // 使用反射获取name属性值
        try {
            Method getNameMethod = o1.getClass().getMethod("getName");
            name1 = (String) getNameMethod.invoke(o1);
            name2 = (String) getNameMethod.invoke(o2);
        } catch (Exception e) {
            // 反射失败则比较字符串形式
            return o1.toString().compareTo(o2.toString());
        }

        // 尝试提取数字部分
        Integer num1 = extractNumber(name1);
        Integer num2 = extractNumber(name2);

        // 如果两者都有数字前缀，按数字排序
        if (num1 != null && num2 != null) {
            return num1.compareTo(num2);
        }

        // 否则按字符串排序
        return name1.compareTo(name2);
    }

    /**
     * 生成简短的UUID
     * 取UUID的前8位作为简短标识
     *
     * @return 简短UUID字符串
     */
    private String generateShortUuid() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

    /**
     * 从字符串中提取数字
     * 例如："1楼" -> 1, "楼栋2" -> 2, "A区" -> null
     *
     * @param text 输入字符串
     * @return 提取的数字，如果没有则返回null
     */
    private Integer extractNumber(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }

        // 匹配字符串中的数字部分
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+");
        java.util.regex.Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            try {
                return Integer.parseInt(matcher.group());
            } catch (NumberFormatException e) {
                return null;
            }
        }

        return null;
    }
}
