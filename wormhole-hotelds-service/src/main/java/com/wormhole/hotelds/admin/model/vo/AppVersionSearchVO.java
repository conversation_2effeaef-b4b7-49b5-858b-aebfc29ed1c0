package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.HdsAppVersionsEntity;
import com.wormhole.hotelds.core.model.entity.HdsBrandEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/5/7 17:38
 * @Description：AppVersionSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppVersionSearchVO {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 渠道
     */
    private String platform;

    /**
     * 版本号
     */
    private String appVersion;

    /**
     * 应用（如hotel_admin / hotel_guest等等）
     */
    private String appCode;

    /**
     * 版本内容
     */
    private String updateContent;

    /**
     * 上线时间
     */
    private String onlineTime;

    /**
     * 是否强制更新（1是 0否）
     */
    private Integer isForce;

    /**
     * 下载地址（Android 为 APK 地址，iOS 可填 App Store 链接）
     */
    private String downloadUrl;

    /**
     * 状态：1 上架 0 下架
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 转换为视图对象
     */
    public static AppVersionSearchVO toVo(HdsAppVersionsEntity entity) {
        AppVersionSearchVO appVersionSearchVO = new AppVersionSearchVO();
        BeanUtils.copyProperties(entity, appVersionSearchVO);
        appVersionSearchVO.setOnlineTime(SimpleDateUtils.formatLocalDateTimeToDate(entity.getOnLineTime()));
        appVersionSearchVO.setCreateTime(SimpleDateUtils.formatLocalDateTimeToDate(entity.getCreatedAt()));
        return appVersionSearchVO;
    }
}
