package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.admin.model.enums.TaskStatusEnum;
import com.wormhole.hotelds.core.model.entity.HdsTaskEntity;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/3/31 11:01
 * @Description：TaskEntityVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TaskVO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * @see TaskStatusEnum
     */
    private Integer status;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 结果
     */
    private String result;

    /**
     * 错误摘要
     */
    private String errorSummary;

    /**
     * 错误信息
     */
    private String errorDetails;

    public static TaskVO toVo(HdsTaskEntity entity) {
        TaskVO taskVO = new TaskVO();
        BeanUtils.copyProperties(entity, taskVO);
        taskVO.setResult(entity.getMessage());
        return taskVO;
    }
}
