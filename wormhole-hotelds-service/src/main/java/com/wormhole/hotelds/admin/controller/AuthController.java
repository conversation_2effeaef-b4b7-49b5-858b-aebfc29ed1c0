package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.model.req.GetAuthInfoReq;
import com.wormhole.hotelds.admin.model.vo.AuthVO;
import com.wormhole.hotelds.admin.service.*;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @author: joker.liu
 * @date: 2025/2/28
 * @Description: 用户认证控制器
 */
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Resource
    private AuthService authService;

    /**
     * 获取用户信息接口
     * @return 用户认证信息
     */
    @PostMapping("/info")
    public Mono<Result<AuthVO>> getAuthInfo(@RequestBody(required = false) GetAuthInfoReq getAuthInfoReq,
                                            @RequestHeader(value = "DeviceType", required = false) String deviceType,
                                            @RequestHeader(value = "DeviceNumber", required = false) String deviceNumber) {
        if (getAuthInfoReq == null) getAuthInfoReq = new GetAuthInfoReq();
        // 运营后台deviceType为空
        getAuthInfoReq.setDeviceType(deviceType);
        getAuthInfoReq.setDeviceId(deviceNumber);
        return authService.getAuthInfo(getAuthInfoReq).flatMap(Result::success);
    }

}
