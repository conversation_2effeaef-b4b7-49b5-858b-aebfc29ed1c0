package com.wormhole.hotelds.admin.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;

/**
 * @Author: flx
 * @Date: 2025-03-27 18:00:08
 * @Description: HdsPayRecord
 */
@Data
@Table("hds_pay_record")
@Accessors(chain = true)
@EqualsAndHashCode
public class HdsPayRecordEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

}