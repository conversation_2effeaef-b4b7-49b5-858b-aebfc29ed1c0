package com.wormhole.hotelds.admin.service;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.vo.OrderConfirmVO;
import com.wormhole.hotelds.admin.repository.HdsOrderDetailRepository;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Author：flx
 * @Date：2025/6/25 15:27
 * @Description：OrderDetailService
 */
@Service
@Slf4j
public class OrderDetailService {

    @Resource
    private HdsOrderDetailRepository hdsOrderDetailRepository;

    /**
     * 创建订单详情
     * @param order
     * @param orderConfirmVO
     * @param packageEntity
     * @param productEntity
     * @param pricingEntity
     * @param hotelPackageOpt
     * @param hotelPricingOpt
     * @return
     */
    public Mono<HdsOrderDetailEntity> createOrderDetail(HdsOrderEntity order,
                                                        OrderConfirmVO orderConfirmVO,
                                                        HdsPackageEntity packageEntity,
                                                        HdsProductEntity productEntity,
                                                        HdsProductPricingEntity pricingEntity,
                                                        Optional<HdsHotelPackageEntity> hotelPackageOpt,
                                                        Optional<HdsHotelProductPricingEntity> hotelPricingOpt) {


        HdsOrderDetailEntity detailEntity = new HdsOrderDetailEntity()
                .setOrderNo(order.getOrderNo())
                .setPackageInfo(JacksonUtils.writeValueAsString(packageEntity))
                .setProductInfo(JacksonUtils.writeValueAsString(productEntity))
                .setPriceInfo(JacksonUtils.writeValueAsString(pricingEntity))
                .setHotelPackageInfo(hotelPackageOpt.map(JacksonUtils::writeValueAsString).orElse(null))
                .setHotelPriceInfo(hotelPricingOpt.map(JacksonUtils::writeValueAsString).orElse(null))
                .setOrderConfirmInfo(JacksonUtils.writeValueAsString(orderConfirmVO));
        detailEntity.setRowStatus(1);

        log.info("订单详情创建成功: orderNo={}", order.getOrderNo());
        return hdsOrderDetailRepository.save(detailEntity);
    }
}
