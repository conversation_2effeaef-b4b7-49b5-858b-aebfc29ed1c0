package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/3/26 14:03
 * @Description：RoomSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RoomSaveReq {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 房间号
     */
    private String roomNum;

    /**
     * 酒店ID
     */
    private Integer hotelId;

    /**
     * 楼层
     */
    private Integer floor;

    /**
     * 房间类型
     */
    private String roomTypeName;

    /**
     * WiFi SSID
     */
    private String wifiSsid;

    /**
     * WiFi密码
     */
    private String wifiPassword;

    /**
     * 状态
     */
    private Integer status;
}
