package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/4/11 15:07
 * @Description：DevicePositionSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DevicePositionSearchReq extends QueryCondition {

    private String hotelCode;

    /**
     * 楼栋
     */
    private String block;

    /**
     * 区域
     */
    private String area;

    /**
     * 位置
     */
    private String positionName;

    /**
     * 位置
     */
    private String deviceAppType;
}
