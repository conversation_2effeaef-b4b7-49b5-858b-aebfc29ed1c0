package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.admin.model.enums.DeviceEventTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceLogEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/4/10 11:30
 * @Description：DeviceLogVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceLogVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件时间
     */
    private String eventTime;

    public static DeviceLogVO toVo(HdsDeviceLogEntity hdsDeviceLogEntity) {
        DeviceLogVO deviceLogVO = new DeviceLogVO();
        BeanUtils.copyProperties(hdsDeviceLogEntity, deviceLogVO);
        deviceLogVO.setEventTime(SimpleDateUtils.formatLocalDateTimeToDateHour(hdsDeviceLogEntity.getEventTime()));
        return deviceLogVO;
    }
}
