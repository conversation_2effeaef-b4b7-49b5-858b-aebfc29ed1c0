package com.wormhole.hotelds.admin.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/4/3 14:30
 * @Description：设备类别枚举
 */
@Getter
@AllArgsConstructor
public enum DeviceCategoryEnum {
    
    /**
     * 平板设备
     */
    PAD("0", "平板"),
    
    /**
     * 智能音箱
     */
    SPEAKER("1", "音箱"),
    
    /**
     * 电话
     */
    PHONE("2", "电话"),
    
    /**
     * 空调
     */
    AIR_CONDITIONER("3", "空调"),
    
    /**
     * 电视
     */
    TV("4", "电视"),
    
    /**
     * 门锁
     */
    DOOR_LOCK("5", "门锁"),
    
    /**
     * 照明设备
     */
    LIGHTING("6", "照明"),
    
    /**
     * 窗帘
     */
    CURTAIN("7", "窗帘"),
    
    /**
     * 传感器
     */
    SENSOR("8", "传感器"),
    
    /**
     * 其他设备
     */
    OTHER("9", "其他");
    
    /**
     * 类别编码
     */
    private final String code;
    
    /**
     * 类别名称
     */
    private final String name;
    
    /**
     * 代码映射表，用于提高查找效率
     */
    private static final Map<String, DeviceCategoryEnum> CODE_MAP = 
            Arrays.stream(DeviceCategoryEnum.values())
                  .collect(Collectors.toMap(DeviceCategoryEnum::getCode, Function.identity()));
    
    /**
     * 根据类别编码获取枚举实例
     * @param code 类别编码
     * @return 枚举实例或null
     */
    public static DeviceCategoryEnum getByCode(String code) {
        return CODE_MAP.get(code);
    }
    
    /**
     * 检查编码是否有效
     * @param code 类别编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return CODE_MAP.containsKey(code);
    }
    
    /**
     * 用于JSON序列化，返回编码值
     * @return 编码值
     */
    @JsonValue
    public String getValue() {
        return this.code;
    }
    
    /**
     * 用于JSON反序列化，根据编码值创建枚举实例
     * @param code 编码值
     * @return 枚举实例
     */
    @JsonCreator
    public static DeviceCategoryEnum create(String code) {
        DeviceCategoryEnum result = getByCode(code);
        if (result == null) {
            throw new IllegalArgumentException("无效的设备类别编码: " + code);
        }
        return result;
    }
    
    @Override
    public String toString() {
        return this.code + ":" + this.name;
    }
}