package com.wormhole.hotelds.admin.aop;

import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/5/7 11:22
 * @Description：LoggingContext
 */
@Data
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class LoggingContext {

    /**
     * 请求头信息
     */
    private HeaderUtils.HeaderInfo headerInfo;

    /**
     * 业务类型
     */
    private BussinessTypeEnum businessType;

    /**
     * 操作类型
     */
    private OperationTypeEnum operationType;

    /**
     * 请求参数
     */
    private Object oprParams;

    /**
     * 业务类型id，一般是主键id
     */
    private Long businessId;

    /**
     * 请求方法名
     */
    private String methodName;

    private LocalDateTime oprTime;

    /**
     * 执行时间
     */
    private long executionTime;

    /**
     * 操作描述
     */
    private String oprContent;

    /**
     * 操作结果
     */
    private String oprResult;

    /**
     * 操作前对象
     */
    private Object beforeObj;

    /**
     * 操作后对象
     */
    private Object afterObj;

    /**
     * 错误信息
     */
    private String errorMsg;
}
