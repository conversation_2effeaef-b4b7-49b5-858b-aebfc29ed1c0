package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-26 19:30:00
 * @Description: 发票申请取消请求
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceApplicationCancelReq {

    /**
     * 发票申请ID（必填）
     */
    private Long id;
} 