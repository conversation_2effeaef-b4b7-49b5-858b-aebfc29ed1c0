package com.wormhole.hotelds.admin.service;

import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.AgentFlowTypeEnum;
import com.wormhole.hotelds.admin.model.enums.AiProductTypeEnum;
import com.wormhole.hotelds.admin.model.enums.StatusEnum;
import com.wormhole.hotelds.admin.model.req.AgentTimeFlowSearchReq;
import com.wormhole.hotelds.admin.model.req.DeviceModelSearchReq;
import com.wormhole.hotelds.admin.model.req.HotelSaveReq;
import com.wormhole.hotelds.admin.model.vo.AgentTimeFlowSearchVO;
import com.wormhole.hotelds.admin.model.vo.DeviceModelSearchVO;
import com.wormhole.hotelds.admin.model.vo.DevicePositionSearchVO;
import com.wormhole.hotelds.admin.repository.AgentTimeFlowRepository;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/5/21 10:22
 * @Description：agentTimeFlowService
 */
@Slf4j
@Service
public class AgentTimeFlowService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private AgentTimeFlowRepository agentTimeFlowRepository;

    /**
     * @param agentTimeFlowSearchReq
     * @return
     */
    public Mono<Long> searchCount(AgentTimeFlowSearchReq agentTimeFlowSearchReq) {
        Criteria baseCriteria = buildBaseCriteria(agentTimeFlowSearchReq);
        Query query = Query.query(baseCriteria);
        return r2dbcEntityTemplate.count(query, HdsAgentTimeFlowEntity.class);
    }


    /**
     * 搜索
     *
     * @param agentTimeFlowSearchReq
     * @return
     */
    public Mono<List<AgentTimeFlowSearchVO>> search(AgentTimeFlowSearchReq agentTimeFlowSearchReq) {
        Criteria criteria = buildBaseCriteria(agentTimeFlowSearchReq);
        Sort sort = Sort.by(Sort.Direction.DESC, HdsAgentTimeFlowFieldEnum.created_at.name());
        Query query = Query.query(criteria)
                .sort(sort)
                .limit(agentTimeFlowSearchReq.getPageSize())
                .offset((long) (agentTimeFlowSearchReq.getCurrent() - 1) * agentTimeFlowSearchReq.getPageSize());

        return r2dbcEntityTemplate.select(query, HdsAgentTimeFlowEntity.class)
                .collectList()
                .map(hdsAgentTimeFlowEntities -> hdsAgentTimeFlowEntities.stream()
                        .map(AgentTimeFlowSearchVO::toVo)
                        .toList());
    }

    private Criteria buildBaseCriteria(AgentTimeFlowSearchReq agentTimeFlowSearchReq) {
        Criteria criteria = Criteria.empty();
        if (StringUtils.isNotBlank(agentTimeFlowSearchReq.getHotelCode())) {
            criteria = criteria.and(HdsAgentTimeFlowFieldEnum.hotel_code.name()).is(agentTimeFlowSearchReq.getHotelCode());
        }
        return criteria;
    }

    /**
     * 获取或准备保存的代理时间流水实体
     *
     * @param req               酒店保存请求
     * @param existingHotelMono 现有酒店实体Mono（更新场景下使用，创建场景为null）
     * @param headerInfo        请求头信息
     * @param isCreate          是否为创建场景
     * @return 代理时间流水实体Mono
     */
    public Mono<HdsAgentTimeFlowEntity> getOrPrepareSaveAgentTimeFlow(HotelSaveReq req, Mono<HdsHotelInfoEntity> existingHotelMono, HeaderUtils.HeaderInfo headerInfo, boolean isCreate) {
        if (CollectionUtils.isEmpty(req.getAiProductTypes()) || !req.getAiProductTypes().contains(AiProductTypeEnum.OTA_AGENT.getCode())) {
            return Mono.just(new HdsAgentTimeFlowEntity());
        }

        Integer otaExtendMonths = req.getOtaExtendMonths();

        // 创建场景：直接创建初始赠送流水
        if (isCreate) {
            return createInitialGiftEntity(otaExtendMonths, headerInfo);
        }
        // 更新场景：根据与现有值的比较创建更新流水
        else {
            if (Objects.isNull(req.getOtaExtendMonths())) {
                return Mono.just(new HdsAgentTimeFlowEntity());
            }
            return createUpdateTimeFlowEntity(existingHotelMono, otaExtendMonths, headerInfo);
        }
    }

    /**
     * 创建初始赠送流水实体
     *
     * @param otaExtendMonths 初始延期月数
     * @param headerInfo      请求头信息
     * @return 初始赠送流水实体
     */
    public static Mono<HdsAgentTimeFlowEntity> createInitialGiftEntity(Integer otaExtendMonths, HeaderUtils.HeaderInfo headerInfo) {
        HdsAgentTimeFlowEntity entity = createBaseEntity(headerInfo);

        // 设置流水类型为初始赠送
        entity.setFlowType(AgentFlowTypeEnum.INITIAL_GIFT.getCode()); // 1-初始赠送
        entity.setOperationType(1); // 1-增加

        // 设置月数相关字段
        entity.setMonthsChanged(1);
        entity.setOtaExtendMonthsBefore(0);
        entity.setOtaExtendMonthsAfter(otaExtendMonths);
        entity.setOtaRewardMonthsBefore(0);
        entity.setOtaRewardMonthsAfter(0);

        return Mono.just(entity);
    }

    /**
     * 创建基础的时间流水实体对象
     *
     * @param headerInfo 请求头信息（用于设置审计字段）
     * @return 基础配置的时间流水实体
     */
    public static HdsAgentTimeFlowEntity createBaseEntity(HeaderUtils.HeaderInfo headerInfo) {
        HdsAgentTimeFlowEntity entity = new HdsAgentTimeFlowEntity();

        // 设置OTA类型
        entity.setType(0);

        // 设置审计字段
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setRowStatus(1);

        return entity;
    }

    /**
     * 创建更新时间流水实体
     */
    private Mono<HdsAgentTimeFlowEntity> createUpdateTimeFlowEntity(Mono<HdsHotelInfoEntity> existingHotelMono, Integer otaExtendMonths, HeaderUtils.HeaderInfo headerInfo) {
        return existingHotelMono.flatMap(existingHotel -> {
            // 获取当前值
            Integer currentExtendMonths = existingHotel.getOtaExtendMonths();

            // 如果延期月数没有变化，返回空实体
            if (currentExtendMonths != null && currentExtendMonths.equals(otaExtendMonths)) {
                return Mono.just(createEmptyEntity());
            }

            LocalDateTime expirationBefore = existingHotel.getCreatedAt();
            if (Objects.nonNull(existingHotel.getOtaExtendMonths())) {
                expirationBefore = expirationBefore.plusMonths(existingHotel.getOtaExtendMonths());
            }
            if (Objects.nonNull(existingHotel.getOtaRewardMonths())) {
                expirationBefore = expirationBefore.plusMonths(existingHotel.getOtaRewardMonths());
            }
            return Mono.just(createAdminAdjustEntity(
                    expirationBefore,
                    existingHotel.getHotelCode(),
                    currentExtendMonths,
                    otaExtendMonths,
                    headerInfo
            ));
        });
    }

    /**
     * 创建管理员调整流水实体
     *
     * @param hotelCode           酒店编码
     * @param currentExtendMonths 当前延期月数
     * @param newExtendMonths     新的延期月数
     * @param headerInfo          请求头信息
     * @return 管理员调整流水实体
     */
    public static HdsAgentTimeFlowEntity createAdminAdjustEntity(
            LocalDateTime expirationBefore,
            String hotelCode,
            Integer currentExtendMonths,
            Integer newExtendMonths,
            HeaderUtils.HeaderInfo headerInfo) {

        HdsAgentTimeFlowEntity entity = createBaseEntity(headerInfo);

        // 设置基本信息
        entity.setHotelCode(hotelCode);
        entity.setFlowType(AgentFlowTypeEnum.ADMIN_ADJUSTMENT.getCode()); // 4-管理员调整

        // 计算变更情况
        int oldValue = currentExtendMonths != null ? currentExtendMonths : 0;
        int newValue = newExtendMonths != null ? newExtendMonths : 0;

        // 确定是增加还是减少
        boolean isIncrease = newValue > oldValue;
        entity.setOperationType(isIncrease ? 1 : 2); // 1-增加, 2-减少

        // 设置变更月数（绝对值）
        entity.setMonthsChanged(newExtendMonths);

        // 设置修改前后的值
        entity.setOtaExtendMonthsBefore(oldValue);
        entity.setOtaExtendMonthsAfter(newValue);

        entity.setExpirationBefore(expirationBefore);
        entity.setExpirationAfter(expirationBefore.plusMonths(newValue));
        return entity;
    }

    /**
     * 创建空的流水实体（用于无需记录流水的场景）
     *
     * @return 空的流水实体
     */
    public static HdsAgentTimeFlowEntity createEmptyEntity() {
        return new HdsAgentTimeFlowEntity();
    }

    public Mono<HdsAgentTimeFlowEntity> save(HdsAgentTimeFlowEntity hdsAgentTimeFlowEntity) {
        return agentTimeFlowRepository.save(hdsAgentTimeFlowEntity);
    }
}
