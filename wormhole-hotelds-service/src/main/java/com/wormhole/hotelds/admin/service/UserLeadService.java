package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.req.UserLeadSaveReq;
import com.wormhole.hotelds.admin.model.req.UserLeadSearchReq;
import com.wormhole.hotelds.admin.model.vo.UserLeadSearchVO;
import com.wormhole.hotelds.admin.repository.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.CodePoolManager;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/5/20 10:31
 * @Description：MerchantLeadService
 */
@Slf4j
@Service
public class UserLeadService {

    @Resource
    private UserLeadRepository userLeadRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;

    @Resource
    private InvitationCodeRepository invitationCodeRepository;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private UserHotelLeadMappingRepository userHotelLeadMappingRepository;

    @Resource
    private HotelLeadRepository hotelLeadRepository;


    /**
     * 创建用户线索
     *
     * @param req 用户线索保存请求
     * @return 创建结果
     */
    public Mono<Boolean> create(UserLeadSaveReq req) {
        // 参数验证
        Preconditions.checkArgument(Objects.nonNull(req), "请求参数不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getInviteeId()), "被邀请人ID不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getInviteCode()), "邀请码不能为空");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 1. 先检查用户线索是否已存在
                    return userLeadRepository.existsByInviteeId(req.getInviteeId())
                            .flatMap(exists -> {
                                if (Boolean.TRUE.equals(exists)) {
                                    return Mono.just(Boolean.TRUE);
                                } else {
                                    // 2. 如果不存在，继续创建流程
                                    return invitationCodeRepository.findByInvitationCode(req.getInviteCode())
                                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "邀请码不存在")))
                                            // 3. 获取线索编码
                                            .flatMap(invitationCode -> codePoolManager.getCodeFromPool(BussinessTypeEnum.USER_LEAD.getBusinessType())
                                                    // 4. 构建用户线索实体
                                                    .map(leadCode -> buildUserLeadEntity(leadCode, invitationCode, req, headerInfo))
                                                    // 5. 保存用户线索
                                                    .flatMap(userLeadEntity -> saveUserLead(userLeadEntity, headerInfo))
                                            );
                                }
                            });
                });
    }

    /**
     * 构建用户线索实体
     */
    private HdsUserLeadEntity buildUserLeadEntity(String leadCode, HdsInvitationCodeEntity invitationCode,
                                                  UserLeadSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        HdsUserLeadEntity entity = new HdsUserLeadEntity();
        entity.setLeadCode(leadCode);
        entity.setInviterId(Integer.valueOf(invitationCode.getCreatedBy()));
        entity.setInviteeId(Integer.valueOf(headerInfo.getUserId()));
        entity.setInviteCode(req.getInviteCode());
        entity.setStatus(0);
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setRowStatus(1);
        return entity;
    }

    /**
     * 保存用户线索实体
     */
    private Mono<Boolean> saveUserLead(HdsUserLeadEntity entity, HeaderUtils.HeaderInfo headerInfo) {
        return userLeadRepository.save(entity)
                .as(transactionalOperator::transactional)
                .doOnSuccess(savedEntity -> log.info("创建用户线索成功: leadCode={}, inviterId={}, inviteeId={}, inviteCode={}",
                        savedEntity.getLeadCode(), savedEntity.getInviterId(), savedEntity.getInviteeId(), savedEntity.getInviteCode()))
                .map(savedEntity -> true)
                .onErrorResume(error -> {
                    log.error("创建用户线索失败: {}", error.getMessage(), error);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建用户线索失败: " + error.getMessage()));
                });
    }

    /**
     * 统计商户线索数量
     */
    public Mono<Long> searchCount(UserLeadSearchReq req) {
        return buildSearchCriteria(req).flatMap(criteria -> {
            Query query = Query.query(criteria);
            return r2dbcEntityTemplate.count(query, HdsUserLeadEntity.class);
        });
    }

    /**
     * 搜索商户线索列表
     */
    public Mono<List<UserLeadSearchVO>> search(UserLeadSearchReq req) {
        return buildSearchCriteria(req).flatMap(criteria -> {
            // 设置排序方式，默认按创建时间降序
            Sort sort = Sort.by(Sort.Direction.DESC, HdsUserLeadFieldEnum.created_at.name());


            // 构建查询对象，设置分页参数
            Query query = Query.query(criteria)
                    .sort(sort)
                    .limit(req.getPageSize())
                    .offset((long) (req.getCurrent() - 1) * req.getPageSize());

            // 执行查询
            return r2dbcEntityTemplate.select(query, HdsUserLeadEntity.class)
                    .collectList()
                    .flatMap(this::enrichUserLeadEntities);
        });
    }

    /**
     * 构建搜索条件
     */
    private Mono<Criteria> buildSearchCriteria(UserLeadSearchReq req) {
        Criteria baseCriteria = Criteria.empty();
        // 基础条件
//        Criteria baseCriteria = buildBaseCriteria(req);

        if (Objects.nonNull(req.getStatus())) {
            return filterByStatus(req, baseCriteria)
                    .flatMap(criteria -> preFilterByContactPhone(req, criteria));
        }
        return preFilterByContactPhone(req, baseCriteria);
    }

    private Criteria buildBaseCriteria(UserLeadSearchReq req) {
        Criteria criteria = Criteria.empty();
        return criteria;
    }

    private Mono<Criteria> filterByStatus(UserLeadSearchReq req, Criteria baseCriteria) {
        // 如果查询有效状态(ebkCount > 0)
        if (Objects.equals(req.getStatus(), 1)) {
            return userHotelLeadMappingRepository.findAll()
                    .groupBy(HdsUserHotelLeadMappingEntity::getUserLeadId)
                    .flatMap(group -> Mono.just(group.key()))
                    .collectList()
                    .map(userLeadIds -> {
                        if (userLeadIds.isEmpty()) {
                            // 如果没有关联记录，返回一个不可能匹配的条件
                            return baseCriteria.and(HdsUserLeadFieldEnum.id.name()).is(-1L);
                        }
                        return baseCriteria.and(HdsUserLeadFieldEnum.id.name()).in(userLeadIds);
                    });
        }
        // 如果查询无效状态(ebkCount = 0)
        else if (Objects.equals(req.getStatus(), 0)) {
            return userHotelLeadMappingRepository.findAll()
                    .groupBy(HdsUserHotelLeadMappingEntity::getUserLeadId)
                    .flatMap(group -> Mono.just(group.key()))
                    .collectList()
                    .map(userLeadIds -> {
                        if (userLeadIds.isEmpty()) {
                            // 如果没有关联记录，所有记录都是无效的
                            return baseCriteria;
                        }
                        return baseCriteria.and(HdsUserLeadFieldEnum.id.name()).notIn(userLeadIds);
                    });
        }

        return Mono.just(baseCriteria);
    }

    private Mono<Criteria> preFilterByContactPhone(UserLeadSearchReq req, Criteria baseCriteria) {
        if (StringUtils.isBlank(req.getMobile())) {
            return Mono.just(baseCriteria);
        }
        Criteria employeeCriteria = Criteria.where(HdsEmployeeFieldEnum.mobile.name()).is(req.getMobile());

        return r2dbcEntityTemplate.select(HdsEmployeeEntity.class)
                .matching(Query.query(employeeCriteria))
                .all()
                .map(HdsEmployeeEntity::getId)
                .collectList()
                .map(employeeIds -> {
                    if (CollectionUtils.isEmpty(employeeIds)) {
                        return Criteria.where(HdsUserLeadFieldEnum.invitee_id.name()).is("-1");
                    }
                    return baseCriteria.and(HdsUserLeadFieldEnum.invitee_id.name()).in(employeeIds);
                });
    }

    /**
     * 丰富用户线索实体，添加关联信息
     */
    private Mono<List<UserLeadSearchVO>> enrichUserLeadEntities(List<HdsUserLeadEntity> entities) {
        // 如果没有查询到数据，直接返回空列表
        if (entities.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }

        // 1. 提取用户线索ID和员工ID
        Set<Long> userLeadIds = extractUserLeadIds(entities);
        Set<Integer> employeeIds = extractEmployeeIds(entities);

        // 2. 并行查询关联数据
        Mono<Map<Integer, HdsEmployeeEntity>> employeeMapMono = fetchEmployeeMap(employeeIds);
        Mono<Map<Long, Set<Long>>> userHotelLeadMapMono = fetchUserHotelLeadMap(userLeadIds);
        Mono<Map<Long, HdsHotelLeadEntity>> hotelLeadMapMono = fetchHotelLeadMap(userHotelLeadMapMono);

        // 3. 组合数据并构建VO
        return combineDataAndBuildVOs(entities, employeeMapMono, userHotelLeadMapMono, hotelLeadMapMono);
    }

    /**
     * 提取用户线索ID集合
     */
    private Set<Long> extractUserLeadIds(List<HdsUserLeadEntity> entities) {
        return entities.stream()
                .map(HdsUserLeadEntity::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 提取员工ID集合（包括邀请人和被邀请人）
     */
    private Set<Integer> extractEmployeeIds(List<HdsUserLeadEntity> entities) {
        Set<Integer> employeeIds = new HashSet<>();

        // 添加邀请人ID
        entities.stream()
                .map(HdsUserLeadEntity::getInviterId)
                .filter(Objects::nonNull)
                .forEach(employeeIds::add);

        // 添加被邀请人ID
        entities.stream()
                .map(HdsUserLeadEntity::getInviteeId)
                .filter(Objects::nonNull)
                .forEach(employeeIds::add);

        return employeeIds;
    }

    /**
     * 获取员工信息映射
     */
    private Mono<Map<Integer, HdsEmployeeEntity>> fetchEmployeeMap(Set<Integer> employeeIds) {
        if (employeeIds.isEmpty()) {
            return Mono.just(Collections.emptyMap());
        }

        return hdsEmployeeRepository.findByIdIn(employeeIds)
                .collectList()
                .map(employees -> employees.stream()
                        .collect(Collectors.toMap(
                                HdsEmployeeEntity::getId,
                                employee -> employee,
                                (e1, e2) -> e1
                        )))
                .defaultIfEmpty(Collections.emptyMap())
                .onErrorResume(e -> {
                    log.error("获取员工信息失败", e);
                    return Mono.just(Collections.emptyMap());
                });
    }

    /**
     * 获取用户-门店线索映射关系
     */
    private Mono<Map<Long, Set<Long>>> fetchUserHotelLeadMap(Set<Long> userLeadIds) {
        if (userLeadIds.isEmpty()) {
            return Mono.just(Collections.emptyMap());
        }

        return userHotelLeadMappingRepository.findByUserLeadIdIn(userLeadIds)
                .collectList()
                .flatMap(mappings -> {
                    // 获取所有门店线索ID
                    Set<Long> hotelLeadIds = mappings.stream()
                            .map(HdsUserHotelLeadMappingEntity::getHotelLeadId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());

                    if (hotelLeadIds.isEmpty()) {
                        return Mono.just(Collections.<Long, Set<Long>>emptyMap());
                    }

                    // 查询存在的门店线索
                    return hotelLeadRepository.findAllByIdIn(hotelLeadIds)
                            .map(HdsHotelLeadEntity::getId)
                            .collectList()
                            .map(existingHotelLeadIds -> {
                                Set<Long> validHotelLeadIds = new HashSet<>(existingHotelLeadIds);
                                return createUserHotelLeadMap(mappings, validHotelLeadIds);
                            });
                })
                .defaultIfEmpty(Collections.emptyMap())
                .onErrorResume(e -> {
                    log.error("获取用户-门店线索映射关系失败", e);
                    return Mono.just(Collections.emptyMap());
                });
    }

    /**
     * 创建用户-门店线索映射
     */
    private Map<Long, Set<Long>> createUserHotelLeadMap(List<HdsUserHotelLeadMappingEntity> mappings, Set<Long> validHotelLeadIds) {
        Map<Long, Set<Long>> userHotelLeadMap = new HashMap<>();

        for (HdsUserHotelLeadMappingEntity mapping : mappings) {
            if (mapping.getUserLeadId() != null && mapping.getHotelLeadId() != null
                    && validHotelLeadIds.contains(mapping.getHotelLeadId())) {
                userHotelLeadMap
                        .computeIfAbsent(mapping.getUserLeadId(), k -> new HashSet<>())
                        .add(mapping.getHotelLeadId());
            }
        }

        return userHotelLeadMap;
    }

    /**
     * 获取门店线索信息映射
     */
    private Mono<Map<Long, HdsHotelLeadEntity>> fetchHotelLeadMap(Mono<Map<Long, Set<Long>>> userHotelLeadMapMono) {
        return userHotelLeadMapMono
                .flatMap(userHotelLeadMap -> {
                    // 提取所有门店线索ID
                    Set<Long> hotelLeadIds = userHotelLeadMap.values().stream()
                            .flatMap(Set::stream)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());

                    if (hotelLeadIds.isEmpty()) {
                        return Mono.just(Collections.emptyMap());
                    }

                    return hotelLeadRepository.findByIdIn(hotelLeadIds)
                            .collectList()
                            .map(hotelLeads -> hotelLeads.stream()
                                    .collect(Collectors.toMap(
                                            HdsHotelLeadEntity::getId,
                                            hotelLead -> hotelLead,
                                            (e1, e2) -> e1
                                    )))
                            .defaultIfEmpty(Collections.emptyMap())
                            .onErrorResume(e -> {
                                log.error("获取门店线索信息失败", e);
                                return Mono.just(Collections.emptyMap());
                            });
                });
    }

    /**
     * 组合数据并构建VO列表
     */
    private Mono<List<UserLeadSearchVO>> combineDataAndBuildVOs(
            List<HdsUserLeadEntity> entities,
            Mono<Map<Integer, HdsEmployeeEntity>> employeeMapMono,
            Mono<Map<Long, Set<Long>>> userHotelLeadMapMono,
            Mono<Map<Long, HdsHotelLeadEntity>> hotelLeadMapMono) {

        return Mono.zip(employeeMapMono, userHotelLeadMapMono, hotelLeadMapMono)
                .map(tuple -> {
                    Map<Integer, HdsEmployeeEntity> employeeMap = tuple.getT1();
                    Map<Long, Set<Long>> userHotelLeadMap = tuple.getT2();
                    Map<Long, HdsHotelLeadEntity> hotelLeadMap = tuple.getT3();

                    return entities.stream()
                            .map(entity -> buildUserLeadSearchVO(entity, employeeMap, userHotelLeadMap, hotelLeadMap))
                            .collect(Collectors.toList());
                })
                .onErrorResume(e -> {
                    log.error("组合数据并构建VO失败", e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "构建用户线索数据失败: " + e.getMessage()));
                });
    }

    /**
     * 构建单个用户线索VO
     */
    private UserLeadSearchVO buildUserLeadSearchVO(
            HdsUserLeadEntity entity,
            Map<Integer, HdsEmployeeEntity> employeeMap,
            Map<Long, Set<Long>> userHotelLeadMap,
            Map<Long, HdsHotelLeadEntity> hotelLeadMap) {

        UserLeadSearchVO vo = new UserLeadSearchVO();

        // 复制基本属性
        BeanUtils.copyProperties(entity, vo);

        // 设置员工信息
        setEmployeeInfo(vo, entity, employeeMap);

        // 设置EBK数和门店数
        setEbkAndHotelCounts(vo, entity, userHotelLeadMap, hotelLeadMap);

        // 设置线索状态
        vo.setStatus(vo.getEbkCount() > 0 ? 1 : 0);

        // 设置注册时间
        vo.setRegisteredAt(SimpleDateUtils.formatLocalDateTimeToDateHour(entity.getCreatedAt()));

        return vo;
    }

    /**
     * 设置员工信息
     */
    private void setEmployeeInfo(
            UserLeadSearchVO vo,
            HdsUserLeadEntity entity,
            Map<Integer, HdsEmployeeEntity> employeeMap) {

        // 设置被邀请人手机号
        if (entity.getInviteeId() != null) {
            HdsEmployeeEntity invitee = employeeMap.get(entity.getInviteeId());
            if (invitee != null) {
                vo.setMobile(invitee.getMobile());
            }
        }

        // 设置邀请人信息
        if (entity.getInviterId() != null) {
            HdsEmployeeEntity inviter = employeeMap.get(entity.getInviterId());
            if (inviter != null) {
                vo.setInvitedBy(getInvitedBy(inviter));
            }
        }
    }

    /**
     * 设置EBK数和门店数
     */
    private void setEbkAndHotelCounts(
            UserLeadSearchVO vo,
            HdsUserLeadEntity entity,
            Map<Long, Set<Long>> userHotelLeadMap,
            Map<Long, HdsHotelLeadEntity> hotelLeadMap) {

        // 获取该用户线索关联的所有门店线索ID
        Set<Long> relatedHotelLeadIds = userHotelLeadMap.getOrDefault(entity.getId(), Collections.emptySet());

        // 统计获取EBK数 - 关联的门店线索数量
        vo.setEbkCount(relatedHotelLeadIds.size());

        // 统计创建门店数 - 关联的已完成门店线索数量
        int completedHotelCount = (int) relatedHotelLeadIds.stream()
                .filter(hotelLeadId -> {
                    HdsHotelLeadEntity hotelLead = hotelLeadMap.get(hotelLeadId);
                    return hotelLead != null &&
                            hotelLead.getCompleteTime() != null &&
                            Objects.equals(entity.getInviteeId(),hotelLead.getAdminUserId());
                })
                .count();
        vo.setHotelCount(completedHotelCount);
    }

    /**
     * 根据员工信息生成创建人字符串
     * 格式：账号姓名+账号，账号优先级：手机号码 > 自定义账号 > 邮箱
     *
     * @param employee 员工实体
     * @return 创建人字符串
     */
    public static String getInvitedBy(HdsEmployeeEntity employee) {
        // 获取姓名，确保不为空
        String employeeName = employee.getName();

        // 按优先级获取账号
        String account = null;

        if (StringUtils.isNotBlank(employee.getMobile())) {
            // 优先使用手机号
            account = employee.getMobile();
        } else if (StringUtils.isNotBlank(employee.getUsername())) {
            // 其次使用自定义账号
            account = employee.getUsername();
        } else if (StringUtils.isNotBlank(employee.getEmail())) {
            // 最后使用邮箱
            account = employee.getEmail();
        }
        // 组合姓名和账号
        return employeeName + "/" + account;
    }

    public Mono<HdsUserLeadEntity> findByInviteeId(Integer inviteeId){
        return userLeadRepository.findByInviteeId(inviteeId);
    }
}
