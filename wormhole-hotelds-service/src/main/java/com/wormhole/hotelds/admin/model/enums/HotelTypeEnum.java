package com.wormhole.hotelds.admin.model.enums;

import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/3/27 09:31
 * @Description：酒店类型枚举
 */
@Getter
public enum HotelTypeEnum {

    SINGLE(1, "单体"),
    CHAIN(2, "连锁");

    private final Integer code;
    private final String desc;

    HotelTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static HotelTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (HotelTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断code是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
