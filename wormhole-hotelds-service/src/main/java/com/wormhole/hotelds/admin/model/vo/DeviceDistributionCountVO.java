package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/16 17:46
 * @Description：DeviceDistributionCountVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
public class DeviceDistributionCountVO {

    /**
     * 设备类型分布列表
     */
    private List<DeviceDistributionCountVO.DeviceTypeCount> deviceAppTypes;

    /**
     * 设备类型统计
     */
    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class DeviceTypeCount {

        /**
         * 设备类型（全部设备、AI云电话、AI智能座机）
         */
        private String deviceAppType;

        /**
         * 设备数量
         */
        private Integer deviceCount;

        private Integer appTypeSortOrder;
    }
}
