package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.UserLeadSaveReq;
import com.wormhole.hotelds.admin.model.req.UserLeadSearchReq;
import com.wormhole.hotelds.admin.model.vo.UserLeadSearchVO;
import com.wormhole.hotelds.admin.service.UserLeadService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/5/20 10:22
 * @Description：MerchantLeadController
 */
@RestController
@RequestMapping("/user_lead")
public class UserLeadController {

    @Resource
    private UserLeadService userLeadService;

    /**
     * 创建用户邀请线索
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody UserLeadSaveReq userLeadSaveReq) {
        return userLeadService.create(userLeadSaveReq).flatMap(Result::success);
    }

    /**
     * 搜索用户线索列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<UserLeadSearchVO>>> search(@RequestBody UserLeadSearchReq searchReq) {
        return Mono.zip(
                        userLeadService.searchCount(searchReq),
                        userLeadService.search(searchReq)
                )
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }
}
