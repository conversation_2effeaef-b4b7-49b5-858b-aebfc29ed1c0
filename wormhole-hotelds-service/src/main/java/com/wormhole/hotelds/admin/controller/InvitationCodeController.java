package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.HotelVO;
import com.wormhole.hotelds.admin.model.vo.InvitationCodeLogSearchVO;
import com.wormhole.hotelds.admin.model.vo.InvitationCodeSearchVO;
import com.wormhole.hotelds.admin.model.vo.InvitationCodeVO;
import com.wormhole.hotelds.admin.service.InvitationCodeService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/5/19 09:37
 * @Description：InvitationCodeController
 */
@RestController
@RequestMapping("/invitation_code")
public class InvitationCodeController {

    @Resource
    private InvitationCodeService invitationCodeService;

    /**
     * 创建邀请码
     */
    @PostMapping("/create")
    public Mono<Result<InvitationCodeSearchVO>> create(@RequestBody InvitationCodeSaveReq invitationCodeSaveReq) {
        return invitationCodeService.create(invitationCodeSaveReq).flatMap(Result::success);
    }

    /**
     * 更新邀请码
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody InvitationCodeSaveReq invitationCodeSaveReq) {
        return invitationCodeService.update(invitationCodeSaveReq).flatMap(Result::success);
    }

    /**
     * 删除邀请码
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody InvitationCodeDeleteReq invitationCodeDeleteReq) {
        return invitationCodeService.delete(invitationCodeDeleteReq).flatMap(Result::success);
    }

    /**
     * 修改邀请码状态（启用/禁用）
     */
    @PostMapping("/updateStatus")
    public Mono<Result<Boolean>> updateStatus(@RequestBody InvitationCodeStatusReq invitationCodeStatusReq) {
        return invitationCodeService.updateStatus(invitationCodeStatusReq).flatMap(Result::success);
    }

    /**
     * 搜索邀请码列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<InvitationCodeSearchVO>>> search(@RequestBody InvitationCodeSearchReq invitationCodeSearchReq) {
        return Mono.zip(
                        invitationCodeService.searchCount(invitationCodeSearchReq),
                        invitationCodeService.search(invitationCodeSearchReq)
                )
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    /**
     * 根据ID查询邀请码详情
     */
    @GetMapping("/query/{id}")
    public Mono<Result<InvitationCodeVO>> findById(@PathVariable("id") Integer id) {
        return invitationCodeService.findById(id).flatMap(Result::success);
    }

    @GetMapping("/queryByCode")
    public Mono<Result<InvitationCodeVO>> findByHotelCode(@RequestParam("hotel_code") String hotelCode) {
        return invitationCodeService.findByHotelCode(hotelCode).flatMap(Result::success);
    }

    /**
     * 日志
     */
    @PostMapping("/log/search")
    public Mono<Result<PageResult<InvitationCodeLogSearchVO>>> logSearch(@RequestBody InvitationCodeLogSearchReq invitationCodeLogSearchReq) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> Mono.zip(invitationCodeService.logSearchCount(headerInfo, invitationCodeLogSearchReq),
                        invitationCodeService.logSearch(headerInfo, invitationCodeLogSearchReq)))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }
}
