package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.DeviceLogQueryReq;
import com.wormhole.hotelds.admin.model.vo.DeviceLogVO;
import com.wormhole.hotelds.admin.model.vo.HotelVO;
import com.wormhole.hotelds.admin.service.DeviceLogService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/10 11:29
 * @Description：DeviceLogController
 */
@RestController
@RequestMapping("/device_log")
public class DeviceLogController {

    @Resource
    private DeviceLogService deviceLogService;

    /**
     * 获取门店列表
     * @return
     */
    @PostMapping("/query/list")
    public Mono<Result<List<DeviceLogVO>>> queryList(@RequestBody DeviceLogQueryReq deviceLogQueryReq){
        return deviceLogService.queryList(deviceLogQueryReq).flatMap(Result::success);
    }
}
