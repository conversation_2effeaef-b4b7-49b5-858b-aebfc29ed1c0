package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/6/20 14:14
 * @Description：YopRefundOrderStatusEnum
 */
@AllArgsConstructor
@Getter
public enum YopOrderStatusEnum {

    /**
     * 订单待支付
     */
    PROCESSING("PROCESSING", "订单待支付", 0),

    /**
     * 订单支付成功
     */
    SUCCESS("SUCCESS", "订单支付成功", 1),

    /**
     * 订单已过期
     */
    TIME_OUT("TIME_OUT", "订单已过期", 2),

    /**
     * 订单支付失败
     */
    FAIL("FAIL", "订单支付失败", 2),

    /**
     * 订单关闭
     */
    CLOSE("CLOSE", "订单关闭", 2);

    /**
     * 易宝支付状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 对应系统内部状态码
     * 0: 待支付
     * 1: 已支付
     * 2: 已取消
     */
    private final Integer systemStatusCode;

    /**
     * 获取对应的系统内部订单状态
     * @return 系统内部订单状态
     */
    public OrderStatusEnum toSystemOrderStatus() {
        return OrderStatusEnum.getByCode(systemStatusCode);
    }

    /**
     * 根据易宝支付状态码获取对应的枚举值
     * @param code 易宝支付状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static YopOrderStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }

        for (YopOrderStatusEnum status : YopOrderStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
