package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/3/27 18:26
 * @Description：DeviceModelSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceModelSearchReq extends QueryCondition {

    private Long id;

    /**
     * 设备类型名称
     */
    private String modelName;

    /**
     * 设备类型code
     */
    private String modelCode;
}
