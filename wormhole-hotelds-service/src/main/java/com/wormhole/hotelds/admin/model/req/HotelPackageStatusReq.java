package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/17 11:08
 * @Description：HotelPackageStatusReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelPackageStatusReq {

    /**
     * 门店套餐ID列表
     */
    private String hotelPackageCode;

    /**
     * 状态：1-启用，0-停用
     */
    private Integer status;
}
