package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/5/22 14:28
 * @Description：HotelLeadSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelLeadSaveReq {

    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 确认绑定EBK
     */
    private Boolean bindEbk;

    /**
     * 设置竞对酒店
     */
    private Boolean setRivalHotel;

    /**
     * 同步全量点评
     */
    private Boolean syncReview;

    /**
     * 同步酒店静态信息
     */
    private Boolean syncStaticInfo;

    /**
     * 同步竞对评论
     */
    private Boolean syncRivalReview;

    /**
     * 同步竞品价格
     */
    private Boolean syncRivalPrice;

    /**
     * 生成初始化报告
     */
    private Boolean generateReport;
}
