package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-24 11:52:30
 * @Description: 发票信息搜索请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceInfoSearchReq extends QueryCondition {

    /**
     * 酒店编码
     */
    private String hotelCode;
    
    /**
     * 是否默认：1-是，0-否
     */
    private Integer isDefault;
}
