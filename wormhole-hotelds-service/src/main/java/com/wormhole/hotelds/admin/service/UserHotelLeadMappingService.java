package com.wormhole.hotelds.admin.service;

import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.HotelSaveReq;
import com.wormhole.hotelds.admin.repository.UserHotelLeadMappingRepository;
import com.wormhole.hotelds.admin.repository.UserLeadRepository;
import com.wormhole.hotelds.core.model.entity.HdsHotelLeadEntity;
import com.wormhole.hotelds.core.model.entity.HdsUserHotelLeadMappingEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/6/3 10:58
 * @Description：UserHotelLeadMappingService
 */
@Slf4j
@Service
public class UserHotelLeadMappingService {

    @Resource
    private UserLeadRepository userLeadRepository;

    @Resource
    private UserHotelLeadMappingRepository userHotelLeadMappingRepository;

    public Mono<HdsUserHotelLeadMappingEntity> getOrPrepareUserHotelLeadMapping(HdsHotelLeadEntity hdsHotelLeadEntity,
                                                                                HotelSaveReq req,
                                                                                HeaderUtils.HeaderInfo headerInfo) {
        return userLeadRepository.findByInviteeId(Integer.valueOf(headerInfo.getUserId()))
                .flatMap(userLeadEntity -> {
                    if (Objects.nonNull(hdsHotelLeadEntity.getId())) {
                        // 如果酒店线索ID存在，检查映射关系是否已存在
                        return userHotelLeadMappingRepository.existsByUserLeadIdAndHotelLeadId(
                                        userLeadEntity.getId(), hdsHotelLeadEntity.getId())
                                .flatMap(exists -> {
                                    if (exists) {
                                        // 如果映射关系已存在，返回一个带有特殊ID的实体，表示无需创建
                                        return Mono.just(new HdsUserHotelLeadMappingEntity().setId(-1L));
                                    } else {
                                        // 如果映射关系不存在，创建新的映射实体
                                        HdsUserHotelLeadMappingEntity mappingEntity = new HdsUserHotelLeadMappingEntity();
                                        mappingEntity.setUserLeadId(userLeadEntity.getId());
                                        mappingEntity.setHotelLeadId(hdsHotelLeadEntity.getId()); // 设置酒店线索ID
                                        mappingEntity.setCreatedBy(headerInfo.getUserId());
                                        mappingEntity.setCreatedByName(headerInfo.getUsername());
                                        mappingEntity.setCreatedAt(LocalDateTime.now());
                                        mappingEntity.setRowStatus(1);
                                        return Mono.just(mappingEntity);
                                    }
                                });
                    } else {
                        HdsUserHotelLeadMappingEntity mappingEntity = new HdsUserHotelLeadMappingEntity();
                        mappingEntity.setUserLeadId(userLeadEntity.getId());
                        mappingEntity.setCreatedBy(headerInfo.getUserId());
                        mappingEntity.setCreatedByName(headerInfo.getUsername());
                        mappingEntity.setCreatedAt(LocalDateTime.now());
                        return Mono.just(mappingEntity);
                    }
                })
                .defaultIfEmpty(new HdsUserHotelLeadMappingEntity().setId(-1L));
    }

    public Mono<HdsUserHotelLeadMappingEntity> save(HdsUserHotelLeadMappingEntity entity) {
        return userHotelLeadMappingRepository.save(entity);
    }
}
