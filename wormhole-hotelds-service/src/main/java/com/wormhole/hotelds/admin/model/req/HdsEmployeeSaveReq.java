package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/3 09:17
 * @Description：HdsEmployeeSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsEmployeeSaveReq {

    private Integer id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 角色code
     */
    private String roleCode;

    /**
     * 多角色code
     */
    private List<String> roleCodes;

    /**
     * 账号类型，1总机 2分机
     */
    private Integer accountType;
    /**
     * 工单分配表权限标识
     */
    private Integer ticketAssignmentFlag;

//    /**
//     * 工单类型
//     */
//    private List<String> tickets;
//
    /**
     * 关联的酒店编码列表
     */
    private List<String> hotelCodes;

    /**
     * 员工类型 1-集团 2-服务商 3-酒店
     */
    private Integer type;
//
//    /**
//     * 设备位置code
//     */
//    private List<String> positionCodes;
    private String createdBy;

    private String createdByName;

    private String from;

}
