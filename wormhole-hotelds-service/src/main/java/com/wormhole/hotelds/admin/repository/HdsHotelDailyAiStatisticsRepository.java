package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.admin.model.entity.HdsHotelDailyAiStatisticsEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Collection;

/**
 * @Author: AI Assistant
 * @Date: 2025-08-15
 * @Description: 酒店每日AI运营统计表仓库接口
 */
@Repository
public interface HdsHotelDailyAiStatisticsRepository extends ReactiveCrudRepository<HdsHotelDailyAiStatisticsEntity, Long> {

    /**
     * 根据酒店编码和业务日期查询
     */
    Mono<HdsHotelDailyAiStatisticsEntity> findByHotelCodeAndBusinessDate(String hotelCode, LocalDate businessDate);

    /**
     * 根据酒店编码和日期范围查询
     */
    Flux<HdsHotelDailyAiStatisticsEntity> findByHotelCodeAndBusinessDateBetween(String hotelCode, LocalDate startDate, LocalDate endDate);

    /**
     * 根据日期范围查询
     */
    Flux<HdsHotelDailyAiStatisticsEntity> findByBusinessDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * 根据酒店编码列表和日期范围查询
     */
    Flux<HdsHotelDailyAiStatisticsEntity> findByHotelCodeInAndBusinessDateBetween(Collection<String> hotelCodes, LocalDate startDate, LocalDate endDate);
}
