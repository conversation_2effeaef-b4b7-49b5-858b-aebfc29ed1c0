package com.wormhole.hotelds.admin.model.resp;

import lombok.Data;

/**
 * @Author：flx
 * @Date：2025/6/20 13:39
 * @Description：YopNotifyResp
 */
@Data
public class YopNotifyResp {

    /**
     * 对应系统里面的orderNo
     */
    private String orderId;

    /**
     * 易宝订单号
     */
    private String uniqueOrderNo;

    /**
     * 订单金额
     */
    private String orderAmount;

    /**
     * 支付金额
     */
    private String payAmount;

    /**
     * 支付结果：SUCCESS
     */
    private String status;

    /**
     * 支付成功时间
     */
    private String paySuccessDate;

    /**
     * 该笔订单在微信支付宝侧的唯一订单号，微信交易单号/支付宝订单号
     */
    private String channelTrxId;
}
