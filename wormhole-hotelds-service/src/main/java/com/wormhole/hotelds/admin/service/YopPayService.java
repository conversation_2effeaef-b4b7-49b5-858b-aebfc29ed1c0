package com.wormhole.hotelds.admin.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.IdUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.YopOrderStatusEnum;
import com.wormhole.hotelds.admin.model.enums.YopResponseCodeEnum;
import com.wormhole.hotelds.admin.model.resp.OrderStatusResp;
import com.wormhole.hotelds.admin.model.resp.YopNotifyResp;
import com.wormhole.hotelds.admin.model.vo.YeeOrderVO;
import com.wormhole.hotelds.config.YopPayProperties;
import com.wormhole.hotelds.util.SimpleDateUtils;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.http.YopContentType;
import com.yeepay.yop.sdk.service.cashier.CashierClient;
import com.yeepay.yop.sdk.service.cashier.model.UnifiedOrderUnifiedCashierOrderResponseDTOResult;
import com.yeepay.yop.sdk.service.cashier.request.UnifiedOrderRequest;
import com.yeepay.yop.sdk.service.cashier.response.UnifiedOrderResponse;
import com.yeepay.yop.sdk.service.common.YopCallbackEngine;
import com.yeepay.yop.sdk.service.common.callback.YopCallback;
import com.yeepay.yop.sdk.service.common.callback.YopCallbackRequest;
import com.yeepay.yop.sdk.service.trade.TradeClient;
import com.yeepay.yop.sdk.service.trade.model.YopQueryOrderResDTO;
import com.yeepay.yop.sdk.service.trade.request.OrderQueryRequest;
import com.yeepay.yop.sdk.service.trade.response.OrderQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import javax.swing.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * @Author：flx
 * @Date：2025/6/19 16:38
 * @Description：YeepayService
 */
@Service
@Slf4j
public class YopPayService {

    @Resource
    private CashierClient cashierClient;

    @Resource
    private YopPayProperties yopPayProperties;

    @Resource
    private TradeClient tradeClient;

    @Resource
    private OrderPaymentHandler orderPaymentHandler;

    /**
     * 易宝下单
     *
     * @param orderNo
     * @param orderAmount
     * @param goodsName
     * @param paymentMethod
     * @param expireAt
     * @return
     */
    public Mono<YeeOrderVO> createUnifiedOrder(String orderNo, BigDecimal orderAmount, String goodsName, String paymentMethod, LocalDateTime expireAt) {
        UnifiedOrderRequest request = new UnifiedOrderRequest();
        request.setParentMerchantNo(yopPayProperties.getParentMerchantNo());
        request.setMerchantNo(yopPayProperties.getMerchantNo());
        request.setOrderId(orderNo);
        request.setOrderAmount(orderAmount);
        request.setGoodsName(goodsName);
        request.setNotifyUrl(yopPayProperties.getNotifyUrl());
        request.setLimitPayType(paymentMethod);
        request.setExpiredTime(SimpleDateUtils.formatLocalDateTimeToDateHour(expireAt));
        request.setReturnUrl(yopPayProperties.getReturnUrl() + "?order_no=" + orderNo);
        request.setAggParam("{\"scene\":\"XIANXIA\"}");
        try {
            String activeProfile = SpringUtil.getActiveProfile();
            if (StringUtils.equals(activeProfile, "dev") || StringUtils.equals(activeProfile, "test")) {
                return Mono.just(new YeeOrderVO().setUniqueOrderNo(IdUtils.generateId()).setCashierUrl(yopPayProperties.getMockCashierUrl() + "?order_no=" + orderNo));
            } else {
                UnifiedOrderResponse response = cashierClient.unifiedOrder(request);
                UnifiedOrderUnifiedCashierOrderResponseDTOResult result = response.getResult();
                log.info("result:{}", response.getResult());
                log.info("metadata:{}", JacksonUtils.writeValueAsString(response.getMetadata()));

                if (!StringUtils.equals(result.getCode(), YopResponseCodeEnum.SUCCESS.getCode())) {
                    log.error("YeePayService createUnifiedOrder error code:{} message:{}", result.getCode(), result.getMessage());
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, result.getMessage()));
                }
                YeeOrderVO yeeOrderVO = new YeeOrderVO();
                yeeOrderVO.setUniqueOrderNo(result.getUniqueOrderNo());
                yeeOrderVO.setCashierUrl(result.getCashierUrl());
                return Mono.just(yeeOrderVO);
            }
        } catch (YopClientException e) {
            log.error("Exception when calling CashierClient#unifiedOrder, ex:", e);
            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, e.getMessage()));
        }
    }

    /**
     * 支付回调
     *
     * @param data
     * @param headers
     * @return
     */
    public String payNotify(String data, Map<String, String> headers) {
        String logPrefix = "易宝支付回调";
        log.info("{}收到通知 data:{} headers:{}", logPrefix, data, JSON.toJSONString(headers));

        try {
            YopNotifyResp yopNotifyResp;
            String activeProfile = SpringUtil.getActiveProfile();
            activeProfile = "dev";
            if (StringUtils.equals(activeProfile, "dev") || StringUtils.equals(activeProfile, "test")) {
                yopNotifyResp = JacksonUtils.readValue(data, YopNotifyResp.class);
                yopNotifyResp.setUniqueOrderNo(IdUtils.generateId());
                yopNotifyResp.setChannelTrxId(IdUtils.generateId());
            } else {
                YopCallback yopCallback = SM2Decrypt(data, headers);
                if (yopCallback == null || StringUtils.isBlank(yopCallback.getBizData())) {
                    log.warn("{}解密后数据为空", logPrefix);
                    return "FAIL";
                }
                yopNotifyResp = JSON.parseObject(yopCallback.getBizData(), new TypeReference<>() {
                });
            }

            if (yopNotifyResp == null) {
                log.warn("{}业务数据解析失败", logPrefix);
                return "FAIL";
            }

            if (!YopOrderStatusEnum.SUCCESS.name().equals(yopNotifyResp.getStatus())) {
                log.info("{}订单状态不是成功 status:{}", logPrefix, yopNotifyResp.getStatus());
                return "FAIL";
            }

            String orderNo = yopNotifyResp.getOrderId();
            // 异步处理支付结果
            orderPaymentHandler.processPaymentResult(orderNo, yopNotifyResp);

            return "SUCCESS";
        } catch (Exception e) {
            log.error("{}处理异常 data:{}", logPrefix, data, e);
            return "FAIL";
        }
    }

    public YopCallback SM2Decrypt(String data, Map<String, String> headers) {
        String callback = "";
        YopCallbackRequest request =
                new YopCallbackRequest(callback, "POST")
                        .setContentType(YopContentType.JSON)
                        .setHeaders(headers)
                        .setContent(data);
        YopCallback parse = YopCallbackEngine.parse(request);
        log.info("YopPayService.SM2Decrypt parse :{}", JSON.toJSONString(parse));
        return parse;
    }

    /**
     * 根据订单号调易宝接口查询订单状态
     *
     * @param orderNo 订单号
     * @return 订单状态响应
     */
    public Mono<OrderStatusResp> queryOrderStatus(String orderNo) {
        log.info("查询易宝支付订单状态: orderNo={}", orderNo);

        // 构建查询请求
        OrderQueryRequest orderQueryRequest = buildOrderQueryRequest(orderNo);

        return Mono.fromCallable(() -> tradeClient.orderQuery(orderQueryRequest))
                .subscribeOn(Schedulers.boundedElastic())
                .map(response -> {
                    log.info("queryOrderStatus response: {}", response.getResult());
                    return convertToOrderStatusResp(orderNo, response);
                })
                .onErrorResume(YopClientException.class, e -> {
                    log.error("调用易宝支付查询API失败: orderNo={}, error={}", orderNo, e.getMessage(), e);
                    return Mono.error(e);
                })
                .onErrorResume(e -> {
                    log.error("查询易宝支付订单状态异常: orderNo={}", orderNo, e);
                    return Mono.error(e);
                });
    }

    /**
     * 构建订单查询请求
     */
    private OrderQueryRequest buildOrderQueryRequest(String orderNo) {
        OrderQueryRequest request = new OrderQueryRequest();
        request.setMerchantNo(yopPayProperties.getMerchantNo());
        request.setOrderId(orderNo);
        request.setParentMerchantNo(yopPayProperties.getParentMerchantNo());
        return request;
    }

    /**
     * 将易宝响应转换为系统订单状态响应
     */
    private OrderStatusResp convertToOrderStatusResp(String orderNo, OrderQueryResponse response) {
        OrderStatusResp resp = new OrderStatusResp();
        resp.setOrderId(orderNo);

        if (response == null || response.getResult() == null) {
            log.warn("易宝支付返回结果为空");
            return resp;
        }

        YopQueryOrderResDTO result = response.getResult();

        // 判断响应码是否成功
        if (!YopResponseCodeEnum.isSuccess(result.getCode())) {
            log.warn("易宝支付查询返回错误: code={}, message={}",
                    result.getCode(), result.getMessage());
            return resp;
        }

        // 设置订单状态
        YopOrderStatusEnum yopStatus = YopOrderStatusEnum.getByCode(result.getStatus());
        if (yopStatus != null) {
            resp.setStatus(yopStatus.toSystemOrderStatus().getCode());
        }

        // 设置易宝订单号
        resp.setUniqueOrderNo(result.getUniqueOrderNo());

        // 设置订单金额
        resp.setOrderAmount(result.getOrderAmount());

        // 设置支付金额
        resp.setPayAmount(result.getPayAmount());

        // 设置支付成功时间
        resp.setPaySuccessDate(result.getPaySuccessDate());

        // 设置渠道交易ID
        resp.setChannelTrxId(result.getChannelTrxId());
        return resp;
    }
}
