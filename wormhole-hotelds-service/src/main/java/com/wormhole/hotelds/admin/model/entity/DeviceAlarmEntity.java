package com.wormhole.hotelds.admin.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:58:23
 * @Description: DeviceAlarm
 */
@Data
@Table("hds_device_alarm")
@Accessors(chain = true)
@EqualsAndHashCode
public class DeviceAlarmEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

}