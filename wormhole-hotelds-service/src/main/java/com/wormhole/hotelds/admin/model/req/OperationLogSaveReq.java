package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/4/3 09:42
 * @Description：OperationLogSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OperationLogSaveReq {

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 员工ID
     */
    private Long employeeId;

    /**
     * 员工名称
     */
    private String employeeName;

    /**
     * 模块编码 设备管理，商家管理，酒店管理，房间管理等等
     */
    private String moduleCode;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 操作对象ID
     */
    private Long objectId;

    /**
     * 操作对象类型(hotel:门店,device:设备,contract:合同,merchant:商家等)
     */
    private String objectType;

    /**
     * 操作对象名称
     */
    private String objectName;

    /**
     * 操作类型(add:新增,update:修改,delete:删除,query:查询,export:导出,import:导入,login:登录,logout:登出等)
     */
    private String oprType;

    /**
     * 调用方法
     */
    private String oprMethod;

    /**
     * 操作IP地址
     */
    private String oprIp;

    /**
     * 请求参数
     */
    private String oprParams;

    /**
     * 操作内容描述
     */
    private String oprContent;

    /**
     * 操作前数据(JSON格式)
     */
    private String oprBefore;

    /**
     * 操作后数据(JSON格式)
     */
    private String oprAfter;

    /**
     * 操作时间
     */
    private LocalDateTime oprTime;

    /**
     * 操作结果(success:成功,fail:失败)
     */
    private String oprResult;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 执行时长(毫秒)
     */
    private Long executionTime;
}
