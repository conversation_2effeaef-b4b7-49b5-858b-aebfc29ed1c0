package com.wormhole.hotelds.admin.job;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.model.enums.OrderStatusEnum;
import com.wormhole.hotelds.admin.model.req.OrderMessageReq;
import com.wormhole.hotelds.admin.model.resp.OrderStatusResp;
import com.wormhole.hotelds.admin.service.OrderPaymentHandler;
import com.wormhole.hotelds.admin.service.OrderService;
import com.wormhole.hotelds.admin.service.YopPayService;
import com.wormhole.hotelds.constant.SystemConstant;
import com.wormhole.mq.producer.ReactiveMessageSender;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 订单状态检查定时任务
 * 定期检查处于待支付状态的订单，查询其在支付平台的实际状态
 */
@Slf4j
@Component
public class OrderStatusCheckJob {

    @Resource
    private OrderService orderService;

    @Resource
    private YopPayService yopPayService;

    @Resource
    private OrderPaymentHandler orderPaymentHandler;

    @Resource
    private ReactiveMessageSender reactiveMessageSender;

    /**
     * 检查待支付订单状态
     * 每15分钟执行一次，重点检查即将过期和新创建的订单
     */
    @XxlJob("checkPendingOrderStatusJob")
    public void checkPendingOrderStatus(String param) {
        log.info("开始检查待支付订单状态，参数: {}", param);

        // 解析参数
        final List<String> orderNos;
        final Integer minutes;

        if (StringUtils.isNotEmpty(param)) {
            Map<String, Object> map = JacksonUtils.readValue(param);
            orderNos = (List<String>) map.get("orderNos");
            minutes = map.get("minutes") != null ? (Integer) map.get("minutes") : 30;
        } else {
            orderNos = Collections.emptyList();
            minutes = 30;
        }

        // 查询待处理的订单 - 重点检查最近30分钟内创建的订单和即将过期的订单
        orderService.findRecentAndNearExpiryOrders(orderNos, minutes)
                .flatMapMany(Flux::fromIterable)
                .flatMap(order -> {
                    String orderNo = order.getOrderNo();
                    log.info("检查订单状态: orderNo={}, createTime={}, expireAt={}",
                            orderNo, order.getCreatedAt(), order.getExpireAt());

                    // 检查订单是否已过期
                    if (order.getExpireAt() != null && order.getExpireAt().isBefore(LocalDateTime.now())) {
                        log.info("订单已过期，发送订单过期消息: orderNo={}", orderNo);
                        return reactiveMessageSender.sendMessage(
                                SystemConstant.ORDER_OVERDUE_EVENT_TOPIC,
                                orderNo
                        ).then(Mono.empty());
                    }

                    // 查询易宝支付状态
                    return yopPayService.queryOrderStatus(orderNo)
                            .flatMap(orderStatusResp -> {
                                // 处理查询结果
                                if (OrderStatusEnum.PAID.getCode().equals(orderStatusResp.getStatus())) {
                                    log.info("订单已支付，处理支付成功逻辑: orderNo={}", orderNo);
                                    return orderPaymentHandler.executeWithLock(orderNo, lockedOrderNo ->
                                            orderService.findByOrderNo(lockedOrderNo)
                                                    .flatMap(orderEntity -> {
                                                        // 再次检查订单状态，避免重复处理已支付的订单
                                                        if (OrderStatusEnum.PAID.getCode().equals(orderEntity.getOrderStatus())) {
                                                            log.info("订单已经处于已支付状态，无需重复处理: orderNo={}", lockedOrderNo);
                                                            return Mono.empty();
                                                        }
                                                        // 处理支付成功逻辑
                                                        return orderPaymentHandler.handlePaymentSuccess(orderStatusResp);
                                                    })
                                    );
                                } else if (OrderStatusEnum.CANCELLED.getCode().equals(orderStatusResp.getStatus())) {
                                    log.info("订单已取消，发送订单过期消息: orderNo={}", orderNo);
                                    return reactiveMessageSender.sendMessage(
                                            SystemConstant.ORDER_OVERDUE_EVENT_TOPIC,
                                            orderNo
                                    ).then(Mono.empty());
                                } else {
                                    return Mono.empty();
                                }
                            })
                            .onErrorResume(e -> {
                                log.error("查询订单状态失败: orderNo={}", orderNo, e);
                                return Mono.empty();
                            });
                })
                .collectList()
                .doOnSuccess(result -> log.info("完成待支付订单状态检查，处理订单数: {}", result.size()))
                .doOnError(e -> log.error("检查待支付订单状态时发生错误", e))
                .block();
    }

    /**
     * 清理过期订单
     * 每小时执行一次，处理所有已过期但未被取消的订单
     */
    @XxlJob("cleanupExpiredOrdersJob")
    public void cleanupExpiredOrders(String param) {
        log.info("开始清理过期订单，参数: {}", param);

        orderService.findExpiredPendingOrders()
                .flatMapMany(Flux::fromIterable)
                .flatMap(order -> {
                    String orderNo = order.getOrderNo();
                    log.info("处理过期订单: orderNo={}, expireAt={}", orderNo, order.getExpireAt());

                    // 发送订单过期消息
                    return reactiveMessageSender.sendMessage(
                            SystemConstant.ORDER_OVERDUE_EVENT_TOPIC,
                            orderNo
                    ).then(Mono.empty());
                })
                .collectList()
                .doOnSuccess(result -> log.info("完成过期订单清理，处理订单数: {}", result.size()))
                .doOnError(e -> log.error("清理过期订单时发生错误", e))
                .block();
    }
}