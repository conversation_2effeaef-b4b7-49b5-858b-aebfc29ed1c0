package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.*;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Set;

/**
 * @Author：flx
 * @Date：2025/3/26 10:24
 * @Description：品牌Repository
 */
@Repository
public interface BrandRepository extends ReactiveCrudRepository<HdsBrandEntity, Integer> {

    @Query("SELECT brand_code FROM hds_brand WHERE brand_name = :brandName")
    Mono<HdsBrandEntity> findByBrandName(String brandName);

    Mono<Boolean> existsByBrandCode(String brandCode);

    Flux<HdsBrandEntity> findByBrandNameLike(String brandName);

    Flux<HdsBrandEntity> findAllByBrandCodeIn(Set<String> brandCodes);

    Mono<HdsBrandEntity> findByBrandCode(String brandCode);
}
