package com.wormhole.hotelds.admin.controller;


import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationCancelReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationCreateReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationSearchReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationStatusUpdateReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceApplicationUpdateReq;
import com.wormhole.hotelds.admin.model.vo.HdsInvoiceApplicationDetailVO;
import com.wormhole.hotelds.admin.model.vo.HdsInvoiceApplicationSearchVO;
import com.wormhole.hotelds.admin.service.HdsInvoiceApplyService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-25 17:59:55
 * @Description: HdsInvoiceApplicationController
 */
@RestController
@RequestMapping("/invoice_apply")
public class HdsInvoiceApplyController {

    @Resource
    private HdsInvoiceApplyService service;

    /**
     * 创建发票申请
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody HdsInvoiceApplicationCreateReq req) {
        return service.create(req).flatMap(Result::success);
    }

    /**
     * 搜索发票申请列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<HdsInvoiceApplicationSearchVO>>> search(@RequestBody HdsInvoiceApplicationSearchReq req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> Mono.zip(service.searchCount(req, headerInfo), service.search(req, headerInfo))
                        .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                        .flatMap(Result::success));
    }

    /**
     * 修改发票申请
     * 支持修改发票基本信息和关联的账单
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HdsInvoiceApplicationUpdateReq req) {
        return service.update(req).flatMap(Result::success);
    }

    /**
     * 取消发票申请
     */
    @PostMapping("/cancel")
    public Mono<Result<Boolean>> cancel(@RequestBody HdsInvoiceApplicationCancelReq req) {
        return service.cancel(req).flatMap(Result::success);
    }

    /**
     * 根据ID查询发票申请详情
     */
    @GetMapping("/query/{id}")
    public Mono<Result<HdsInvoiceApplicationDetailVO>> findById(@PathVariable("id") Long id) {
        return service.findById(id).flatMap(Result::success);
    }

    /**
     * 更新发票申请状态
     */
    @PostMapping("/update_status")
    public Mono<Result<Boolean>> updateStatus(@RequestBody HdsInvoiceApplicationStatusUpdateReq req) {
        return service.updateStatus(req).flatMap(Result::success);
    }

}