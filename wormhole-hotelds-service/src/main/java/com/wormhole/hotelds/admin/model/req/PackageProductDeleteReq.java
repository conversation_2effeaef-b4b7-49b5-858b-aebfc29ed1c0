package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/6/17 17:52
 * @Description：HotelPackageProductDeleteReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PackageProductDeleteReq {

    /**
     * 套餐id
     */
    private String packageCode;

    /**
     * 产品id
     */
    private Integer productId;
}
