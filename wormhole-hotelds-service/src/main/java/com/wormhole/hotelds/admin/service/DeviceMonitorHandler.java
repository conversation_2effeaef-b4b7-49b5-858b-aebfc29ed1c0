package com.wormhole.hotelds.admin.service;

import cn.hutool.core.date.*;
import com.wormhole.channel.consts.*;
import com.wormhole.channel.consts.enums.*;
import com.wormhole.channel.consts.message.*;
import com.wormhole.common.enums.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.mq.producer.*;
import lombok.extern.slf4j.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.cloud.context.config.annotation.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.util.*;
import java.util.stream.*;

@Service
@Slf4j
@RefreshScope
public class DeviceMonitorHandler {

    @Value("${rocketmq.topic.channelSseEvent: channel_sse_event}")
    private String channelSseEventTopic;

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private HdsEmployeeService hdsEmployeeService;

    @Resource
    private ReactiveMessageSender reactiveMessageSender;

    /**
     * 发送设备离线消息
     * @param hotelCode
     * @param eventEnum
     * @return
     */
    public Mono<Void> sendDeviceOfflineMessage(String hotelCode, ChannelEventEnum eventEnum) {
        String code = SourcePlatform.HDS_OP.getCode();
        Mono<List<String>> groupUserIds = hdsEmployeeService.getGroupEmployee()
                .map(employees -> employees.stream()
                        .map(HdsEmployeeEntity::getId)
                        .map(String::valueOf)
                        .map(id -> code.concat("_").concat(id))
                        .collect(Collectors.toList()));

        List<String> sourceList = List.of(SourcePlatform.HDS_MC.getCode());
        Mono<List<String>> hotelUserIds = hdsEmployeeHotelService.getNotifyEmployees(hotelCode, sourceList);

        return Mono.zip(hotelUserIds, groupUserIds)
                .flatMap(tuple -> {
                    List<String> allUserIds = new ArrayList<>(tuple.getT1());
                    allUserIds.addAll(tuple.getT2());

                    DeviceMonitorMessage message = new DeviceMonitorMessage();
                    message.setEvent(eventEnum.name());
                    message.setUserIds(allUserIds);
                    message.setHotelCode(hotelCode);
                    message.setMessage("有设备离线，请及时查看");

                    MessageBody messageBody = new MessageBody(SseActionEnum.DEVICE_MONITOR.name(), String.valueOf(SystemClock.now()), message);
                    log.info("发送设备离线消息: userIds={}, message={}", allUserIds, JacksonUtils.writeValueAsString(messageBody));
                    return reactiveMessageSender.sendMessage(channelSseEventTopic, messageBody);
                })
                .then();
    }

}