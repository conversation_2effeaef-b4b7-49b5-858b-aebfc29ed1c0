package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsUserLeadEntity;
import io.reactivex.Flowable;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/22 13:58
 * @Description：HdsUserLeadRepository
 */
@Repository
public interface UserLeadRepository extends ReactiveCrudRepository<HdsUserLeadEntity,Long> {

    Mono<HdsUserLeadEntity> findByInviteeId(Integer inviteeId);

    Mono<Boolean> existsByLeadCode(String leadCode);

    Flux<HdsUserLeadEntity> findByInviteCodeIn(Collection<String> inviteCodes);

    Flux<HdsUserLeadEntity> findByInviteCodeInAndStatus(Collection<String> inviteCodes, Integer status);

    Mono<Boolean> existsByInviteeId(Integer inviteeId);
}
