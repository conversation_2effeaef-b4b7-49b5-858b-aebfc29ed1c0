package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.PackageOptionVO;
import com.wormhole.hotelds.admin.model.vo.PackageSearchVO;
import com.wormhole.hotelds.admin.model.vo.PackageVO;
import com.wormhole.hotelds.admin.service.PackageService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/6/16 13:53
 * @Description：PaymentPackageController
 */
@RestController
@RequestMapping("/payment/package")
public class PackageController {

    @Resource
    private PackageService packageService;

    /**
     * 创建套餐
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody PackageSaveReq packageSaveReq) {
        return packageService.create(packageSaveReq).flatMap(Result::success);
    }

    /**
     * 更新套餐
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody PackageSaveReq packageSaveReq) {
        return packageService.update(packageSaveReq).flatMap(Result::success);
    }

    /**
     * 套餐列表查询
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<PackageSearchVO>>> search(@RequestBody PackageSearchReq packageSearchReq) {
        return Mono.zip(packageService.searchCount(packageSearchReq), packageService.search(packageSearchReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    /**
     * 根据ID查询套餐详情
     */
    @GetMapping("/query")
    public Mono<Result<PackageVO>> findById(@RequestParam("id") Integer id) {
        return packageService.findById(id).flatMap(Result::success);
    }

    /**
     * 启用/停用套餐
     */
    @PostMapping("/update/status")
    public Mono<Result<Boolean>> updateStatus(@RequestBody PackageStatusReq packageStatusReq) {
        return packageService.updateStatus(packageStatusReq).flatMap(Result::success);
    }


    @PostMapping("/deleteProduct")
    public Mono<Result<Boolean>> deleteProduct(@RequestBody PackageProductDeleteReq req){
        return packageService.deleteProduct(req).flatMap(Result::success);
    }

    @PostMapping("/addProduct")
    public Mono<Result<Boolean>> addProduct(@RequestBody PackageSaveReq req){
        return packageService.addProduct(req).flatMap(Result::success);
    }

    /**
     * 设置推荐套餐
     */
    @PostMapping("/update/recommend")
    public Mono<Result<Boolean>> updateRecommend(@RequestBody PackageRecommendReq packageRecommendReq) {
        return packageService.updateRecommend(packageRecommendReq).flatMap(Result::success);
    }

    /**
     * 获取所有启用的套餐列表（用于下拉选择）
     */
    @GetMapping("/getEnableList")
    public Mono<Result<List<PackageSearchVO>>> getEnabledPackages() {
        return packageService.getEnabledPackages().flatMap(Result::success);
    }
}
