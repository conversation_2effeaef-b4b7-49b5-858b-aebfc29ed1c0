package com.wormhole.hotelds.admin.model.enums;

/**
 * @Author: AI Assistant
 * @Date: 2025-08-15
 * @Description: 酒店每日AI运营统计表字段枚举
 */
public enum HdsHotelDailyAiStatisticsFieldEnum {
    id,
    business_date,
    hotel_code,
    complaint_ticket_count,
    complaint_warning_ticket_count,
    ota_negative_review_count,
    ai_call_count,
    return_call_count,
    ticket_count,
    completed_ticket_count,
    inquiry_count,
    inquiry_completed_count,
    service_need_count,
    service_need_completed_count,
    complaint_completed_count,
    emergency_count,
    emergency_completed_count,
    room_use_count,
    avg_call_duration_seconds,
    text_dialogue_count,
    ai_solve_count,
    avg_complete_duration_seconds,
    valid_complete_duration_ticket_count,
    overdue_count,
    return_call_ticket_count,
    created_at,
    created_by,
    created_by_name,
    updated_at,
    updated_by,
    updated_by_name,
    row_status
}
