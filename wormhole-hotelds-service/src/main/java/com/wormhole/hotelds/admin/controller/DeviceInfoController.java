package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.DeviceInfoDeleteReq;
import com.wormhole.hotelds.admin.model.req.DeviceInfoSaveReq;
import com.wormhole.hotelds.admin.service.DeviceInfoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/4/7 09:46
 * @Description：DeviceInfoController
 */
@RestController
@RequestMapping("/device_info")
public class DeviceInfoController {

    @Resource
    private DeviceInfoService deviceInfoService;

    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody DeviceInfoSaveReq deviceInfoSaveReq) {
        return deviceInfoService.create(deviceInfoSaveReq).flatMap(Result::success);
    }

    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody DeviceInfoSaveReq deviceInfoSaveReq) {
        return deviceInfoService.update(deviceInfoSaveReq).flatMap(Result::success);
    }

    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody DeviceInfoDeleteReq deviceDeleteReq) {
        return deviceInfoService.delete(deviceDeleteReq).flatMap(Result::success);
    }

//    @PostMapping("/search")
//    public Mono<Result<PageResult<BrandVO>>> search(@RequestBody BrandSearchReq brandSearchReq) {
//        return Mono.zip(brandService.searchCount(brandSearchReq), brandService.search(brandSearchReq))
//                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
//                .flatMap(Result::success);
//    }
//
//    @GetMapping("/query_list")
//    public Mono<Result<List<BrandVO>>> queryList() {
//        return brandService.queryList().flatMap(Result::success);
//    }
//
//    @GetMapping("/query/{id}")
//    public Mono<Result<BrandVO>> findById(@PathVariable("id") Integer id) {
//        return brandService.findById(id).flatMap(Result::success);
//    }
}
