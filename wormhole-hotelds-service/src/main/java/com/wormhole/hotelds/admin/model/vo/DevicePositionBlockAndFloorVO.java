package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/16 09:50
 * @Description：DevicePositionSortVO
 */
@Builder
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DevicePositionBlockAndFloorVO {

    /**
     * 设备归属类型 (front: ai智能座机; room: ai云电话pad版)
     */
    private String deviceAppType;

    /**
     * 楼层区域列表
     */
    private List<PositionGroup> positionGroups;

    @Builder
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PositionGroup {

        /**
         * id
         */
        private Long id;

        /**
         * 位置名称 block + floor
         */
        private String groupName;

        /**
         * 排序顺序
         */
        private Integer sortOrder;

        /**
         * 位置id集合
         */
        private List<Long> ids;
    }
}
