package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.HdsHotelLeadEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/5/22 17:07
 * @Description：HotelLeadVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelLeadVO {

    private Long id;

    /**
     * 确认绑定EBK
     */
    private Boolean bindEbk;

    /**
     * 设置竞对酒店
     */
    private Boolean setRivalHotel;

    /**
     * 同步全量点评
     */
    private Boolean syncReview;

    /**
     * 同步酒店静态信息
     */
    private Boolean syncStaticInfo;

    /**
     * 同步竞对评论
     */
    private Boolean syncRivalReview;

    /**
     * 生成初始化报告
     */
    private Boolean generateReport;
}
