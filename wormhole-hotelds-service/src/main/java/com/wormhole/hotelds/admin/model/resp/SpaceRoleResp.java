package com.wormhole.hotelds.admin.model.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SpaceRoleResp implements Serializable {

    private String code;

    private String name;

}
