package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsPackageEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Author：flx
 * @Date：2025/1/16 15:30
 * @Description：HdsPaymentPackageRepository
 */
@Repository
public interface HdsPackageRepository extends ReactiveCrudRepository<HdsPackageEntity, Integer> {

    Mono<Boolean> existsByIsRecommend(Integer isRecommend);

    Flux<HdsPackageEntity> findByStatus(Integer status);

    Mono<HdsPackageEntity> findByPackageCode(String packageCode);

    Flux<HdsPackageEntity> findByIsRecommend(Integer isRecommend);

    Mono<Boolean> existsByPackageCode(String packageCode);
}