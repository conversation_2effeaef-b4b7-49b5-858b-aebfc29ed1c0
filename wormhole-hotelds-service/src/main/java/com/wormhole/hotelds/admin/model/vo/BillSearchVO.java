package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/6/19 15:13
 * @Description：BillSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BillSearchVO {

    /**
     * id
     */
    private Integer id;

    /**
     * 流水号
     */
    private String transactionNo;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 周期类型：1-月度，2-季度，3-年度
     */
    private Integer periodType;

    /**
     * 到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime otaEndTime;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 支付方式：1-微信，2-支付宝
     */
    private Integer payMethod;

    /**
     * 交易状态
     */
    private Integer transactionStatus;

    /**
     * 订单交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transactionAt;
    
    /**
     * 是否已申请发票：0-未申请，1-已申请
     */
    private Integer isInvoiceApplied;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;
    
    /**
     * 订单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderCreatedAt;
} 