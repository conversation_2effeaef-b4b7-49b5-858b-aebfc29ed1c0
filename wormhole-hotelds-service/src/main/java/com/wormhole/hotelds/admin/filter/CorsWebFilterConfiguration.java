package com.wormhole.hotelds.admin.filter;

/**
 * CorsWebFilterConfiguration
 *
 * <AUTHOR>
 * @version 2024/11/1
 */
//@Configuration
public class CorsWebFilterConfiguration {
//    @Bean
//    public CorsWebFilter corsWebFilter() {
//        CorsConfiguration corsConfiguration = new CorsConfiguration();
//        // 允许的请求头
//        corsConfiguration.addAllowedHeader("*");
//        // 允许的请求源 （如：http://localhost:8080）
//        corsConfiguration.addAllowedOrigin("*");
//        // 允许的请求方法 ==> GET, HEAD, POST, PUT, PATCH, DELETE, OPTIONS, TRACE
//        corsConfiguration.addAllowedMethod("*");
//        // URL 映射 （如： /admin/**）
//        UrlBasedCorsConfigurationSource urlBasedCorsConfigurationSource = new UrlBasedCorsConfigurationSource();
//        urlBasedCorsConfigurationSource.registerCorsConfiguration("/**", corsConfiguration);
//        return new CorsWebFilter(urlBasedCorsConfigurationSource);
//    }
}
