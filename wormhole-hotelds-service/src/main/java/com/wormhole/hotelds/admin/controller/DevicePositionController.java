package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.DevicePositionService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/11 09:12
 * @Description：DevicePositionController
 */
@RestController
@RequestMapping("/device_positions")
public class DevicePositionController {

    @Resource
    private DevicePositionService devicePositionService;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody DevicePositionSaveReq devicePositionSaveReq) {
        return devicePositionService.create(devicePositionSaveReq).flatMap(Result::success);
    }

    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody DevicePositionSaveReq devicePositionSaveReq) {
        return devicePositionService.update(devicePositionSaveReq).flatMap(Result::success);
    }

    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody DevicePositionDeleteReq devicePositionDeleteReq) {
        return devicePositionService.delete(devicePositionDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<DevicePositionSearchVO>>> search(@RequestBody DevicePositionSearchReq devicePositionSearchReq) {
        return Mono.zip(devicePositionService.searchCount(devicePositionSearchReq), devicePositionService.search(devicePositionSearchReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    @GetMapping("/query/{id}")
    public Mono<Result<DevicePositionVO>> findById(@PathVariable("id") Long id) {
        return devicePositionService.findById(id).flatMap(Result::success);
    }

    /**
     * 获取楼层区域排序
     */
    @GetMapping("/blockFloor")
    public Mono<Result<DevicePositionBlockAndFloorVO>> getBlockAndFloor(@RequestParam("hotelCode") String hotelCode, @RequestParam("deviceAppType") String deviceAppType) {
        return devicePositionService.getBlockAndFloor(hotelCode, deviceAppType)
                .flatMap(Result::success);
    }

    /**
     * 获取设备类型列表
     *
     * @param hotelCode
     * @return
     */
    @GetMapping("/deviceAppType")
    public Mono<Result<List<DeviceAppTypeVO>>> getDeviceAppTypes(@RequestParam(value = "hotelCode", required = false) String hotelCode) {
        return devicePositionService.getDeviceAppTypes(hotelCode)
                .flatMap(Result::success);
    }

    /**
     * 查询楼栋列表
     */
    @GetMapping("/blocks")
    public Mono<Result<List<String>>> getBlocks(@RequestParam("hotelCode") String
                                                        hotelCode, @RequestParam("deviceAppType") String deviceAppType) {
        return devicePositionService.getBlocks(hotelCode, deviceAppType)
                .flatMap(Result::success);
    }

    /**
     * 查询楼层列表
     *
     * @param deviceBlockSearchReq
     * @return
     */
    @PostMapping("/floors")
    public Mono<Result<List<String>>> getFloors(@RequestBody DeviceBlockSearchReq deviceBlockSearchReq) {
        return devicePositionService.getFloors(deviceBlockSearchReq)
                .flatMap(Result::success);
    }

    /**
     * 查询位置列表
     */
    @PostMapping("/positions")
    public Mono<Result<List<DevicePositionNameVO>>> getPositions(@RequestBody DeviceBlockAreaSearchReq deviceBlockAreaSearchReq) {
        return devicePositionService.getPositions(deviceBlockAreaSearchReq)
                .flatMap(Result::success);
    }

    /**
     * 保存位置排序
     */
    @PostMapping("/positionName/sort/update")
    public Mono<Result<Boolean>> updatePositionNameSort(@RequestBody DevicePositionDragSortReq
                                                                devicePositionDragSortReq) {
        return devicePositionService.updatePositionNameSort(devicePositionDragSortReq)
                .flatMap(Result::success);
    }

    /**
     * 保存楼栋楼层排序
     *
     * @param devicePositionDragSortReq
     * @return
     */
    @PostMapping("/positionBlockFloor/sort/update")
    public Mono<Result<Boolean>> updatePositionBlockAndFloorSort(@RequestBody DevicePositionDragSortReq
                                                                         devicePositionDragSortReq) {
        return devicePositionService.updatePositionBlockAndFloorSort(devicePositionDragSortReq)
                .flatMap(Result::success);
    }

    /**
     * 保存设备类型排序
     *
     * @param devicePositionAppTypeDragSortReq
     * @return
     */
    @PostMapping("/positionAppType/sort/update")
    public Mono<Result<Boolean>> updatePositionAppTypeSort(@RequestBody DevicePositionAppTypeDragSortReq
                                                                   devicePositionAppTypeDragSortReq) {
        return devicePositionService.updatePositionAppTypeSort(devicePositionAppTypeDragSortReq)
                .flatMap(Result::success);
    }

    /**
     * 获取位置树形结构
     *
     * @return
     */
    @GetMapping("/getPositionTreeStructure")
    public Mono<Result<DevicePositionTreeVO>> getPositionTreeStructure() {
        return devicePositionService.getPositionTreeStructure()
                .flatMap(Result::success);
    }
}
