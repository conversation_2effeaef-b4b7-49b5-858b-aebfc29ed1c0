package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.AgentTimeFlowSearchReq;
import com.wormhole.hotelds.admin.model.vo.AgentTimeFlowSearchVO;
import com.wormhole.hotelds.admin.service.AgentTimeFlowService;
import com.wormhole.hotelds.api.hotel.req.PluginHotelReq;
import com.wormhole.hotelds.util.LinkDataHotelExtractorUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/5/21 10:18
 * @Description：AgentTimeFlowController
 */
@RestController
@RequestMapping("/agent_time_flow")
public class AgentTimeFlowController {

    @Resource
    private AgentTimeFlowService agentTimeFlowService;

    @Resource
    private LinkDataHotelExtractorUtils linkDataHotelExtractorUtils;

    /**
     * 搜索OTA时间流水列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<AgentTimeFlowSearchVO>>> search(@RequestBody AgentTimeFlowSearchReq agentTimeFlowSearchReq) {
        return Mono.zip(
                        agentTimeFlowService.searchCount(agentTimeFlowSearchReq),
                        agentTimeFlowService.search(agentTimeFlowSearchReq)
                )
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    /**
     * 搜索OTA时间流水列表
     */
    @PostMapping("/extractHotelData")
    public Mono<Result<PluginHotelReq>> extractHotelData() {
        return linkDataHotelExtractorUtils.extractHotelData("https://hotels.ctrip.com/hotels/detail/?cityId=22&checkIn=2025-08-20&checkOut=2025-08-21&hotelId=437034&adult=1&crn=1&children=0&highprice=-1&lowprice=0&listfilter=1","TEST")
                .flatMap(Result::success);
    }
}
