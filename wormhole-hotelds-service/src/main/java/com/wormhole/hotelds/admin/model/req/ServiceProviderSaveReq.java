package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/3/27 17:24
 * @Description：ServiceProviderSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ServiceProviderSaveReq {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 公司编码
     */
    private String providerId;

    /**
     * 公司名称
     */
    private String providerName;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 公司电话
     */
    private String providerPhone;

    /**
     * 公司邮箱
     */
    private String providerEmail;

    /**
     * 联系人
     */
    private String providerContacts;

    /**
     * 公司营业执照
     */
    private String providerLicenseUrl;

    /**
     * 状态 1有效 0无效
     */
    private Integer status;
}
