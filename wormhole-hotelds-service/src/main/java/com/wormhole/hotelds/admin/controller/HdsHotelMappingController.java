package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.dto.HdsHotelMappingDTO;
import com.wormhole.hotelds.admin.model.req.HdsHotelMappingDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsHotelMappingQueryReq;
import com.wormhole.hotelds.admin.model.req.HdsHotelMappingSaveReq;
import com.wormhole.hotelds.admin.model.req.HotelInfoListReq;
import com.wormhole.hotelds.admin.model.resp.HotelSimpleInfoResp;
import com.wormhole.hotelds.admin.service.HdsHotelMappingService;
import com.wormhole.hotelds.core.model.entity.HdsHotelMappingEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/16
 */
@RestController
@RequestMapping("/hotel_mapping")
public class HdsHotelMappingController {

    @Autowired
    private HdsHotelMappingService hdsHotelMappingService;

    @PostMapping("/save")
    public Mono<Result<Boolean>> save(@RequestBody HdsHotelMappingSaveReq req){
        return hdsHotelMappingService.save(req).flatMap(Result::success);
    }

    @PostMapping("/query")
    public Mono<Result<HdsHotelMappingDTO>> query(@RequestBody HdsHotelMappingQueryReq req){
        return hdsHotelMappingService.query(req).flatMap(Result::success).switchIfEmpty(Result.success(null));
    }
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody HdsHotelMappingDeleteReq req) {
        return hdsHotelMappingService.delete(req).flatMap(Result::success);
    }

    @PostMapping("/query_hotel_info")
    public Mono<Result<List<HotelSimpleInfoResp>>> queryHotelInfo(@RequestBody HotelInfoListReq hotelInfoListReq){
        return  hdsHotelMappingService.queryHotelInfo(hotelInfoListReq).flatMap(Result::success);
    }

}
