package com.wormhole.hotelds.admin.service;

import com.google.common.base.Function;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.annotation.OperationLog;
import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.HdsEmployeeVO;
import com.wormhole.hotelds.admin.model.vo.HotelWithExternalVO;
import com.wormhole.hotelds.admin.repository.DevicePositionRepository;
import com.wormhole.hotelds.admin.repository.HdsEmployeeRepository;
import com.wormhole.hotelds.constant.RoleEnum;
import com.wormhole.hotelds.core.enums.AccountTypeEnum;
import com.wormhole.hotelds.core.enums.EmployeeStatusEnum;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.CodePoolManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/4/3 09:16
 * @Description：HdsEmployeeService
 */
@Service
@Slf4j
public class HdsEmployeeService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsEmployeeRepository hdsEmployeeRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private HdsEmployeeTicketMappingService hdsEmployeeTicketMappingService;

    @Resource
    private HotelService hotelService;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private DevicePositionRepository devicePositionRepository;

    /**
     * 默认密码
     */
    private static final String DEFAULT_PASSWORD = "31ad2f10870bb5e4c517357bf9f95e58";

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     */
    public Mono<Boolean> isExist(String username) {
        if (StringUtils.isEmpty(username)) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "用户名不能为空"));
        }
        return hdsEmployeeRepository.existsByUsername(username);
    }

    private Mono<Boolean> validateFieldUniqueness(String fieldValue, Function<String, Mono<Boolean>> existsMethod, String errorMessage) {
        if (StringUtils.isNotBlank(fieldValue)) {
            return existsMethod.apply(fieldValue)
                    .flatMap(exists -> {
                        if (exists) {
                            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, errorMessage));
                        }
                        return Mono.just(true);
                    });
        }
        return Mono.just(true);
    }


    private Mono<Boolean> checkMobileAndEmailExist(HdsEmployeeSaveReq hdsEmployeeSaveReq) {
        if (checkRequestFromExcel(hdsEmployeeSaveReq)) {
            return Mono.just(true);
        }

        Mono<Boolean> validateMobile = validateFieldUniqueness(
                hdsEmployeeSaveReq.getMobile(),
                hdsEmployeeRepository::existsByMobile,
                "手机号已存在"
        );

        Mono<Boolean> validateEmail = validateFieldUniqueness(
                hdsEmployeeSaveReq.getEmail(),
                hdsEmployeeRepository::existsByEmail,
                "邮箱已存在"
        );
        return Mono.when(validateMobile, validateEmail).thenReturn(true);
    }

    private Boolean checkRequestFromExcel(HdsEmployeeSaveReq hdsEmployeeSaveReq){
        return /*StringUtils.equals(hdsEmployeeSaveReq.getFrom(), "excel") && */Objects.nonNull(hdsEmployeeSaveReq.getId());
    }

    /**
     * 检查手机号或邮箱是否已存在，存在则返回员工实体，否则返回 Mono.empty()
     */
    private Mono<Optional<HdsEmployeeEntity>> findEmployeeByMobileOrEmail(HdsEmployeeSaveReq req) {
        if (checkRequestFromExcel(req)) {
            return Mono.just(Optional.empty());
        }
        if (StringUtils.isNotBlank(req.getMobile())) {
            return hdsEmployeeRepository.findByMobile(req.getMobile())
                    .map(Optional::of)
                    .switchIfEmpty(Mono.defer(() -> {
                        if (StringUtils.isNotBlank(req.getEmail())) {
                            return hdsEmployeeRepository.findByEmail(req.getEmail())
                                    .map(Optional::of)
                                    .defaultIfEmpty(Optional.empty());
                        }
                        return Mono.just(Optional.empty());
                    }));
        } else if (StringUtils.isNotBlank(req.getEmail())) {
            return hdsEmployeeRepository.findByEmail(req.getEmail())
                    .map(Optional::of)
                    .defaultIfEmpty(Optional.empty());
        }
        return Mono.just(Optional.empty());
    }

    /**
     * 创建员工
     */
    @OperationLog(businessType = BussinessTypeEnum.EMPLOYEE,
            operationType = OperationTypeEnum.ADD)
    public Mono<Boolean> create(HdsEmployeeSaveReq hdsEmployeeSaveReq) {
        // 参数校验
        if (Objects.isNull(hdsEmployeeSaveReq)) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "请求参数不能为空"));
        }
        return findEmployeeByMobileOrEmail(hdsEmployeeSaveReq)
            .flatMap(optionalEmployee -> {
                optionalEmployee.ifPresent(employee -> {
                    hdsEmployeeSaveReq.setId(employee.getId());
                });

                // 处理用户名
                if (StringUtils.isEmpty(hdsEmployeeSaveReq.getUsername())) {
                    return codePoolManager.getCodeFromPool(BussinessTypeEnum.EMPLOYEE.getBusinessType());
                } else {
                    return isExist(hdsEmployeeSaveReq.getUsername())
                            .flatMap(flag -> {
                                if (checkRequestFromExcel(hdsEmployeeSaveReq)){
                                    return Mono.just(hdsEmployeeSaveReq.getUsername());
                                }
                                if (flag) {
                                    return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "账号已存在"));
                                }
                                return Mono.just(hdsEmployeeSaveReq.getUsername());
                            });
                }
            })
            .flatMap(username -> {
                hdsEmployeeSaveReq.setUsername(username);
                // 获取请求头信息并执行创建操作
                return HeaderUtils.getHeaderInfo()
                        .flatMap(headerInfo -> createEmployee(hdsEmployeeSaveReq, headerInfo))
                        .onErrorResume(ex -> {
                            log.error("员工创建失败", ex);
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "员工创建失败"));
                        });
            })
            .defaultIfEmpty(false);
    }

    /**
     * 事务创建
     */
    private Mono<Boolean> createEmployee(HdsEmployeeSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        if (CollectionUtils.isNotEmpty(req.getRoleCodes())
                && req.getRoleCodes().contains(RoleEnum.HOTEL_ADMIN.getCode())
                && Objects.isNull(req.getAccountType())) {
            req.setAccountType(AccountTypeEnum.MAIN_SWITCHBOARD.getCode());
        }
        Mono<HdsEmployeeEntity> saveMono = checkRequestFromExcel(req) ? Mono.just(buildEmployeeEntity(req, headerInfo)) : hdsEmployeeRepository.save(buildEmployeeEntity(req, headerInfo));

        return transactionalOperator.transactional(
                // 构建员工实体并保存
                saveMono.flatMap(savedEmployee -> {
                            // 如果没有酒店编码，直接返回成功
                            if (CollectionUtils.isEmpty(req.getHotelCodes())) {
                                return Mono.just(true);
                            }

                            if (CollectionUtils.isNotEmpty(req.getRoleCodes())) {
                                req.setRoleCode(String.join(",", req.getRoleCodes()));
                            }


                            // 处理酒店关联
                            Mono<Boolean> hotelAssociationMono = hdsEmployeeHotelService.save(
                                            savedEmployee.getId(),
                                            req.getHotelCodes(),
                                            req.getRoleCode())
                                    .map(e -> true)
                                    .defaultIfEmpty(false);
                            // 处理工单类型映射
                            Mono<Boolean> ticketMappingMono = Mono.just(true);
                            if (Objects.nonNull(req.getTicketAssignmentFlag()) || Objects.nonNull(req.getAccountType())) {
                                ticketMappingMono = hdsEmployeeTicketMappingService.save(
                                        savedEmployee.getId(),
                                        savedEmployee.getName(),
                                        req.getHotelCodes(),
                                        req.getAccountType(),
                                        req.getTicketAssignmentFlag(),
                                        false
                                ).defaultIfEmpty(false);
                            }
                            // 并行执行酒店关联和工单类型映射
                            return Mono.zip(hotelAssociationMono, ticketMappingMono)
                                    .map(tuple -> tuple.getT1() && tuple.getT2());
                        })
                        .onErrorResume(ex -> {
                            log.error("创建员工失败", ex);
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                    "创建员工失败"));
                        })
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 重置密码
     *
     * @param employeeId 员工ID
     * @return 操作结果
     */
    @OperationLog(businessType = BussinessTypeEnum.EMPLOYEE,
            operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> resetPassword(Integer employeeId) {
        if (Objects.isNull(employeeId)) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "员工ID不能为空"));
        }

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> hdsEmployeeRepository.findById(employeeId)
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "员工不存在")))
                        .flatMap(employee -> {
                            // 设置默认密码
                            employee.setPassword(DEFAULT_PASSWORD);
                            employee.setUpdatedAt(LocalDateTime.now());
                            employee.setUpdatedBy(headerInfo.getUserId());
                            employee.setUpdatedByName(headerInfo.getUsername());

                            return hdsEmployeeRepository.save(employee)
                                    .then(Mono.just(true));
                        }))
                .defaultIfEmpty(false);
    }

    /**
     * 更新员工
     */
    @OperationLog(businessType = BussinessTypeEnum.EMPLOYEE,
            operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> update(HdsEmployeeSaveReq hdsEmployeeSaveReq) {
        // 参数校验
        if (Objects.isNull(hdsEmployeeSaveReq) || Objects.isNull(hdsEmployeeSaveReq.getId())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "请求参数不能为空"));
        }
        if (StringUtils.isEmpty(hdsEmployeeSaveReq.getUsername())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "账号不能为空"));
        }

        // 获取原记录信息并校验手机号和邮箱
        return hdsEmployeeRepository.findById(hdsEmployeeSaveReq.getId())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "员工不存在")))
                .flatMap(existingEmployee ->
                        validateMobileAndEmail(existingEmployee, hdsEmployeeSaveReq)
                                .then(HeaderUtils.getHeaderInfo()
                                        .flatMap(headerInfo -> validateAndUpdateUsername(existingEmployee, hdsEmployeeSaveReq, headerInfo))
                                )
                )
                .onErrorResume(ex -> {
                    log.error("员工更新失败", ex);
                    if (ex instanceof BusinessException) {
                        return Mono.error(ex);
                    }
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "员工更新失败"));
                });
    }

    /**
     * 校验手机号和邮箱的唯一性
     */
    private Mono<Boolean> validateMobileAndEmail(HdsEmployeeEntity existingEmployee, HdsEmployeeSaveReq hdsEmployeeSaveReq) {
        // 校验手机号唯一性
        Mono<Boolean> mobileCheck = StringUtils.equals(existingEmployee.getMobile(), hdsEmployeeSaveReq.getMobile())
                ? Mono.just(true)
                : validateFieldUniqueness(hdsEmployeeSaveReq.getMobile(), hdsEmployeeRepository::existsByMobile, "手机号已存在");

        // 校验邮箱唯一性
        Mono<Boolean> emailCheck = StringUtils.equals(existingEmployee.getEmail(), hdsEmployeeSaveReq.getEmail())
                ? Mono.just(true)
                : validateFieldUniqueness(hdsEmployeeSaveReq.getEmail(), hdsEmployeeRepository::existsByEmail, "邮箱已存在");

        return Mono.zip(mobileCheck, emailCheck).thenReturn(true);
    }

    private Mono<Boolean> validateAndUpdateUsername(HdsEmployeeEntity existingEmployee, HdsEmployeeSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        if (!StringUtils.equals(existingEmployee.getUsername(), req.getUsername())) {
            return isExist(req.getUsername())
                    .flatMap(exists -> {
                        if (exists) {
                            log.warn("账号{}已存在", req.getUsername());
                            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "账号已存在"));
                        }
                        return updateEmployee(existingEmployee, req, headerInfo);
                    });
        }
        return updateEmployee(existingEmployee, req, headerInfo);
    }

    /**
     * 事务更新
     *
     * @param existingEmployee 已查询到的员工实体
     * @param req              更新请求
     * @param headerInfo       请求头信息
     */
    private Mono<Boolean> updateEmployee(HdsEmployeeEntity existingEmployee, HdsEmployeeSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        if (CollectionUtils.isNotEmpty(req.getRoleCodes())
                && req.getRoleCodes().contains(RoleEnum.HOTEL_ADMIN.getCode())
                && Objects.isNull(req.getAccountType())) {
            req.setAccountType(AccountTypeEnum.MAIN_SWITCHBOARD.getCode());
        }
        return transactionalOperator.transactional(
                // 直接更新已查询到的员工实体
                Mono.just(existingEmployee)
                        .flatMap(employee -> {
                            updateEmployeeEntity(employee, req, headerInfo);
                            return hdsEmployeeRepository.save(employee);
                        })
                        .flatMap(savedEmployee -> {
                            // 如果没有酒店编码直接返回成功
                            if (CollectionUtils.isEmpty(req.getHotelCodes())) {
                                return Mono.just(true);
                            }

                            if (CollectionUtils.isNotEmpty(req.getRoleCodes())) {
                                Set<String> allRoleCodes = new HashSet<>(req.getRoleCodes());

                                // 如果原有的roleCode不为空，将其拆分并添加到集合中
                                if (StringUtils.isNotBlank(req.getRoleCode())) {
                                    allRoleCodes.add(req.getRoleCode());
                                }

                                // 将集合转换为逗号分隔的字符串并设置回请求对象
                                req.setRoleCode(String.join(",", allRoleCodes));
                            }

                            // 并行处理酒店关联和工单类型映射
                            Mono<Boolean> hotelAssociationMono = Mono.just(true);
                            if (CollectionUtils.isNotEmpty(req.getHotelCodes())) {
                                hotelAssociationMono = hdsEmployeeHotelService.saveOrUpdate(
                                                savedEmployee.getId(),
                                                req.getHotelCodes(),
                                                req.getRoleCode(),
                                                savedEmployee.getStatus())
                                        .defaultIfEmpty(false);
                            }
                            Mono<Boolean> ticketMappingMono = Mono.just(true);
                            if (Objects.nonNull(req.getTicketAssignmentFlag()) || Objects.nonNull(req.getAccountType())) {
                                ticketMappingMono = hdsEmployeeTicketMappingService.save(
                                        savedEmployee.getId(),
                                        savedEmployee.getName(),
                                        req.getHotelCodes(),
                                        req.getAccountType(),
                                        req.getTicketAssignmentFlag(), true
                                ).defaultIfEmpty(false);
                            }
                            // 并行执行并合并结果
                            return Mono.zip(hotelAssociationMono, ticketMappingMono)
                                    .map(tuple -> tuple.getT1() && tuple.getT2());
                        })
                        .onErrorResume(ex -> {
                            log.error("更新员工失败", ex);
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新员工失败"));
                        })
                        .defaultIfEmpty(false)
        );
    }

    private Mono<List<String>> listHotelCodes(Integer employeeId) {
        return hdsEmployeeHotelService.listByEmployeeId(employeeId, null, null)
                .map(employeeHotelEntities -> employeeHotelEntities.stream()
                        .map(HdsEmployeeHotelEntity::getHotelCode)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList()));
    }

    /**
     * 修改员工状态
     */
    @OperationLog(businessType = BussinessTypeEnum.EMPLOYEE,
            operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> updateStatus(HdsEmployeeUpdateStatusReq req) {
        // 参数校验
        if (Objects.isNull(req) || Objects.isNull(req.getId()) || Objects.isNull(req.getStatus())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "请求参数不能为空"));
        }

        // 获取请求头信息并执行删除操作
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> updateStatus(headerInfo, req)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "修改员工状态失败"))))
                .defaultIfEmpty(false);
    }

    /**
     * 事务更新状态
     */
    private Mono<Boolean> updateStatus(HeaderUtils.HeaderInfo headerInfo, HdsEmployeeUpdateStatusReq req) {
        return transactionalOperator.transactional(
                hdsEmployeeRepository.findById(req.getId())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "员工不存在")))
                        .flatMap(employee -> {
                            Mono<Boolean> updateOperation = Mono.just(true);
                            if (EmployeeTypeEnum.isGroupEmployee(employee.getType())) {
                                // 更新员工状态
                                employee.setStatus(req.getStatus());
                                employee.setUpdatedBy(headerInfo.getUserId());
                                employee.setUpdatedByName(headerInfo.getUsername());
                                employee.setUpdatedAt(LocalDateTime.now());
                                updateOperation = hdsEmployeeRepository.save(employee)
                                        .then(Mono.just(true));
                                return updateOperation;
                            } else if (employee.getType() == 3) {
                                if (CollectionUtils.isNotEmpty(req.getHotelCodes())) {
                                    updateOperation = hdsEmployeeHotelService.updateEmployeeHotelStatus(req.getId(),
                                            req.getHotelCodes(), req.getStatus(), false);
                                }
                            }
                            return updateOperation;

                        })
                        .defaultIfEmpty(false)
        );
    }

    public Mono<PageResult<HdsEmployeeVO>> searchPage(HdsEmployeeSearchReq req) {
        // 集团侧查询
        if (req.getIsGroup()) {
            return Mono.zip(searchCount(req), search(req))
                    .flatMap(tuple -> {
                        Long total = tuple.getT1();
                        List<HdsEmployeeVO> employees = tuple.getT2();

                        Set<Integer> createdBySet = getCreatedBySet(employees);

                        Mono<Map<String, HdsEmployeeEntity>> employeeMapMono = queryCreatedByEmployees(createdBySet);

                        return employeeMapMono.flatMap(employeeMap -> {
                            employees.forEach(employee -> {
                                employee.setCreatedBy(generateCreatedByInfo(employeeMap.get(employee.getCreatedBy())));
                            });
                            return Mono.just(PageResult.create(total, employees));
                        });
                    });
        }

        // 门店测查询
        return HeaderUtils.getHeaderInfo().flatMap(headerInfo -> {
            String hotelCode = headerInfo.getHotelCode();
            String userId = headerInfo.getUserId();

            // 获取当前登录员工关联的所有角色
            Mono<Set<String>> roleCodesMono = StringUtils.isBlank(hotelCode) ? Mono.just(Collections.emptySet()) : hdsEmployeeRepository.findById(Integer.valueOf(userId))
                    .flatMap(hdsEmployee -> {
                        // 新增逻辑，如果员工账号是集团类型，在门店端则查询所有角色的员工
                        if (EmployeeTypeEnum.GROUP.getCode().equals(hdsEmployee.getType())) {
                            return Mono.just(Collections.emptySet());
                        } else {
                            return hdsEmployeeHotelService.listByEmployeeId(Integer.valueOf(userId), null, List.of(hotelCode))
                                    .map(hotelEntities -> {
                                        if (CollectionUtils.isEmpty(hotelEntities)) {
                                            return Collections.emptySet();
                                        }

                                        // 使用流式处理提取和汇总roleCodes
                                        return hotelEntities.stream()
                                                .map(HdsEmployeeHotelEntity::getRoleCode)
                                                .filter(StringUtils::isNotBlank)
                                                .map(roleCodesStr -> roleCodesStr.split(","))
                                                .flatMap(Arrays::stream)
                                                .map(String::trim)
                                                .filter(StringUtils::isNotBlank)
                                                .collect(Collectors.toSet());
                                    });
                        }
                    });

            return roleCodesMono.flatMap(roleCodes -> {
                // 判断是否需要OTA过滤
                boolean isOtaAdminOnly = CollectionUtils.isNotEmpty(roleCodes) &&
                        roleCodes.contains(RoleEnum.OTA_AGENT_ADMIN.getCode()) &&
                        !roleCodes.contains(RoleEnum.HOTEL_ADMIN.getCode());

                // 使用带角色过滤的查询方法
                int otaFilterFlag = isOtaAdminOnly ? 1 : 0;


                Flux<HdsEmployeeVO> employeeFlux = hdsEmployeeRepository.list(hotelCode,
                        req.getStatus(), req.getRoleCode(), req.getUserKeyword(), req.getGroupKeyword(),
                        req.getType(),
                        otaFilterFlag,
                        req.getPageSize(), (long) (req.getCurrent() - 1) * req.getPageSize());

                Mono<Long> countMono = hdsEmployeeRepository.count(hotelCode,
                        req.getStatus(), req.getRoleCode(), req.getUserKeyword(), req.getGroupKeyword(), req.getType(),otaFilterFlag);

                return Mono.zip(countMono, employeeFlux.collectList())
                        .flatMap(tuple -> {
                            Long total = tuple.getT1();
                            List<HdsEmployeeVO> employees = tuple.getT2();

//                            if (CollectionUtils.isEmpty(employees)) {
//                                return Mono.just(PageResult.create(total, employees));
//                            }
//                            /*
//                             * 1. 如果当前登录员工是OTA管理员，则只查询OTA管理员和OTA员工
//                             * 2.如果当前登录员工是Ai客服管理员，则查询所有员工
//                             */
//                            if (CollectionUtils.isNotEmpty(roleCodes) &&
//                                    roleCodes.contains(RoleEnum.OTA_AGENT_ADMIN.getCode()) &&
//                                    !roleCodes.contains(RoleEnum.HOTEL_ADMIN.getCode())) {
//                                employees = employees.stream()
//                                        .filter(employee -> {
//                                            if (StringUtils.isBlank(employee.getRoleCode())) {
//                                                return false;
//                                            }
//                                            List<String> roleCodeList =  Arrays.stream(employee.getRoleCode().split(",")).toList();
//                                            for (String roleCode : roleCodeList) {
//                                                if (RoleEnum.OTA_AGENT_ADMIN.getCode().equals(roleCode) ||
//                                                        RoleEnum.OTA_AGENT_STAFF.getCode().equals(roleCode)) {
//                                                    return true;
//                                                }
//                                            }
//                                            return false;
//                                        })
//                                        .collect(Collectors.toList());
//                            }

                            return enrichEmployeesWithTickets(employees)
                                    .map(enrichedList -> PageResult.create(total, enrichedList));
                        });
            });
        });
    }


    /**
     * 搜索商户列表
     */
    public Mono<List<HdsEmployeeVO>> search(HdsEmployeeSearchReq req) {
        Criteria criteria = buildBaseCriteria(req);
        Query query = Query.query(criteria)
                .sort(Sort.by(Sort.Direction.DESC, HdsHotelInfoFieldEnum.created_at.name()))
                .limit(req.getPageSize())
                .offset((long) (req.getCurrent() - 1) * req.getPageSize());
        return r2dbcEntityTemplate.select(query, HdsEmployeeEntity.class)
                .collectList()
                .map(hdsEmployeeEntities -> hdsEmployeeEntities.stream()
                        .map(HdsEmployeeVO::toVo)
                        .toList());
    }

    public Mono<Long> searchCount(HdsEmployeeSearchReq req) {
        Criteria criteria = buildBaseCriteria(req);
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.count(query, HdsEmployeeEntity.class);
    }

    /**
     * 构建查询条件
     *
     * @param req
     * @return
     */
    private Criteria buildBaseCriteria(HdsEmployeeSearchReq req) {
        Criteria criteria = Criteria.empty();

        // 设备id
        if (StringUtils.isNotBlank(req.getUserKeyword())) {
            String keyword = "%" + req.getUserKeyword().trim() + "%";
            Criteria keywordCriteria = Criteria.where(HdsEmployeeFieldEnum.name.name()).like(keyword)
                    .or(HdsEmployeeFieldEnum.mobile.name()).like(keyword);
            criteria = criteria.and(keywordCriteria);
        }

        // 设备编码
        if (Objects.nonNull(req.getStatus())) {
            criteria = criteria.and(HdsEmployeeFieldEnum.status.name())
                    .is(req.getStatus());
        }

        // 员工类型
        criteria = criteria.and(HdsEmployeeFieldEnum.type.name())
                .in(Objects.nonNull(req.getType()) ? List.of(req.getType()) :
                        List.of(EmployeeTypeEnum.GROUP.getCode(), EmployeeTypeEnum.GROUP_SERVICE.getCode()));
        return criteria;
    }

    private Mono<List<HdsEmployeeVO>> enrichEmployeesWithTickets(List<HdsEmployeeVO> employees) {
        List<Integer> employeeIds = employees.stream().map(HdsEmployeeVO::getId).toList();

        Set<Integer> createdBySet = getCreatedBySet(employees);

        Mono<Map<String, HdsEmployeeEntity>> employeeMapMono = queryCreatedByEmployees(createdBySet);

        Mono<Map<Integer, List<HdsEmployeeTicketMappingEntity>>> byEmployeeIds = hdsEmployeeTicketMappingService.findByEmployeeIds(employeeIds);

        return Mono.zip(employeeMapMono, byEmployeeIds)
                .flatMap(tuple -> {
                    Map<String, HdsEmployeeEntity> employeeMap = tuple.getT1();
                    Map<Integer, List<HdsEmployeeTicketMappingEntity>> mappingMap = tuple.getT2();

                    // 创建处理每个员工的Mono列表
                    List<Mono<HdsEmployeeVO>> enrichedEmployeeMonos = employees.stream()
                            .map(employee -> enrichSingleEmployee(employee, mappingMap.get(employee.getId()), employeeMap.get(employee.getCreatedBy())))
                            .collect(Collectors.toList());

                    return Flux.fromIterable(enrichedEmployeeMonos)
                            .flatMap(java.util.function.Function.identity())
                            .collectList();
                });
    }

    private Set<Integer> getCreatedBySet(List<HdsEmployeeVO> employees) {
        return employees.stream()
                .map(HdsEmployeeVO::getCreatedBy)
                .map(Integer::valueOf)
                .collect(Collectors.toSet());
    }

    private Mono<Map<String, HdsEmployeeEntity>> queryCreatedByEmployees(Set<Integer> createdBySet) {
        return createdBySet.isEmpty() ? Mono.just(Collections.emptyMap())
                : r2dbcEntityTemplate.select(HdsEmployeeEntity.class)
                .matching(Query.query(Criteria.where(HdsEmployeeFieldEnum.id.name()).in(createdBySet)))
                .all()
                .collectMap(hdsEmployeeEntity -> String.valueOf(hdsEmployeeEntity.getId()));
    }


    private Mono<HdsEmployeeVO> enrichSingleEmployee(HdsEmployeeVO employee, List<HdsEmployeeTicketMappingEntity> mappings, HdsEmployeeEntity createdByEmployee) {
        Optional.ofNullable(employee.getRoleCode())
                .filter(StringUtils::isNotBlank)
                .map(str -> Arrays.asList(str.split(",")))
                .ifPresent(employee::setRoleCodes);

        employee.setCreatedBy(generateCreatedByInfo(createdByEmployee));

        if (CollectionUtils.isEmpty(mappings) || StringUtils.isBlank(employee.getHotelCode())) {
            return Mono.just(employee);
        }

        return Mono.justOrEmpty(
                mappings.stream()
                        .filter(m -> StringUtils.equals(employee.getHotelCode(), m.getHotelCode()))
                        .findFirst()
        ).flatMap(mapping -> {
            employee.setAccountType(mapping.getEmployeeType());
            employee.setTicketAssignmentFlag(mapping.getTicketAssignmentFlag());
            Optional.ofNullable(mapping.getTicketCategories())
                    .filter(StringUtils::isNotBlank)
                    .map(str -> Arrays.asList(str.split(",")))
                    .ifPresent(tickets -> {
                        employee.setTickets(tickets);
                        employee.setTicketLabels(tickets.stream()
                                .map(code -> Optional.ofNullable(ServiceCategory.getByCode(code, false))
                                        .map(ServiceCategory::getChineseName)
                                        .orElse(code))
                                .collect(Collectors.toList()));
                    });

            // 处理位置编码并查询相关信息
            List<String> positionCodeList = getPositionCodes(mapping.getPositionCodes());


            // 如果位置编码列表为空，直接返回
            if (positionCodeList.isEmpty()) {
                return Mono.just(employee);
            }

            return getFormatPositionDetails(positionCodeList)
                    .map(positionDetails -> {
                        employee.setPositionDetails(positionDetails);
                        return employee;
                    })
                    .defaultIfEmpty(employee);
        }).defaultIfEmpty(employee);
    }

    /**
     * 根据员工信息生成创建人字符串
     * 格式：账号姓名+账号，账号优先级：手机号码 > 自定义账号 > 邮箱
     *
     * @param employee 员工实体
     * @return 创建人字符串
     */
    public String generateCreatedByInfo(HdsEmployeeEntity employee) {
        if (employee == null) {
            return "系统自动";
        }

        // 获取姓名，确保不为空
        String employeeName = employee.getName();

        // 按优先级获取账号
        String account = null;

        if (StringUtils.isNotBlank(employee.getMobile())) {
            // 优先使用手机号
            account = employee.getMobile();
        } else if (StringUtils.isNotBlank(employee.getUsername())) {
            // 其次使用自定义账号
            account = employee.getUsername();
        } else if (StringUtils.isNotBlank(employee.getEmail())) {
            // 最后使用邮箱
            account = employee.getEmail();
        }
        // 组合姓名和账号
        return employeeName + "/" + account;
    }

    private List<String> getPositionCodes(String positionCodes) {
        if (StringUtils.isBlank(positionCodes)) {
            return Collections.emptyList();
        }
        return Arrays.asList(positionCodes.split(","));
    }

    /**
     * 获取格式化的位置详情
     *
     * @param positionCodes 位置编码列表
     * @return 格式化后的位置详情列表
     */
    private Mono<List<String>> getFormatPositionDetails(List<String> positionCodes) {
        if (CollectionUtils.isEmpty(positionCodes)) {
            return Mono.just(Collections.emptyList());
        }

        return devicePositionRepository.findByPositionCodeIn(positionCodes)
                .collectList()
                .map(positions -> {
                    if (CollectionUtils.isEmpty(positions)) {
                        return Collections.emptyList();
                    }

                    // 直接返回格式化的位置详情列表
                    return positions.stream()
                            .map(this::formatPositionDetail)
                            .collect(Collectors.toList());
                });
    }

    /**
     * 格式化位置信息为"楼栋-楼层-区域"格式
     */
    private String formatPositionDetail(HdsDevicePositionEntity position) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(position.getBlock())) {
            sb.append(position.getBlock());
        }

        if (StringUtils.isNotBlank(position.getArea())) {
            sb.append("-").append(position.getArea());
        }

        if (StringUtils.isNotBlank(position.getPositionName())) {
            sb.append("-").append(position.getPositionName());
        }

        return sb.toString();
    }


    /**
     * 根据ID查询员工
     */
    public Mono<HdsEmployeeVO> query(HdsEmployeeQueryReq req) {
        if (Objects.isNull(req) || Objects.isNull(req.getId())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "员工ID不能为空"));
        }

        return hdsEmployeeRepository.findById(req.getId())
                .flatMap(entity -> convertToVO(entity, req.getHotelCode()))
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "员工不存在")));
    }

    /**
     * 通过手机号查询员工
     *
     * @return 员工实体
     */
    public Mono<HdsEmployeeEntity> findByMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "手机号不能为空"));
        }
        return getEmployeeEntity(mobile, null, null)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "员工不存在")));
    }

    /**
     * 创建酒店员工账号
     * 根据门店联系人、联系电话、邮箱时，创建员工账号
     *
     * @param hotelSaveReq 酒店保存请求
     * @param hotelCode    新创建的酒店编码
     * @return Mono<Void>
     */
    public Mono<Void> createHotelEmployee(HotelSaveReq hotelSaveReq, String hotelCode) {
        log.info("尝试为酒店[{}]创建员工账号，联系人：{}，电话：{}", hotelCode, hotelSaveReq.getMainPerson(), hotelSaveReq.getPhone());

        return findByMobile(hotelSaveReq.getPhone())
                .flatMap(existingEmployee -> handleExistingEmployee(existingEmployee, hotelCode))
                .then(createNewEmployee(hotelSaveReq, hotelCode))
                .onErrorResume(e -> {
                    log.warn("创建酒店员工账号失败，但不影响酒店创建: {}", e.getMessage());
                    return Mono.empty();
                });
    }

    /**
     * 处理已存在的员工账号，将其关联到新酒店
     *
     * @param existingEmployee 已存在的员工
     * @param hotelCode        酒店编码
     * @return Mono<Void>
     */
    public Mono<Void> handleExistingEmployee(HdsEmployeeEntity existingEmployee, String hotelCode) {
        log.info("找到已存在的员工账号，ID：{}", existingEmployee.getId());
        return hdsEmployeeHotelService.listByEmployeeId(existingEmployee.getId(), EmployeeStatusEnum.ACTIVE.getCode(), List.of(hotelCode))
                .flatMap(hotelAssociations -> {
                    if (CollectionUtils.isEmpty(hotelAssociations)) {
                        log.info("员工账号未关联酒店[{}]，添加关联记录", hotelCode);
                        return hdsEmployeeHotelService.create(
                                existingEmployee.getId(),
                                List.of(hotelCode),
                                RoleEnum.HOTEL_ADMIN.getCode()
                        ).then();
                    }
                    log.info("员工账号已关联酒店[{}]，无需处理", hotelCode);
                    return Mono.empty();
                })
                .onErrorResume(e -> {
                    log.warn("关联员工账号到酒店[{}]失败: {}", hotelCode, e.getMessage());
                    return Mono.empty();
                });
    }

    /**
     * 创建新员工账号并关联到酒店
     *
     * @param hotelSaveReq 酒店保存请求
     * @param hotelCode    酒店编码
     * @return Mono<Void>
     */
    public Mono<Void> createNewEmployee(HotelSaveReq hotelSaveReq, String hotelCode) {
        log.info("创建新员工账号，关联酒店[{}]", hotelCode);
        HdsEmployeeSaveReq employeeSaveReq = new HdsEmployeeSaveReq()
                .setName(hotelSaveReq.getMainPerson())
                .setMobile(hotelSaveReq.getPhone())
                .setEmail(hotelSaveReq.getEmail())
                .setRoleCode(RoleEnum.HOTEL_ADMIN.getCode())
                .setHotelCodes(List.of(hotelCode));

        return create(employeeSaveReq)
                .doOnSuccess(success -> {
                    if (success) {
                        log.info("成功创建员工账号并关联酒店[{}]", hotelCode);
                    } else {
                        log.warn("创建员工账号失败");
                    }
                })
                .onErrorResume(e -> {
                    log.warn("创建员工账号失败: {}", e.getMessage());
                    return Mono.empty();
                })
                .then();
    }

    /**
     * 通过用户名查询员工
     *
     * @param username 用户名
     * @return 员工实体
     */
    public Mono<HdsEmployeeEntity> findByUsername(String username) {
        if (StringUtils.isEmpty(username)) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "用户名不能为空"));
        }
        return getEmployeeEntity(null, username, null)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "员工不存在")));
    }

    public Mono<List<HdsEmployeeEntity>> getGroupEmployee() {
        Criteria criteria =
                Criteria.where(HdsEmployeeFieldEnum.status.name()).is(EmployeeStatusEnum.ACTIVE.getCode())
                        .and(HdsEmployeeFieldEnum.type.name()).is(1);
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsEmployeeEntity.class).collectList();
    }

    public Mono<HdsEmployeeEntity> getEmployeeEntity(String mobile, String userName, String email) {
        return r2dbcEntityTemplate.select(buildEmployeeQuery(mobile, userName, email), HdsEmployeeEntity.class).next();
    }

    private Query buildEmployeeQuery(String mobile, String userName, String email) {
        Criteria criteria = Criteria.where(HdsEmployeeFieldEnum.status.name()).is(EmployeeStatusEnum.ACTIVE.getCode());

        if (StringUtils.isNotEmpty(mobile)) {
            criteria = criteria.and(HdsEmployeeFieldEnum.mobile.name()).is(mobile);
        }
        if (StringUtils.isNotEmpty(email)) {
            criteria = criteria.and(HdsEmployeeFieldEnum.email.name()).is(email);
        }
        if (StringUtils.isNotEmpty(userName)) {
            criteria = criteria.and(HdsEmployeeFieldEnum.username.name()).is(userName);
        }

        return Query.query(criteria);
    }

    private Mono<Query> buildSearchQuery(HdsEmployeeSearchReq req) {
        Criteria baseCriteria = Criteria.empty();

        // 根据搜索条件添加查询条件
        if (Objects.nonNull(req.getStatus())) {
            baseCriteria = baseCriteria.and(HdsEmployeeFieldEnum.status.name()).is(req.getStatus());
        } else {
            baseCriteria = baseCriteria.and(HdsEmployeeFieldEnum.status.name()).is(EmployeeStatusEnum.ACTIVE.getCode());
        }

        // 用户信息
        if (StringUtils.isNotEmpty(req.getUserKeyword())) {
            String userKeyword = "%" + req.getUserKeyword() + "%";
            baseCriteria = baseCriteria.and(
                    Criteria.where(HdsEmployeeFieldEnum.name.name()).like(userKeyword)
                            .or(HdsEmployeeFieldEnum.mobile.name()).like(userKeyword)
            );
        }

        // 关联信息搜索（商户名称、品牌名称、门店名称等）
        if (StringUtils.isNotEmpty(req.getGroupKeyword())) {
            return handleGroupKeywordSearch(req.getGroupKeyword(), baseCriteria);
        }

        return Mono.just(Query.query(baseCriteria));
    }

    private Mono<Query> handleGroupKeywordSearch(String groupKeyword, Criteria baseCriteria) {
        return hotelService.searchHotelCodes(groupKeyword)
                .flatMap(hotelCodes -> {
                    if (CollectionUtils.isNotEmpty(hotelCodes)) {
                        return hdsEmployeeHotelService.findEmployeeIdsByHotelCodes(hotelCodes)
                                .map(employeeIds -> buildGroupCriteria(baseCriteria, employeeIds));
                    }
                    return Mono.just(buildGroupCriteria(baseCriteria, Collections.emptyList()));
                });
    }

    private Query buildGroupCriteria(Criteria baseCriteria, List<Integer> employeeIds) {
        Criteria groupCriteria = CollectionUtils.isNotEmpty(employeeIds)
                ? Criteria.where(HdsEmployeeFieldEnum.id.name()).in(employeeIds)
                : Criteria.where(HdsEmployeeFieldEnum.id.name()).is(-1);
        return Query.query(baseCriteria.and(groupCriteria));
    }

    /**
     * 将实体转换为VO
     */
    private Mono<HdsEmployeeVO> convertToVO(HdsEmployeeEntity entity, String hotelCode) {
        HdsEmployeeVO vo = new HdsEmployeeVO();
        BeanUtils.copyProperties(entity, vo);
        if (StringUtils.isEmpty(hotelCode)) {
            return Mono.just(vo);
        }

        List<String> hotelCodes = Lists.newArrayList(hotelCode);
        return hdsEmployeeHotelService.listByEmployeeId(entity.getId(), null, hotelCodes)
                .flatMap(hotels -> {
                    if (hotels.isEmpty()) {
                        return Mono.just(vo);
                    }

                    HdsEmployeeHotelEntity employeeHotel = hotels.get(0);
                    vo.setHotelCode(employeeHotel.getHotelCode());

                    // 从逗号分隔的字符串中获取角色名称列表
                    List<String> roleCodeList = Optional.ofNullable(employeeHotel.getRoleCode())
                            .filter(StringUtils::isNotBlank)
                            .map(roleCodesStr -> Arrays.stream(roleCodesStr.split(","))
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList()))
                            .orElse(Collections.emptyList());
                    vo.setRoleCodes(roleCodeList);

                    Mono<HdsEmployeeVO> ticketMappingMono = hdsEmployeeTicketMappingService.findByEmployeeIdAndHotelCode(entity.getId(), employeeHotel.getHotelCode())
                            .flatMap(ticketMapping -> {
                                vo.setAccountType(ticketMapping.getEmployeeType());
                                vo.setTicketAssignmentFlag(ticketMapping.getTicketAssignmentFlag());

                                if (StringUtils.isNotBlank(ticketMapping.getTicketCategories())) {
                                    vo.setTickets(Splitter.on(",").splitToList(ticketMapping.getTicketCategories()));
                                }

                                List<String> positionCodes = getPositionCodes(ticketMapping.getPositionCodes());

                                if (CollectionUtils.isEmpty(positionCodes)) {
                                    return Mono.just(vo);
                                }
                                vo.setPositionCodes(positionCodes);

                                return getFormatPositionDetails(positionCodes)
                                        .map(positionDetails -> {
                                            vo.setPositionDetails(positionDetails);
                                            return vo;
                                        })
                                        .defaultIfEmpty(vo);
                            })
                            .defaultIfEmpty(vo);

                    Mono<Void> hotelDetailsMono = hotelService.findByHotelCode(employeeHotel.getHotelCode())
                            .doOnNext(hotelVO -> {
                                vo.setHotelName(hotelVO.getHotelName());
                                vo.setMerchantName(hotelVO.getMerchantName());
                            })
                            .then();

                    return Mono.when(ticketMappingMono, hotelDetailsMono).thenReturn(vo);
                })
                .switchIfEmpty(Mono.just(vo));
    }

    /**
     * 构建员工实体
     */
    private HdsEmployeeEntity buildEmployeeEntity(HdsEmployeeSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        HdsEmployeeEntity entity = new HdsEmployeeEntity();
        // 设置实体属性，根据实际情况设置
        entity.setUsername(req.getUsername())
                .setName(req.getName())
                .setMobile(req.getMobile())
                .setEmail(req.getEmail())
                .setStatus(EmployeeStatusEnum.ACTIVE.getCode())
                .setPassword(DEFAULT_PASSWORD)
                .setType(Objects.isNull(req.getType()) ? 3 : req.getType());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setCreatedBy(req.getCreatedBy() != null ? req.getCreatedBy() : headerInfo.getUserId());
        entity.setCreatedByName(req.getCreatedByName() != null ? req.getCreatedByName() : headerInfo.getUsername());
        entity.setRowStatus(EmployeeStatusEnum.ACTIVE.getCode());
        entity.setId(req.getId());
        return entity;
    }

    /**
     * 更新员工实体
     */
    private void updateEmployeeEntity(HdsEmployeeEntity entity, HdsEmployeeSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        entity.setMobile(req.getMobile());
        entity.setEmail(req.getEmail());
        if (StringUtils.isNotEmpty(req.getName())) {
            entity.setName(req.getName());
        }
        entity.setUsername(req.getUsername());
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setUpdatedBy(headerInfo.getUserId());
        entity.setUpdatedByName(headerInfo.getUsername());
    }

    public Mono<List<HotelWithExternalVO>> queryHotelList(Integer employeeId) {
        // 启用、停用都需要查询出来
        return hdsEmployeeHotelService.listByEmployeeId(employeeId, null, null)
                .flatMapMany(Flux::fromIterable)
                .flatMap(hotelEntity -> hotelService.findByHotelCode(hotelEntity.getHotelCode())
                        .map(hotelVO -> new HotelWithExternalVO()
                                .setHotelCode(hotelEntity.getHotelCode())
                                .setHotelName(hotelVO.getHotelName()))
                )
                .collectList();
    }

    @OperationLog(businessType = BussinessTypeEnum.EMPLOYEE,
            operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> updateHotelList(HdsEmployeeUpdateHotelReq req) {
        // 查询数据库中员工关联的酒店记录
        return hdsEmployeeHotelService.listByEmployeeId(req.getId(), null, null)
                .flatMap(existingRecords -> {
                    // 提取数据库中已有的酒店代码
                    List<String> existingHotelCodes = existingRecords.stream()
                            .map(HdsEmployeeHotelEntity::getHotelCode)
                            .toList();

                    // 找出请求参数中新增的酒店代码
                    List<String> codesToAdd = req.getHotelCodes().stream()
                            .filter(code -> !existingHotelCodes.contains(code))
                            .collect(Collectors.toList());

                    // 找出数据库中存在但请求参数中没有的酒店代码（需要删除的）
                    List<String> codesToRemove = existingHotelCodes.stream()
                            .filter(code -> !req.getHotelCodes().contains(code))
                            .collect(Collectors.toList());

                    // 如果没有新增或删除的操作，直接返回成功
                    if (codesToAdd.isEmpty() && codesToRemove.isEmpty()) {
                        return Mono.just(true);
                    }

                    // 执行新增操作
                    Mono<Boolean> addOperation = codesToAdd.isEmpty()
                            ? Mono.just(true)
                            : hdsEmployeeHotelService.create(req.getId(), codesToAdd, req.getRoleCode());

                    // 保存门店与员工的关联
                    Mono<Boolean> addMappingOperation = codesToAdd.isEmpty()
                            ? Mono.just(true)
                            : hdsEmployeeTicketMappingService.save(req.getId(), req.getName(), codesToAdd, req.getAccountType(), req.getTicketAssignmentFlag(), true);

                    // 执行逻辑删除操作（将状态更新为 注销）
                    Mono<Boolean> removeOperation = codesToRemove.isEmpty()
                            ? Mono.just(true)
                            : hdsEmployeeHotelService.updateStatus(req.getId(), codesToRemove,
                            EmployeeStatusEnum.CANCELED.getCode(), true);

                    // 合并新增和删除操作的结果
                    return Mono.zip(addOperation, addMappingOperation, removeOperation)
                            .map(tuple -> tuple.getT1() && tuple.getT2() && tuple.getT3());
                });
    }

    public Mono<Boolean> importEmployee(HdsEmployeeSaveReq req) {
        return create(req)
                .doOnSuccess(success -> log.info("创建员工账号并关联酒店结果: {},{}", success,JacksonUtils.writeValueAsString(req)))
                .onErrorResume(e -> {
                    log.error("创建员工账号失败, req:{} message:{}", JacksonUtils.writeValueAsString(req), e.getMessage());
                    return Mono.just(false);
                });
    }

    public Mono<List<HdsEmployeeEntity>> batchQueryEmployees(Set<String> mobiles, Set<String> usernames, Set<String> emails) {
        // 检查所有参数是否为空
        if (CollectionUtils.isEmpty(mobiles) && CollectionUtils.isEmpty(usernames) && CollectionUtils.isEmpty(emails)) {
            return Mono.just(Collections.emptyList());
        }
        Criteria criteria = null;

        // 添加手机号条件
        if (CollectionUtils.isNotEmpty(mobiles)) {
            criteria = Criteria.where(HdsEmployeeFieldEnum.mobile.name()).in(mobiles);
        }

        // 添加用户名条件
        if (CollectionUtils.isNotEmpty(usernames)) {
            Criteria usernameCriteria = Criteria.where(HdsEmployeeFieldEnum.username.name()).in(usernames);
            criteria = (criteria == null) ? usernameCriteria : criteria.or(usernameCriteria);
        }

        // 添加邮箱条件
        if (CollectionUtils.isNotEmpty(emails)) {
            Criteria emailCriteria = Criteria.where(HdsEmployeeFieldEnum.email.name()).in(emails);
            criteria = (criteria == null) ? emailCriteria : criteria.or(emailCriteria);
        }

        // 构建查询
        if (criteria == null) {
            return Mono.just(Collections.emptyList());
        }

        Query query = Query.query(criteria);

        // 执行查询并返回结果
        return r2dbcEntityTemplate.select(query, HdsEmployeeEntity.class)
                .collectList();
    }

    /**
     * 员工删除
     *
     * @param req
     * @return
     */
    public Mono<Boolean> delete(HdsEmployeeDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id must not be null");

        // 获取请求头信息并执行删除操作
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionDelete(headerInfo, req)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "修改员工状态失败"))))
                .defaultIfEmpty(false);
    }

    /**
     * 事务更新状态
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(HeaderUtils.HeaderInfo headerInfo, HdsEmployeeDeleteReq req) {
        return transactionalOperator.transactional(
                hdsEmployeeRepository.findById(req.getId())
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "员工不存在")))
                        .flatMap(employee -> {
                            Mono<Boolean> updateOperation = Mono.just(true);
                            if (EmployeeTypeEnum.isGroupEmployee(employee.getType())) {
                                // 更新员工状态
                                employee.setRowStatus(0);
                                employee.setUpdatedBy(headerInfo.getUserId());
                                employee.setUpdatedByName(headerInfo.getUsername());
                                employee.setUpdatedAt(LocalDateTime.now());
                                updateOperation = hdsEmployeeRepository.save(employee)
                                        .then(Mono.just(true));
                                return updateOperation;
                            } else if (EmployeeTypeEnum.HOTEL.getCode().equals(employee.getType())) {
                                if (CollectionUtils.isNotEmpty(req.getHotelCodes())) {
                                    updateOperation = hdsEmployeeHotelService.updateEmployeeHotelStatus(req.getId(),
                                            req.getHotelCodes(), null, true);
                                }
                            }
                            return updateOperation;
                        })
                        .defaultIfEmpty(false)
        );
    }

    public Mono<Boolean> needFilterByEmployeeId(HeaderUtils.HeaderInfo headerInfo) {
        // 如果没有用户ID，则不需要过滤
        if (headerInfo == null || org.apache.commons.lang.StringUtils.isBlank(headerInfo.getUserId())) {
            return Mono.just(false);
        }
        return hdsEmployeeRepository.findById(Integer.parseInt(headerInfo.getUserId()))
                .map(employee -> {
                    // 根据员工类型判断是否需要过滤
                    return !EmployeeTypeEnum.GROUP.getCode().equals(employee.getType());
                })
                .defaultIfEmpty(false)
                .onErrorResume(e -> {
                    log.error("获取员工信息失败: userId={}, error={}",
                            headerInfo.getUserId(), e.getMessage(), e);
                    return Mono.just(false);
                });
    }
}
