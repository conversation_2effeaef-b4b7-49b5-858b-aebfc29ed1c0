package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/4/1 14:44
 * @Description：门店统计信息
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelStatisticsVO {

    /**
     * 总商户数量
     */
    private Long totalMerchantCount;

    /**
     * 总门店数量
     */
    private Long totalHotelCount;

    /**
     * 总设备数量
     */
    private Long totalDeviceCount;
}
