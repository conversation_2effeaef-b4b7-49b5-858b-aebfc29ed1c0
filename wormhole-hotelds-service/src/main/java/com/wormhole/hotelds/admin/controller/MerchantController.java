package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.MerchantDeleteReq;
import com.wormhole.hotelds.admin.model.req.MerchantSearchReq;
import com.wormhole.hotelds.admin.model.req.MerchantSaveReq;
import com.wormhole.hotelds.admin.model.vo.MerchantVO;
import com.wormhole.hotelds.admin.service.MerchantService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/3/26 10:57
 * @Description：商户控制层
 */
@RestController
@RequestMapping("/merchant")
public class MerchantController {

    @Resource
    private MerchantService merchantService;

    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody MerchantSaveReq merchantSaveReq) {
        return merchantService.create(merchantSaveReq).flatMap(Result::success);
    }

    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody MerchantSaveReq merchantSaveReq) {
        return merchantService.update(merchantSaveReq).flatMap(Result::success);
    }

    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody MerchantDeleteReq req) {
        return merchantService.delete(req).flatMap(Result::success);
    }

    @PostMapping("/search")
    public Mono<Result<PageResult<MerchantVO>>> search(@RequestBody MerchantSearchReq merchantQueryReq) {
        return Mono.zip(merchantService.searchCount(merchantQueryReq),merchantService.search(merchantQueryReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    @GetMapping("/query_list")
    public Mono<Result<List<MerchantVO>>> queryList() {
        return merchantService.queryList().flatMap(Result::success);
    }

    @GetMapping("/query/{id}")
    public Mono<Result<MerchantVO>> findById(@PathVariable("id") Integer id) {
        // 1. 参数验证
        if (id == null || id <= 0) {
            return Result.failed("invalid_parameter", "无效的ID参数");
        }
        return merchantService.findById(id).flatMap(Result::success);
    }
}
