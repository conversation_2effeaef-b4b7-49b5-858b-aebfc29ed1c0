package com.wormhole.hotelds.admin.service;

import com.wormhole.hotelds.admin.model.entity.DeviceAlarmEntity;
import com.wormhole.hotelds.admin.model.vo.DeviceAlarmVO;
import com.wormhole.hotelds.admin.model.req.DeviceAlarmSaveReq;
import com.wormhole.hotelds.admin.model.req.DeviceAlarmDeleteReq;
import com.wormhole.hotelds.admin.model.req.DeviceAlarmSearchReq;
import com.wormhole.hotelds.admin.repository.DeviceAlarmRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:58:23
 * @Description: DeviceAlarm
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeviceAlarmService {

    private final DeviceAlarmRepository repository;
    private final R2dbcEntityTemplate template;

    /**
     * 创建记录
     */
    public Mono<Boolean> create(DeviceAlarmSaveReq req) {
        return repository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 更新记录
     */
    public Mono<Boolean> update(DeviceAlarmSaveReq req) {
        return repository.save(convertToEntity(req))
                .map(result -> true);
    }

    /**
     * 删除记录
     */
    public Mono<Boolean> delete(DeviceAlarmDeleteReq req) {
        return repository.deleteById(req.getId())
                .thenReturn(true);
    }

    /**
     * 搜索列表
     */
    public Mono<List<DeviceAlarmVO>> search(DeviceAlarmSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        return template.select(DeviceAlarmEntity.class)
                .matching(Query.query(criteria))
                .all()
                .map(this::convertToVO)
                .collectList();
    }

    /**
     * 根据ID查询
     */
    public Mono<DeviceAlarmVO> findById(Long id) {
        return repository.findById(id)
                .map(this::convertToVO);
    }

    private Criteria buildSearchCriteria(DeviceAlarmSearchReq req) {
        Criteria criteria = Criteria.empty();
        // TODO: 根据实际查询条件构建Criteria
        return criteria;
    }

    private DeviceAlarmEntity convertToEntity(DeviceAlarmSaveReq req) {
        DeviceAlarmEntity entity = new DeviceAlarmEntity();
        // TODO: 实现请求对象到实体的转换
        return entity;
    }

    private DeviceAlarmVO convertToVO(DeviceAlarmEntity entity) {
        DeviceAlarmVO vo = new DeviceAlarmVO();
        // TODO: 实现实体到VO的转换
        return vo;
    }
}