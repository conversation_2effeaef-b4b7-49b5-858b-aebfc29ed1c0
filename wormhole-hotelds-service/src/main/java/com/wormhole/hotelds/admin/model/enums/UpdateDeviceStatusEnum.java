package com.wormhole.hotelds.admin.model.enums;

import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/3/28 09:05
 * @Description：DeviceStatusEnum
 */
@Getter
public enum UpdateDeviceStatusEnum {

    ENABLE(1, "启用"),
    DISABLE(0, "停用");

    private final Integer code;
    private final String description;

    UpdateDeviceStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举值
     */
    public static UpdateDeviceStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UpdateDeviceStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的状态值
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
