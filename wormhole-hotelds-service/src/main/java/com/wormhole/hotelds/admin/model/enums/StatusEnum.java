package com.wormhole.hotelds.admin.model.enums;

import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/3/26 10:53
 * @Description：StatusEnum
 */
@Getter
public enum StatusEnum {

    /**
     * 有效
     */
    VALID(1),

    /**
     * 无效
     */
    INVALID(0);

    private final Integer code;

    StatusEnum(Integer code) {
        this.code = code;
    }

    public static StatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
