package com.wormhole.hotelds.admin.controller;

import com.wormhole.hotelds.admin.model.vo.HdsRefundRecordVO;
import com.wormhole.hotelds.admin.model.req.HdsRefundRecordSaveReq;
import com.wormhole.hotelds.admin.model.req.HdsRefundRecordDeleteReq;
import com.wormhole.hotelds.admin.model.req.HdsRefundRecordSearchReq;
import com.wormhole.hotelds.admin.service.HdsRefundRecordService;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 18:00:26
 * @Description: HdsRefundRecord
 */
@RestController
@RequestMapping("/hdsrefundrecord")
public class HdsRefundRecordController {

    @Resource
    private HdsRefundRecordService service;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody HdsRefundRecordSaveReq hdsRefundRecordSaveReq) {
        return service.create(hdsRefundRecordSaveReq).flatMap(Result::success);
    }

    /**
     * 更新记录
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HdsRefundRecordSaveReq hdsRefundRecordSaveReq) {
        return service.update(hdsRefundRecordSaveReq).flatMap(Result::success);
    }

    /**
     * 删除记录
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody HdsRefundRecordDeleteReq hdsRefundRecordDeleteReq) {
        return service.delete(hdsRefundRecordDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<List<HdsRefundRecordVO>>> search(@RequestBody HdsRefundRecordSearchReq hdsRefundRecordSearchReq) {
        return service.search(hdsRefundRecordSearchReq).flatMap(Result::success);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<HdsRefundRecordVO>> findById(@PathVariable("id") Long id) {
        return service.findById(id).flatMap(Result::success);
    }
}