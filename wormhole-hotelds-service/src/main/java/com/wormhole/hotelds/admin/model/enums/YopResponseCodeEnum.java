package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/6/24 11:02
 * @Description：YopResponseCodeEnum
 */
@Getter
@AllArgsConstructor
public enum YopResponseCodeEnum {

    /**
     * 成功
     */
    SUCCESS("00000", "成功"),

    /**
     * 参数错误
     */
    PARAM_ERROR("00002", "参数错误"),

    /**
     * 商户状态或信息异常
     */
    MERCHANT_STATUS_ERROR("00011", "商户状态或信息异常"),

    /**
     * 产品未开通
     */
    PRODUCT_NOT_OPENED("00012", "产品未开通"),

    /**
     * 订单已经成功
     */
    ORDER_ALREADY_SUCCESS("00102", "订单已经成功"),

    /**
     * 订单处理中
     */
    ORDER_PROCESSING("00105", "订单处理中"),

    /**
     * 下单失败
     */
    ORDER_FAILED("00201", "下单失败"),

    /**
     * 风控拦截
     */
    RISK_CONTROL_INTERCEPT("00202", "风控拦截"),

    /**
     * 系统异常
     */
    SYSTEM_EXCEPTION("99999", "系统异常"),

    /**
     * 商户关系有误
     */
    MERCHANT_RELATION_ERROR("OPR00001", "商户关系有误"),

    /**
     * 父商编不能为空
     */
    PARENT_MERCHANT_NO_EMPTY("OPR00016", "父商编不能为空"),

    /**
     * 父商编长度有误
     */
    PARENT_MERCHANT_NO_LENGTH_ERROR("OPR00017", "父商编长度有误"),

    /**
     * 子商编长度有误
     */
    MERCHANT_NO_LENGTH_ERROR("OPR00019", "子商编长度有误"),

    /**
     * 商户订单号不能为空
     */
    ORDER_NO_EMPTY("OPR00027", "商户订单号不能为空"),

    /**
     * 商户订单号长度有误
     */
    ORDER_NO_LENGTH_ERROR("OPR00028", "商户订单号长度有误"),

    /**
     * 订单不存在
     */
    ORDER_NOT_EXIST("OPR12002", "订单不存在"),

    /**
     * 调用底层系统异常
     */
    CALL_SYSTEM_ERROR("OPR16001", "调用底层系统异常"),

    /**
     * 系统异常
     */
    SYSTEM_ERROR("OPR99999", "系统异常"),

    /**
     * 未知错误
     */
    UNKNOWN_ERROR("UNKNOWN", "未知错误");

    /**
     * 响应码
     */
    private final String code;

    /**
     * 响应描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 响应码
     * @return 响应码枚举
     */
    public static YopResponseCodeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return UNKNOWN_ERROR;
        }
        for (YopResponseCodeEnum responseCode : values()) {
            if (responseCode.getCode().equals(code)) {
                return responseCode;
            }
        }
        return UNKNOWN_ERROR;
    }

    /**
     * 判断是否成功
     *
     * @param code 响应码
     * @return 是否成功
     */
    public static boolean isSuccess(String code) {
        return SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为系统错误
     *
     * @param code 响应码
     * @return 是否为系统错误
     */
    public static boolean isSystemError(String code) {
        return CALL_SYSTEM_ERROR.getCode().equals(code) || SYSTEM_ERROR.getCode().equals(code);
    }

    /**
     * 判断是否需要重试
     * 系统错误可以重试
     *
     * @param code 响应码
     * @return 是否需要重试
     */
    public static boolean isRetryable(String code) {
        return isSystemError(code);
    }
}
