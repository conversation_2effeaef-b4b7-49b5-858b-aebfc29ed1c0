package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.admin.model.enums.SpaceRoleEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SpaceUserRoleReq implements Serializable {

    private String userId;

    private String spaceCode;

    private String roleCode = SpaceRoleEnum.SPACE_MEMBER.getCode();

}
