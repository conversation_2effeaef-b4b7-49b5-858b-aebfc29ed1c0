package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/5/30 17:59
 * @Description：AgentFlowTypeEnum
 */
@AllArgsConstructor
@Getter
public enum AgentFlowTypeEnum {

    INITIAL_GIFT(1, "初始赠送"),
    INVITE_REWARD(2, "邀请奖励"),
    RECHARGE(3, "充值获得"),
    ADMIN_ADJUSTMENT(4, "人工设置"),
    SYSTEM_ADJUSTMENT(5, "系统调整");

    private final Integer code;
    private final String name;

    /**
     * 根据 code 获取对应的枚举值
     *
     * @param code AI 产品类型的 code
     * @return 对应的枚举值，如果没有找到则返回 null
     */
    public static String fromCode(Integer code) {
        for (AgentFlowTypeEnum type : values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type.getName();
            }
        }
        return "未知";
    }
}
