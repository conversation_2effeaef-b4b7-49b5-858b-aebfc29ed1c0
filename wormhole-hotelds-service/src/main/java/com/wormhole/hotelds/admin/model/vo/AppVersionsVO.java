package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import com.wormhole.hotelds.core.model.entity.HdsAppVersionsEntity;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.*;
import lombok.experimental.*;
import org.springframework.beans.BeanUtils;

import java.io.*;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:58:35
 * @Description: AppVersions
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppVersionsVO implements Serializable {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 渠道 安卓/ios
     */
    private String platform;

    /**
     * 上线日期
     */
    private String onlineTime;

    /**
     * app版本号
     */
    private String appVersion;

    /**
     * 应用（如hotel_admin / hotel_guest等等）
     */
    private String appCode;

    /**
     * 版本内容
     */
    private String updateContent;

    /**
     * app安卓下载地址
     */
    private String downloadUrl;

    /**
     * 是否强制更新（1是 0否）
     */
    private Integer isForce;

    /**
     * 状态：有效/无效
     */
    private Integer status;

    /**
     * 是否需要更新
     */
    private Boolean needUpdate;

    public static AppVersionsVO toVO(HdsAppVersionsEntity entity){
        AppVersionsVO vo = new AppVersionsVO();
        BeanUtils.copyProperties(entity,vo);
        vo.setOnlineTime(SimpleDateUtils.formatLocalDateTimeToDate(entity.getOnLineTime()));
        return vo;
    }
}