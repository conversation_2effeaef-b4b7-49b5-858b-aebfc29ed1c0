package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.HdsDeviceModelEntity;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * @Author：flx
 * @Date：2025/4/7 14:34
 * @Description：DeviceModelVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceModelVO {

    private Long id;

    /**
     * 设备类型
     */
    private String category;

    /**
     * 设备类型名称
     */
    private String modelName;

    /**
     * 设备型号
     */
    private String modelCode;

    /**
     * 厂商名称
     */
    private String manufacturer;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 联系人
     */
    private String manufacturerContactPerson;

    /**
     * 联系方式
     */
    private String manufacturerContact;

    /**
     * 保修期（月）
     */
    private Integer warrantyMonths;

    /**
     * 备注
     */
    private String remark;

    /**
     * app类型
     */
    private String deviceAppType;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 厂商服务提供商
     */
    private String manufacturerServiceProvider;

    /**
     * 保修情况
     */
    private String warrantyInfo;

    public static DeviceModelVO toVo(HdsDeviceModelEntity deviceModelEntity) {
        DeviceModelVO deviceModelVO = new DeviceModelVO();
        BeanUtils.copyProperties(deviceModelEntity, deviceModelVO);
        return deviceModelVO;
    }
}
