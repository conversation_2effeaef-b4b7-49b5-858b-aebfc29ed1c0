package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/5/19 09:43
 * @Description：InvitationCodeSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InvitationCodeSearchReq extends QueryCondition {

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 邀请码名称
     */
    private String name;


    /**
     * 状态: 1-启用, 0-关闭
     */
    private Integer status;

    /**
     * 门店code
     */
    private String hotelCode;
}
