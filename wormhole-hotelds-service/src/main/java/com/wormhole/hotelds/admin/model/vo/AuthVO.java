package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @author: huang<PERSON>jie
 * @date: 2025/5/15
 * @Description: 用户认证信息VO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AuthVO {
    
    /**
     * 用户ID
     */
    private Integer id;

    /**
     * 用户名
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String name;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 邮箱
     */
    private String email;

    /**
     * 员工类型 1-集团 2-服务商 3-酒店
     */
    private Integer type;

    /**
     * 用户关联的酒店列表
     */
    private List<HotelWithExternalVO> hotels;


}