package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.resp.SpaceRoleResp;
import com.wormhole.hotelds.admin.service.SpaceRoleService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
@RestController
@RequestMapping("/space/role")
public class SpaceRoleController {

    @Resource
    private SpaceRoleService spaceRoleService;

    @GetMapping("/list")
    public Mono<Result<List<SpaceRoleResp>>> list() {
        return spaceRoleService.list().flatMap(Result::success);
    }

}
