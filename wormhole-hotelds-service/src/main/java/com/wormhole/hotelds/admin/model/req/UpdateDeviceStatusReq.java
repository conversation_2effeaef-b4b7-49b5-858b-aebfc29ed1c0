package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.admin.model.enums.UpdateDeviceStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/3/28 09:15
 * @Description：UpdateDeviceStatusReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UpdateDeviceStatusReq {

    /**
     * 可以传单个也可以传多个
     */
    private List<Integer> idList;

    /**
     * @see UpdateDeviceStatusEnum
     */
    private Integer updateDeviceStatus;
}
