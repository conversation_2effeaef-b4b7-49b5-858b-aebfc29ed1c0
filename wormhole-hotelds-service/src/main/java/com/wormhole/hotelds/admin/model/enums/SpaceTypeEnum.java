package com.wormhole.hotelds.admin.model.enums;

import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/3/27 09:37
 * @Description：SpaceTypeEnum
 */
@Getter
public enum SpaceTypeEnum {

    ROOM("ROOM", "客房"),
    FRONT_DESK("FRONT_DESK", "前台"),
    RESTAURANT("RESTAURANT", "餐厅"),
    GYM("GYM", "健身房"),
    CONFERENCE_ROOM("CONFERENCE_ROOM", "会议室"),
    LOBBY("LOBBY", "大堂"),
    PARKING("PARKING", "停车场"),
    SWIMMING_POOL("SWIMMING_POOL", "游泳池"),
    SPA("SPA", "水疗中心"),
    BAR("BAR", "酒吧"),
    SHOP("SHOP", "商店"),
    LAUNDRY("LAUNDRY", "洗衣房"),
    KITCHEN("K<PERSON><PERSON><PERSON>", "厨房"),
    STORAGE("STORAGE", "储物间"),
    OFFICE("OFFICE", "办公室"),
    OTHER("OTHER", "其他");

    private final String code;
    private final String desc;

    SpaceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static SpaceTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SpaceTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断code是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
}
