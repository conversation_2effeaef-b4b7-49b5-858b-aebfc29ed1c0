package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2025/4/21 18:11
 */
@Data
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@NoArgsConstructor
@AllArgsConstructor
public class HdsWechatQrCodeVO extends DevicePositionSearchVO {

    private String qrCodeId;

    private String qrCodeUrl;

    private String posterUrl;

    private String normalPosterUrl;

}
