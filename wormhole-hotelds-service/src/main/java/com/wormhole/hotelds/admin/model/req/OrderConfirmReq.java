package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/6/19 09:30
 * @Description：ConfirmOrderReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderConfirmReq {

    /**
     * 套餐编码
     */
    private String packageCode;

    /**
     * 门店套餐编码
     */
    private String hotelPackageCode;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 周期类型
     */
    private Integer periodType;
}
