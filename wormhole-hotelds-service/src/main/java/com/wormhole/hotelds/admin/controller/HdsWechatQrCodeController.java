package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.DevicePositionSearchReq;
import com.wormhole.hotelds.admin.model.req.WechatQrCodeDownlandReq;
import com.wormhole.hotelds.admin.model.vo.HdsWechatQrCodeVO;
import com.wormhole.hotelds.admin.service.HdsWechatQrCodeService;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 微信二维码数据表 Controller
 *
 * <AUTHOR>
 * @date 2025-04-09 10:51:40
 */
@RestController
@RequestMapping("/hdswechatqrcode")
public class HdsWechatQrCodeController {

    @Resource
    private HdsWechatQrCodeService wechatQrCodeService;

    @GetMapping("/downlandByPositionCode")
    public Mono<Result<String>> downlandByPositionCode(@RequestParam("hotel_code") String hotelCode,
                                                       @RequestParam("position_code") String positionCode,
                                                       @RequestParam("type") String type) {
        return wechatQrCodeService.downlandByPositionCode(hotelCode, positionCode, type, null).flatMap(Result::success);
    }

    @PostMapping("/downlandByIds")
    public Mono<Result<String>> downlandByIds(@RequestBody WechatQrCodeDownlandReq req) {
        return wechatQrCodeService.downlandByIds(req).flatMap(Result::success);
    }

    @PostMapping("/query/page")
    public Mono<Result<PageResult<HdsWechatQrCodeVO>>> queryPage(@RequestBody DevicePositionSearchReq wechatQrCodeReq) {
        wechatQrCodeReq.setDeviceAppType(DeviceTypeEnum.ROOM.getCode());
        return Mono.zip(wechatQrCodeService.searchCount(wechatQrCodeReq), wechatQrCodeService.search(wechatQrCodeReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }



}