package com.wormhole.hotelds.admin.service;

import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.*;
import lombok.extern.slf4j.*;
import org.apache.commons.lang3.*;
import org.springframework.data.r2dbc.core.*;
import org.springframework.data.relational.core.query.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * 国家区域 Service
 *
 * <AUTHOR>
 * @date 2025-03-27 11:56:48
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AreaService {

    private final R2dbcEntityTemplate template;


    public Flux<HdsAreaEntity> findByCondition(Map<String, Object> condition) {
        // 构建查询条件
        Criteria criteria = Criteria.empty();
        for (Map.Entry<String, Object> entry : condition.entrySet()) {
            criteria = criteria.and(Criteria.where(entry.getKey()).is(entry.getValue()));
        }

        // 执行查询
        return template.select(HdsAreaEntity.class)
                .matching(Query.query(criteria))
                .all();
    }

    /**
     * 查询列表
     */
    public Mono<List<AreaVo>> list(AreaReq req) {
        Map<String, Object> condition = new HashMap<>();
        if (StringUtils.isNotBlank(req.getParentCode())) {
            condition.put(HdsAreaFieldEnum.parent_code.name(), req.getParentCode());
        }
        return findByCondition(condition).map(this::convertToAreaVo).collectList();
    }

    /**
     * 递归查找所有子级区域
     */
    public Mono<List<AreaVo>> listRecursively(AreaReq req) {
        // 1. 一次查出所有区域
        String parentCode = StringUtils.isEmpty(req.getParentCode()) ? "CN" : req.getParentCode();
        return template.select(HdsAreaEntity.class)
                .all()
                .collectList()
                .map(allEntities -> {
                    // 2. 递归查找所有子孙节点
                    List<AreaVo> result = new ArrayList<>();
                    Map<String, List<HdsAreaEntity>> parentMap = new HashMap<>();
                    for (HdsAreaEntity entity : allEntities) {
                        parentMap.computeIfAbsent(entity.getParentCode(), k -> new ArrayList<>()).add(entity);
                    }
                    collectChildren(parentCode, parentMap, result);
                    return result;
                });
    }

    // 递归收集所有子节点
    private void collectChildren(String parentCode, Map<String, List<HdsAreaEntity>> parentMap, List<AreaVo> result) {
        List<HdsAreaEntity> children = parentMap.get(parentCode);
        if (children == null) return;
        for (HdsAreaEntity child : children) {
            result.add(convertToAreaVo(child));
            collectChildren(child.getAreaCode(), parentMap, result);
        }
    }

    private AreaVo convertToAreaVo(HdsAreaEntity entity) {
        return new AreaVo()
                .setAreaType(entity.getAreaType())
                .setAreaCode(entity.getAreaCode())
                .setParentCode(entity.getParentCode())
                .setAreaPinyin(entity.getAreaPinyin())
                .setFirstLetter(entity.getFirstLetter())
                .setPostCode(entity.getPostCode())
                .setAreaName(entity.getAreaName())
                .setAreaNameEn(entity.getAreaNameEn());
    }

}