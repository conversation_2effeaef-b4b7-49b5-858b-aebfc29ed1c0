package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.HdsHotelPackageEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Author：flx
 * @Date：2025/1/16 15:30
 * @Description：HdsHotelPaymentPackageRepository
 */
@Repository
public interface HdsHotelPackageRepository extends ReactiveCrudRepository<HdsHotelPackageEntity, Integer> {

    Flux<HdsHotelPackageEntity> findByHotelCodeAndStatus(String hotelCode, Integer status);

    Mono<Boolean> existsByPackageCode(String packageCode);

    Mono<HdsHotelPackageEntity> findByHotelPackageCode(String hotelPackageCode);

    Mono<Boolean> existsByPackageCodeAndStatus(String packageCode, Integer status);

    Mono<Boolean> existsByHotelCodeAndStatus(String hotelCode, Integer status);

    Flux<HdsHotelPackageEntity> findAllByPackageCode(String packageCode);
}