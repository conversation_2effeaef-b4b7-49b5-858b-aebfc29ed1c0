package com.wormhole.hotelds.admin.job;

import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.*;
import com.xxl.job.core.handler.annotation.*;
import lombok.extern.slf4j.*;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.time.*;
import java.util.*;

/**
 * 酒店服务到期检查任务
 */
@Slf4j
@Component
public class HotelServiceExpiryJob {

    @Resource
    private HotelService hotelService;

    @Resource
    private HotelServiceExpiryHandler hotelServiceExpiryHandler;

    /**
     * 检查OTA智能体服务到期情况
     * 每天凌晨12点执行
     */
    @XxlJob("checkHotelServiceExpiryJob")
    public void checkHotelServiceExpiry(String param) {
        log.info("开始检查酒店OTA智能体服务到期情况,参数:{}", param);

        final List<String> hotelCodes;
        if (StringUtils.isNotEmpty(param)) {
            Map<String, Object> map = JacksonUtils.readValue(param);
            hotelCodes = (List<String>) map.get("hotelCodes");
        } else {
            hotelCodes = Collections.emptyList();
        }
        hotelService.queryList()
                .flatMapMany(Flux::fromIterable)
                .filter(hotel -> isOtaServiceExpired(hotel, hotelCodes))
                .collectList()
                .flatMap(hotels -> {
                    if (CollectionUtils.isEmpty(hotels)) {
                        log.info("没有需要检查的酒店OTA智能体服务到期情况");
                        return Mono.empty();
                    }
                    log.info("需要检查的酒店数量: {}", hotels.size());
                    return Flux.fromIterable(hotels)
                            .flatMap(hotel -> hotelServiceExpiryHandler.sendHotelServiceExpiryMessage(hotel.getHotelCode(), ChannelEventEnum.hotel_service_expiry)
                                    .doOnError(e -> log.error("发送酒店服务到期消息失败: hotelCode={}, error={}", hotel.getHotelCode(), e.getMessage())))
                            .then();
                })
                .doOnSuccess(unused -> log.info("完成检查酒店OTA智能体服务到期情况"))
                .doOnError(e -> log.error("检查酒店OTA智能体服务到期情况时发生错误", e))
                .block();
    }

    /**
     * 判断酒店的OTA服务是否已到期
     *
     * @param hotel 酒店信息
     * @return 是否到期
     */
    private boolean isOtaServiceExpired(HotelVO hotel, List<String> hotelCodes) {
        return (CollectionUtils.isEmpty(hotelCodes) || hotelCodes.contains(hotel.getHotelCode()))
                && StatusEnum.VALID.getCode().equals(hotel.getRowStatus())
                && StatusEnum.VALID.getCode().equals(hotel.getStatus())
                && hotel.getAiProductTypes().contains(AiProductTypeEnum.OTA_AGENT.getCode())
                && StringUtils.isNotEmpty(hotel.getOtaEndTime())
                && LocalDate.parse(hotel.getOtaEndTime()).isBefore(LocalDate.now());
    }
}