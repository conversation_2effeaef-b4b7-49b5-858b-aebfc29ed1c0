package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

import java.util.*;

/**
 * @Author：flx
 * @Date：2025/4/3 09:19
 * @Description：HdsEmployeeDeleteReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsEmployeeQueryReq {

    private Integer id;

    private String hotelCode;

}
