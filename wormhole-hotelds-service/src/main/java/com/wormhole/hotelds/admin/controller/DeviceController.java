package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.DeviceService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:57:52
 * @Description: Device
 */
@RestController
@RequestMapping("/device")
public class DeviceController {

    @Resource
    private DeviceService deviceService;

    /**
     * 创建记录
     */
    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody DeviceSaveReq deviceSaveReq) {
        return deviceService.create(deviceSaveReq).flatMap(Result::success);
    }

    /**
     * 更新记录
     */
    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody DeviceSaveReq deviceSaveReq) {
        return deviceService.update(deviceSaveReq).flatMap(Result::success);
    }

    /**
     * 删除记录
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody DeviceDeleteReq deviceDeleteReq) {
        return deviceService.delete(deviceDeleteReq).flatMap(Result::success);
    }

    /**
     * 搜索列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<DeviceSearchVO>>> search(@RequestBody DeviceSearchReq deviceSearchReq) {
        return Mono.zip(deviceService.searchCount(deviceSearchReq), deviceService.search(deviceSearchReq))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/query/{id}")
    public Mono<Result<DeviceVO>> findById(@PathVariable("id") Long id) {
        return deviceService.findById(id).flatMap(Result::success);
    }

    /**
     * 获取门店统计信息
     *
     * @param deviceStatisticsReq
     * @return
     */
    @PostMapping("/statistics")
    public Mono<Result<DeviceStatisticsVO>> getStatistics(@RequestBody DeviceStatisticsReq deviceStatisticsReq) {
        return deviceService.getStatistics(deviceStatisticsReq).flatMap(Result::success);
    }

    /**
     * 获取门店统计信息
     *
     * @param deviceStatisticsReq
     * @return
     */
    @PostMapping("/distribution/count")
    public Mono<Result<DeviceDistributionCountVO>> getDeviceDistributionCount(@RequestBody DeviceDistributionReq deviceStatisticsReq) {
        return deviceService.getDeviceDistributionCount(deviceStatisticsReq).flatMap(Result::success);
    }

    /**
     * 获取门店统计信息
     *
     * @param deviceStatisticsReq
     * @return
     */
    @PostMapping("/distribution")
    public Mono<Result<DeviceDistributionVO>> getDeviceDistribution(@RequestBody DeviceDistributionReq deviceStatisticsReq) {
        return deviceService.getDeviceDistribution(deviceStatisticsReq).flatMap(Result::success);
    }

    /**
     * 设备解绑
     */
    @PostMapping("/unbind")
    public Mono<Result<Boolean>> unbind(@RequestBody DeviceUnbindReq deviceUnbindReq) {
        return deviceService.unbind(deviceUnbindReq).flatMap(Result::success);
    }
}