package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-27 17:30:00
 * @Description: 发票申请修改请求
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsInvoiceApplicationUpdateReq {

    /**
     * 申请ID（必填）
     */
    private Long id;

    /**
     * 勾选的账单ID数组（必填）
     */
    private List<Integer> billIds;

    /**
     * 发票抬头（必填）
     */
    private String invoiceHeader;

    /**
     * 纳税人识别号（必填）
     */
    private String taxNumber;

    /**
     * 发票内容
     */
    private String invoiceContent = "服务费";

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 接收邮箱（必填）
     */
    private String receiveEmail;

    /**
     * 更多信息/备注
     */
    private String moreInfo;

    /**
     * 是否默认：1-是，0-否
     */
    private Integer isDefault = 0;
} 