package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @Author：flx
 * @Date：2025/3/27 18:24
 * @Description：DeviceSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceSearchReq extends QueryCondition {

    /**
     * id
     */
    private Long id;

    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * 是否模糊查询门店code
     */
    private boolean hotelCodeLike;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 设备类型code
     */
    private String modelCode;

    private Long modelId;

    /**
     * 设备类型名称
     */
    private String modelName;

    /**
     * 设备code
     */
    private String deviceCode;

    /**
     * 设备状态
     */
    private Integer deviceStatus;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;
}
