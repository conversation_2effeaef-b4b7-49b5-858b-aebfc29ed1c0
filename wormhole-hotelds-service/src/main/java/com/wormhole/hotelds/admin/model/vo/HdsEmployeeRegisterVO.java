package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/5/20 11:50
 * @Description：HdsEmployeeRegisterVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsEmployeeRegisterVO {

    /**
     * 新老用户
     */
    private boolean isNewUser;

    /**
     * 角色列表
     */
    private List<String> roleCodes;
}
