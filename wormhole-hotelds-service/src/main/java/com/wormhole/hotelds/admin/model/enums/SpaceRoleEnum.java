package com.wormhole.hotelds.admin.model.enums;

import lombok.Getter;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
@Getter
public enum SpaceRoleEnum {

    SPACE_OWNER("owner", "所有者"),

    SPACE_MANAGER("manager", "管理者"),

    SPACE_MEMBER("member", "成员"),

    ;

    private final String code;

    private final String name;

    SpaceRoleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (SpaceRoleEnum spaceRoleEnum : values()) {
            if (spaceRoleEnum.getCode().equals(code)) {
                return spaceRoleEnum.getName();
            }
        }
        return null;
    }

}
