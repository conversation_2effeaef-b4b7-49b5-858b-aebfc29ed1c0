package com.wormhole.hotelds.admin.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.*;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.StatusEnum;
import com.wormhole.hotelds.admin.model.req.BrandDeleteReq;
import com.wormhole.hotelds.admin.model.req.BrandSaveReq;
import com.wormhole.hotelds.admin.model.req.BrandSearchReq;
import com.wormhole.hotelds.admin.model.vo.BrandVO;
import com.wormhole.hotelds.admin.repository.BrandRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/3/26 10:27
 * @Description：BrandService
 */
@Slf4j
@Service
public class BrandService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private BrandRepository brandRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodeGeneratorUtils codeGeneratorUtils;

    @Resource
    private CodePoolManager codePoolManager;

    public Mono<HdsBrandEntity> save(HdsBrandEntity hdsBrandEntity){
        return brandRepository.save(hdsBrandEntity);
    }

    /**
     * 创建商户
     */
    public Mono<Boolean> create(BrandSaveReq brandSaveReq) {
        ValidatorUtils.validateBrandSaveReq(brandSaveReq);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionCreate(brandSaveReq, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "merchant create error")))
                );
    }

    /**
     * 事务创建
     */
    private Mono<Boolean> executeTransferWithinTransactionCreate(BrandSaveReq brandSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        return codeGeneratorUtils.generateCodes(BussinessTypeEnum.BRAND.getBusinessType())
                .flatMap(codeInfo -> transactionalOperator.transactional(
                        brandRepository.save(buildHdsBrandEntity(brandSaveReq, headerInfo,codeInfo)))
                        .doOnSuccess(v -> log.info("brand create success: id={}, username={}", brandSaveReq.getId(), headerInfo.getUsername()))
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 删除商户
     */
    public Mono<Boolean> delete(BrandDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(userInfo -> executeTransferWithinTransactionDelete(userInfo, req.getId())
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "brand delete error")))
                );
    }

    /**
     * 事务删除
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(HeaderUtils.HeaderInfo headerInfo, Integer id) {
        return transactionalOperator.transactional(
                brandRepository.deleteById(id)
                        .doOnSuccess(v -> log.info("brand delete success: id={}, username={}", id, headerInfo.getUsername()))
                        .then(Mono.fromCallable(() -> true))
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 更新商户
     */
    public Mono<Boolean> update(BrandSaveReq brandSaveReq) {
        Preconditions.checkArgument(Objects.nonNull(brandSaveReq), "brandSaveReq must not be null");
        Preconditions.checkArgument(Objects.nonNull(brandSaveReq.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionUpdate(brandSaveReq, headerInfo)
                        .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "brand update error")))
                );
    }

    /**
     * 事务更新
     */
    private Mono<Boolean> executeTransferWithinTransactionUpdate(BrandSaveReq brandSaveReq, HeaderUtils.HeaderInfo headerInfo) {
        return transactionalOperator.transactional(
                brandRepository.findById(brandSaveReq.getId())
                        .flatMap(existingBrand -> {
                            updateHdsBrandEntity(existingBrand, brandSaveReq, headerInfo);
                            return brandRepository.save(existingBrand);
                        })
                        .doOnSuccess(v -> log.info("brand update success: id={}, username={}", brandSaveReq.getId(), headerInfo.getUsername()))
                        .map(v -> true)
                        .defaultIfEmpty(false)
        );
    }

    /**
     * 搜索商户列表
     */
    public Mono<List<BrandVO>> search(BrandSearchReq brandSearchReq) {
        Query query = buildQuery(brandSearchReq);
        return r2dbcEntityTemplate.select(query, HdsBrandEntity.class)
                .collectList()
                .map(brandEntities -> brandEntities.stream()
                        .map(BrandVO::toVo)
                        .toList());
    }
    public Mono<Long> searchCount(BrandSearchReq brandSearchReq) {
        Query query = buildQuery(brandSearchReq);
        return r2dbcEntityTemplate.count(query, HdsBrandEntity.class);
    }

    private Query buildQuery(BrandSearchReq brandSearchReq) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(brandSearchReq.getBrandName())) {
            criteria = criteria.and("brand_name").like("%" + brandSearchReq.getBrandName() + "%");
        }
        if (StringUtils.isNotBlank(brandSearchReq.getBrandLogo())) {
            criteria = criteria.and("brand_logo").is(brandSearchReq.getBrandLogo());
        }
        if (StringUtils.isNotBlank(brandSearchReq.getBrandDesc())) {
            criteria = criteria.and("brand_desc").like("%" + brandSearchReq.getBrandDesc() + "%");
        }
        if (StringUtils.isNotBlank(brandSearchReq.getMerchantId())) {
            criteria = criteria.and("merchant_id").is(brandSearchReq.getMerchantId());
        }
        if (StringUtils.isNotBlank(brandSearchReq.getMerchantName())) {
            criteria = criteria.and("merchant_name").like("%" + brandSearchReq.getMerchantName() + "%");
        }
        if (Objects.nonNull(brandSearchReq.getStarType())) {
            criteria = criteria.and("star_type").is(brandSearchReq.getStarType());
        }
        if (Objects.nonNull(brandSearchReq.getStatus())) {
            criteria = criteria.and("status").is(brandSearchReq.getStatus());
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "created_at");
        return Query.query(criteria)
                .sort(sort)
                .limit(brandSearchReq.getPageSize())
                .offset((long) (brandSearchReq.getCurrent() - 1) * brandSearchReq.getPageSize());
    }

    /**
     * 查询品牌列表
     * @return
     */
    public Mono<List<BrandVO>> queryList() {
        Criteria criteria = Criteria.where("status").is(StatusEnum.VALID.getCode());
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.select(query, HdsBrandEntity.class)
                .collectList()
                .map(brandEntities -> brandEntities.stream()
                        .map(BrandVO::toVo)
                        .toList());
    }

    /**
     * 根据ID查询商户
     */
    public Mono<BrandVO> findById(Integer id) {
        Preconditions.checkArgument(Objects.nonNull(id), "id must not be null");
        return brandRepository.findById(id)
                .map(BrandVO::toVo)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "merchant not found")));
    }

    /**
     * 构建商户实体
     */
    private HdsBrandEntity buildHdsBrandEntity(BrandSaveReq req, HeaderUtils.HeaderInfo headerInfo, CodeInfo codeInfo) {
        HdsBrandEntity brandEntity = new HdsBrandEntity();
        BeanUtils.copyProperties(req, brandEntity);
        brandEntity.setStatus(StatusEnum.VALID.getCode());
        brandEntity.setBrandCode(codeInfo.getCode());
        brandEntity.setCreatedBy(UserUtils.getUserId(headerInfo));
        brandEntity.setCreatedByName(UserUtils.getUserName(headerInfo));
        brandEntity.setCreatedAt(LocalDateTime.now());
        return brandEntity;
    }

    /**
     * 更新商户实体
     */
    private void updateHdsBrandEntity(HdsBrandEntity existingBrand, BrandSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        if (StringUtils.isNotBlank(req.getBrandName())) {
            existingBrand.setBrandName(req.getBrandName());
        }
        if (StringUtils.isNotBlank(req.getBrandLogo())) {
            existingBrand.setBrandLogo(req.getBrandLogo());
        }
        if (StringUtils.isNotBlank(req.getBrandDesc())) {
            existingBrand.setBrandDesc(req.getBrandDesc());
        }
        if (Objects.nonNull(req.getStarType())) {
            existingBrand.setStarType(req.getStarType());
        }

        existingBrand.setUpdatedBy(UserUtils.getUserId(headerInfo));
        existingBrand.setUpdatedByName(UserUtils.getUserName(headerInfo));
        existingBrand.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * 查询品牌是否存在，如果不存在则创建一个空的品牌对象，如果存在则返回存在的品牌对象
     *
     * @param brandName 品牌名称
     * @return 返回品牌实体
     */
    public Mono<HdsBrandEntity> getOrPrepareBrand(String brandName, HeaderUtils.HeaderInfo headerInfo) {
        if (org.apache.commons.lang.StringUtils.isBlank(brandName)) {
            return Mono.just(new HdsBrandEntity());
        }
        return brandRepository.findByBrandName(brandName)
                .switchIfEmpty(
                        codePoolManager.getCodeFromPool(BussinessTypeEnum.BRAND.getBusinessType())
                                .map(code -> buildHdsBrandEntity(brandName, code, headerInfo)));
    }

    /**
     * 查询品牌是否存在，如果存在则更新品牌对象（更新场景）
     *
     * @param brandName         品牌名称
     * @param headerInfo        请求头信息
     * @param existingHotelMono 现有酒店信息
     * @return 品牌实体
     */
    public Mono<HdsBrandEntity> getAndUpdateBrand(String brandName, HeaderUtils.HeaderInfo headerInfo, Mono<HdsHotelInfoEntity> existingHotelMono) {
        return existingHotelMono.flatMap(existingHotel -> {
            if (org.apache.commons.lang.StringUtils.isBlank(existingHotel.getBrandCode())) {
                // 酒店没有关联品牌
                if (org.apache.commons.lang.StringUtils.isNotBlank(brandName)) {
                    // 需要创建并关联新品牌
                    return codePoolManager.getCodeFromPool(BussinessTypeEnum.BRAND.getBusinessType())
                            .map(code -> buildHdsBrandEntity(brandName, code, headerInfo));
                } else {
                    // 不需要关联品牌
                    return Mono.just(new HdsBrandEntity());
                }
            } else {
                // 酒店已有关联品牌，查询并处理
                return brandRepository.findByBrandCode(existingHotel.getBrandCode())
                        .map(existingBrand -> {
                            // 只有当提供了新品牌名称且与现有不同时才更新
                            boolean needUpdate = false;

                            if (org.apache.commons.lang.StringUtils.isNotBlank(brandName) &&
                                    !brandName.equals(existingBrand.getBrandName())) {
                                existingBrand.setBrandName(brandName);
                                needUpdate = true;
                            }

                            // 如果需要更新，设置更新信息
                            if (needUpdate) {
                                existingBrand.setUpdatedBy(UserUtils.getUserId(headerInfo));
                                existingBrand.setUpdatedByName(UserUtils.getUserName(headerInfo));
                                existingBrand.setUpdatedAt(LocalDateTime.now());
                                return existingBrand;
                            } else {
                                // 无需更新，返回空对象
                                return new HdsBrandEntity();
                            }
                        })
                        .switchIfEmpty(
                                // 关联的品牌ID在数据库中不存在，创建新品牌
                                codePoolManager.getCodeFromPool(BussinessTypeEnum.BRAND.getBusinessType())
                                        .map(code -> buildHdsBrandEntity(brandName, code, headerInfo))
                        );
            }
        });
    }

    private HdsBrandEntity buildHdsBrandEntity(String brandName, String code, HeaderUtils.HeaderInfo headerInfo) {
        HdsBrandEntity brandEntity = new HdsBrandEntity();
        brandEntity.setBrandCode(code);
        brandEntity.setBrandName(brandName);
        brandEntity.setCreatedBy(UserUtils.getUserId(headerInfo));
        brandEntity.setCreatedByName(UserUtils.getUserName(headerInfo));
        brandEntity.setCreatedAt(LocalDateTime.now());
        brandEntity.setRowStatus(1);
        return brandEntity;
    }
}
