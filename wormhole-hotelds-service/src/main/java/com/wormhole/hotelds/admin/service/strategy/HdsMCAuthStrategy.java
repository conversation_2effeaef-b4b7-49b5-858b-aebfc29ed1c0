package com.wormhole.hotelds.admin.service.strategy;

import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.hotelds.admin.model.req.GetAuthInfoReq;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.*;
import com.wormhole.hotelds.constant.RoleEnum;
import com.wormhole.hotelds.core.enums.EmployeeStatusEnum;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.model.entity.HdsEmployeeHotelEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: huangweijie
 * @date: 2025/5/15
 * @Description: 酒店来源认证策略实现
 */
@Component
public class HdsMCAuthStrategy implements AuthSourceStrategy<HotelAuthVO> {

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private HdsHotelMappingService hotelMappingService;

    @Resource
    private HotelWithExternalService hotelWithExternalService;

    @Resource
    private HotelService hotelService;

    @Override
    public Mono<HotelAuthVO> processAuthInfo(GetAuthInfoReq getAuthInfoReq, AuthVO authVO, HeaderUtils.HeaderInfo headerInfo) {
        HotelAuthVO hotelAuthVO = new HotelAuthVO();
        BeanUtils.copyProperties(authVO, hotelAuthVO);
        return getUserHotels(getAuthInfoReq, hotelAuthVO);
    }

    private Mono<HotelAuthVO> getUserHotels(GetAuthInfoReq getAuthInfoReq, HotelAuthVO authVO) {
        String targetHotelCode = getAuthInfoReq.getHotelCode();
        // 获取用户可见酒店列表
        Mono<List<HotelVO>> hotelListMono = Objects.equals(authVO.getType(), 1)
                ? hotelService.queryList()
                : hdsEmployeeHotelService.listByEmployeeId(authVO.getId(), EmployeeStatusEnum.ACTIVE.getCode(), null)
                .map(this::filterHotelCodes)
                .flatMap(hdsEmployeeHotelEntities -> {
                    if (CollectionUtils.isEmpty(hdsEmployeeHotelEntities)) {
                        return Mono.just(Collections.emptyList());
                    }
                    List<String> roleCodeList = hdsEmployeeHotelEntities.stream()
                            .filter(Objects::nonNull)
                            .filter(entity -> StringUtils.isNotBlank(entity.getRoleCode()))
                            .filter(entity -> StringUtils.isBlank(targetHotelCode) || targetHotelCode.equals(entity.getHotelCode()))
                            .findFirst()
                            .map(entity -> Arrays.stream(entity.getRoleCode().split(","))
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList()))
                            .orElse(Collections.emptyList());
                    authVO.setRoleCodes(roleCodeList);
                    List<String> hotelCodeList = hdsEmployeeHotelEntities.stream().map(HdsEmployeeHotelEntity::getHotelCode).collect(Collectors.toList());

                    return hotelService.findByHotelCodes(hotelCodeList).collectList();
                });

        return hotelListMono.flatMap(hotelList -> {
            if (CollectionUtils.isEmpty(hotelList)) {
                authVO.setHotels(Collections.emptyList());
                return Mono.just(authVO);
            }
            return processHotels(hotelList, authVO);
        });
    }

    private List<HdsEmployeeHotelEntity> filterHotelCodes(List<HdsEmployeeHotelEntity> employeeHotels) {
        String hotelAdminCode = RoleEnum.HOTEL_ADMIN.getCode();
        String otaAgentAdminCode = RoleEnum.OTA_AGENT_ADMIN.getCode();
        String otaAgentStaffCode = RoleEnum.OTA_AGENT_STAFF.getCode();

        return employeeHotels.stream()
                .filter(employeeHotel -> Optional.ofNullable(employeeHotel.getRoleCode())
                        .filter(StringUtils::isNotBlank)
                        .map(roleCodes -> {
                            // 将逗号分隔的角色拆分为列表
                            List<String> roleCodeList = Arrays.stream(roleCodes.split(","))
                                    .map(String::trim)
                                    .filter(StringUtils::isNotBlank)
                                    .toList();

                            // 检查是否包含目标角色
                            return roleCodeList.contains(hotelAdminCode) ||
                                    roleCodeList.contains(otaAgentAdminCode) ||
                                    roleCodeList.contains(otaAgentStaffCode);
                        })
                        .orElse(false))
                .collect(Collectors.toList());
    }

    private Mono<HotelAuthVO> processHotels(List<HotelVO> hotelList, HotelAuthVO authVO) {
        List<String> hotelCodeList = hotelList.stream()
                .map(HotelVO::getHotelCode)
                .toList();

        Mono<List<HotelMappingVO>> hotelMappingListMono = hotelMappingService.listHotelMappingVOByHotelCodeList(hotelCodeList);

        return hotelMappingListMono.map(hotelMappingVOList -> {
            List<HotelWithExternalVO> hotelWithExternalVOList = mergeHotelData(hotelList, hotelMappingVOList);
            authVO.setHotels(hotelWithExternalVOList);


            // 收集所有已开通的产品类型
            Set<String> openedProductTypes = hotelList.stream()
                    .filter(hotel -> CollectionUtils.isNotEmpty(hotel.getAiProductTypes()))
                    .flatMap(hotel -> hotel.getAiProductTypes().stream())
                    .collect(Collectors.toSet());

            // 统一移除未开通产品对应的角色
            if (CollectionUtils.isNotEmpty(authVO.getRoleCodes())) {
                List<String> filteredRoles = new ArrayList<>(authVO.getRoleCodes());
                for (Map.Entry<String, List<String>> entry : RoleEnum.PRODUCT_ROLE_REMOVE_MAP.entrySet()) {
                    if (!openedProductTypes.contains(entry.getKey())) {
                        filteredRoles.removeAll(entry.getValue());
                    }
                }
                authVO.setRoleCodes(filteredRoles);
            }


            return authVO;
        });
    }

    private List<HotelWithExternalVO> mergeHotelData(List<HotelVO> hotelList, List<HotelMappingVO> hotelMappingVOList) {
        List<HotelWithExternalVO> hotelWithExternalVOList = new ArrayList<>(hotelWithExternalService.convertToHotelWithExternalVOList(hotelMappingVOList));

        Set<String> mappedCodes = hotelMappingVOList.stream()
                .map(HotelMappingVO::getHotelCode)
                .collect(Collectors.toSet());

        List<HotelWithExternalVO> unmappedHotels = hotelList.stream()
                .filter(hotelVO -> !mappedCodes.contains(hotelVO.getHotelCode()))
                .map(hotelVO -> new HotelWithExternalVO()
                        .setHotelCode(hotelVO.getHotelCode())
                        .setHotelName(hotelVO.getHotelName())
                        .setExternalHotels(Collections.emptyList()))
                .toList();

        hotelWithExternalVOList.addAll(unmappedHotels);
        return hotelWithExternalVOList;
    }

    @Override
    public String getSourceCode() {
        return SourcePlatform.HDS_MC.getCode();
    }
}