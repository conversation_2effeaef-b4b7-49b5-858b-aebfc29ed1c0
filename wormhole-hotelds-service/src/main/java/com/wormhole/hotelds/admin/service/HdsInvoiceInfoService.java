package com.wormhole.hotelds.admin.service;

import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.admin.model.enums.InvoiceInfoDefaultEnum;
import com.wormhole.hotelds.admin.repository.HdsInvoiceInfoRepository;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceInfoSearchReq;
import com.wormhole.hotelds.admin.model.req.HdsInvoiceInfoSaveReq;
import com.wormhole.hotelds.admin.model.vo.HdsInvoiceInfoSearchVO;
import com.wormhole.hotelds.core.model.entity.HdsInvoiceInfoEntity;
import com.wormhole.hotelds.core.model.entity.HdsInvoiceInfoFieldEnum;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.util.UserUtils;
import com.wormhole.hotelds.util.ValidatorUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;

/**
 * @Author: yongfeng.chen
 * @Date: 2025-06-24 11:55:30
 * @Description: 发票信息Service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HdsInvoiceInfoService {

    private final R2dbcEntityTemplate r2dbcEntityTemplate;
    private final HdsInvoiceInfoRepository hdsInvoiceInfoRepository;

    /**
     * 搜索发票信息列表
     * @param req 搜索请求
     * @return 发票信息列表
     */
    public Mono<List<HdsInvoiceInfoSearchVO>> search(HdsInvoiceInfoSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        Sort sort = Sort.by(Sort.Direction.DESC, HdsInvoiceInfoFieldEnum.updated_at.name());
        Query query = Query.query(criteria)
                .sort(sort)
                .limit(req.getPageSize())
                .offset((long) (req.getCurrent() - 1) * req.getPageSize());

        return r2dbcEntityTemplate.select(query, HdsInvoiceInfoEntity.class)
                .map(HdsInvoiceInfoSearchVO::toVo)
                .collectList();
    }

    /**
     * 搜索发票信息数量
     * @param req 搜索请求
     * @return 数量
     */
    public Mono<Long> searchCount(HdsInvoiceInfoSearchReq req) {
        Criteria criteria = buildSearchCriteria(req);
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.count(query, HdsInvoiceInfoEntity.class);
    }

    /**
     * 构建搜索条件
     * @param req 搜索请求
     * @return 条件
     */
    private Criteria buildSearchCriteria(HdsInvoiceInfoSearchReq req) {
        Criteria criteria = Criteria.empty();

        // 根据酒店编码精确搜索
        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(HdsInvoiceInfoFieldEnum.hotel_code.name()).is(req.getHotelCode());
        }

        // 根据是否默认搜索
        if (Objects.nonNull(req.getIsDefault())) {
            criteria = criteria.and(HdsInvoiceInfoFieldEnum.is_default.name()).is(req.getIsDefault());
        }

        // 只查询有效记录
        criteria = criteria.and(HdsInvoiceInfoFieldEnum.row_status.name()).is(1);

        return criteria;
    }

    /**
     * 获取默认发票信息或最近更新的发票信息
     * @param hotelCode 酒店编码
     * @return 发票信息
     */
    public Mono<HdsInvoiceInfoSearchVO> getDefaultOrLatest(String hotelCode) {
        if (StringUtils.isBlank(hotelCode)) {
            return Mono.empty();
        }

        // 先查询默认的 按更新时间倒序 取第一条
        return hdsInvoiceInfoRepository.findByHotelCodeAndIsDefault(hotelCode, 1)
                // 按更新时间倒序
                .sort((a, b) -> b.getUpdatedAt().compareTo(a.getUpdatedAt()))
                // 取第一条
                .take(1)
                .next()
                .switchIfEmpty(
                    // 如果没有默认的，查询最近更新的
                    r2dbcEntityTemplate.select(HdsInvoiceInfoEntity.class)
                        .matching(
                            Query.query(
                                Criteria.where(HdsInvoiceInfoFieldEnum.hotel_code.name()).is(hotelCode)
                                    .and(HdsInvoiceInfoFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId())
                            )
                            .sort(Sort.by(Sort.Direction.DESC, HdsInvoiceInfoFieldEnum.updated_at.name(), HdsInvoiceInfoFieldEnum.id.name()))
                            .limit(1)
                        )
                        .first()
                )
                .map(HdsInvoiceInfoSearchVO::toVo);
    }
    
    /**
     * 创建或更新发票信息
     * @param req 发票信息保存请求
     * @return 发票信息ID
     */
    public Mono<Integer> create(HdsInvoiceInfoSaveReq req) {
        // 参数校验
        ValidatorUtils.validateInvoiceInfoSaveReq(req);
        
        // 获取当前用户信息
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 查询是否存在相同纳税人识别号的记录
                    return hdsInvoiceInfoRepository.findByHotelCodeAndTaxNumber(req.getHotelCode(), req.getTaxNumber())
                            .flatMap(existingEntity -> {
                                // 存在则更新
                                return updateInvoiceInfo(existingEntity, req, headerInfo);
                            })
                            .switchIfEmpty(
                                // 不存在则新增
                                createInvoiceInfo(req, headerInfo)
                            );
                });
    }
    
    /**
     * 创建发票信息
     * @param req 发票信息保存请求
     * @param headerInfo 当前用户信息
     * @return 发票信息ID
     */
    private Mono<Integer> createInvoiceInfo(HdsInvoiceInfoSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 如果是默认发票信息，需要将同一酒店的其他发票信息设置为非默认
        Mono<Void> clearDefaultMono = Mono.empty();
        if (Objects.equals(req.getIsDefault(), InvoiceInfoDefaultEnum.DEFAULT.getCode())) {
            clearDefaultMono = clearDefaultInvoiceInfo(req.getHotelCode());
        }
        
        return clearDefaultMono.then(Mono.defer(() -> {
            // 构建实体
            HdsInvoiceInfoEntity entity = new HdsInvoiceInfoEntity();
            entity.setHotelCode(req.getHotelCode())
                    .setInvoiceHeader(req.getInvoiceHeader())
                    .setIsDefault(req.getIsDefault() != null ? req.getIsDefault() : 0)
                    .setTaxNumber(req.getTaxNumber())
                    .setReceiveEmail(req.getReceiveEmail())
                    .setInvoiceContent(StringUtils.defaultIfBlank(req.getInvoiceContent(), "服务费"))
                    .setMoreInfo(req.getMoreInfo());
            entity.setCreatedBy(UserUtils.getUserId(headerInfo));
            entity.setCreatedByName(UserUtils.getUserName(headerInfo));
            entity.setUpdatedBy(UserUtils.getUserId(headerInfo));
            entity.setUpdatedByName(UserUtils.getUserName(headerInfo));
            entity.setRowStatus(RowStatusEnum.VALID.getId());
            
            // 保存实体
            return r2dbcEntityTemplate.insert(entity)
                    .map(HdsInvoiceInfoEntity::getId);
        }));
    }
    
    /**
     * 更新发票信息
     * @param existingEntity 已存在的发票信息实体
     * @param req 发票信息保存请求
     * @param headerInfo 当前用户信息
     * @return 发票信息ID
     */
    private Mono<Integer> updateInvoiceInfo(HdsInvoiceInfoEntity existingEntity, HdsInvoiceInfoSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 如果是默认发票信息，需要将同一酒店的其他发票信息设置为非默认
        Mono<Void> clearDefaultMono = Mono.empty();
        if (Objects.equals(req.getIsDefault(), InvoiceInfoDefaultEnum.DEFAULT.getCode()) &&
                !Objects.equals(existingEntity.getIsDefault(), InvoiceInfoDefaultEnum.DEFAULT.getCode())) {
            clearDefaultMono = clearDefaultInvoiceInfo(req.getHotelCode());
        }
        
        return clearDefaultMono.then(Mono.defer(() -> {
            // 更新实体
            Update update = Update.update(HdsInvoiceInfoFieldEnum.invoice_header.name(), req.getInvoiceHeader())
                    .set(HdsInvoiceInfoFieldEnum.is_default.name(), req.getIsDefault() != null ? req.getIsDefault() : existingEntity.getIsDefault())
                    .set(HdsInvoiceInfoFieldEnum.receive_email.name(), req.getReceiveEmail())
                    .set(HdsInvoiceInfoFieldEnum.invoice_content.name(), StringUtils.defaultIfBlank(req.getInvoiceContent(), existingEntity.getInvoiceContent()))
                    .set(HdsInvoiceInfoFieldEnum.more_info.name(), req.getMoreInfo())
                    .set(HdsInvoiceInfoFieldEnum.updated_by.name(), UserUtils.getUserId(headerInfo))
                    .set(HdsInvoiceInfoFieldEnum.updated_by_name.name(), UserUtils.getUserName(headerInfo))
                    .set(HdsInvoiceInfoFieldEnum.updated_at.name(), LocalDateTime.now());
            
            // 执行更新
            return r2dbcEntityTemplate.update(HdsInvoiceInfoEntity.class)
                    .matching(Query.query(Criteria.where(HdsInvoiceInfoFieldEnum.id.name()).is(existingEntity.getId())))
                    .apply(update)
                    .thenReturn(existingEntity.getId());
        }));
    }
    
    /**
     * 清除默认发票信息
     * @param hotelCode 酒店编码
     * @return void
     */
    private Mono<Void> clearDefaultInvoiceInfo(String hotelCode) {
        Update update = Update.update(HdsInvoiceInfoFieldEnum.is_default.name(), InvoiceInfoDefaultEnum.NON_DEFAULT.getCode());
        
        return r2dbcEntityTemplate.update(HdsInvoiceInfoEntity.class)
                .matching(Query.query(Criteria.where(HdsInvoiceInfoFieldEnum.hotel_code.name()).is(hotelCode)
                        .and(HdsInvoiceInfoFieldEnum.is_default.name()).is(InvoiceInfoDefaultEnum.DEFAULT.getCode())))
                .apply(update)
                .then();
    }
}