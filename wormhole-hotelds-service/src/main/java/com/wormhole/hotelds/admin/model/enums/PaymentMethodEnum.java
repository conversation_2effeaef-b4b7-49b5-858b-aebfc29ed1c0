package com.wormhole.hotelds.admin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/6/19 17:17
 * @Description：PaymentMethodEnum
 */
@Getter
@AllArgsConstructor
public enum PaymentMethodEnum {

    /**
     * 微信支付
     */
    WECHAT(1, "微信支付", "WECHAT", "微信主扫"),

    /**
     * 支付宝
     */
    ALIPAY(2, "支付宝", "ALIPAY", "支付宝主扫"),

    /**
     * 易宝一键支付
     */
    YEEPAY_ONE_CLICK(3, "易宝一键支付", "YJZF", "易宝一键支付"),

    /**
     * 账户支付
     */
    ACCOUNT_PAY(4, "账户支付", "ZHZF", "账户支付"),

    /**
     * 银行转账支付
     */
    BANK_TRANSFER(5, "银行转账支付", "BANK_TRANSFER_PAY", "银行转账支付"),

    /**
     * 银行转账7*24h
     */
    BANK_TRANSFER_24H(6, "银行转账7*24h", "BANK_TRANSFER_TO_BANK", "银行转账7*24h"),

    /**
     * 钱包支付
     */
    WALLET_PAY(7, "钱包支付", "WALLET_PAY", "钱包支付"),

    /**
     * 网银B2B支付
     */
    EBANK_B2B(8, "网银B2B支付", "EBANK_B2B", "收银台只展示网银支付模块且只展示B2B"),

    /**
     * 网银B2C支付
     */
    EBANK_B2C(9, "网银B2C支付", "EBANK_B2C", "收银台只展示网银支付模块且只展示B2C"),

    /**
     * 网银支付
     */
    EBANK(10, "网银支付", "EBANK", "收银台只展示网银支付模块");

    /**
     * 支付方式代码（系统内部使用）
     */
    private final Integer code;

    /**
     * 支付方式名称
     */
    private final String name;

    /**
     * 易宝支付方式代码
     */
    private final String yeepayCode;

    /**
     * 支付方式描述
     */
    private final String description;

    /**
     * 根据系统支付方式代码获取枚举
     *
     * @param code 支付方式代码
     * @return 支付方式枚举
     */
    public static PaymentMethodEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PaymentMethodEnum method : values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }
        return null;
    }

    /**
     * 根据易宝支付方式代码获取枚举
     *
     * @param yeepayCode 易宝支付方式代码
     * @return 支付方式枚举
     */
    public static PaymentMethodEnum getByYeepayCode(String yeepayCode) {
        if (yeepayCode == null || yeepayCode.trim().isEmpty()) {
            return null;
        }
        for (PaymentMethodEnum method : values()) {
            if (method.getYeepayCode().equals(yeepayCode)) {
                return method;
            }
        }
        return null;
    }

    /**
     * 根据系统支付方式代码获取易宝支付方式代码
     *
     * @param code 系统支付方式代码
     * @return 易宝支付方式代码
     */
    public static String getYeepayCodeByCode(Integer code) {
        PaymentMethodEnum method = getByCode(code);
        return method != null ? method.getYeepayCode() : null;
    }

    /**
     * 根据系统支付方式代码获取支付方式名称
     *
     * @param code 系统支付方式代码
     * @return 支付方式名称
     */
    public static String getNameByCode(Integer code) {
        PaymentMethodEnum method = getByCode(code);
        return method != null ? method.getName() : "未知支付方式";
    }

    /**
     * 检查是否为主要支付方式（微信、支付宝）
     *
     * @param code 支付方式代码
     * @return 是否为主要支付方式
     */
    public static boolean isMainPaymentMethod(Integer code) {
        return WECHAT.getCode().equals(code) || ALIPAY.getCode().equals(code);
    }

    /**
     * 获取所有主要支付方式（微信、支付宝）
     *
     * @return 主要支付方式数组
     */
    public static PaymentMethodEnum[] getMainPaymentMethods() {
        return new PaymentMethodEnum[]{WECHAT, ALIPAY};
    }
}
