package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.HdsEmployeeService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/3 09:16
 * @Description：HdsEmployeeController
 */
@RestController
@RequestMapping("/employee")
public class HdsEmployeeController {

    @Resource
    private HdsEmployeeService hdsEmployeeService;

    @PostMapping("/create")
    public Mono<Result<Boolean>> create(@RequestBody HdsEmployeeSaveReq hdsEmployeeSaveReq) {
        return hdsEmployeeService.create(hdsEmployeeSaveReq).flatMap(Result::success);
    }

    @PostMapping("/update")
    public Mono<Result<Boolean>> update(@RequestBody HdsEmployeeSaveReq hdsEmployeeSaveReq) {
        return hdsEmployeeService.update(hdsEmployeeSaveReq).flatMap(Result::success);
    }

    @PostMapping("/updateStatus")
    public Mono<Result<Boolean>> updateStatus(@RequestBody HdsEmployeeUpdateStatusReq req) {
        return hdsEmployeeService.updateStatus(req).flatMap(Result::success);
    }

    @PostMapping("/search")
    public Mono<Result<PageResult<HdsEmployeeVO>>> search(@RequestBody HdsEmployeeSearchReq hdsEmployeeSearchReq) {
        return hdsEmployeeService.searchPage(hdsEmployeeSearchReq).flatMap(Result::success);
    }

    @PostMapping("/query")
    public Mono<Result<HdsEmployeeVO>> query(@RequestBody HdsEmployeeQueryReq req) {
        return hdsEmployeeService.query(req).flatMap(Result::success);
    }

    @GetMapping("/isExist")
    public Mono<Result<Boolean>> isExist(@RequestParam("username") String username) {
        return hdsEmployeeService.isExist(username).flatMap(Result::success);
    }

    @GetMapping("/resetPassword")
    public Mono<Result<Boolean>> resetPassword(@RequestParam("employee_id") Integer employeeId) {
        return hdsEmployeeService.resetPassword(employeeId).flatMap(Result::success);
    }

    @GetMapping("/queryHotelList")
    public Mono<Result<List<HotelWithExternalVO>>> queryHotelList(@RequestParam("employee_id") Integer employeeId) {
        return hdsEmployeeService.queryHotelList(employeeId).flatMap(Result::success);
    }

    @PostMapping("/updateHotelList")
    public Mono<Result<Boolean>> updateHotelList(@RequestBody HdsEmployeeUpdateHotelReq req) {
        return hdsEmployeeService.updateHotelList(req).flatMap(Result::success);
    }

    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody HdsEmployeeDeleteReq req) {
        return hdsEmployeeService.delete(req).flatMap(Result::success);
    }

//    @PostMapping("/register")
//    public Mono<Result<HdsEmployeeRegisterVO>> register(@RequestBody HdsEmployeeRegisterReq req) {
//        return hdsEmployeeService.register(req).flatMap(Result::success);
//    }
}
