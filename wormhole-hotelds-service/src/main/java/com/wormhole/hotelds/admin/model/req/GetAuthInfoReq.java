package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/5/21 15:08
 * @Description：GetAuthInfoReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GetAuthInfoReq {

    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * FRONT("front", "AI智能座机"),
     * ROOM("room", "AI云电话客房端"),
     * WECHAT_MINI_APP("wechat_mini_app", "客房AI小程序"),
     * BDW_APP("bdw_app", "百达屋APP"),
     * FRONT_APP("front_app", "AI智能座机APP"),
     */
    private String deviceType;

    private String deviceId;
}
