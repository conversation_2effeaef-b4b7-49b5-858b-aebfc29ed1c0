package com.wormhole.hotelds.admin.service;

import com.wormhole.common.enums.*;
import com.wormhole.common.exception.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.admin.model.req.GetAuthInfoReq;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.service.strategy.*;
import com.wormhole.hotelds.core.model.entity.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.util.*;
import java.util.stream.*;

/**
 * @author: huangweijie
 * @date: 2025/5/15
 * @Description: 用户认证服务实现类
 */
@Service
public class AuthService {

    @Resource
    private HdsEmployeeService hdsEmployeeService;

    @Resource
    private List<AuthSourceStrategy<?>> authSourceStrategies;

    private Map<String, AuthSourceStrategy<?>> strategyMap;

    @PostConstruct
    public void init() {
        strategyMap = authSourceStrategies.stream()
                .collect(Collectors.toMap(AuthSourceStrategy::getSourceCode,
                        strategy -> strategy,
                        (existing, replacement) -> existing));
    }

    public Mono<AuthVO> getAuthInfo(GetAuthInfoReq getAuthInfoReq) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> getUserInfoByUsername(headerInfo)
                        .flatMap(authVO -> {
                            String source = headerInfo.getSource();
                            AuthSourceStrategy<?> strategy = strategyMap.getOrDefault(source,
                                    strategyMap.get(SourcePlatform.OTHER.getCode()));
                            return strategy.processAuthInfo(getAuthInfoReq,authVO, headerInfo);
                        }));
    }

    /**
     * 通过用户名获取用户信息
     * @param headerInfo 请求头信息
     * @return 用户认证信息
     */
    private Mono<AuthVO> getUserInfoByUsername(HeaderUtils.HeaderInfo headerInfo) {
        String username = headerInfo.getUsername();
        return hdsEmployeeService.findByUsername(username)
                .map(this::convertToAuthVO)
                .switchIfEmpty(Mono.error(new BusinessException("HDS-AUTH-ERROR-001","用户不存在")));
    }
    
    /**
     * 将用户实体转换为认证VO
     * @param entity 用户实体
     * @return 认证VO
     */
    private AuthVO convertToAuthVO(HdsEmployeeEntity entity) {
        AuthVO authVO = new AuthVO();
        authVO.setId(entity.getId())
                .setUsername(entity.getUsername())
                .setName(entity.getName())
                .setMobile(entity.getMobile())
                .setEmail(entity.getEmail())
                .setType(entity.getType());
        return authVO;
    }
    

}