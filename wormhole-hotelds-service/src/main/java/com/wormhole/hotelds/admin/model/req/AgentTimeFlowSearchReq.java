package com.wormhole.hotelds.admin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.query.QueryCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：flx
 * @Date：2025/5/21 10:22
 * @Description：AgentTimeFlowSearchReq
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AgentTimeFlowSearchReq extends QueryCondition {

    /**
     * 门店code
     */
    private String hotelCode;
}
