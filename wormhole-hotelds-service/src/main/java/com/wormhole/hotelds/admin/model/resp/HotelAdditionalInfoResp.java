package com.wormhole.hotelds.admin.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class HotelAdditionalInfoResp {

    @JsonProperty("data")
    private DataInfo data;

    @JsonProperty("ResponseStatus")
    private ResponseStatus responseStatus;

    /**
     * 数据主体
     */
    @Data
    public static class DataInfo {

        @JsonProperty("hotelReservationTips")
        private HotelReservationTips hotelReservationTips;

        @JsonProperty("hotelPoiInfo")
        private HotelPoiInfo hotelPoiInfo;
    }

    /**
     * 酒店预订提示信息
     */
    @Data
    public static class HotelReservationTips {

        @JsonProperty("tipList")
        private List<TipItem> tipList;
    }

    /**
     * 提示项
     */
    @Data
    public static class TipItem {

        @JsonProperty("title")
        private String title;

        @JsonProperty("details")
        private List<TipDetail> details;
    }

    /**
     * 提示详情
     */
    @Data
    public static class TipDetail {

        @JsonProperty("items")
        private List<TipContentItem> items;
    }

    /**
     * 提示内容项
     */
    @Data
    public static class TipContentItem {

        @JsonProperty("content")
        private String content;
    }

    /**
     * 酒店周边信息
     */
    @Data
    public static class HotelPoiInfo {

        @JsonProperty("aroundItemList")
        private List<AroundItem> aroundItemList;
    }

    /**
     * 周边分类项
     */
    @Data
    public static class AroundItem {

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("typeName")
        private String typeName;

        @JsonProperty("poiInfoList")
        private List<PoiInfo> poiInfoList;
    }

    /**
     * POI信息详情
     */
    @Data
    public static class PoiInfo {

        @JsonProperty("name")
        private String name;

        @JsonProperty("distance")
        private String distance;

        @JsonProperty("distanceDescText")
        private String distanceDescText;

        @JsonProperty("id")
        private Long id;

        @JsonProperty("arrivalType")
        private String arrivalType;

        @JsonProperty("tagNames")
        private List<String> tagNames;

        @JsonProperty("poiType")
        private Integer poiType;

        @JsonProperty("lat")
        private Double lat;

        @JsonProperty("lng")
        private Double lng;

        @JsonProperty("sinkDistanceText")
        private String sinkDistanceText;

        @JsonProperty("icon")
        private String icon;

        @JsonProperty("newName")
        private String newName;
    }

    /**
     * 响应状态信息
     */
    @Data
    public static class ResponseStatus {

        @JsonProperty("Timestamp")
        private String timestamp;

        @JsonProperty("Ack")
        private String ack;

        @JsonProperty("Errors")
        private List<Object> errors;

        @JsonProperty("Build")
        private String build;

        @JsonProperty("Version")
        private String version;

        @JsonProperty("Extension")
        private List<Extension> extension;
    }

    /**
     * 扩展信息
     */
    @Data
    public static class Extension {

        @JsonProperty("Id")
        private String id;

        @JsonProperty("Value")
        private String value;
    }
}
