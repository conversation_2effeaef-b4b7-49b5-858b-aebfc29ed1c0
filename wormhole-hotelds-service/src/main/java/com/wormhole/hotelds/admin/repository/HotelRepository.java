package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.admin.model.dto.HotelDTO;
import com.wormhole.hotelds.admin.model.dto.HotelStatisticsDTO;
import com.wormhole.hotelds.core.model.entity.*;
import io.reactivex.Flowable;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * @Author：flx
 * @Date：2025/3/26 10:25
 * @Description：门店Repository
 */
@Repository
public interface HotelRepository extends ReactiveCrudRepository<HdsHotelInfoEntity, Integer> {


    /**
     * 批量更新设备状态
     *
     * @param idList   酒店ID列表
     * @param userId
     * @param username
     * @return 更新结果
     */
    @Modifying
    @Query("UPDATE hds_hotel_info SET status = :status, updated_at = NOW(), updated_by = :userId, updated_by_name = :username WHERE id IN (:idList)")
    Mono<Integer> updateHotelInfoStatus(@Param("idList") List<Integer> idList, @Param("status")Integer status, @Param("userId")String userId, @Param("username")String username);

    Mono<HdsHotelInfoEntity> findByHotelCode(String hotelCode);

    Flux<HdsHotelInfoEntity> findByHotelCodeIn(Collection<String> hotelCodes);

    Mono<Boolean> existsByHotelCode(String hotelCode);

    @Query("""
                SELECT
                    COUNT(DISTINCT m.merchant_id) as merchant_count,
                    COUNT(DISTINCT h.id) as hotel_count,
                    COUNT(DISTINCT d.id) as device_count
                FROM hds_hotel_info h
                LEFT JOIN hds_merchant m ON h.merchant_id = m.merchant_id
                LEFT JOIN hds_brand b ON h.brand_code = b.brand_code
                LEFT JOIN hds_device d ON h.hotel_code = d.hotel_code
                WHERE (:merchantName IS NULL OR 
                    (m.merchant_name LIKE CONCAT('%', :merchantName, '%') 
                    OR m.subject_name LIKE CONCAT('%', :merchantName, '%')))
                AND (:brandName IS NULL OR b.brand_name LIKE CONCAT('%', :brandName, '%'))
                AND (:hotelName IS NULL OR h.hotel_name LIKE CONCAT('%', :hotelName, '%'))
            """)
    Mono<HotelStatisticsDTO> getStatistics(
            @Param("merchantName") String merchantName,
            @Param("brandName") String brandName,
            @Param("hotelName") String hotelName
    );

    @Query("""
                SELECT h.id, h.hotel_code, h.hotel_name, h.total_room,
                    m.merchant_name, m.subject_name,
                    b.brand_name, b.brand_logo,
                    COUNT(DISTINCT d.id) as device_count
                FROM hds_hotel_info h
                LEFT JOIN hds_merchant m ON h.merchant_id = m.merchant_id
                LEFT JOIN hds_brand b ON h.brand_code = b.brand_code
                LEFT JOIN hds_device d ON h.hotel_code = d.hotel_code
                WHERE (:merchantName IS NULL OR
                    (m.merchant_name LIKE CONCAT('%', :merchantName, '%') 
                    OR m.subject_name LIKE CONCAT('%', :merchantName, '%')))
                AND (:brandName IS NULL OR b.brand_name LIKE CONCAT('%', :brandName, '%'))
                AND (:hotelName IS NULL OR h.hotel_name LIKE CONCAT('%', :hotelName, '%'))
                AND (:hotelName IS NULL OR h.hotel_name LIKE CONCAT('%', :hotelName, '%'))
                AND (:hotelCode IS NULL OR h.hotel_code LIKE CONCAT('%', :hotelCode, '%'))            
                ORDER BY h.created_at DESC
                LIMIT :limit OFFSET :offset
            """)
    Flux<HotelDTO> searchHotels(
            @Param("merchantName") String merchantName,
            @Param("brandName") String brandName,
            @Param("hotelName") String hotelName,
            @Param("hotelCode") String hotelCode,
            @Param("limit") int limit,
            @Param("offset") long offset
    );

    @Query("""
        SELECT COUNT(DISTINCT h.id)
        FROM hds_hotel_info h
        LEFT JOIN hds_merchant m ON h.merchant_id = m.merchant_id
        LEFT JOIN hds_brand b ON h.brand_code = b.brand_code
        WHERE (:merchantName IS NULL OR 
            (m.merchant_name LIKE CONCAT('%', :merchantName, '%') 
            OR m.subject_name LIKE CONCAT('%', :merchantName, '%')))
        AND (:brandName IS NULL OR b.brand_name LIKE CONCAT('%', :brandName, '%'))
        AND (:hotelName IS NULL OR h.hotel_name LIKE CONCAT('%', :hotelName, '%'))
        AND (:hotelCode IS NULL OR h.hotel_code LIKE CONCAT('%', :hotelCode, '%'))
    """)
    Mono<Long> countHotels(
            @Param("merchantName") String merchantName,
            @Param("brandName") String brandName,
            @Param("hotelName") String hotelName,
            @Param("hotelCode") String hotelCode
    );

    Flux<HdsHotelInfoEntity> findAllByHotelCodeInAndHotelNameLike(Set<String> hotelCodes, String s);

    Mono<Boolean> existsByHotelName(String hotelName);

    Mono<Boolean> existsByHotelNameAndIdNot(String hotelName, Integer id);

    @Modifying
    @Query("""
                UPDATE hds_hotel_info 
                SET row_status = :rowStatus,
                    updated_by = :updatedBy,
                    updated_by_name = :updatedByName,
                    updated_at = NOW()
                WHERE id = hotelId
            """)
    Mono<Integer> updateEmployeeRowStatus(@Param("hotelId") Integer hotelId,
                                          @Param("rowStatus") Integer rowStatus,
                                          @Param("updatedBy") String updatedBy,
                                          @Param("updatedByName") String updatedByName);

    Mono<HdsHotelInfoEntity> findByInvitationCode(String invitationCode);

    Mono<Boolean> existsByHotelNameAndHotelCodeNot(String hotelName, String hotelCode);

    Mono<HdsHotelInfoEntity> findByHotelName(String hotelName);

    Mono<HdsHotelInfoEntity> findByHotelCodeAndStatus(String hotelCode, Integer status);

    /**
     * 根据酒店名称和多个状态查询酒店信息
     * @param hotelName 酒店名称
     * @param statuses 状态集合
     * @return 酒店实体Mono
     */
    Mono<HdsHotelInfoEntity> findByHotelNameAndStatusIn(String hotelName, Collection<Integer> statuses);

    Mono<Boolean> existsByHotelNameAndStatus(String hotelName, Integer status);

    Mono<Boolean> existsByHotelNameAndIdNotAndStatus(String hotelName, Integer id, Integer status);

    Mono<Boolean> existsByHotelNameAndHotelCodeNotAndStatus(String hotelName, String hotelCode, Integer status);
}
