package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.admin.model.enums.AiProductTypeEnum;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.relational.core.mapping.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/3/26 17:08
 * @Description：HotelVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 酒店类型
     */
    private Integer hotelType;

    /**
     * 总房间数
     */
    private Integer totalRoom;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 预订标志
     */
    private Integer bookFlag;

    /**
     * 主要负责人
     */
    private String mainPerson;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 前台电话
     */
    private String frontPhone;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 开业日期
     */
    private String openDate;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店名称拼音
     */
    private String hotelNamePinYin;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户简称
     */
    private String merchantShortName;

    /**
     * 门店logo
     */
    private String hotelLogo;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 描述
     */
    private String description;

    /**
     * 地址
     */
    private String address;

    /**
     * 携程EBK链接
     */
    private String ctripEbkUrl;

    /**
     * 状态: 1-正常, 0-停用 2:未完成初始化
     */
    private Integer status;

    /**
     * 高德坐标经度
     */
    private String longitude;

    /**
     * 高德坐标纬度
     */
    private String latitude;

    /**
     * ai产品
     */
    private List<String> aiProductTypes;

    /**
     * ota到期时间
     */
    private String otaEndTime;

    private boolean isOtaServiceExpired;

    /**
     * 来源 0:web 1:ota
     */
    private Integer source;

    /**
     * 是否购买过套餐
     */
    private Boolean hasPurchasedPackage;

    /**
     * 创建人ID
a     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 更新人ID
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedByName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 记录状态(1:有效, 0:无效)
     */
    private Integer rowStatus;

    /**
     * sos启用开关;0-否 1-是
     */
    private Integer sosSwitch;

    /**
     * AI客服通话类型：token_req（AI通话）、transfer_to_human（真人通话）
     */
    private String callCommand;

    /**
     * 文字对话启用开关;0-否 1-是
     */
    private Integer dialogSwitch;

    /**
     * 将实体转换为VO
     */
    public static HotelVO toVo(HdsHotelInfoEntity entity) {
        if (entity == null) {
            return null;
        }
        HotelVO hotelVO = new HotelVO();
        BeanUtils.copyProperties(entity, hotelVO);
        hotelVO.setLongitude(entity.getGaodeLongitude());
        hotelVO.setLatitude(entity.getGaodeLatitude());

        if (StringUtils.isNotBlank(entity.getAiProductTypes())) {
            hotelVO.setAiProductTypes(Arrays.stream(entity.getAiProductTypes().split(",")).toList());
        }
        // 计算ota过期时间
        LocalDateTime initFinishedAt = entity.getInitFinishedAt();

        if (CollectionUtils.isNotEmpty(hotelVO.getAiProductTypes())
                && hotelVO.getAiProductTypes().contains(AiProductTypeEnum.OTA_AGENT.getCode())
                && Objects.nonNull(initFinishedAt)) {
            if (Objects.nonNull(entity.getOtaExtendMonths())) {
                initFinishedAt = initFinishedAt.plusMonths(entity.getOtaExtendMonths());
            }
            if (Objects.nonNull(entity.getOtaRewardMonths())) {
                initFinishedAt = initFinishedAt.plusMonths(entity.getOtaRewardMonths());
            }
            LocalDateTime expireTime = initFinishedAt.with(LocalTime.MAX);
            hotelVO.setOtaEndTime(SimpleDateUtils.formatLocalDateTimeToDate(expireTime));
        }
        hotelVO.setOtaServiceExpired(isOtaServiceExpired(hotelVO));
        return hotelVO;
    }

    public static boolean isOtaServiceExpired(HotelVO hotelVO) {
        if (StringUtils.isBlank(hotelVO.getOtaEndTime())) {
            return true;
        }
        try {
            return LocalDate.parse(hotelVO.getOtaEndTime()).isBefore(LocalDate.now());
        } catch (DateTimeParseException e) {
            return true;
        }
    }
}
