package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.core.model.entity.HdsEmployeeEntity;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.time.*;
import java.util.*;

/**
 * @Author：flx
 * @Date：2025/4/3 09:21
 * @Description：HdsEmployeeVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsEmployeeVO {

    private Integer id;

    /**
     * 账号
     */
    private String username;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 性别 1男 2女
     */
    private Integer gender;

    /**
     * 状态 1正常 2停用 3注销
     */
    private Integer status;

    /**
     * 员工类型 1-集团 2-服务商 3-酒店
     */
    private Integer type;

    private String roleCode;

    private List<String> roleCodes;

    private List<String> positionCodes;

    private List<String> positionDetails;

    private String roleName;

    /**
     * 账号类型，1总机 2分机
     */
    private Integer accountType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 工单类型标签列表
     */
    private List<String> ticketLabels;

    /**
     * 工单类型列表
     */
    private List<String> tickets;

    /**
     * 酒店编码
     */
    private String hotelCode;


    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 创建人
     */
    private String createdBy;

    private Integer ticketAssignmentFlag;

    public static HdsEmployeeVO toVo(HdsEmployeeEntity hdsEmployeeEntity) {
        HdsEmployeeVO hdsEmployeeVO = new HdsEmployeeVO();
        BeanUtils.copyProperties(hdsEmployeeEntity, hdsEmployeeVO);
        return hdsEmployeeVO;
    }
}
