package com.wormhole.hotelds.admin.service;

import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.req.HotelPackageSaveReq;
import com.wormhole.hotelds.admin.repository.HdsHotelProductPricingRepository;
import com.wormhole.hotelds.core.model.entity.HdsHotelProductPricingEntity;
import com.wormhole.hotelds.core.model.entity.HdsProductPricingEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/6/16 18:33
 * @Description：门市产品价格服务
 */
@Service
@Slf4j
public class HotelProductPricingService {

    @Resource
    private HdsHotelProductPricingRepository hotelProductPricingRepository;

    /**
     * 创建门店产品价格
     *
     * @param discountMode：优惠方式
     * @param hotelPackageCode：门店套餐编码
     * @param productId：产品id
     * @param customPricingList：自定义定价列表
     * @param standardPricingList：标准定价列表
     * @param headerInfo：头部信息
     * @return
     */
    public Mono<Void> createSmartPricingForProduct(Integer discountMode,
                                                   String hotelPackageCode,
                                                   Integer productId,
                                                   List<HotelPackageSaveReq.HotelProductCustomPricingReq> customPricingList,
                                                   List<HdsProductPricingEntity> standardPricingList,
                                                   HeaderUtils.HeaderInfo headerInfo,
                                                   List<HdsHotelProductPricingEntity> entities) {

        // 将自定义定价按周期类型分组
        Map<Integer, HotelPackageSaveReq.HotelProductCustomPricingReq> customPricingMap = customPricingList.stream()
                .filter(pricing -> pricing.getProductId().equals(productId))
                .collect(Collectors.toMap(
                        HotelPackageSaveReq.HotelProductCustomPricingReq::getPeriodType,
                        pricing -> pricing,
                        (existing, replacement) -> replacement
                ));

        // 将标准定价按周期类型分组
        Map<Integer, HdsProductPricingEntity> standardPricingMap = standardPricingList.stream()
                .collect(Collectors.toMap(
                        HdsProductPricingEntity::getPeriodType,
                        pricing -> pricing,
                        (existing, replacement) -> replacement
                ));

        return createSmartCompletePricing(
                discountMode,
                hotelPackageCode,
                productId, customPricingMap,
                standardPricingMap,
                headerInfo,
                entities);
    }

    /**
     * 智能补全定价逻辑
     */
    private Mono<Void> createSmartCompletePricing(Integer discountMode,
                                                  String hotelPackageCode,
                                                  Integer productId,
                                                  Map<Integer, HotelPackageSaveReq.HotelProductCustomPricingReq> customPricingMap,
                                                  Map<Integer, HdsProductPricingEntity> standardPricingMap,
                                                  HeaderUtils.HeaderInfo headerInfo,
                                                  List<HdsHotelProductPricingEntity> entities) {

        // 检查哪些周期有自定义定价
        boolean hasMonthlyCustom = hasValidCustomPricing(customPricingMap.get(1));
        boolean hasQuarterlyCustom = hasValidCustomPricing(customPricingMap.get(2));
        boolean hasYearlyCustom = hasValidCustomPricing(customPricingMap.get(3));

        Map<Integer, HdsHotelProductPricingEntity> entityMap = entities.stream()
                .collect(Collectors.toMap(HdsHotelProductPricingEntity::getId, Function.identity()));

        // 月度、季度、年度
        return Flux.fromIterable(List.of(1, 2, 3))
                .flatMap(periodType -> {
                    HotelPackageSaveReq.HotelProductCustomPricingReq customPricing = customPricingMap.get(periodType);
                    HdsProductPricingEntity standardPricing = standardPricingMap.get(periodType);

                    // 根据智能补全规则创建定价
                    return createPricingBySmartRule(
                            discountMode,
                            hotelPackageCode,
                            productId,
                            periodType,
                            customPricing,
                            standardPricing,
                            hasMonthlyCustom,
                            hasQuarterlyCustom,
                            hasYearlyCustom,
                            customPricingMap,
                            headerInfo,
                            entityMap);
                })
                .then();
    }

    private boolean hasValidCustomPricing(HotelPackageSaveReq.HotelProductCustomPricingReq customPricing) {
        if (customPricing == null) {
            return false;
        }

        // 检查优惠价是否有效（非空且不为零）
        boolean validDiscountPrice = StringUtils.isNotBlank(customPricing.getCustomDiscountPrice()) &&
                hasNonZeroValue(customPricing.getCustomDiscountPrice());

        // 检查折扣率是否有效（非空且不为零）
        boolean validDiscountRate = StringUtils.isNotBlank(customPricing.getCustomDiscountRate()) &&
                hasNonZeroValue(customPricing.getCustomDiscountRate());

        return validDiscountPrice || validDiscountRate;
    }

    private boolean hasNonZeroValue(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        return !StringUtils.equals(value, "0");
    }

    /**
     * 根据智能补全规则创建定价
     */
    private Mono<HdsHotelProductPricingEntity> createPricingBySmartRule(Integer discountMode,
                                                                        String hotelPackageCode,
                                                                        Integer productId,
                                                                        Integer periodType,
                                                                        HotelPackageSaveReq.HotelProductCustomPricingReq customPricing,
                                                                        HdsProductPricingEntity standardPricing,
                                                                        boolean hasMonthlyCustom, boolean hasQuarterlyCustom, boolean hasYearlyCustom,
                                                                        Map<Integer, HotelPackageSaveReq.HotelProductCustomPricingReq> customPricingMap,
                                                                        HeaderUtils.HeaderInfo headerInfo,
                                                                        Map<Integer, HdsHotelProductPricingEntity> entityMap) {

        // 1. 获取或创建实体
        HdsHotelProductPricingEntity entity = getOrCreateHotelPricingEntity(
                customPricing, hotelPackageCode, productId, periodType, headerInfo, entityMap);

        if (entity == null) {
            return Mono.empty();
        }

        // 2. 检查是否需要更新（仅对已存在实体）
        if (customPricing != null && customPricing.getId() != null && !checkPricingNeedUpdate(entity, customPricing, discountMode)) {
            return Mono.empty();
        }

        // 设置默认市场价（取自标准定价）
        entity.setCustomMarketPrice(standardPricing.getMarketPrice());

        // 获取月度定价信息（大多数计算都基于月度）
        HotelPackageSaveReq.HotelProductCustomPricingReq monthlyPricing = customPricingMap.get(1);

        // 处理定价逻辑
        switch (periodType) {
            case 1: // 月度
                if (hasMonthlyCustom) {
                    // 有月度自定义，传入相同值作为月度价
                    applyPricingValues(discountMode, entity, customPricing, customPricing, 1);
                } else {
                    // 没有自定义，使用标准定价
                    applyStandardPricingValues(entity, standardPricing);
                }
                break;
            case 2: // 季度
                if (hasQuarterlyCustom) {
                    // 有季度自定义，但基础价格依然基于月度
                    applyPricingValues(discountMode, entity, customPricing, monthlyPricing, 3);
                } else if (hasMonthlyCustom) {
                    // 使用月度价格 * 3
                    applyPricingValues(discountMode, entity, monthlyPricing, monthlyPricing, 3);
                } else {
                    // 没有自定义，使用标准定价
                    applyStandardPricingValues(entity, standardPricing);
                }
                break;
            case 3: // 年度
                if (hasYearlyCustom) {
                    // 有年度自定义，但基础价格依然基于月度
                    applyPricingValues(discountMode, entity, customPricing, monthlyPricing, 12);
                } else if (hasMonthlyCustom) {
                    // 使用月度价格 * 12
                    applyPricingValues(discountMode, entity, monthlyPricing, monthlyPricing, 12);
                } else {
                    // 没有自定义，使用标准定价
                    applyStandardPricingValues(entity, standardPricing);
                }
                break;
            default:
                // 其他情况使用标准定价
                applyStandardPricingValues(entity, standardPricing);
        }

        return hotelProductPricingRepository.save(entity);
    }

    /**
     * 应用标准定价值
     */
    private void applyStandardPricingValues(HdsHotelProductPricingEntity entity, HdsProductPricingEntity standardPricing) {
        entity.setCustomDiscountPrice(standardPricing.getDiscountPrice());
        entity.setCustomDiscountRate(standardPricing.getDiscountRate());
        entity.setFinalPrice(standardPricing.getFinalPrice());
        entity.setIsCustomPricing(0);
    }

    /**
     * 应用定价值
     */
    private void applyPricingValues(Integer discountMode, HdsHotelProductPricingEntity entity,
                                    HotelPackageSaveReq.HotelProductCustomPricingReq pricing,
                                    HotelPackageSaveReq.HotelProductCustomPricingReq monthlyPricing,
                                    int multiplier) {
        if (discountMode == 1) {
            // 一口价模式
            if (StringUtils.isNotBlank(pricing.getCustomDiscountPrice())) {
                BigDecimal price = new BigDecimal(pricing.getCustomDiscountPrice());
                entity.setCustomDiscountPrice(price);
                entity.setFinalPrice(price);
            }
        } else {
            // 折扣模式
            BigDecimal discountRate = StringUtils.isNotBlank(pricing.getCustomDiscountRate())
                    ? new BigDecimal(pricing.getCustomDiscountRate())
                    : BigDecimal.ZERO;
            entity.setCustomDiscountRate(discountRate);

            // 基础价格应该使用月度折扣价 * 倍数
            BigDecimal basePrice;
            if (StringUtils.isNotBlank(monthlyPricing.getCustomDiscountPrice())) {
                // 使用月度折扣价乘以倍数
                basePrice = new BigDecimal(monthlyPricing.getCustomDiscountPrice())
                        .multiply(BigDecimal.valueOf(multiplier));
            } else if (StringUtils.isNotBlank(pricing.getCustomDiscountPrice())) {
                // 如果没有月度折扣价，尝试使用当前周期的折扣价
                basePrice = new BigDecimal(pricing.getCustomDiscountPrice());
            } else {
                basePrice = BigDecimal.ZERO;
            }

            // 应用折扣：价格 * (1 - 折扣率/100)
            BigDecimal finalPrice = basePrice.multiply(
                    BigDecimal.ONE.subtract(
                            discountRate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP)
                    )
            ).setScale(2, RoundingMode.HALF_UP);

            entity.setFinalPrice(finalPrice);
            entity.setCustomDiscountPrice(finalPrice);
        }
        entity.setIsCustomPricing(1);
    }

    /**
     * 获取或创建酒店定价实体
     */
    private HdsHotelProductPricingEntity getOrCreateHotelPricingEntity(
            HotelPackageSaveReq.HotelProductCustomPricingReq customPricing,
            String hotelPackageCode,
            Integer productId,
            Integer periodType,
            HeaderUtils.HeaderInfo headerInfo,
            Map<Integer, HdsHotelProductPricingEntity> entityMap) {

        if (customPricing == null || customPricing.getId() == null) {
            return createNewHotelPricingEntity(hotelPackageCode, productId, periodType, headerInfo);
        }

        return entityMap.get(customPricing.getId());
    }

    /**
     * 创建新的酒店定价实体
     */
    private HdsHotelProductPricingEntity createNewHotelPricingEntity(String hotelPackageCode,
                                                                     Integer productId,
                                                                     Integer periodType,
                                                                     HeaderUtils.HeaderInfo headerInfo) {
        HdsHotelProductPricingEntity entity = new HdsHotelProductPricingEntity();
        entity.setHotelPackageCode(hotelPackageCode);
        entity.setProductId(productId);
        entity.setPeriodType(periodType);
        setAuditFields(entity, headerInfo);
        return entity;
    }

    /**
     * 检查价格信息是否需要更新
     */
    private boolean checkPricingNeedUpdate(HdsHotelProductPricingEntity entity, HotelPackageSaveReq.HotelProductCustomPricingReq customPricing, Integer discountMode) {
        // 检查门市价是否变化
        BigDecimal reqMarketPrice = new BigDecimal(customPricing.getCustomMarketPrice());
        if (entity.getCustomMarketPrice().compareTo(reqMarketPrice) != 0) {
            return true;
        }

        // 按照折扣模式检查
        if (Objects.equals(discountMode, 1)) {
            // 一口价模式，检查折扣价
            if (StringUtils.isNotBlank(customPricing.getCustomDiscountPrice())) {
                BigDecimal reqDiscountPrice = new BigDecimal(customPricing.getCustomDiscountPrice());
                return entity.getCustomDiscountPrice() == null || entity.getCustomDiscountPrice().compareTo(reqDiscountPrice) != 0;
            }
        } else {
            // 折扣模式，检查折扣率
            if (StringUtils.isNotBlank(customPricing.getCustomDiscountRate())) {
                BigDecimal reqDiscountRate = new BigDecimal(customPricing.getCustomDiscountRate());
                return entity.getCustomDiscountRate() == null || entity.getCustomDiscountRate().compareTo(reqDiscountRate) != 0;
            }
        }

        return false;
    }

    /**
     * 设置审计字段
     */
    private void setAuditFields(HdsHotelProductPricingEntity entity, HeaderUtils.HeaderInfo headerInfo) {
        LocalDateTime now = LocalDateTime.now();
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(now);
        entity.setUpdatedBy(headerInfo.getUserId());
        entity.setUpdatedByName(headerInfo.getUsername());
        entity.setUpdatedAt(now);
        entity.setRowStatus(1);
    }

    public Flux<HdsHotelProductPricingEntity> findByHotelPackageCode(String hotelPackageCode) {
        return hotelProductPricingRepository.findByHotelPackageCode(hotelPackageCode);
    }

    public Flux<HdsHotelProductPricingEntity> findByHotelPackageCodeAndProductId(String hotelPackageCode, Integer productId) {
        return hotelProductPricingRepository.findByHotelPackageCodeAndProductId(hotelPackageCode, productId);
    }

    public Mono<List<HdsHotelProductPricingEntity>> findByIds(Set<Integer> priceIds) {
        return hotelProductPricingRepository.findByIdIn(priceIds)
                .collectList();
    }

    public Mono<HdsHotelProductPricingEntity> findByHotelPackageCodeAndProductIdAndPeriodType(String hotelPackageCode, Integer productId, Integer periodType) {
        return hotelProductPricingRepository.findByHotelPackageCodeAndProductIdAndPeriodType(hotelPackageCode, productId, periodType);
    }

    public Mono<Void> deleteByHotelPackageCodeAndProductIds(String hotelPackageCode, List<Integer> productIds) {
        return hotelProductPricingRepository.deleteAllByHotelPackageCodeAndProductIdIn(hotelPackageCode, productIds);
    }
}
