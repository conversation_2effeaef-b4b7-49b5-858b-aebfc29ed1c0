package com.wormhole.hotelds.admin.service;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.enums.TaskStatusEnum;
import com.wormhole.hotelds.excel.BaseExportReq;
import com.wormhole.hotelds.excel.GenericExcelService;
import com.wormhole.hotelds.storage.file.FileInput;
import com.wormhole.hotelds.admin.model.req.TaskSearchReq;
import com.wormhole.hotelds.admin.model.req.TemplateDownloadReq;
import com.wormhole.hotelds.admin.model.vo.TaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;

/**
 * @Author：flx
 * @Date：2025/3/28 14:42
 * @Description：ExcelService
 */
@Slf4j
@Service
public class ExcelService {

    @Resource
    private GenericExcelService genericExcelService;

    /**
     * Excel导入
     *
     * @param basePath
     * @param fileInput
     * @return
     */
    public Mono<Integer> importExcel(String basePath, FileInput fileInput, String businessType, String businessId) {
        return genericExcelService.importExcel(basePath, fileInput, businessType, businessId);
    }

    /**
     * excel导出
     *
     * @param req
     * @return
     */
    public Mono<ResponseEntity<InputStreamResource>> exportExcel(BaseExportReq req) {
        return genericExcelService.exportExcel(req);
    }

    /**
     * 下载模板
     *
     * @param templateDownloadReq 下载请求
     * @return ResponseEntity
     */
    public Mono<ResponseEntity<InputStreamResource>> downloadTemplate(TemplateDownloadReq templateDownloadReq) {
        return genericExcelService.downloadTemplate(templateDownloadReq);
    }


    /**
     * 查询任务
     *
     * @param taskSearchReq
     * @return
     */
    public Mono<List<TaskVO>> search(TaskSearchReq taskSearchReq) {
        return genericExcelService.search(taskSearchReq);
    }

    public Mono<Long> searchCount(TaskSearchReq taskSearchReq) {
        return genericExcelService.count(taskSearchReq);
    }

    /**
     * 根据任务id查询任务状态
     *
     * @param taskId
     * @return
     */
    public Mono<TaskVO> queryTaskById(Integer taskId) {
        return genericExcelService.queryTaskById(taskId)
                .timeout(Duration.ofSeconds(10)) // 设置超时
                .delayElement(Duration.ofSeconds(2)) // 成功时延迟2秒再返回
                .onErrorResume(throwable -> {
                    log.warn("查询任务{}超时，返回默认状态", taskId, throwable);
                    return Mono.just(createDefaultTaskVO(taskId));
                });
    }

    private TaskVO createDefaultTaskVO(Integer taskId){
        TaskVO taskVO = new TaskVO();
        taskVO.setId(taskId);
        taskVO.setStatus(TaskStatusEnum.PROCESSING.getCode());
        return taskVO;
    }
}
