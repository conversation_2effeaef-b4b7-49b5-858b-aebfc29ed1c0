environments:
  - name: production_batch1
    description: 生产环境部署第一批次
    env:
      name: production_batch1
      # CNB_BRANCH: 环境变量，部署事件中，为 tag 名
      tag_name: $CNB_BRANCH
    button:
      - name: 回滚第一批次
        # 如存在，则将作为流水线 title，否则流水线使用默认 title
        description: 回滚生产环境第一批次
        event: web_trigger_rollback_production_batch1
    # 部署前置条件检查（支持对环境、元数据、审批流程的检查），满足所有前置条件才可进行部署操作
    require:
    # 1 对部署环境是否满足要求的检查

    # 1.1 要求 development 环境部署成功


    # 对审批流程是否满足要求的检查（可按以下方式自定义审批流程）
    # - 审批顺序：如下 1、2、3 审批流程需按顺序进行，即 1 审批通过，2 才能进行审批。1、2、3 审批流程全部通过才算通过审批
    # - 审批操作：包括 同意、拒绝。一人同意即算通过。如果拒绝，其他审批人无法再操作，直到拒绝的审批人再修改审批结果为同意

    # 按用户名审批，其中一人审批通过即可
    #- approver:
    #    users:
    #      - chunhuai.wang
    #    roles:
    #      - owner
    #      - master
    #  title: 审批



  - name: production_batch2
    description: 生产环境部署第二批次
    env:
      name: production_batch2
      # CNB_BRANCH: 环境变量，部署事件中，为 tag 名
      tag_name: $CNB_BRANCH
    button:
      - name: 回滚上一次版本
        # 如存在，则将作为流水线 title，否则流水线使用默认 title
        description: 回滚生产环境上一次版本
        event: web_trigger_rollback_production_batch2
    # 部署前置条件检查（支持对环境、元数据、审批流程的检查），满足所有前置条件才可进行部署操作
    require:
      # 1 对部署环境是否满足要求的检查

      # 1.1 要求 development 环境部署成功
      - environmentName: production_batch1
      #- approver:
      #    users:
      #      - chunhuai.wang
      #    roles:
      #      - owner
      #      - master
      #      - developer
      #  title: 通过，继续发布

  ##########################################################################################################
  - name: intl_production_batch1
    description: 国际化--新加坡生产-预发，1个节点;<br>正式集群发布后此节点会被回滚;<br><b>去观察服务和日志，10分钟后才可发正式集群</b>;
    env:
      name: intl_production_batch1
      # CNB_BRANCH: 环境变量，部署事件中，为 tag 名
      tag_name: $CNB_BRANCH
    button:
      - name: 国际化--回滚第一批次
        # 如存在，则将作为流水线 title，否则流水线使用默认 title
        description: 国际化--回滚生产环境第一批次
        event: web_trigger_rollback_production_batch1_intl
    # 部署前置条件检查（支持对环境、元数据、审批流程的检查），满足所有前置条件才可进行部署操作
    require:
  - name: intl_production_batch2
    description: 国际化--生产环境部署第二批次
    env:
      name: intl_production_batch2
      # CNB_BRANCH: 环境变量，部署事件中，为 tag 名
      tag_name: $CNB_BRANCH
    button:
      - name: 国际化--新加坡生产-正式滚动发布;<br><b>注意预发节点观察10分钟后才可进行此步骤；</b><br>注意预发节点会被回滚;
        # 如存在，则将作为流水线 title，否则流水线使用默认 title
        description: 国际化--回滚生产环境上一次版本
        event: web_trigger_rollback_production_batch2_intl
    # 部署前置条件检查（支持对环境、元数据、审批流程的检查），满足所有前置条件才可进行部署操作
    require:
      # 1 对部署环境是否满足要求的检查

      # 1.1 要求 development 环境部署成功
      - environmentName: intl_production_batch1

  ##########################################################################################################
  - name: task_production_batch1
    description: task-ai-预发，1个节点;<br>正式集群发布后此节点会被回滚;<br><b>去观察服务和日志，10分钟后才可发正式集群</b>;
    env:
      name: task_production_batch1
      # CNB_BRANCH: 环境变量，部署事件中，为 tag 名
      tag_name: $CNB_BRANCH
    button:
      - name: task-ai--回滚第一批次
        # 如存在，则将作为流水线 title，否则流水线使用默认 title
        description: task-ai--回滚生产环境第一批次
        event: web_trigger_rollback_production_batch1_task
    # 部署前置条件检查（支持对环境、元数据、审批流程的检查），满足所有前置条件才可进行部署操作
    require:
  - name: task_production_batch2
    description: 香港-ai--生产环境部署第二批次
    env:
      name: task_production_batch2
      # CNB_BRANCH: 环境变量，部署事件中，为 tag 名
      tag_name: $CNB_BRANCH
    button:
      - name: task-ai生产-正式滚动发布;<br><b>注意预发节点观察10分钟后才可进行此步骤；</b><br>注意预发节点会被回滚;
        # 如存在，则将作为流水线 title，否则流水线使用默认 title
        description: task-ai--回滚生产环境上一次版本
        event: web_trigger_rollback_production_batch2_task
    # 部署前置条件检查（支持对环境、元数据、审批流程的检查），满足所有前置条件才可进行部署操作
    require:
      # 1 对部署环境是否满足要求的检查
      # 1.1 要求 development 环境部署成功
      - environmentName: task_production_batch1